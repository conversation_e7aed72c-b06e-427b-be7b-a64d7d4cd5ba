{"ast": null, "code": "import React,{useState,useEffect}from'react';import{useNavigate}from'react-router-dom';import axios from'axios';import{FaUsers,FaUserPlus,FaUserCheck,FaUserClock,FaEdit,FaSearch,FaEye,FaUserSlash,FaBan}from'react-icons/fa';import CustomModal from'../components/CustomModal';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const API_BASE_URL='/backend';function UserManagement(){const navigate=useNavigate();const[users,setUsers]=useState([]);const[teams,setTeams]=useState([]);const[error,setError]=useState('');const[success,setSuccess]=useState('');const[editingUserId,setEditingUserId]=useState(null);const[searchTerm,setSearchTerm]=useState('');const[currentPage,setCurrentPage]=useState(1);const[usersPerPage]=useState(10);const[stats,setStats]=useState({totalUsers:0,activeUsers:0,newUsers:0,pendingUsers:0});const[editingUser,setEditingUser]=useState({username:'',full_name:'',email:'',favorite_team:'',balance:0});const[showAddUserModal,setShowAddUserModal]=useState(false);const[newUser,setNewUser]=useState({username:'',full_name:'',email:'',password:'',favorite_team:'',balance:0});// Modal states\nconst[modalState,setModalState]=useState({isOpen:false,type:'confirm',title:'',message:'',onConfirm:null,confirmText:'Confirm',confirmButtonColor:'blue'});useEffect(()=>{fetchUsers();fetchTeams();},[]);const fetchUsers=async()=>{try{const response=await axios.get(`${API_BASE_URL}/handlers/user_management.php`);if(response.data.success){const userData=response.data.data||[];setUsers(userData);// Calculate stats\nconst now=new Date();const oneWeekAgo=new Date(now.getTime()-7*24*60*60*1000);// For demo purposes, we'll simulate some stats\nconst totalUsers=userData.length;const activeUsers=userData.filter(user=>user.last_login&&new Date(user.last_login)>oneWeekAgo).length||Math.floor(totalUsers*0.7);const newUsers=userData.filter(user=>user.created_at&&new Date(user.created_at)>oneWeekAgo).length||Math.floor(totalUsers*0.2);const pendingUsers=userData.filter(user=>user.status==='pending').length||Math.floor(totalUsers*0.1);setStats({totalUsers,activeUsers,newUsers,pendingUsers});}else{setError(response.data.message||'Failed to fetch users');}}catch(err){setError('Failed to fetch users. Please check your network connection and try again.');console.error('Error fetching users:',err);}};const fetchTeams=async()=>{try{const response=await axios.get(`${API_BASE_URL}/handlers/team_management.php`);setTeams(response.data.data||[]);}catch(err){setError('Failed to fetch teams');}};const getTeamLogo=teamName=>{const team=teams.find(team=>team.name===teamName);return team?`${API_BASE_URL}/${team.logo}`:null;};const getDefaultAvatar=()=>{return\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='40' height='40' viewBox='0 0 40 40'%3E%3Ccircle cx='20' cy='20' r='20' fill='%23e5e7eb'/%3E%3Cpath d='M20 20c3.3 0 6-2.7 6-6s-2.7-6-6-6-6 2.7-6 6 2.7 6 6 6zm0 3c-4 0-12 2-12 6v3h24v-3c0-4-8-6-12-6z' fill='%23374151'/%3E%3C/svg%3E\";};const handleEdit=user=>{setEditingUserId(user.user_id);setEditingUser(user);};const handleUpdate=async e=>{e.preventDefault();try{const response=await axios.put(`${API_BASE_URL}/handlers/user_management.php?id=${editingUserId}`,editingUser);if(response.data.success){setSuccess('User updated successfully!');fetchUsers();setEditingUserId(null);setTimeout(()=>setSuccess(''),3000);}else{setError(response.data.message||'Failed to update user');setTimeout(()=>setError(''),3000);}}catch(err){setError('Failed to update user');}};const handleInputChange=e=>{const{name,value}=e.target;setEditingUser(prev=>({...prev,[name]:value}));};const handleNewUserInputChange=e=>{const{name,value}=e.target;setNewUser(prev=>({...prev,[name]:value}));};const handleAddUser=async e=>{e.preventDefault();try{const response=await axios.post(`${API_BASE_URL}/handlers/add_user.php`,newUser);if(response.data.success){setSuccess('User added successfully!');fetchUsers();setShowAddUserModal(false);setNewUser({username:'',full_name:'',email:'',password:'',favorite_team:'',balance:0});// Clear success message after 3 seconds\nsetTimeout(()=>setSuccess(''),3000);}else{setError(response.data.message||'Failed to add user');setTimeout(()=>setError(''),3000);}}catch(err){setError('Failed to add user');setTimeout(()=>setError(''),3000);}};const handleSuspendUser=user=>{setModalState({isOpen:true,type:'confirm',title:'Suspend User',message:`Are you sure you want to suspend user \"${user.username}\"? This will prevent them from accessing their account.`,confirmText:'Suspend User',confirmButtonColor:'yellow',onConfirm:()=>performSuspendUser(user)});};const performSuspendUser=async user=>{try{const response=await axios.post(`${API_BASE_URL}/handlers/suspend_user.php`,{user_id:user.user_id,action:'suspend'});if(response.data.success){setSuccess(`User \"${user.username}\" suspended successfully!`);fetchUsers();setTimeout(()=>setSuccess(''),3000);}else{setError(response.data.message||'Failed to suspend user');setTimeout(()=>setError(''),3000);}}catch(err){setError('Failed to suspend user');setTimeout(()=>setError(''),3000);}};const handleBanUser=user=>{setModalState({isOpen:true,type:'confirm',title:'Ban User',message:`Are you sure you want to ban user \"${user.username}\"? This action cannot be undone and will permanently prevent them from accessing their account.`,confirmText:'Ban User',confirmButtonColor:'red',onConfirm:()=>performBanUser(user)});};const performBanUser=async user=>{try{const response=await axios.post(`${API_BASE_URL}/handlers/ban_user.php`,{user_id:user.user_id,action:'ban'});if(response.data.success){setSuccess(`User \"${user.username}\" banned successfully!`);fetchUsers();setTimeout(()=>setSuccess(''),3000);}else{setError(response.data.message||'Failed to ban user');setTimeout(()=>setError(''),3000);}}catch(err){setError('Failed to ban user');setTimeout(()=>setError(''),3000);}};// Search functionality\nconst handleSearch=e=>{setSearchTerm(e.target.value);setCurrentPage(1);// Reset to first page when searching\n};// Filter users based on search term\nconst filteredUsers=users.filter(user=>user.username.toLowerCase().includes(searchTerm.toLowerCase())||user.full_name.toLowerCase().includes(searchTerm.toLowerCase())||user.email.toLowerCase().includes(searchTerm.toLowerCase()));// Pagination\nconst indexOfLastUser=currentPage*usersPerPage;const indexOfFirstUser=indexOfLastUser-usersPerPage;const currentUsers=filteredUsers.slice(indexOfFirstUser,indexOfLastUser);const totalPages=Math.ceil(filteredUsers.length/usersPerPage);const paginate=pageNumber=>setCurrentPage(pageNumber);// Generate page numbers\nconst pageNumbers=[];for(let i=1;i<=totalPages;i++){pageNumbers.push(i);}return/*#__PURE__*/_jsxs(\"div\",{className:\"p-6 bg-gray-50 min-h-screen\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"mb-8\",children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-2xl font-bold text-gray-800\",children:\"User Management\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600\",children:\"Manage all users in the system\"})]}),error&&/*#__PURE__*/_jsx(\"div\",{className:\"mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative\",role:\"alert\",children:/*#__PURE__*/_jsx(\"span\",{className:\"block sm:inline\",children:error})}),success&&/*#__PURE__*/_jsx(\"div\",{className:\"mb-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative\",role:\"alert\",children:/*#__PURE__*/_jsx(\"span\",{className:\"block sm:inline\",children:success})}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded-lg shadow-sm p-6 flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"rounded-full bg-blue-100 p-3 mr-4\",children:/*#__PURE__*/_jsx(FaUsers,{className:\"text-blue-500 text-xl\"})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-500 uppercase tracking-wider\",children:\"Total Users\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-2xl font-bold text-gray-800\",children:stats.totalUsers})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded-lg shadow-sm p-6 flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"rounded-full bg-green-100 p-3 mr-4\",children:/*#__PURE__*/_jsx(FaUserCheck,{className:\"text-green-500 text-xl\"})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-500 uppercase tracking-wider\",children:\"Active Users\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-2xl font-bold text-gray-800\",children:stats.activeUsers})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded-lg shadow-sm p-6 flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"rounded-full bg-yellow-100 p-3 mr-4\",children:/*#__PURE__*/_jsx(FaUserPlus,{className:\"text-yellow-500 text-xl\"})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-500 uppercase tracking-wider\",children:\"New Users (7d)\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-2xl font-bold text-gray-800\",children:stats.newUsers})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded-lg shadow-sm p-6 flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"rounded-full bg-purple-100 p-3 mr-4\",children:/*#__PURE__*/_jsx(FaUserClock,{className:\"text-purple-500 text-xl\"})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-500 uppercase tracking-wider\",children:\"Pending Users\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-2xl font-bold text-gray-800\",children:stats.pendingUsers})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded-lg shadow-sm p-6 mb-8\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col md:flex-row md:items-center md:justify-between mb-4\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-lg font-semibold text-white bg-blue-600 px-4 py-2 rounded mb-4 md:mb-0\",children:\"User List\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col md:flex-row md:items-center space-y-2 md:space-y-0 md:space-x-4\",children:[/*#__PURE__*/_jsxs(\"button\",{onClick:()=>setShowAddUserModal(true),className:\"flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700\",children:[/*#__PURE__*/_jsx(FaUserPlus,{className:\"mr-2\"}),\"Add User\"]}),/*#__PURE__*/_jsxs(\"div\",{className:\"relative w-full md:w-64\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",children:/*#__PURE__*/_jsx(FaSearch,{className:\"h-4 w-4 text-gray-400\"})}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",className:\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm\",placeholder:\"Search users...\",value:searchTerm,onChange:handleSearch})]})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"overflow-x-auto\",children:/*#__PURE__*/_jsxs(\"table\",{className:\"min-w-full divide-y divide-gray-200\",children:[/*#__PURE__*/_jsx(\"thead\",{className:\"bg-blue-600\",children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{scope:\"col\",className:\"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider\",children:\"#\"}),/*#__PURE__*/_jsx(\"th\",{scope:\"col\",className:\"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider\",children:\"Avatar\"}),/*#__PURE__*/_jsx(\"th\",{scope:\"col\",className:\"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider\",children:\"Username\"}),/*#__PURE__*/_jsx(\"th\",{scope:\"col\",className:\"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider\",children:\"Full Name\"}),/*#__PURE__*/_jsx(\"th\",{scope:\"col\",className:\"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider\",children:\"Email\"}),/*#__PURE__*/_jsx(\"th\",{scope:\"col\",className:\"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider\",children:\"Favorite Team\"}),/*#__PURE__*/_jsx(\"th\",{scope:\"col\",className:\"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider\",children:\"Balance\"}),/*#__PURE__*/_jsx(\"th\",{scope:\"col\",className:\"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider\",children:\"Actions\"})]})}),/*#__PURE__*/_jsx(\"tbody\",{className:\"bg-white divide-y divide-gray-200\",children:currentUsers.map((user,index)=>/*#__PURE__*/_jsxs(\"tr\",{className:\"hover:bg-gray-50\",children:[/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",children:indexOfFirstUser+index+1}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap\",children:/*#__PURE__*/_jsx(\"div\",{className:\"flex items-center\",children:/*#__PURE__*/_jsx(\"img\",{src:getTeamLogo(user.favorite_team)||getDefaultAvatar(),alt:user.favorite_team||'Default Avatar',className:\"w-10 h-10 rounded-full object-contain border border-gray-200\",onError:e=>{e.target.src=getDefaultAvatar();}})})}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap\",children:/*#__PURE__*/_jsx(\"div\",{className:\"text-sm font-medium text-gray-900\",children:user.username})}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap\",children:/*#__PURE__*/_jsx(\"div\",{className:\"text-sm text-gray-500\",children:user.full_name})}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap\",children:/*#__PURE__*/_jsx(\"div\",{className:\"text-sm text-gray-500\",children:user.email})}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2\",children:[user.favorite_team&&getTeamLogo(user.favorite_team)&&/*#__PURE__*/_jsx(\"img\",{src:getTeamLogo(user.favorite_team),alt:user.favorite_team,className:\"w-5 h-5 rounded-full object-contain\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-sm text-gray-500\",children:user.favorite_team||'No team selected'})]})}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"text-sm font-medium text-gray-900\",children:[user.balance,\" FanCoins\"]})}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap text-sm font-medium\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex space-x-1\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>navigate(`/admin/users/${user.user_id}`),className:\"text-green-600 hover:text-green-900 p-1\",title:\"View Details\",children:/*#__PURE__*/_jsx(FaEye,{})}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>handleEdit(user),className:\"text-blue-600 hover:text-blue-900 p-1\",title:\"Edit User\",children:/*#__PURE__*/_jsx(FaEdit,{})}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>handleSuspendUser(user),className:\"text-yellow-600 hover:text-yellow-900 p-1\",title:\"Suspend User\",children:/*#__PURE__*/_jsx(FaUserSlash,{})}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>handleBanUser(user),className:\"text-red-600 hover:text-red-900 p-1\",title:\"Ban User\",children:/*#__PURE__*/_jsx(FaBan,{})})]})})]},user.user_id))})]})}),totalPages>1&&/*#__PURE__*/_jsx(\"div\",{className:\"flex items-center justify-between border-t border-gray-200 px-4 py-3 sm:px-6 mt-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between\",children:[/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsxs(\"p\",{className:\"text-sm text-gray-700\",children:[\"Showing \",/*#__PURE__*/_jsx(\"span\",{className:\"font-medium\",children:indexOfFirstUser+1}),\" to\",' ',/*#__PURE__*/_jsx(\"span\",{className:\"font-medium\",children:indexOfLastUser>filteredUsers.length?filteredUsers.length:indexOfLastUser}),' ',\"of \",/*#__PURE__*/_jsx(\"span\",{className:\"font-medium\",children:filteredUsers.length}),\" results\"]})}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsxs(\"nav\",{className:\"relative z-0 inline-flex rounded-md shadow-sm -space-x-px\",\"aria-label\":\"Pagination\",children:[/*#__PURE__*/_jsxs(\"button\",{onClick:()=>paginate(currentPage>1?currentPage-1:1),disabled:currentPage===1,className:`relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium ${currentPage===1?'text-gray-300 cursor-not-allowed':'text-gray-500 hover:bg-gray-50'}`,children:[/*#__PURE__*/_jsx(\"span\",{className:\"sr-only\",children:\"Previous\"}),/*#__PURE__*/_jsx(\"svg\",{className:\"h-5 w-5\",xmlns:\"http://www.w3.org/2000/svg\",viewBox:\"0 0 20 20\",fill:\"currentColor\",\"aria-hidden\":\"true\",children:/*#__PURE__*/_jsx(\"path\",{fillRule:\"evenodd\",d:\"M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z\",clipRule:\"evenodd\"})})]}),pageNumbers.map(number=>/*#__PURE__*/_jsx(\"button\",{onClick:()=>paginate(number),className:`relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium ${currentPage===number?'z-10 bg-blue-50 border-blue-500 text-blue-600':'text-gray-500 hover:bg-gray-50'}`,children:number},number)),/*#__PURE__*/_jsxs(\"button\",{onClick:()=>paginate(currentPage<totalPages?currentPage+1:totalPages),disabled:currentPage===totalPages,className:`relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium ${currentPage===totalPages?'text-gray-300 cursor-not-allowed':'text-gray-500 hover:bg-gray-50'}`,children:[/*#__PURE__*/_jsx(\"span\",{className:\"sr-only\",children:\"Next\"}),/*#__PURE__*/_jsx(\"svg\",{className:\"h-5 w-5\",xmlns:\"http://www.w3.org/2000/svg\",viewBox:\"0 0 20 20\",fill:\"currentColor\",\"aria-hidden\":\"true\",children:/*#__PURE__*/_jsx(\"path\",{fillRule:\"evenodd\",d:\"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z\",clipRule:\"evenodd\"})})]})]})})]})})]}),editingUserId&&/*#__PURE__*/_jsx(\"div\",{className:\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded-lg shadow-xl w-full max-w-md\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between items-center p-4 border-b\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-semibold text-gray-800\",children:\"Edit User\"}),/*#__PURE__*/_jsx(\"button\",{className:\"text-gray-500 hover:text-gray-700 text-2xl focus:outline-none\",onClick:()=>setEditingUserId(null),children:\"\\xD7\"})]}),/*#__PURE__*/_jsxs(\"form\",{onSubmit:handleUpdate,className:\"p-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-4\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-1\",children:\"Username\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",name:\"username\",value:editingUser.username,onChange:handleInputChange,className:\"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\",required:true})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-1\",children:\"Full Name\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",name:\"full_name\",value:editingUser.full_name,onChange:handleInputChange,className:\"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\",required:true})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-1\",children:\"Email\"}),/*#__PURE__*/_jsx(\"input\",{type:\"email\",name:\"email\",value:editingUser.email,onChange:handleInputChange,className:\"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\",required:true})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-1\",children:\"Favorite Team\"}),/*#__PURE__*/_jsxs(\"select\",{name:\"favorite_team\",value:editingUser.favorite_team,onChange:handleInputChange,className:\"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\",required:true,children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select Favorite Team\"}),teams.map(team=>/*#__PURE__*/_jsx(\"option\",{value:team.name,children:team.name},team.id))]})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-1\",children:\"Balance (FanCoins)\"}),/*#__PURE__*/_jsx(\"input\",{type:\"number\",name:\"balance\",value:editingUser.balance,onChange:handleInputChange,className:\"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\",required:true})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mt-6 flex justify-end space-x-3\",children:[/*#__PURE__*/_jsx(\"button\",{type:\"button\",onClick:()=>setEditingUserId(null),className:\"px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",children:\"Cancel\"}),/*#__PURE__*/_jsx(\"button\",{type:\"submit\",className:\"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",children:\"Update User\"})]})]})]})}),showAddUserModal&&/*#__PURE__*/_jsx(\"div\",{className:\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded-lg shadow-xl w-full max-w-md\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between items-center p-4 border-b\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-semibold text-gray-800\",children:\"Add New User\"}),/*#__PURE__*/_jsx(\"button\",{className:\"text-gray-500 hover:text-gray-700 text-2xl focus:outline-none\",onClick:()=>setShowAddUserModal(false),children:\"\\xD7\"})]}),/*#__PURE__*/_jsxs(\"form\",{onSubmit:handleAddUser,className:\"p-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-4\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-1\",children:\"Username\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",name:\"username\",value:newUser.username,onChange:handleNewUserInputChange,className:\"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm\",required:true})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-1\",children:\"Full Name\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",name:\"full_name\",value:newUser.full_name,onChange:handleNewUserInputChange,className:\"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm\",required:true})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-1\",children:\"Email\"}),/*#__PURE__*/_jsx(\"input\",{type:\"email\",name:\"email\",value:newUser.email,onChange:handleNewUserInputChange,className:\"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm\",required:true})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-1\",children:\"Password\"}),/*#__PURE__*/_jsx(\"input\",{type:\"password\",name:\"password\",value:newUser.password,onChange:handleNewUserInputChange,className:\"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm\",required:true})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-1\",children:\"Favorite Team\"}),/*#__PURE__*/_jsxs(\"select\",{name:\"favorite_team\",value:newUser.favorite_team,onChange:handleNewUserInputChange,className:\"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select Favorite Team\"}),teams.map(team=>/*#__PURE__*/_jsx(\"option\",{value:team.name,children:team.name},team.id))]})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-1\",children:\"Initial Balance (FanCoins)\"}),/*#__PURE__*/_jsx(\"input\",{type:\"number\",name:\"balance\",value:newUser.balance,onChange:handleNewUserInputChange,className:\"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm\",min:\"0\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mt-6 flex justify-end space-x-3\",children:[/*#__PURE__*/_jsx(\"button\",{type:\"button\",onClick:()=>setShowAddUserModal(false),className:\"px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500\",children:\"Cancel\"}),/*#__PURE__*/_jsx(\"button\",{type:\"submit\",className:\"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500\",children:\"Add User\"})]})]})]})}),/*#__PURE__*/_jsx(CustomModal,{isOpen:modalState.isOpen,onClose:()=>setModalState({...modalState,isOpen:false}),onConfirm:modalState.onConfirm,title:modalState.title,message:modalState.message,type:modalState.type,confirmText:modalState.confirmText,confirmButtonColor:modalState.confirmButtonColor})]});}export default UserManagement;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "axios", "FaUsers", "FaUserPlus", "FaUserCheck", "FaUserClock", "FaEdit", "FaSearch", "FaEye", "FaUserSlash", "FaBan", "CustomModal", "jsx", "_jsx", "jsxs", "_jsxs", "API_BASE_URL", "UserManagement", "navigate", "users", "setUsers", "teams", "setTeams", "error", "setError", "success", "setSuccess", "editingUserId", "setEditingUserId", "searchTerm", "setSearchTerm", "currentPage", "setCurrentPage", "usersPerPage", "stats", "setStats", "totalUsers", "activeUsers", "newUsers", "pendingUsers", "editingUser", "setEditingUser", "username", "full_name", "email", "favorite_team", "balance", "showAddUserModal", "setShowAddUserModal", "newUser", "setNewUser", "password", "modalState", "setModalState", "isOpen", "type", "title", "message", "onConfirm", "confirmText", "confirmButtonColor", "fetchUsers", "fetchTeams", "response", "get", "data", "userData", "now", "Date", "oneWeekAgo", "getTime", "length", "filter", "user", "last_login", "Math", "floor", "created_at", "status", "err", "console", "getTeamLogo", "teamName", "team", "find", "name", "logo", "getDefaultAvatar", "handleEdit", "user_id", "handleUpdate", "e", "preventDefault", "put", "setTimeout", "handleInputChange", "value", "target", "prev", "handleNewUserInputChange", "handleAddUser", "post", "handleSuspendUser", "performSuspendUser", "action", "handleBanUser", "performBanUser", "handleSearch", "filteredUsers", "toLowerCase", "includes", "indexOfLastUser", "indexOfFirstUser", "currentUsers", "slice", "totalPages", "ceil", "paginate", "pageNumber", "pageNumbers", "i", "push", "className", "children", "role", "onClick", "placeholder", "onChange", "scope", "map", "index", "src", "alt", "onError", "disabled", "xmlns", "viewBox", "fill", "fillRule", "d", "clipRule", "number", "onSubmit", "required", "id", "min", "onClose"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/pages/UserManagement.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport axios from 'axios';\r\nimport { FaUsers, FaUserPlus, FaUserCheck, FaUserClock, FaEdit, FaSearch, FaEye, FaUserSlash, FaBan } from 'react-icons/fa';\r\nimport CustomModal from '../components/CustomModal';\r\n\r\nconst API_BASE_URL = '/backend';\r\n\r\nfunction UserManagement() {\r\n    const navigate = useNavigate();\r\n    const [users, setUsers] = useState([]);\r\n    const [teams, setTeams] = useState([]);\r\n    const [error, setError] = useState('');\r\n    const [success, setSuccess] = useState('');\r\n    const [editingUserId, setEditingUserId] = useState(null);\r\n    const [searchTerm, setSearchTerm] = useState('');\r\n    const [currentPage, setCurrentPage] = useState(1);\r\n    const [usersPerPage] = useState(10);\r\n    const [stats, setStats] = useState({\r\n        totalUsers: 0,\r\n        activeUsers: 0,\r\n        newUsers: 0,\r\n        pendingUsers: 0\r\n    });\r\n    const [editingUser, setEditingUser] = useState({\r\n        username: '',\r\n        full_name: '',\r\n        email: '',\r\n        favorite_team: '',\r\n        balance: 0\r\n    });\r\n    const [showAddUserModal, setShowAddUserModal] = useState(false);\r\n    const [newUser, setNewUser] = useState({\r\n        username: '',\r\n        full_name: '',\r\n        email: '',\r\n        password: '',\r\n        favorite_team: '',\r\n        balance: 0\r\n    });\r\n\r\n    // Modal states\r\n    const [modalState, setModalState] = useState({\r\n        isOpen: false,\r\n        type: 'confirm',\r\n        title: '',\r\n        message: '',\r\n        onConfirm: null,\r\n        confirmText: 'Confirm',\r\n        confirmButtonColor: 'blue'\r\n    });\r\n\r\n    useEffect(() => {\r\n        fetchUsers();\r\n        fetchTeams();\r\n    }, []);\r\n\r\n    const fetchUsers = async () => {\r\n        try {\r\n            const response = await axios.get(`${API_BASE_URL}/handlers/user_management.php`);\r\n            if (response.data.success) {\r\n                const userData = response.data.data || [];\r\n                setUsers(userData);\r\n\r\n                // Calculate stats\r\n                const now = new Date();\r\n                const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);\r\n\r\n                // For demo purposes, we'll simulate some stats\r\n                const totalUsers = userData.length;\r\n                const activeUsers = userData.filter(user => user.last_login && new Date(user.last_login) > oneWeekAgo).length || Math.floor(totalUsers * 0.7);\r\n                const newUsers = userData.filter(user => user.created_at && new Date(user.created_at) > oneWeekAgo).length || Math.floor(totalUsers * 0.2);\r\n                const pendingUsers = userData.filter(user => user.status === 'pending').length || Math.floor(totalUsers * 0.1);\r\n\r\n                setStats({\r\n                    totalUsers,\r\n                    activeUsers,\r\n                    newUsers,\r\n                    pendingUsers\r\n                });\r\n            } else {\r\n                setError(response.data.message || 'Failed to fetch users');\r\n            }\r\n        } catch (err) {\r\n            setError('Failed to fetch users. Please check your network connection and try again.');\r\n            console.error('Error fetching users:', err);\r\n        }\r\n    };\r\n\r\n    const fetchTeams = async () => {\r\n        try {\r\n            const response = await axios.get(`${API_BASE_URL}/handlers/team_management.php`);\r\n            setTeams(response.data.data || []);\r\n        } catch (err) {\r\n            setError('Failed to fetch teams');\r\n        }\r\n    };\r\n\r\n    const getTeamLogo = (teamName) => {\r\n        const team = teams.find(team => team.name === teamName);\r\n        return team ? `${API_BASE_URL}/${team.logo}` : null;\r\n    };\r\n\r\n    const getDefaultAvatar = () => {\r\n        return \"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='40' height='40' viewBox='0 0 40 40'%3E%3Ccircle cx='20' cy='20' r='20' fill='%23e5e7eb'/%3E%3Cpath d='M20 20c3.3 0 6-2.7 6-6s-2.7-6-6-6-6 2.7-6 6 2.7 6 6 6zm0 3c-4 0-12 2-12 6v3h24v-3c0-4-8-6-12-6z' fill='%23374151'/%3E%3C/svg%3E\";\r\n    };\r\n\r\n    const handleEdit = (user) => {\r\n        setEditingUserId(user.user_id);\r\n        setEditingUser(user);\r\n    };\r\n\r\n    const handleUpdate = async (e) => {\r\n        e.preventDefault();\r\n        try {\r\n            const response = await axios.put(`${API_BASE_URL}/handlers/user_management.php?id=${editingUserId}`, editingUser);\r\n            if (response.data.success) {\r\n                setSuccess('User updated successfully!');\r\n                fetchUsers();\r\n                setEditingUserId(null);\r\n                setTimeout(() => setSuccess(''), 3000);\r\n            } else {\r\n                setError(response.data.message || 'Failed to update user');\r\n                setTimeout(() => setError(''), 3000);\r\n            }\r\n        } catch (err) {\r\n            setError('Failed to update user');\r\n        }\r\n    };\r\n\r\n    const handleInputChange = (e) => {\r\n        const { name, value } = e.target;\r\n        setEditingUser(prev => ({ ...prev, [name]: value }));\r\n    };\r\n\r\n    const handleNewUserInputChange = (e) => {\r\n        const { name, value } = e.target;\r\n        setNewUser(prev => ({ ...prev, [name]: value }));\r\n    };\r\n\r\n    const handleAddUser = async (e) => {\r\n        e.preventDefault();\r\n        try {\r\n            const response = await axios.post(`${API_BASE_URL}/handlers/add_user.php`, newUser);\r\n            if (response.data.success) {\r\n                setSuccess('User added successfully!');\r\n                fetchUsers();\r\n                setShowAddUserModal(false);\r\n                setNewUser({\r\n                    username: '',\r\n                    full_name: '',\r\n                    email: '',\r\n                    password: '',\r\n                    favorite_team: '',\r\n                    balance: 0\r\n                });\r\n                // Clear success message after 3 seconds\r\n                setTimeout(() => setSuccess(''), 3000);\r\n            } else {\r\n                setError(response.data.message || 'Failed to add user');\r\n                setTimeout(() => setError(''), 3000);\r\n            }\r\n        } catch (err) {\r\n            setError('Failed to add user');\r\n            setTimeout(() => setError(''), 3000);\r\n        }\r\n    };\r\n\r\n    const handleSuspendUser = (user) => {\r\n        setModalState({\r\n            isOpen: true,\r\n            type: 'confirm',\r\n            title: 'Suspend User',\r\n            message: `Are you sure you want to suspend user \"${user.username}\"? This will prevent them from accessing their account.`,\r\n            confirmText: 'Suspend User',\r\n            confirmButtonColor: 'yellow',\r\n            onConfirm: () => performSuspendUser(user)\r\n        });\r\n    };\r\n\r\n    const performSuspendUser = async (user) => {\r\n        try {\r\n            const response = await axios.post(`${API_BASE_URL}/handlers/suspend_user.php`, {\r\n                user_id: user.user_id,\r\n                action: 'suspend'\r\n            });\r\n            if (response.data.success) {\r\n                setSuccess(`User \"${user.username}\" suspended successfully!`);\r\n                fetchUsers();\r\n                setTimeout(() => setSuccess(''), 3000);\r\n            } else {\r\n                setError(response.data.message || 'Failed to suspend user');\r\n                setTimeout(() => setError(''), 3000);\r\n            }\r\n        } catch (err) {\r\n            setError('Failed to suspend user');\r\n            setTimeout(() => setError(''), 3000);\r\n        }\r\n    };\r\n\r\n    const handleBanUser = (user) => {\r\n        setModalState({\r\n            isOpen: true,\r\n            type: 'confirm',\r\n            title: 'Ban User',\r\n            message: `Are you sure you want to ban user \"${user.username}\"? This action cannot be undone and will permanently prevent them from accessing their account.`,\r\n            confirmText: 'Ban User',\r\n            confirmButtonColor: 'red',\r\n            onConfirm: () => performBanUser(user)\r\n        });\r\n    };\r\n\r\n    const performBanUser = async (user) => {\r\n        try {\r\n            const response = await axios.post(`${API_BASE_URL}/handlers/ban_user.php`, {\r\n                user_id: user.user_id,\r\n                action: 'ban'\r\n            });\r\n            if (response.data.success) {\r\n                setSuccess(`User \"${user.username}\" banned successfully!`);\r\n                fetchUsers();\r\n                setTimeout(() => setSuccess(''), 3000);\r\n            } else {\r\n                setError(response.data.message || 'Failed to ban user');\r\n                setTimeout(() => setError(''), 3000);\r\n            }\r\n        } catch (err) {\r\n            setError('Failed to ban user');\r\n            setTimeout(() => setError(''), 3000);\r\n        }\r\n    };\r\n\r\n    // Search functionality\r\n    const handleSearch = (e) => {\r\n        setSearchTerm(e.target.value);\r\n        setCurrentPage(1); // Reset to first page when searching\r\n    };\r\n\r\n    // Filter users based on search term\r\n    const filteredUsers = users.filter(user =>\r\n        user.username.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n        user.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n        user.email.toLowerCase().includes(searchTerm.toLowerCase())\r\n    );\r\n\r\n    // Pagination\r\n    const indexOfLastUser = currentPage * usersPerPage;\r\n    const indexOfFirstUser = indexOfLastUser - usersPerPage;\r\n    const currentUsers = filteredUsers.slice(indexOfFirstUser, indexOfLastUser);\r\n    const totalPages = Math.ceil(filteredUsers.length / usersPerPage);\r\n\r\n    const paginate = (pageNumber) => setCurrentPage(pageNumber);\r\n\r\n    // Generate page numbers\r\n    const pageNumbers = [];\r\n    for (let i = 1; i <= totalPages; i++) {\r\n        pageNumbers.push(i);\r\n    }\r\n\r\n    return (\r\n        <div className=\"p-6 bg-gray-50 min-h-screen\">\r\n            {/* Page Header */}\r\n            <div className=\"mb-8\">\r\n                <h1 className=\"text-2xl font-bold text-gray-800\">User Management</h1>\r\n                <p className=\"text-gray-600\">Manage all users in the system</p>\r\n            </div>\r\n\r\n            {/* Notification Messages */}\r\n            {error && (\r\n                <div className=\"mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative\" role=\"alert\">\r\n                    <span className=\"block sm:inline\">{error}</span>\r\n                </div>\r\n            )}\r\n            {success && (\r\n                <div className=\"mb-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative\" role=\"alert\">\r\n                    <span className=\"block sm:inline\">{success}</span>\r\n                </div>\r\n            )}\r\n\r\n            {/* Stats Cards */}\r\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\r\n                {/* Total Users */}\r\n                <div className=\"bg-white rounded-lg shadow-sm p-6 flex items-center\">\r\n                    <div className=\"rounded-full bg-blue-100 p-3 mr-4\">\r\n                        <FaUsers className=\"text-blue-500 text-xl\" />\r\n                    </div>\r\n                    <div>\r\n                        <p className=\"text-sm text-gray-500 uppercase tracking-wider\">Total Users</p>\r\n                        <h3 className=\"text-2xl font-bold text-gray-800\">{stats.totalUsers}</h3>\r\n                    </div>\r\n                </div>\r\n\r\n                {/* Active Users */}\r\n                <div className=\"bg-white rounded-lg shadow-sm p-6 flex items-center\">\r\n                    <div className=\"rounded-full bg-green-100 p-3 mr-4\">\r\n                        <FaUserCheck className=\"text-green-500 text-xl\" />\r\n                    </div>\r\n                    <div>\r\n                        <p className=\"text-sm text-gray-500 uppercase tracking-wider\">Active Users</p>\r\n                        <h3 className=\"text-2xl font-bold text-gray-800\">{stats.activeUsers}</h3>\r\n                    </div>\r\n                </div>\r\n\r\n                {/* New Users */}\r\n                <div className=\"bg-white rounded-lg shadow-sm p-6 flex items-center\">\r\n                    <div className=\"rounded-full bg-yellow-100 p-3 mr-4\">\r\n                        <FaUserPlus className=\"text-yellow-500 text-xl\" />\r\n                    </div>\r\n                    <div>\r\n                        <p className=\"text-sm text-gray-500 uppercase tracking-wider\">New Users (7d)</p>\r\n                        <h3 className=\"text-2xl font-bold text-gray-800\">{stats.newUsers}</h3>\r\n                    </div>\r\n                </div>\r\n\r\n                {/* Pending Users */}\r\n                <div className=\"bg-white rounded-lg shadow-sm p-6 flex items-center\">\r\n                    <div className=\"rounded-full bg-purple-100 p-3 mr-4\">\r\n                        <FaUserClock className=\"text-purple-500 text-xl\" />\r\n                    </div>\r\n                    <div>\r\n                        <p className=\"text-sm text-gray-500 uppercase tracking-wider\">Pending Users</p>\r\n                        <h3 className=\"text-2xl font-bold text-gray-800\">{stats.pendingUsers}</h3>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            {/* Search and Filter */}\r\n            <div className=\"bg-white rounded-lg shadow-sm p-6 mb-8\">\r\n                <div className=\"flex flex-col md:flex-row md:items-center md:justify-between mb-4\">\r\n                    <h2 className=\"text-lg font-semibold text-white bg-blue-600 px-4 py-2 rounded mb-4 md:mb-0\">User List</h2>\r\n                    <div className=\"flex flex-col md:flex-row md:items-center space-y-2 md:space-y-0 md:space-x-4\">\r\n                        <button\r\n                            onClick={() => setShowAddUserModal(true)}\r\n                            className=\"flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700\"\r\n                        >\r\n                            <FaUserPlus className=\"mr-2\" />\r\n                            Add User\r\n                        </button>\r\n                        <div className=\"relative w-full md:w-64\">\r\n                            <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n                                <FaSearch className=\"h-4 w-4 text-gray-400\" />\r\n                            </div>\r\n                            <input\r\n                                type=\"text\"\r\n                                className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm\"\r\n                                placeholder=\"Search users...\"\r\n                                value={searchTerm}\r\n                                onChange={handleSearch}\r\n                            />\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n\r\n                {/* Users Table */}\r\n                <div className=\"overflow-x-auto\">\r\n                    <table className=\"min-w-full divide-y divide-gray-200\">\r\n                        <thead className=\"bg-blue-600\">\r\n                            <tr>\r\n                                <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider\">\r\n                                    #\r\n                                </th>\r\n                                <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider\">\r\n                                    Avatar\r\n                                </th>\r\n                                <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider\">\r\n                                    Username\r\n                                </th>\r\n                                <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider\">\r\n                                    Full Name\r\n                                </th>\r\n                                <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider\">\r\n                                    Email\r\n                                </th>\r\n                                <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider\">\r\n                                    Favorite Team\r\n                                </th>\r\n                                <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider\">\r\n                                    Balance\r\n                                </th>\r\n                                <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider\">\r\n                                    Actions\r\n                                </th>\r\n                            </tr>\r\n                        </thead>\r\n                        <tbody className=\"bg-white divide-y divide-gray-200\">\r\n                            {currentUsers.map((user, index) => (\r\n                                <tr key={user.user_id} className=\"hover:bg-gray-50\">\r\n                                    <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\r\n                                        {indexOfFirstUser + index + 1}\r\n                                    </td>\r\n                                    <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                                        <div className=\"flex items-center\">\r\n                                            <img\r\n                                                src={getTeamLogo(user.favorite_team) || getDefaultAvatar()}\r\n                                                alt={user.favorite_team || 'Default Avatar'}\r\n                                                className=\"w-10 h-10 rounded-full object-contain border border-gray-200\"\r\n                                                onError={(e) => {\r\n                                                    e.target.src = getDefaultAvatar();\r\n                                                }}\r\n                                            />\r\n                                        </div>\r\n                                    </td>\r\n                                    <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                                        <div className=\"text-sm font-medium text-gray-900\">{user.username}</div>\r\n                                    </td>\r\n                                    <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                                        <div className=\"text-sm text-gray-500\">{user.full_name}</div>\r\n                                    </td>\r\n                                    <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                                        <div className=\"text-sm text-gray-500\">{user.email}</div>\r\n                                    </td>\r\n                                    <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                                        <div className=\"flex items-center space-x-2\">\r\n                                            {user.favorite_team && getTeamLogo(user.favorite_team) && (\r\n                                                <img\r\n                                                    src={getTeamLogo(user.favorite_team)}\r\n                                                    alt={user.favorite_team}\r\n                                                    className=\"w-5 h-5 rounded-full object-contain\"\r\n                                                />\r\n                                            )}\r\n                                            <div className=\"text-sm text-gray-500\">{user.favorite_team || 'No team selected'}</div>\r\n                                        </div>\r\n                                    </td>\r\n                                    <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                                        <div className=\"text-sm font-medium text-gray-900\">{user.balance} FanCoins</div>\r\n                                    </td>\r\n                                    <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\r\n                                        <div className=\"flex space-x-1\">\r\n                                            <button\r\n                                                onClick={() => navigate(`/admin/users/${user.user_id}`)}\r\n                                                className=\"text-green-600 hover:text-green-900 p-1\"\r\n                                                title=\"View Details\"\r\n                                            >\r\n                                                <FaEye />\r\n                                            </button>\r\n                                            <button\r\n                                                onClick={() => handleEdit(user)}\r\n                                                className=\"text-blue-600 hover:text-blue-900 p-1\"\r\n                                                title=\"Edit User\"\r\n                                            >\r\n                                                <FaEdit />\r\n                                            </button>\r\n                                            <button\r\n                                                onClick={() => handleSuspendUser(user)}\r\n                                                className=\"text-yellow-600 hover:text-yellow-900 p-1\"\r\n                                                title=\"Suspend User\"\r\n                                            >\r\n                                                <FaUserSlash />\r\n                                            </button>\r\n                                            <button\r\n                                                onClick={() => handleBanUser(user)}\r\n                                                className=\"text-red-600 hover:text-red-900 p-1\"\r\n                                                title=\"Ban User\"\r\n                                            >\r\n                                                <FaBan />\r\n                                            </button>\r\n                                        </div>\r\n                                    </td>\r\n                                </tr>\r\n                            ))}\r\n                        </tbody>\r\n                    </table>\r\n                </div>\r\n\r\n                {/* Pagination */}\r\n                {totalPages > 1 && (\r\n                    <div className=\"flex items-center justify-between border-t border-gray-200 px-4 py-3 sm:px-6 mt-4\">\r\n                        <div className=\"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between\">\r\n                            <div>\r\n                                <p className=\"text-sm text-gray-700\">\r\n                                    Showing <span className=\"font-medium\">{indexOfFirstUser + 1}</span> to{' '}\r\n                                    <span className=\"font-medium\">\r\n                                        {indexOfLastUser > filteredUsers.length ? filteredUsers.length : indexOfLastUser}\r\n                                    </span>{' '}\r\n                                    of <span className=\"font-medium\">{filteredUsers.length}</span> results\r\n                                </p>\r\n                            </div>\r\n                            <div>\r\n                                <nav className=\"relative z-0 inline-flex rounded-md shadow-sm -space-x-px\" aria-label=\"Pagination\">\r\n                                    <button\r\n                                        onClick={() => paginate(currentPage > 1 ? currentPage - 1 : 1)}\r\n                                        disabled={currentPage === 1}\r\n                                        className={`relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium ${\r\n                                            currentPage === 1 ? 'text-gray-300 cursor-not-allowed' : 'text-gray-500 hover:bg-gray-50'\r\n                                        }`}\r\n                                    >\r\n                                        <span className=\"sr-only\">Previous</span>\r\n                                        <svg className=\"h-5 w-5\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 20 20\" fill=\"currentColor\" aria-hidden=\"true\">\r\n                                            <path fillRule=\"evenodd\" d=\"M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\r\n                                        </svg>\r\n                                    </button>\r\n\r\n                                    {pageNumbers.map(number => (\r\n                                        <button\r\n                                            key={number}\r\n                                            onClick={() => paginate(number)}\r\n                                            className={`relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium ${\r\n                                                currentPage === number\r\n                                                    ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'\r\n                                                    : 'text-gray-500 hover:bg-gray-50'\r\n                                            }`}\r\n                                        >\r\n                                            {number}\r\n                                        </button>\r\n                                    ))}\r\n\r\n                                    <button\r\n                                        onClick={() => paginate(currentPage < totalPages ? currentPage + 1 : totalPages)}\r\n                                        disabled={currentPage === totalPages}\r\n                                        className={`relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium ${\r\n                                            currentPage === totalPages ? 'text-gray-300 cursor-not-allowed' : 'text-gray-500 hover:bg-gray-50'\r\n                                        }`}\r\n                                    >\r\n                                        <span className=\"sr-only\">Next</span>\r\n                                        <svg className=\"h-5 w-5\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 20 20\" fill=\"currentColor\" aria-hidden=\"true\">\r\n                                            <path fillRule=\"evenodd\" d=\"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z\" clipRule=\"evenodd\" />\r\n                                        </svg>\r\n                                    </button>\r\n                                </nav>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                )}\r\n            </div>\r\n\r\n            {/* Edit User Modal */}\r\n            {editingUserId && (\r\n                <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\r\n                    <div className=\"bg-white rounded-lg shadow-xl w-full max-w-md\">\r\n                        <div className=\"flex justify-between items-center p-4 border-b\">\r\n                            <h3 className=\"text-lg font-semibold text-gray-800\">Edit User</h3>\r\n                            <button\r\n                                className=\"text-gray-500 hover:text-gray-700 text-2xl focus:outline-none\"\r\n                                onClick={() => setEditingUserId(null)}\r\n                            >\r\n                                ×\r\n                            </button>\r\n                        </div>\r\n\r\n                        <form onSubmit={handleUpdate} className=\"p-6\">\r\n                            <div className=\"space-y-4\">\r\n                                <div>\r\n                                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Username</label>\r\n                                    <input\r\n                                        type=\"text\"\r\n                                        name=\"username\"\r\n                                        value={editingUser.username}\r\n                                        onChange={handleInputChange}\r\n                                        className=\"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\r\n                                        required\r\n                                    />\r\n                                </div>\r\n\r\n                                <div>\r\n                                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Full Name</label>\r\n                                    <input\r\n                                        type=\"text\"\r\n                                        name=\"full_name\"\r\n                                        value={editingUser.full_name}\r\n                                        onChange={handleInputChange}\r\n                                        className=\"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\r\n                                        required\r\n                                    />\r\n                                </div>\r\n\r\n                                <div>\r\n                                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Email</label>\r\n                                    <input\r\n                                        type=\"email\"\r\n                                        name=\"email\"\r\n                                        value={editingUser.email}\r\n                                        onChange={handleInputChange}\r\n                                        className=\"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\r\n                                        required\r\n                                    />\r\n                                </div>\r\n\r\n                                <div>\r\n                                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Favorite Team</label>\r\n                                    <select\r\n                                        name=\"favorite_team\"\r\n                                        value={editingUser.favorite_team}\r\n                                        onChange={handleInputChange}\r\n                                        className=\"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\r\n                                        required\r\n                                    >\r\n                                        <option value=\"\">Select Favorite Team</option>\r\n                                        {teams.map(team => (\r\n                                            <option key={team.id} value={team.name}>{team.name}</option>\r\n                                        ))}\r\n                                    </select>\r\n                                </div>\r\n\r\n                                <div>\r\n                                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Balance (FanCoins)</label>\r\n                                    <input\r\n                                        type=\"number\"\r\n                                        name=\"balance\"\r\n                                        value={editingUser.balance}\r\n                                        onChange={handleInputChange}\r\n                                        className=\"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\r\n                                        required\r\n                                    />\r\n                                </div>\r\n                            </div>\r\n\r\n                            <div className=\"mt-6 flex justify-end space-x-3\">\r\n                                <button\r\n                                    type=\"button\"\r\n                                    onClick={() => setEditingUserId(null)}\r\n                                    className=\"px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\r\n                                >\r\n                                    Cancel\r\n                                </button>\r\n                                <button\r\n                                    type=\"submit\"\r\n                                    className=\"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\r\n                                >\r\n                                    Update User\r\n                                </button>\r\n                            </div>\r\n                        </form>\r\n                    </div>\r\n                </div>\r\n            )}\r\n\r\n            {/* Add User Modal */}\r\n            {showAddUserModal && (\r\n                <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\r\n                    <div className=\"bg-white rounded-lg shadow-xl w-full max-w-md\">\r\n                        <div className=\"flex justify-between items-center p-4 border-b\">\r\n                            <h3 className=\"text-lg font-semibold text-gray-800\">Add New User</h3>\r\n                            <button\r\n                                className=\"text-gray-500 hover:text-gray-700 text-2xl focus:outline-none\"\r\n                                onClick={() => setShowAddUserModal(false)}\r\n                            >\r\n                                ×\r\n                            </button>\r\n                        </div>\r\n\r\n                        <form onSubmit={handleAddUser} className=\"p-6\">\r\n                            <div className=\"space-y-4\">\r\n                                <div>\r\n                                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Username</label>\r\n                                    <input\r\n                                        type=\"text\"\r\n                                        name=\"username\"\r\n                                        value={newUser.username}\r\n                                        onChange={handleNewUserInputChange}\r\n                                        className=\"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm\"\r\n                                        required\r\n                                    />\r\n                                </div>\r\n\r\n                                <div>\r\n                                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Full Name</label>\r\n                                    <input\r\n                                        type=\"text\"\r\n                                        name=\"full_name\"\r\n                                        value={newUser.full_name}\r\n                                        onChange={handleNewUserInputChange}\r\n                                        className=\"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm\"\r\n                                        required\r\n                                    />\r\n                                </div>\r\n\r\n                                <div>\r\n                                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Email</label>\r\n                                    <input\r\n                                        type=\"email\"\r\n                                        name=\"email\"\r\n                                        value={newUser.email}\r\n                                        onChange={handleNewUserInputChange}\r\n                                        className=\"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm\"\r\n                                        required\r\n                                    />\r\n                                </div>\r\n\r\n                                <div>\r\n                                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Password</label>\r\n                                    <input\r\n                                        type=\"password\"\r\n                                        name=\"password\"\r\n                                        value={newUser.password}\r\n                                        onChange={handleNewUserInputChange}\r\n                                        className=\"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm\"\r\n                                        required\r\n                                    />\r\n                                </div>\r\n\r\n                                <div>\r\n                                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Favorite Team</label>\r\n                                    <select\r\n                                        name=\"favorite_team\"\r\n                                        value={newUser.favorite_team}\r\n                                        onChange={handleNewUserInputChange}\r\n                                        className=\"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm\"\r\n                                    >\r\n                                        <option value=\"\">Select Favorite Team</option>\r\n                                        {teams.map(team => (\r\n                                            <option key={team.id} value={team.name}>{team.name}</option>\r\n                                        ))}\r\n                                    </select>\r\n                                </div>\r\n\r\n                                <div>\r\n                                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Initial Balance (FanCoins)</label>\r\n                                    <input\r\n                                        type=\"number\"\r\n                                        name=\"balance\"\r\n                                        value={newUser.balance}\r\n                                        onChange={handleNewUserInputChange}\r\n                                        className=\"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm\"\r\n                                        min=\"0\"\r\n                                    />\r\n                                </div>\r\n                            </div>\r\n\r\n                            <div className=\"mt-6 flex justify-end space-x-3\">\r\n                                <button\r\n                                    type=\"button\"\r\n                                    onClick={() => setShowAddUserModal(false)}\r\n                                    className=\"px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500\"\r\n                                >\r\n                                    Cancel\r\n                                </button>\r\n                                <button\r\n                                    type=\"submit\"\r\n                                    className=\"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500\"\r\n                                >\r\n                                    Add User\r\n                                </button>\r\n                            </div>\r\n                        </form>\r\n                    </div>\r\n                </div>\r\n            )}\r\n\r\n            {/* Custom Modal */}\r\n            <CustomModal\r\n                isOpen={modalState.isOpen}\r\n                onClose={() => setModalState({ ...modalState, isOpen: false })}\r\n                onConfirm={modalState.onConfirm}\r\n                title={modalState.title}\r\n                message={modalState.message}\r\n                type={modalState.type}\r\n                confirmText={modalState.confirmText}\r\n                confirmButtonColor={modalState.confirmButtonColor}\r\n            />\r\n        </div>\r\n    );\r\n}\r\nexport default UserManagement;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,MAAO,CAAAC,KAAK,KAAM,OAAO,CACzB,OAASC,OAAO,CAAEC,UAAU,CAAEC,WAAW,CAAEC,WAAW,CAAEC,MAAM,CAAEC,QAAQ,CAAEC,KAAK,CAAEC,WAAW,CAAEC,KAAK,KAAQ,gBAAgB,CAC3H,MAAO,CAAAC,WAAW,KAAM,2BAA2B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEpD,KAAM,CAAAC,YAAY,CAAG,UAAU,CAE/B,QAAS,CAAAC,cAAcA,CAAA,CAAG,CACtB,KAAM,CAAAC,QAAQ,CAAGlB,WAAW,CAAC,CAAC,CAC9B,KAAM,CAACmB,KAAK,CAAEC,QAAQ,CAAC,CAAGtB,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAACuB,KAAK,CAAEC,QAAQ,CAAC,CAAGxB,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAACyB,KAAK,CAAEC,QAAQ,CAAC,CAAG1B,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAAC2B,OAAO,CAAEC,UAAU,CAAC,CAAG5B,QAAQ,CAAC,EAAE,CAAC,CAC1C,KAAM,CAAC6B,aAAa,CAAEC,gBAAgB,CAAC,CAAG9B,QAAQ,CAAC,IAAI,CAAC,CACxD,KAAM,CAAC+B,UAAU,CAAEC,aAAa,CAAC,CAAGhC,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAACiC,WAAW,CAAEC,cAAc,CAAC,CAAGlC,QAAQ,CAAC,CAAC,CAAC,CACjD,KAAM,CAACmC,YAAY,CAAC,CAAGnC,QAAQ,CAAC,EAAE,CAAC,CACnC,KAAM,CAACoC,KAAK,CAAEC,QAAQ,CAAC,CAAGrC,QAAQ,CAAC,CAC/BsC,UAAU,CAAE,CAAC,CACbC,WAAW,CAAE,CAAC,CACdC,QAAQ,CAAE,CAAC,CACXC,YAAY,CAAE,CAClB,CAAC,CAAC,CACF,KAAM,CAACC,WAAW,CAAEC,cAAc,CAAC,CAAG3C,QAAQ,CAAC,CAC3C4C,QAAQ,CAAE,EAAE,CACZC,SAAS,CAAE,EAAE,CACbC,KAAK,CAAE,EAAE,CACTC,aAAa,CAAE,EAAE,CACjBC,OAAO,CAAE,CACb,CAAC,CAAC,CACF,KAAM,CAACC,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGlD,QAAQ,CAAC,KAAK,CAAC,CAC/D,KAAM,CAACmD,OAAO,CAAEC,UAAU,CAAC,CAAGpD,QAAQ,CAAC,CACnC4C,QAAQ,CAAE,EAAE,CACZC,SAAS,CAAE,EAAE,CACbC,KAAK,CAAE,EAAE,CACTO,QAAQ,CAAE,EAAE,CACZN,aAAa,CAAE,EAAE,CACjBC,OAAO,CAAE,CACb,CAAC,CAAC,CAEF;AACA,KAAM,CAACM,UAAU,CAAEC,aAAa,CAAC,CAAGvD,QAAQ,CAAC,CACzCwD,MAAM,CAAE,KAAK,CACbC,IAAI,CAAE,SAAS,CACfC,KAAK,CAAE,EAAE,CACTC,OAAO,CAAE,EAAE,CACXC,SAAS,CAAE,IAAI,CACfC,WAAW,CAAE,SAAS,CACtBC,kBAAkB,CAAE,MACxB,CAAC,CAAC,CAEF7D,SAAS,CAAC,IAAM,CACZ8D,UAAU,CAAC,CAAC,CACZC,UAAU,CAAC,CAAC,CAChB,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAD,UAAU,CAAG,KAAAA,CAAA,GAAY,CAC3B,GAAI,CACA,KAAM,CAAAE,QAAQ,CAAG,KAAM,CAAA9D,KAAK,CAAC+D,GAAG,CAAC,GAAGhD,YAAY,+BAA+B,CAAC,CAChF,GAAI+C,QAAQ,CAACE,IAAI,CAACxC,OAAO,CAAE,CACvB,KAAM,CAAAyC,QAAQ,CAAGH,QAAQ,CAACE,IAAI,CAACA,IAAI,EAAI,EAAE,CACzC7C,QAAQ,CAAC8C,QAAQ,CAAC,CAElB;AACA,KAAM,CAAAC,GAAG,CAAG,GAAI,CAAAC,IAAI,CAAC,CAAC,CACtB,KAAM,CAAAC,UAAU,CAAG,GAAI,CAAAD,IAAI,CAACD,GAAG,CAACG,OAAO,CAAC,CAAC,CAAG,CAAC,CAAG,EAAE,CAAG,EAAE,CAAG,EAAE,CAAG,IAAI,CAAC,CAEpE;AACA,KAAM,CAAAlC,UAAU,CAAG8B,QAAQ,CAACK,MAAM,CAClC,KAAM,CAAAlC,WAAW,CAAG6B,QAAQ,CAACM,MAAM,CAACC,IAAI,EAAIA,IAAI,CAACC,UAAU,EAAI,GAAI,CAAAN,IAAI,CAACK,IAAI,CAACC,UAAU,CAAC,CAAGL,UAAU,CAAC,CAACE,MAAM,EAAII,IAAI,CAACC,KAAK,CAACxC,UAAU,CAAG,GAAG,CAAC,CAC7I,KAAM,CAAAE,QAAQ,CAAG4B,QAAQ,CAACM,MAAM,CAACC,IAAI,EAAIA,IAAI,CAACI,UAAU,EAAI,GAAI,CAAAT,IAAI,CAACK,IAAI,CAACI,UAAU,CAAC,CAAGR,UAAU,CAAC,CAACE,MAAM,EAAII,IAAI,CAACC,KAAK,CAACxC,UAAU,CAAG,GAAG,CAAC,CAC1I,KAAM,CAAAG,YAAY,CAAG2B,QAAQ,CAACM,MAAM,CAACC,IAAI,EAAIA,IAAI,CAACK,MAAM,GAAK,SAAS,CAAC,CAACP,MAAM,EAAII,IAAI,CAACC,KAAK,CAACxC,UAAU,CAAG,GAAG,CAAC,CAE9GD,QAAQ,CAAC,CACLC,UAAU,CACVC,WAAW,CACXC,QAAQ,CACRC,YACJ,CAAC,CAAC,CACN,CAAC,IAAM,CACHf,QAAQ,CAACuC,QAAQ,CAACE,IAAI,CAACR,OAAO,EAAI,uBAAuB,CAAC,CAC9D,CACJ,CAAE,MAAOsB,GAAG,CAAE,CACVvD,QAAQ,CAAC,4EAA4E,CAAC,CACtFwD,OAAO,CAACzD,KAAK,CAAC,uBAAuB,CAAEwD,GAAG,CAAC,CAC/C,CACJ,CAAC,CAED,KAAM,CAAAjB,UAAU,CAAG,KAAAA,CAAA,GAAY,CAC3B,GAAI,CACA,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAA9D,KAAK,CAAC+D,GAAG,CAAC,GAAGhD,YAAY,+BAA+B,CAAC,CAChFM,QAAQ,CAACyC,QAAQ,CAACE,IAAI,CAACA,IAAI,EAAI,EAAE,CAAC,CACtC,CAAE,MAAOc,GAAG,CAAE,CACVvD,QAAQ,CAAC,uBAAuB,CAAC,CACrC,CACJ,CAAC,CAED,KAAM,CAAAyD,WAAW,CAAIC,QAAQ,EAAK,CAC9B,KAAM,CAAAC,IAAI,CAAG9D,KAAK,CAAC+D,IAAI,CAACD,IAAI,EAAIA,IAAI,CAACE,IAAI,GAAKH,QAAQ,CAAC,CACvD,MAAO,CAAAC,IAAI,CAAG,GAAGnE,YAAY,IAAImE,IAAI,CAACG,IAAI,EAAE,CAAG,IAAI,CACvD,CAAC,CAED,KAAM,CAAAC,gBAAgB,CAAGA,CAAA,GAAM,CAC3B,MAAO,2SAA2S,CACtT,CAAC,CAED,KAAM,CAAAC,UAAU,CAAIf,IAAI,EAAK,CACzB7C,gBAAgB,CAAC6C,IAAI,CAACgB,OAAO,CAAC,CAC9BhD,cAAc,CAACgC,IAAI,CAAC,CACxB,CAAC,CAED,KAAM,CAAAiB,YAAY,CAAG,KAAO,CAAAC,CAAC,EAAK,CAC9BA,CAAC,CAACC,cAAc,CAAC,CAAC,CAClB,GAAI,CACA,KAAM,CAAA7B,QAAQ,CAAG,KAAM,CAAA9D,KAAK,CAAC4F,GAAG,CAAC,GAAG7E,YAAY,oCAAoCW,aAAa,EAAE,CAAEa,WAAW,CAAC,CACjH,GAAIuB,QAAQ,CAACE,IAAI,CAACxC,OAAO,CAAE,CACvBC,UAAU,CAAC,4BAA4B,CAAC,CACxCmC,UAAU,CAAC,CAAC,CACZjC,gBAAgB,CAAC,IAAI,CAAC,CACtBkE,UAAU,CAAC,IAAMpE,UAAU,CAAC,EAAE,CAAC,CAAE,IAAI,CAAC,CAC1C,CAAC,IAAM,CACHF,QAAQ,CAACuC,QAAQ,CAACE,IAAI,CAACR,OAAO,EAAI,uBAAuB,CAAC,CAC1DqC,UAAU,CAAC,IAAMtE,QAAQ,CAAC,EAAE,CAAC,CAAE,IAAI,CAAC,CACxC,CACJ,CAAE,MAAOuD,GAAG,CAAE,CACVvD,QAAQ,CAAC,uBAAuB,CAAC,CACrC,CACJ,CAAC,CAED,KAAM,CAAAuE,iBAAiB,CAAIJ,CAAC,EAAK,CAC7B,KAAM,CAAEN,IAAI,CAAEW,KAAM,CAAC,CAAGL,CAAC,CAACM,MAAM,CAChCxD,cAAc,CAACyD,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAE,CAACb,IAAI,EAAGW,KAAM,CAAC,CAAC,CAAC,CACxD,CAAC,CAED,KAAM,CAAAG,wBAAwB,CAAIR,CAAC,EAAK,CACpC,KAAM,CAAEN,IAAI,CAAEW,KAAM,CAAC,CAAGL,CAAC,CAACM,MAAM,CAChC/C,UAAU,CAACgD,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAE,CAACb,IAAI,EAAGW,KAAM,CAAC,CAAC,CAAC,CACpD,CAAC,CAED,KAAM,CAAAI,aAAa,CAAG,KAAO,CAAAT,CAAC,EAAK,CAC/BA,CAAC,CAACC,cAAc,CAAC,CAAC,CAClB,GAAI,CACA,KAAM,CAAA7B,QAAQ,CAAG,KAAM,CAAA9D,KAAK,CAACoG,IAAI,CAAC,GAAGrF,YAAY,wBAAwB,CAAEiC,OAAO,CAAC,CACnF,GAAIc,QAAQ,CAACE,IAAI,CAACxC,OAAO,CAAE,CACvBC,UAAU,CAAC,0BAA0B,CAAC,CACtCmC,UAAU,CAAC,CAAC,CACZb,mBAAmB,CAAC,KAAK,CAAC,CAC1BE,UAAU,CAAC,CACPR,QAAQ,CAAE,EAAE,CACZC,SAAS,CAAE,EAAE,CACbC,KAAK,CAAE,EAAE,CACTO,QAAQ,CAAE,EAAE,CACZN,aAAa,CAAE,EAAE,CACjBC,OAAO,CAAE,CACb,CAAC,CAAC,CACF;AACAgD,UAAU,CAAC,IAAMpE,UAAU,CAAC,EAAE,CAAC,CAAE,IAAI,CAAC,CAC1C,CAAC,IAAM,CACHF,QAAQ,CAACuC,QAAQ,CAACE,IAAI,CAACR,OAAO,EAAI,oBAAoB,CAAC,CACvDqC,UAAU,CAAC,IAAMtE,QAAQ,CAAC,EAAE,CAAC,CAAE,IAAI,CAAC,CACxC,CACJ,CAAE,MAAOuD,GAAG,CAAE,CACVvD,QAAQ,CAAC,oBAAoB,CAAC,CAC9BsE,UAAU,CAAC,IAAMtE,QAAQ,CAAC,EAAE,CAAC,CAAE,IAAI,CAAC,CACxC,CACJ,CAAC,CAED,KAAM,CAAA8E,iBAAiB,CAAI7B,IAAI,EAAK,CAChCpB,aAAa,CAAC,CACVC,MAAM,CAAE,IAAI,CACZC,IAAI,CAAE,SAAS,CACfC,KAAK,CAAE,cAAc,CACrBC,OAAO,CAAE,0CAA0CgB,IAAI,CAAC/B,QAAQ,yDAAyD,CACzHiB,WAAW,CAAE,cAAc,CAC3BC,kBAAkB,CAAE,QAAQ,CAC5BF,SAAS,CAAEA,CAAA,GAAM6C,kBAAkB,CAAC9B,IAAI,CAC5C,CAAC,CAAC,CACN,CAAC,CAED,KAAM,CAAA8B,kBAAkB,CAAG,KAAO,CAAA9B,IAAI,EAAK,CACvC,GAAI,CACA,KAAM,CAAAV,QAAQ,CAAG,KAAM,CAAA9D,KAAK,CAACoG,IAAI,CAAC,GAAGrF,YAAY,4BAA4B,CAAE,CAC3EyE,OAAO,CAAEhB,IAAI,CAACgB,OAAO,CACrBe,MAAM,CAAE,SACZ,CAAC,CAAC,CACF,GAAIzC,QAAQ,CAACE,IAAI,CAACxC,OAAO,CAAE,CACvBC,UAAU,CAAC,SAAS+C,IAAI,CAAC/B,QAAQ,2BAA2B,CAAC,CAC7DmB,UAAU,CAAC,CAAC,CACZiC,UAAU,CAAC,IAAMpE,UAAU,CAAC,EAAE,CAAC,CAAE,IAAI,CAAC,CAC1C,CAAC,IAAM,CACHF,QAAQ,CAACuC,QAAQ,CAACE,IAAI,CAACR,OAAO,EAAI,wBAAwB,CAAC,CAC3DqC,UAAU,CAAC,IAAMtE,QAAQ,CAAC,EAAE,CAAC,CAAE,IAAI,CAAC,CACxC,CACJ,CAAE,MAAOuD,GAAG,CAAE,CACVvD,QAAQ,CAAC,wBAAwB,CAAC,CAClCsE,UAAU,CAAC,IAAMtE,QAAQ,CAAC,EAAE,CAAC,CAAE,IAAI,CAAC,CACxC,CACJ,CAAC,CAED,KAAM,CAAAiF,aAAa,CAAIhC,IAAI,EAAK,CAC5BpB,aAAa,CAAC,CACVC,MAAM,CAAE,IAAI,CACZC,IAAI,CAAE,SAAS,CACfC,KAAK,CAAE,UAAU,CACjBC,OAAO,CAAE,sCAAsCgB,IAAI,CAAC/B,QAAQ,iGAAiG,CAC7JiB,WAAW,CAAE,UAAU,CACvBC,kBAAkB,CAAE,KAAK,CACzBF,SAAS,CAAEA,CAAA,GAAMgD,cAAc,CAACjC,IAAI,CACxC,CAAC,CAAC,CACN,CAAC,CAED,KAAM,CAAAiC,cAAc,CAAG,KAAO,CAAAjC,IAAI,EAAK,CACnC,GAAI,CACA,KAAM,CAAAV,QAAQ,CAAG,KAAM,CAAA9D,KAAK,CAACoG,IAAI,CAAC,GAAGrF,YAAY,wBAAwB,CAAE,CACvEyE,OAAO,CAAEhB,IAAI,CAACgB,OAAO,CACrBe,MAAM,CAAE,KACZ,CAAC,CAAC,CACF,GAAIzC,QAAQ,CAACE,IAAI,CAACxC,OAAO,CAAE,CACvBC,UAAU,CAAC,SAAS+C,IAAI,CAAC/B,QAAQ,wBAAwB,CAAC,CAC1DmB,UAAU,CAAC,CAAC,CACZiC,UAAU,CAAC,IAAMpE,UAAU,CAAC,EAAE,CAAC,CAAE,IAAI,CAAC,CAC1C,CAAC,IAAM,CACHF,QAAQ,CAACuC,QAAQ,CAACE,IAAI,CAACR,OAAO,EAAI,oBAAoB,CAAC,CACvDqC,UAAU,CAAC,IAAMtE,QAAQ,CAAC,EAAE,CAAC,CAAE,IAAI,CAAC,CACxC,CACJ,CAAE,MAAOuD,GAAG,CAAE,CACVvD,QAAQ,CAAC,oBAAoB,CAAC,CAC9BsE,UAAU,CAAC,IAAMtE,QAAQ,CAAC,EAAE,CAAC,CAAE,IAAI,CAAC,CACxC,CACJ,CAAC,CAED;AACA,KAAM,CAAAmF,YAAY,CAAIhB,CAAC,EAAK,CACxB7D,aAAa,CAAC6D,CAAC,CAACM,MAAM,CAACD,KAAK,CAAC,CAC7BhE,cAAc,CAAC,CAAC,CAAC,CAAE;AACvB,CAAC,CAED;AACA,KAAM,CAAA4E,aAAa,CAAGzF,KAAK,CAACqD,MAAM,CAACC,IAAI,EACnCA,IAAI,CAAC/B,QAAQ,CAACmE,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACjF,UAAU,CAACgF,WAAW,CAAC,CAAC,CAAC,EAC9DpC,IAAI,CAAC9B,SAAS,CAACkE,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACjF,UAAU,CAACgF,WAAW,CAAC,CAAC,CAAC,EAC/DpC,IAAI,CAAC7B,KAAK,CAACiE,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACjF,UAAU,CAACgF,WAAW,CAAC,CAAC,CAC9D,CAAC,CAED;AACA,KAAM,CAAAE,eAAe,CAAGhF,WAAW,CAAGE,YAAY,CAClD,KAAM,CAAA+E,gBAAgB,CAAGD,eAAe,CAAG9E,YAAY,CACvD,KAAM,CAAAgF,YAAY,CAAGL,aAAa,CAACM,KAAK,CAACF,gBAAgB,CAAED,eAAe,CAAC,CAC3E,KAAM,CAAAI,UAAU,CAAGxC,IAAI,CAACyC,IAAI,CAACR,aAAa,CAACrC,MAAM,CAAGtC,YAAY,CAAC,CAEjE,KAAM,CAAAoF,QAAQ,CAAIC,UAAU,EAAKtF,cAAc,CAACsF,UAAU,CAAC,CAE3D;AACA,KAAM,CAAAC,WAAW,CAAG,EAAE,CACtB,IAAK,GAAI,CAAAC,CAAC,CAAG,CAAC,CAAEA,CAAC,EAAIL,UAAU,CAAEK,CAAC,EAAE,CAAE,CAClCD,WAAW,CAACE,IAAI,CAACD,CAAC,CAAC,CACvB,CAEA,mBACIzG,KAAA,QAAK2G,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAExC5G,KAAA,QAAK2G,SAAS,CAAC,MAAM,CAAAC,QAAA,eACjB9G,IAAA,OAAI6G,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,iBAAe,CAAI,CAAC,cACrE9G,IAAA,MAAG6G,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,gCAA8B,CAAG,CAAC,EAC9D,CAAC,CAGLpG,KAAK,eACFV,IAAA,QAAK6G,SAAS,CAAC,+EAA+E,CAACE,IAAI,CAAC,OAAO,CAAAD,QAAA,cACvG9G,IAAA,SAAM6G,SAAS,CAAC,iBAAiB,CAAAC,QAAA,CAAEpG,KAAK,CAAO,CAAC,CAC/C,CACR,CACAE,OAAO,eACJZ,IAAA,QAAK6G,SAAS,CAAC,qFAAqF,CAACE,IAAI,CAAC,OAAO,CAAAD,QAAA,cAC7G9G,IAAA,SAAM6G,SAAS,CAAC,iBAAiB,CAAAC,QAAA,CAAElG,OAAO,CAAO,CAAC,CACjD,CACR,cAGDV,KAAA,QAAK2G,SAAS,CAAC,2DAA2D,CAAAC,QAAA,eAEtE5G,KAAA,QAAK2G,SAAS,CAAC,qDAAqD,CAAAC,QAAA,eAChE9G,IAAA,QAAK6G,SAAS,CAAC,mCAAmC,CAAAC,QAAA,cAC9C9G,IAAA,CAACX,OAAO,EAACwH,SAAS,CAAC,uBAAuB,CAAE,CAAC,CAC5C,CAAC,cACN3G,KAAA,QAAA4G,QAAA,eACI9G,IAAA,MAAG6G,SAAS,CAAC,gDAAgD,CAAAC,QAAA,CAAC,aAAW,CAAG,CAAC,cAC7E9G,IAAA,OAAI6G,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAEzF,KAAK,CAACE,UAAU,CAAK,CAAC,EACvE,CAAC,EACL,CAAC,cAGNrB,KAAA,QAAK2G,SAAS,CAAC,qDAAqD,CAAAC,QAAA,eAChE9G,IAAA,QAAK6G,SAAS,CAAC,oCAAoC,CAAAC,QAAA,cAC/C9G,IAAA,CAACT,WAAW,EAACsH,SAAS,CAAC,wBAAwB,CAAE,CAAC,CACjD,CAAC,cACN3G,KAAA,QAAA4G,QAAA,eACI9G,IAAA,MAAG6G,SAAS,CAAC,gDAAgD,CAAAC,QAAA,CAAC,cAAY,CAAG,CAAC,cAC9E9G,IAAA,OAAI6G,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAEzF,KAAK,CAACG,WAAW,CAAK,CAAC,EACxE,CAAC,EACL,CAAC,cAGNtB,KAAA,QAAK2G,SAAS,CAAC,qDAAqD,CAAAC,QAAA,eAChE9G,IAAA,QAAK6G,SAAS,CAAC,qCAAqC,CAAAC,QAAA,cAChD9G,IAAA,CAACV,UAAU,EAACuH,SAAS,CAAC,yBAAyB,CAAE,CAAC,CACjD,CAAC,cACN3G,KAAA,QAAA4G,QAAA,eACI9G,IAAA,MAAG6G,SAAS,CAAC,gDAAgD,CAAAC,QAAA,CAAC,gBAAc,CAAG,CAAC,cAChF9G,IAAA,OAAI6G,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAEzF,KAAK,CAACI,QAAQ,CAAK,CAAC,EACrE,CAAC,EACL,CAAC,cAGNvB,KAAA,QAAK2G,SAAS,CAAC,qDAAqD,CAAAC,QAAA,eAChE9G,IAAA,QAAK6G,SAAS,CAAC,qCAAqC,CAAAC,QAAA,cAChD9G,IAAA,CAACR,WAAW,EAACqH,SAAS,CAAC,yBAAyB,CAAE,CAAC,CAClD,CAAC,cACN3G,KAAA,QAAA4G,QAAA,eACI9G,IAAA,MAAG6G,SAAS,CAAC,gDAAgD,CAAAC,QAAA,CAAC,eAAa,CAAG,CAAC,cAC/E9G,IAAA,OAAI6G,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAEzF,KAAK,CAACK,YAAY,CAAK,CAAC,EACzE,CAAC,EACL,CAAC,EACL,CAAC,cAGNxB,KAAA,QAAK2G,SAAS,CAAC,wCAAwC,CAAAC,QAAA,eACnD5G,KAAA,QAAK2G,SAAS,CAAC,mEAAmE,CAAAC,QAAA,eAC9E9G,IAAA,OAAI6G,SAAS,CAAC,6EAA6E,CAAAC,QAAA,CAAC,WAAS,CAAI,CAAC,cAC1G5G,KAAA,QAAK2G,SAAS,CAAC,+EAA+E,CAAAC,QAAA,eAC1F5G,KAAA,WACI8G,OAAO,CAAEA,CAAA,GAAM7E,mBAAmB,CAAC,IAAI,CAAE,CACzC0E,SAAS,CAAC,mFAAmF,CAAAC,QAAA,eAE7F9G,IAAA,CAACV,UAAU,EAACuH,SAAS,CAAC,MAAM,CAAE,CAAC,WAEnC,EAAQ,CAAC,cACT3G,KAAA,QAAK2G,SAAS,CAAC,yBAAyB,CAAAC,QAAA,eACpC9G,IAAA,QAAK6G,SAAS,CAAC,sEAAsE,CAAAC,QAAA,cACjF9G,IAAA,CAACN,QAAQ,EAACmH,SAAS,CAAC,uBAAuB,CAAE,CAAC,CAC7C,CAAC,cACN7G,IAAA,UACI0C,IAAI,CAAC,MAAM,CACXmE,SAAS,CAAC,qJAAqJ,CAC/JI,WAAW,CAAC,iBAAiB,CAC7B9B,KAAK,CAAEnE,UAAW,CAClBkG,QAAQ,CAAEpB,YAAa,CAC1B,CAAC,EACD,CAAC,EACL,CAAC,EACL,CAAC,cAGN9F,IAAA,QAAK6G,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC5B5G,KAAA,UAAO2G,SAAS,CAAC,qCAAqC,CAAAC,QAAA,eAClD9G,IAAA,UAAO6G,SAAS,CAAC,aAAa,CAAAC,QAAA,cAC1B5G,KAAA,OAAA4G,QAAA,eACI9G,IAAA,OAAImH,KAAK,CAAC,KAAK,CAACN,SAAS,CAAC,6EAA6E,CAAAC,QAAA,CAAC,GAExG,CAAI,CAAC,cACL9G,IAAA,OAAImH,KAAK,CAAC,KAAK,CAACN,SAAS,CAAC,6EAA6E,CAAAC,QAAA,CAAC,QAExG,CAAI,CAAC,cACL9G,IAAA,OAAImH,KAAK,CAAC,KAAK,CAACN,SAAS,CAAC,6EAA6E,CAAAC,QAAA,CAAC,UAExG,CAAI,CAAC,cACL9G,IAAA,OAAImH,KAAK,CAAC,KAAK,CAACN,SAAS,CAAC,6EAA6E,CAAAC,QAAA,CAAC,WAExG,CAAI,CAAC,cACL9G,IAAA,OAAImH,KAAK,CAAC,KAAK,CAACN,SAAS,CAAC,6EAA6E,CAAAC,QAAA,CAAC,OAExG,CAAI,CAAC,cACL9G,IAAA,OAAImH,KAAK,CAAC,KAAK,CAACN,SAAS,CAAC,6EAA6E,CAAAC,QAAA,CAAC,eAExG,CAAI,CAAC,cACL9G,IAAA,OAAImH,KAAK,CAAC,KAAK,CAACN,SAAS,CAAC,6EAA6E,CAAAC,QAAA,CAAC,SAExG,CAAI,CAAC,cACL9G,IAAA,OAAImH,KAAK,CAAC,KAAK,CAACN,SAAS,CAAC,6EAA6E,CAAAC,QAAA,CAAC,SAExG,CAAI,CAAC,EACL,CAAC,CACF,CAAC,cACR9G,IAAA,UAAO6G,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAC/CV,YAAY,CAACgB,GAAG,CAAC,CAACxD,IAAI,CAAEyD,KAAK,gBAC1BnH,KAAA,OAAuB2G,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/C9G,IAAA,OAAI6G,SAAS,CAAC,mDAAmD,CAAAC,QAAA,CAC5DX,gBAAgB,CAAGkB,KAAK,CAAG,CAAC,CAC7B,CAAC,cACLrH,IAAA,OAAI6G,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cACvC9G,IAAA,QAAK6G,SAAS,CAAC,mBAAmB,CAAAC,QAAA,cAC9B9G,IAAA,QACIsH,GAAG,CAAElD,WAAW,CAACR,IAAI,CAAC5B,aAAa,CAAC,EAAI0C,gBAAgB,CAAC,CAAE,CAC3D6C,GAAG,CAAE3D,IAAI,CAAC5B,aAAa,EAAI,gBAAiB,CAC5C6E,SAAS,CAAC,8DAA8D,CACxEW,OAAO,CAAG1C,CAAC,EAAK,CACZA,CAAC,CAACM,MAAM,CAACkC,GAAG,CAAG5C,gBAAgB,CAAC,CAAC,CACrC,CAAE,CACL,CAAC,CACD,CAAC,CACN,CAAC,cACL1E,IAAA,OAAI6G,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cACvC9G,IAAA,QAAK6G,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAElD,IAAI,CAAC/B,QAAQ,CAAM,CAAC,CACxE,CAAC,cACL7B,IAAA,OAAI6G,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cACvC9G,IAAA,QAAK6G,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAElD,IAAI,CAAC9B,SAAS,CAAM,CAAC,CAC7D,CAAC,cACL9B,IAAA,OAAI6G,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cACvC9G,IAAA,QAAK6G,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAElD,IAAI,CAAC7B,KAAK,CAAM,CAAC,CACzD,CAAC,cACL/B,IAAA,OAAI6G,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cACvC5G,KAAA,QAAK2G,SAAS,CAAC,6BAA6B,CAAAC,QAAA,EACvClD,IAAI,CAAC5B,aAAa,EAAIoC,WAAW,CAACR,IAAI,CAAC5B,aAAa,CAAC,eAClDhC,IAAA,QACIsH,GAAG,CAAElD,WAAW,CAACR,IAAI,CAAC5B,aAAa,CAAE,CACrCuF,GAAG,CAAE3D,IAAI,CAAC5B,aAAc,CACxB6E,SAAS,CAAC,qCAAqC,CAClD,CACJ,cACD7G,IAAA,QAAK6G,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAElD,IAAI,CAAC5B,aAAa,EAAI,kBAAkB,CAAM,CAAC,EACtF,CAAC,CACN,CAAC,cACLhC,IAAA,OAAI6G,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cACvC5G,KAAA,QAAK2G,SAAS,CAAC,mCAAmC,CAAAC,QAAA,EAAElD,IAAI,CAAC3B,OAAO,CAAC,WAAS,EAAK,CAAC,CAChF,CAAC,cACLjC,IAAA,OAAI6G,SAAS,CAAC,iDAAiD,CAAAC,QAAA,cAC3D5G,KAAA,QAAK2G,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC3B9G,IAAA,WACIgH,OAAO,CAAEA,CAAA,GAAM3G,QAAQ,CAAC,gBAAgBuD,IAAI,CAACgB,OAAO,EAAE,CAAE,CACxDiC,SAAS,CAAC,yCAAyC,CACnDlE,KAAK,CAAC,cAAc,CAAAmE,QAAA,cAEpB9G,IAAA,CAACL,KAAK,GAAE,CAAC,CACL,CAAC,cACTK,IAAA,WACIgH,OAAO,CAAEA,CAAA,GAAMrC,UAAU,CAACf,IAAI,CAAE,CAChCiD,SAAS,CAAC,uCAAuC,CACjDlE,KAAK,CAAC,WAAW,CAAAmE,QAAA,cAEjB9G,IAAA,CAACP,MAAM,GAAE,CAAC,CACN,CAAC,cACTO,IAAA,WACIgH,OAAO,CAAEA,CAAA,GAAMvB,iBAAiB,CAAC7B,IAAI,CAAE,CACvCiD,SAAS,CAAC,2CAA2C,CACrDlE,KAAK,CAAC,cAAc,CAAAmE,QAAA,cAEpB9G,IAAA,CAACJ,WAAW,GAAE,CAAC,CACX,CAAC,cACTI,IAAA,WACIgH,OAAO,CAAEA,CAAA,GAAMpB,aAAa,CAAChC,IAAI,CAAE,CACnCiD,SAAS,CAAC,qCAAqC,CAC/ClE,KAAK,CAAC,UAAU,CAAAmE,QAAA,cAEhB9G,IAAA,CAACH,KAAK,GAAE,CAAC,CACL,CAAC,EACR,CAAC,CACN,CAAC,GAvEA+D,IAAI,CAACgB,OAwEV,CACP,CAAC,CACC,CAAC,EACL,CAAC,CACP,CAAC,CAGL0B,UAAU,CAAG,CAAC,eACXtG,IAAA,QAAK6G,SAAS,CAAC,mFAAmF,CAAAC,QAAA,cAC9F5G,KAAA,QAAK2G,SAAS,CAAC,6DAA6D,CAAAC,QAAA,eACxE9G,IAAA,QAAA8G,QAAA,cACI5G,KAAA,MAAG2G,SAAS,CAAC,uBAAuB,CAAAC,QAAA,EAAC,UACzB,cAAA9G,IAAA,SAAM6G,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAEX,gBAAgB,CAAG,CAAC,CAAO,CAAC,MAAG,CAAC,GAAG,cAC1EnG,IAAA,SAAM6G,SAAS,CAAC,aAAa,CAAAC,QAAA,CACxBZ,eAAe,CAAGH,aAAa,CAACrC,MAAM,CAAGqC,aAAa,CAACrC,MAAM,CAAGwC,eAAe,CAC9E,CAAC,CAAC,GAAG,CAAC,KACT,cAAAlG,IAAA,SAAM6G,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAEf,aAAa,CAACrC,MAAM,CAAO,CAAC,WAClE,EAAG,CAAC,CACH,CAAC,cACN1D,IAAA,QAAA8G,QAAA,cACI5G,KAAA,QAAK2G,SAAS,CAAC,2DAA2D,CAAC,aAAW,YAAY,CAAAC,QAAA,eAC9F5G,KAAA,WACI8G,OAAO,CAAEA,CAAA,GAAMR,QAAQ,CAACtF,WAAW,CAAG,CAAC,CAAGA,WAAW,CAAG,CAAC,CAAG,CAAC,CAAE,CAC/DuG,QAAQ,CAAEvG,WAAW,GAAK,CAAE,CAC5B2F,SAAS,CAAE,gHACP3F,WAAW,GAAK,CAAC,CAAG,kCAAkC,CAAG,gCAAgC,EAC1F,CAAA4F,QAAA,eAEH9G,IAAA,SAAM6G,SAAS,CAAC,SAAS,CAAAC,QAAA,CAAC,UAAQ,CAAM,CAAC,cACzC9G,IAAA,QAAK6G,SAAS,CAAC,SAAS,CAACa,KAAK,CAAC,4BAA4B,CAACC,OAAO,CAAC,WAAW,CAACC,IAAI,CAAC,cAAc,CAAC,cAAY,MAAM,CAAAd,QAAA,cAClH9G,IAAA,SAAM6H,QAAQ,CAAC,SAAS,CAACC,CAAC,CAAC,mHAAmH,CAACC,QAAQ,CAAC,SAAS,CAAE,CAAC,CACnK,CAAC,EACF,CAAC,CAERrB,WAAW,CAACU,GAAG,CAACY,MAAM,eACnBhI,IAAA,WAEIgH,OAAO,CAAEA,CAAA,GAAMR,QAAQ,CAACwB,MAAM,CAAE,CAChCnB,SAAS,CAAE,mGACP3F,WAAW,GAAK8G,MAAM,CAChB,+CAA+C,CAC/C,gCAAgC,EACvC,CAAAlB,QAAA,CAEFkB,MAAM,EARFA,MASD,CACX,CAAC,cAEF9H,KAAA,WACI8G,OAAO,CAAEA,CAAA,GAAMR,QAAQ,CAACtF,WAAW,CAAGoF,UAAU,CAAGpF,WAAW,CAAG,CAAC,CAAGoF,UAAU,CAAE,CACjFmB,QAAQ,CAAEvG,WAAW,GAAKoF,UAAW,CACrCO,SAAS,CAAE,gHACP3F,WAAW,GAAKoF,UAAU,CAAG,kCAAkC,CAAG,gCAAgC,EACnG,CAAAQ,QAAA,eAEH9G,IAAA,SAAM6G,SAAS,CAAC,SAAS,CAAAC,QAAA,CAAC,MAAI,CAAM,CAAC,cACrC9G,IAAA,QAAK6G,SAAS,CAAC,SAAS,CAACa,KAAK,CAAC,4BAA4B,CAACC,OAAO,CAAC,WAAW,CAACC,IAAI,CAAC,cAAc,CAAC,cAAY,MAAM,CAAAd,QAAA,cAClH9G,IAAA,SAAM6H,QAAQ,CAAC,SAAS,CAACC,CAAC,CAAC,oHAAoH,CAACC,QAAQ,CAAC,SAAS,CAAE,CAAC,CACpK,CAAC,EACF,CAAC,EACR,CAAC,CACL,CAAC,EACL,CAAC,CACL,CACR,EACA,CAAC,CAGLjH,aAAa,eACVd,IAAA,QAAK6G,SAAS,CAAC,gFAAgF,CAAAC,QAAA,cAC3F5G,KAAA,QAAK2G,SAAS,CAAC,+CAA+C,CAAAC,QAAA,eAC1D5G,KAAA,QAAK2G,SAAS,CAAC,gDAAgD,CAAAC,QAAA,eAC3D9G,IAAA,OAAI6G,SAAS,CAAC,qCAAqC,CAAAC,QAAA,CAAC,WAAS,CAAI,CAAC,cAClE9G,IAAA,WACI6G,SAAS,CAAC,+DAA+D,CACzEG,OAAO,CAAEA,CAAA,GAAMjG,gBAAgB,CAAC,IAAI,CAAE,CAAA+F,QAAA,CACzC,MAED,CAAQ,CAAC,EACR,CAAC,cAEN5G,KAAA,SAAM+H,QAAQ,CAAEpD,YAAa,CAACgC,SAAS,CAAC,KAAK,CAAAC,QAAA,eACzC5G,KAAA,QAAK2G,SAAS,CAAC,WAAW,CAAAC,QAAA,eACtB5G,KAAA,QAAA4G,QAAA,eACI9G,IAAA,UAAO6G,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,UAAQ,CAAO,CAAC,cAChF9G,IAAA,UACI0C,IAAI,CAAC,MAAM,CACX8B,IAAI,CAAC,UAAU,CACfW,KAAK,CAAExD,WAAW,CAACE,QAAS,CAC5BqF,QAAQ,CAAEhC,iBAAkB,CAC5B2B,SAAS,CAAC,4IAA4I,CACtJqB,QAAQ,MACX,CAAC,EACD,CAAC,cAENhI,KAAA,QAAA4G,QAAA,eACI9G,IAAA,UAAO6G,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,WAAS,CAAO,CAAC,cACjF9G,IAAA,UACI0C,IAAI,CAAC,MAAM,CACX8B,IAAI,CAAC,WAAW,CAChBW,KAAK,CAAExD,WAAW,CAACG,SAAU,CAC7BoF,QAAQ,CAAEhC,iBAAkB,CAC5B2B,SAAS,CAAC,4IAA4I,CACtJqB,QAAQ,MACX,CAAC,EACD,CAAC,cAENhI,KAAA,QAAA4G,QAAA,eACI9G,IAAA,UAAO6G,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,OAAK,CAAO,CAAC,cAC7E9G,IAAA,UACI0C,IAAI,CAAC,OAAO,CACZ8B,IAAI,CAAC,OAAO,CACZW,KAAK,CAAExD,WAAW,CAACI,KAAM,CACzBmF,QAAQ,CAAEhC,iBAAkB,CAC5B2B,SAAS,CAAC,4IAA4I,CACtJqB,QAAQ,MACX,CAAC,EACD,CAAC,cAENhI,KAAA,QAAA4G,QAAA,eACI9G,IAAA,UAAO6G,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,eAAa,CAAO,CAAC,cACrF5G,KAAA,WACIsE,IAAI,CAAC,eAAe,CACpBW,KAAK,CAAExD,WAAW,CAACK,aAAc,CACjCkF,QAAQ,CAAEhC,iBAAkB,CAC5B2B,SAAS,CAAC,4IAA4I,CACtJqB,QAAQ,MAAApB,QAAA,eAER9G,IAAA,WAAQmF,KAAK,CAAC,EAAE,CAAA2B,QAAA,CAAC,sBAAoB,CAAQ,CAAC,CAC7CtG,KAAK,CAAC4G,GAAG,CAAC9C,IAAI,eACXtE,IAAA,WAAsBmF,KAAK,CAAEb,IAAI,CAACE,IAAK,CAAAsC,QAAA,CAAExC,IAAI,CAACE,IAAI,EAArCF,IAAI,CAAC6D,EAAyC,CAC9D,CAAC,EACE,CAAC,EACR,CAAC,cAENjI,KAAA,QAAA4G,QAAA,eACI9G,IAAA,UAAO6G,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,oBAAkB,CAAO,CAAC,cAC1F9G,IAAA,UACI0C,IAAI,CAAC,QAAQ,CACb8B,IAAI,CAAC,SAAS,CACdW,KAAK,CAAExD,WAAW,CAACM,OAAQ,CAC3BiF,QAAQ,CAAEhC,iBAAkB,CAC5B2B,SAAS,CAAC,4IAA4I,CACtJqB,QAAQ,MACX,CAAC,EACD,CAAC,EACL,CAAC,cAENhI,KAAA,QAAK2G,SAAS,CAAC,iCAAiC,CAAAC,QAAA,eAC5C9G,IAAA,WACI0C,IAAI,CAAC,QAAQ,CACbsE,OAAO,CAAEA,CAAA,GAAMjG,gBAAgB,CAAC,IAAI,CAAE,CACtC8F,SAAS,CAAC,2LAA2L,CAAAC,QAAA,CACxM,QAED,CAAQ,CAAC,cACT9G,IAAA,WACI0C,IAAI,CAAC,QAAQ,CACbmE,SAAS,CAAC,+LAA+L,CAAAC,QAAA,CAC5M,aAED,CAAQ,CAAC,EACR,CAAC,EACJ,CAAC,EACN,CAAC,CACL,CACR,CAGA5E,gBAAgB,eACblC,IAAA,QAAK6G,SAAS,CAAC,gFAAgF,CAAAC,QAAA,cAC3F5G,KAAA,QAAK2G,SAAS,CAAC,+CAA+C,CAAAC,QAAA,eAC1D5G,KAAA,QAAK2G,SAAS,CAAC,gDAAgD,CAAAC,QAAA,eAC3D9G,IAAA,OAAI6G,SAAS,CAAC,qCAAqC,CAAAC,QAAA,CAAC,cAAY,CAAI,CAAC,cACrE9G,IAAA,WACI6G,SAAS,CAAC,+DAA+D,CACzEG,OAAO,CAAEA,CAAA,GAAM7E,mBAAmB,CAAC,KAAK,CAAE,CAAA2E,QAAA,CAC7C,MAED,CAAQ,CAAC,EACR,CAAC,cAEN5G,KAAA,SAAM+H,QAAQ,CAAE1C,aAAc,CAACsB,SAAS,CAAC,KAAK,CAAAC,QAAA,eAC1C5G,KAAA,QAAK2G,SAAS,CAAC,WAAW,CAAAC,QAAA,eACtB5G,KAAA,QAAA4G,QAAA,eACI9G,IAAA,UAAO6G,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,UAAQ,CAAO,CAAC,cAChF9G,IAAA,UACI0C,IAAI,CAAC,MAAM,CACX8B,IAAI,CAAC,UAAU,CACfW,KAAK,CAAE/C,OAAO,CAACP,QAAS,CACxBqF,QAAQ,CAAE5B,wBAAyB,CACnCuB,SAAS,CAAC,8IAA8I,CACxJqB,QAAQ,MACX,CAAC,EACD,CAAC,cAENhI,KAAA,QAAA4G,QAAA,eACI9G,IAAA,UAAO6G,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,WAAS,CAAO,CAAC,cACjF9G,IAAA,UACI0C,IAAI,CAAC,MAAM,CACX8B,IAAI,CAAC,WAAW,CAChBW,KAAK,CAAE/C,OAAO,CAACN,SAAU,CACzBoF,QAAQ,CAAE5B,wBAAyB,CACnCuB,SAAS,CAAC,8IAA8I,CACxJqB,QAAQ,MACX,CAAC,EACD,CAAC,cAENhI,KAAA,QAAA4G,QAAA,eACI9G,IAAA,UAAO6G,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,OAAK,CAAO,CAAC,cAC7E9G,IAAA,UACI0C,IAAI,CAAC,OAAO,CACZ8B,IAAI,CAAC,OAAO,CACZW,KAAK,CAAE/C,OAAO,CAACL,KAAM,CACrBmF,QAAQ,CAAE5B,wBAAyB,CACnCuB,SAAS,CAAC,8IAA8I,CACxJqB,QAAQ,MACX,CAAC,EACD,CAAC,cAENhI,KAAA,QAAA4G,QAAA,eACI9G,IAAA,UAAO6G,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,UAAQ,CAAO,CAAC,cAChF9G,IAAA,UACI0C,IAAI,CAAC,UAAU,CACf8B,IAAI,CAAC,UAAU,CACfW,KAAK,CAAE/C,OAAO,CAACE,QAAS,CACxB4E,QAAQ,CAAE5B,wBAAyB,CACnCuB,SAAS,CAAC,8IAA8I,CACxJqB,QAAQ,MACX,CAAC,EACD,CAAC,cAENhI,KAAA,QAAA4G,QAAA,eACI9G,IAAA,UAAO6G,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,eAAa,CAAO,CAAC,cACrF5G,KAAA,WACIsE,IAAI,CAAC,eAAe,CACpBW,KAAK,CAAE/C,OAAO,CAACJ,aAAc,CAC7BkF,QAAQ,CAAE5B,wBAAyB,CACnCuB,SAAS,CAAC,8IAA8I,CAAAC,QAAA,eAExJ9G,IAAA,WAAQmF,KAAK,CAAC,EAAE,CAAA2B,QAAA,CAAC,sBAAoB,CAAQ,CAAC,CAC7CtG,KAAK,CAAC4G,GAAG,CAAC9C,IAAI,eACXtE,IAAA,WAAsBmF,KAAK,CAAEb,IAAI,CAACE,IAAK,CAAAsC,QAAA,CAAExC,IAAI,CAACE,IAAI,EAArCF,IAAI,CAAC6D,EAAyC,CAC9D,CAAC,EACE,CAAC,EACR,CAAC,cAENjI,KAAA,QAAA4G,QAAA,eACI9G,IAAA,UAAO6G,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,4BAA0B,CAAO,CAAC,cAClG9G,IAAA,UACI0C,IAAI,CAAC,QAAQ,CACb8B,IAAI,CAAC,SAAS,CACdW,KAAK,CAAE/C,OAAO,CAACH,OAAQ,CACvBiF,QAAQ,CAAE5B,wBAAyB,CACnCuB,SAAS,CAAC,8IAA8I,CACxJuB,GAAG,CAAC,GAAG,CACV,CAAC,EACD,CAAC,EACL,CAAC,cAENlI,KAAA,QAAK2G,SAAS,CAAC,iCAAiC,CAAAC,QAAA,eAC5C9G,IAAA,WACI0C,IAAI,CAAC,QAAQ,CACbsE,OAAO,CAAEA,CAAA,GAAM7E,mBAAmB,CAAC,KAAK,CAAE,CAC1C0E,SAAS,CAAC,4LAA4L,CAAAC,QAAA,CACzM,QAED,CAAQ,CAAC,cACT9G,IAAA,WACI0C,IAAI,CAAC,QAAQ,CACbmE,SAAS,CAAC,kMAAkM,CAAAC,QAAA,CAC/M,UAED,CAAQ,CAAC,EACR,CAAC,EACJ,CAAC,EACN,CAAC,CACL,CACR,cAGD9G,IAAA,CAACF,WAAW,EACR2C,MAAM,CAAEF,UAAU,CAACE,MAAO,CAC1B4F,OAAO,CAAEA,CAAA,GAAM7F,aAAa,CAAC,CAAE,GAAGD,UAAU,CAAEE,MAAM,CAAE,KAAM,CAAC,CAAE,CAC/DI,SAAS,CAAEN,UAAU,CAACM,SAAU,CAChCF,KAAK,CAAEJ,UAAU,CAACI,KAAM,CACxBC,OAAO,CAAEL,UAAU,CAACK,OAAQ,CAC5BF,IAAI,CAAEH,UAAU,CAACG,IAAK,CACtBI,WAAW,CAAEP,UAAU,CAACO,WAAY,CACpCC,kBAAkB,CAAER,UAAU,CAACQ,kBAAmB,CACrD,CAAC,EACD,CAAC,CAEd,CACA,cAAe,CAAA3C,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}