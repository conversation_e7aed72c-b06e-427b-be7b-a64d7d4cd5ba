{"ast": null, "code": "import React,{useState,useEffect}from'react';import{useParams,useNavigate}from'react-router-dom';import axios from'axios';import{FaUser,FaEnvelope,FaCalendarAlt,FaCoins,FaChartLine,FaTrophy,FaArrowLeft,FaEdit,FaBan,FaUserSlash,FaHistory,FaGamepad}from'react-icons/fa';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const API_BASE_URL='/backend';function UserDetails(){const{userId}=useParams();const navigate=useNavigate();const[user,setUser]=useState(null);const[teams,setTeams]=useState([]);const[userBets,setUserBets]=useState([]);const[userTransactions,setUserTransactions]=useState([]);const[loading,setLoading]=useState(true);const[error,setError]=useState('');const[success,setSuccess]=useState('');useEffect(()=>{if(userId){fetchUserDetails();fetchTeams();fetchUserBets();fetchUserTransactions();}},[userId]);const fetchUserDetails=async()=>{try{const response=await axios.get(`${API_BASE_URL}/handlers/user_details.php?id=${userId}`);if(response.data.success){setUser(response.data.user);}else{setError('User not found');}}catch(err){setError('Failed to fetch user details');console.error('Error fetching user details:',err);}};const fetchTeams=async()=>{try{const response=await axios.get(`${API_BASE_URL}/handlers/team_management.php`);setTeams(response.data.data||[]);}catch(err){console.error('Error fetching teams:',err);}};const fetchUserBets=async()=>{try{const response=await axios.get(`${API_BASE_URL}/handlers/user_bets.php?user_id=${userId}`);if(response.data.success){setUserBets(response.data.bets||[]);}}catch(err){console.error('Error fetching user bets:',err);}};const fetchUserTransactions=async()=>{try{const response=await axios.get(`${API_BASE_URL}/handlers/user_transactions.php?user_id=${userId}`);if(response.data.success){setUserTransactions(response.data.transactions||[]);}}catch(err){console.error('Error fetching user transactions:',err);}finally{setLoading(false);}};const getTeamLogo=teamName=>{const team=teams.find(team=>team.name===teamName);return team?`${API_BASE_URL}/${team.logo}`:null;};const getDefaultAvatar=()=>{return\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='80' height='80' viewBox='0 0 80 80'%3E%3Ccircle cx='40' cy='40' r='40' fill='%23e5e7eb'/%3E%3Cpath d='M40 40c6.6 0 12-5.4 12-12s-5.4-12-12-12-12 5.4-12 12 5.4 12 12 12zm0 6c-8 0-24 4-24 12v6h48v-6c0-8-16-12-24-12z' fill='%23374151'/%3E%3C/svg%3E\";};const handleSuspendUser=async()=>{if(window.confirm('Are you sure you want to suspend this user?')){try{const response=await axios.post(`${API_BASE_URL}/handlers/suspend_user.php`,{user_id:userId,action:'suspend'});if(response.data.success){setSuccess('User suspended successfully');fetchUserDetails();}else{setError(response.data.message||'Failed to suspend user');}}catch(err){setError('Failed to suspend user');}}};const handleBanUser=async()=>{if(window.confirm('Are you sure you want to ban this user? This action cannot be undone.')){try{const response=await axios.post(`${API_BASE_URL}/handlers/ban_user.php`,{user_id:userId,action:'ban'});if(response.data.success){setSuccess('User banned successfully');fetchUserDetails();}else{setError(response.data.message||'Failed to ban user');}}catch(err){setError('Failed to ban user');}}};if(loading){return/*#__PURE__*/_jsx(\"div\",{className:\"flex justify-center items-center min-h-screen\",children:/*#__PURE__*/_jsx(\"div\",{className:\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"})});}if(!user){return/*#__PURE__*/_jsx(\"div\",{className:\"p-6 bg-gray-50 min-h-screen\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-2xl font-bold text-gray-800 mb-4\",children:\"User Not Found\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>navigate('/admin/users'),className:\"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700\",children:\"Back to Users\"})]})});}return/*#__PURE__*/_jsxs(\"div\",{className:\"p-6 bg-gray-50 min-h-screen\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"mb-8\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-4\",children:[/*#__PURE__*/_jsxs(\"button\",{onClick:()=>navigate('/admin/users'),className:\"flex items-center text-blue-600 hover:text-blue-800\",children:[/*#__PURE__*/_jsx(FaArrowLeft,{className:\"mr-2\"}),\"Back to Users\"]}),/*#__PURE__*/_jsx(\"h1\",{className:\"text-2xl font-bold text-gray-800\",children:\"User Details\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex space-x-2\",children:[/*#__PURE__*/_jsxs(\"button\",{onClick:()=>navigate(`/admin/users/edit/${userId}`),className:\"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700\",children:[/*#__PURE__*/_jsx(FaEdit,{className:\"mr-2\"}),\"Edit User\"]}),/*#__PURE__*/_jsxs(\"button\",{onClick:handleSuspendUser,className:\"flex items-center px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700\",children:[/*#__PURE__*/_jsx(FaUserSlash,{className:\"mr-2\"}),\"Suspend\"]}),/*#__PURE__*/_jsxs(\"button\",{onClick:handleBanUser,className:\"flex items-center px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700\",children:[/*#__PURE__*/_jsx(FaBan,{className:\"mr-2\"}),\"Ban User\"]})]})]})}),error&&/*#__PURE__*/_jsx(\"div\",{className:\"mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative\",children:error}),success&&/*#__PURE__*/_jsx(\"div\",{className:\"mb-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative\",children:success}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-lg shadow-sm p-6 mb-8\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-6\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex-shrink-0\",children:/*#__PURE__*/_jsx(\"img\",{src:getTeamLogo(user.favorite_team)||getDefaultAvatar(),alt:user.favorite_team||'User Avatar',className:\"w-20 h-20 rounded-full object-contain border-2 border-gray-200\",onError:e=>{e.target.src=getDefaultAvatar();}})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-2xl font-bold text-gray-900\",children:user.full_name}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-gray-600\",children:[\"@\",user.username]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-4 mt-2\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center text-gray-500\",children:[/*#__PURE__*/_jsx(FaEnvelope,{className:\"mr-1\"}),user.email]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center text-gray-500\",children:[/*#__PURE__*/_jsx(FaCalendarAlt,{className:\"mr-1\"}),\"Joined \",new Date(user.created_at).toLocaleDateString()]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-right\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-3xl font-bold text-green-600\",children:user.balance}),/*#__PURE__*/_jsx(\"div\",{className:\"text-gray-500\",children:\"FanCoins\"})]})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-lg shadow-sm p-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"rounded-full bg-blue-100 p-3 mr-4\",children:/*#__PURE__*/_jsx(FaGamepad,{className:\"text-blue-500 text-xl\"})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-500 uppercase tracking-wider\",children:\"Total Bets\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-2xl font-bold text-gray-800\",children:user.total_bets||0})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-lg shadow-sm p-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"rounded-full bg-green-100 p-3 mr-4\",children:/*#__PURE__*/_jsx(FaTrophy,{className:\"text-green-500 text-xl\"})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-500 uppercase tracking-wider\",children:\"Wins\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-2xl font-bold text-gray-800\",children:user.wins||0})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-lg shadow-sm p-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"rounded-full bg-yellow-100 p-3 mr-4\",children:/*#__PURE__*/_jsx(FaChartLine,{className:\"text-yellow-500 text-xl\"})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-500 uppercase tracking-wider\",children:\"Current Streak\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-2xl font-bold text-gray-800\",children:user.current_streak||0})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-lg shadow-sm p-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"rounded-full bg-purple-100 p-3 mr-4\",children:/*#__PURE__*/_jsx(FaCoins,{className:\"text-purple-500 text-xl\"})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-500 uppercase tracking-wider\",children:\"Total Points\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-2xl font-bold text-gray-800\",children:user.total_points||0})]})]})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 lg:grid-cols-2 gap-8\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded-lg shadow-sm p-6\",children:[/*#__PURE__*/_jsxs(\"h3\",{className:\"text-lg font-semibold text-gray-800 mb-4 flex items-center\",children:[/*#__PURE__*/_jsx(FaGamepad,{className:\"mr-2\"}),\"Recent Bets\"]}),/*#__PURE__*/_jsx(\"div\",{className:\"space-y-4\",children:userBets.length>0?userBets.slice(0,5).map(bet=>/*#__PURE__*/_jsxs(\"div\",{className:\"border-l-4 border-blue-500 pl-4 py-2\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between items-start\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"p\",{className:\"font-medium text-gray-900\",children:[bet.team_a,\" vs \",bet.team_b]}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-sm text-gray-500\",children:[\"Amount: \",bet.amount_user1,\" FC\"]})]}),/*#__PURE__*/_jsx(\"span\",{className:`px-2 py-1 text-xs rounded-full ${bet.bet_status==='completed'?'bg-green-100 text-green-800':bet.bet_status==='open'?'bg-yellow-100 text-yellow-800':'bg-blue-100 text-blue-800'}`,children:bet.bet_status})]}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xs text-gray-400 mt-1\",children:new Date(bet.created_at).toLocaleDateString()})]},bet.bet_id)):/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-500 text-center py-4\",children:\"No bets found\"})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded-lg shadow-sm p-6\",children:[/*#__PURE__*/_jsxs(\"h3\",{className:\"text-lg font-semibold text-gray-800 mb-4 flex items-center\",children:[/*#__PURE__*/_jsx(FaHistory,{className:\"mr-2\"}),\"Recent Transactions\"]}),/*#__PURE__*/_jsx(\"div\",{className:\"space-y-4\",children:userTransactions.length>0?userTransactions.slice(0,5).map(transaction=>/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between items-center py-2 border-b border-gray-100\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"p\",{className:\"font-medium text-gray-900 capitalize\",children:transaction.type}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xs text-gray-400\",children:new Date(transaction.created_at).toLocaleDateString()})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-right\",children:[/*#__PURE__*/_jsxs(\"p\",{className:`font-medium ${transaction.amount>0?'text-green-600':'text-red-600'}`,children:[transaction.amount>0?'+':'',transaction.amount,\" FC\"]}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xs text-gray-500 capitalize\",children:transaction.status})]})]},transaction.transaction_id)):/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-500 text-center py-4\",children:\"No transactions found\"})})]})]})]});}export default UserDetails;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "useNavigate", "axios", "FaUser", "FaEnvelope", "FaCalendarAlt", "FaCoins", "FaChartLine", "FaTrophy", "FaArrowLeft", "FaEdit", "FaBan", "FaUserSlash", "FaHistory", "FaGamepad", "jsx", "_jsx", "jsxs", "_jsxs", "API_BASE_URL", "UserDetails", "userId", "navigate", "user", "setUser", "teams", "setTeams", "userBets", "setUserBets", "userTransactions", "setUserTransactions", "loading", "setLoading", "error", "setError", "success", "setSuccess", "fetchUserDetails", "fetchTeams", "fetchUserBets", "fetchUserTransactions", "response", "get", "data", "err", "console", "bets", "transactions", "getTeamLogo", "teamName", "team", "find", "name", "logo", "getDefaultAvatar", "handleSuspendUser", "window", "confirm", "post", "user_id", "action", "message", "handleBanUser", "className", "children", "onClick", "src", "favorite_team", "alt", "onError", "e", "target", "full_name", "username", "email", "Date", "created_at", "toLocaleDateString", "balance", "total_bets", "wins", "current_streak", "total_points", "length", "slice", "map", "bet", "team_a", "team_b", "amount_user1", "bet_status", "bet_id", "transaction", "type", "amount", "status", "transaction_id"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/pages/UserDetails.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport axios from 'axios';\nimport { \n    FaUser, \n    FaEnvelope, \n    FaCalendarAlt, \n    FaCoins, \n    FaChartLine, \n    FaTrophy, \n    FaArrowLeft,\n    FaEdit,\n    FaBan,\n    FaUserSlash,\n    FaHistory,\n    FaGamepad\n} from 'react-icons/fa';\n\nconst API_BASE_URL = '/backend';\n\nfunction UserDetails() {\n    const { userId } = useParams();\n    const navigate = useNavigate();\n    const [user, setUser] = useState(null);\n    const [teams, setTeams] = useState([]);\n    const [userBets, setUserBets] = useState([]);\n    const [userTransactions, setUserTransactions] = useState([]);\n    const [loading, setLoading] = useState(true);\n    const [error, setError] = useState('');\n    const [success, setSuccess] = useState('');\n\n    useEffect(() => {\n        if (userId) {\n            fetchUserDetails();\n            fetchTeams();\n            fetchUserBets();\n            fetchUserTransactions();\n        }\n    }, [userId]);\n\n    const fetchUserDetails = async () => {\n        try {\n            const response = await axios.get(`${API_BASE_URL}/handlers/user_details.php?id=${userId}`);\n            if (response.data.success) {\n                setUser(response.data.user);\n            } else {\n                setError('User not found');\n            }\n        } catch (err) {\n            setError('Failed to fetch user details');\n            console.error('Error fetching user details:', err);\n        }\n    };\n\n    const fetchTeams = async () => {\n        try {\n            const response = await axios.get(`${API_BASE_URL}/handlers/team_management.php`);\n            setTeams(response.data.data || []);\n        } catch (err) {\n            console.error('Error fetching teams:', err);\n        }\n    };\n\n    const fetchUserBets = async () => {\n        try {\n            const response = await axios.get(`${API_BASE_URL}/handlers/user_bets.php?user_id=${userId}`);\n            if (response.data.success) {\n                setUserBets(response.data.bets || []);\n            }\n        } catch (err) {\n            console.error('Error fetching user bets:', err);\n        }\n    };\n\n    const fetchUserTransactions = async () => {\n        try {\n            const response = await axios.get(`${API_BASE_URL}/handlers/user_transactions.php?user_id=${userId}`);\n            if (response.data.success) {\n                setUserTransactions(response.data.transactions || []);\n            }\n        } catch (err) {\n            console.error('Error fetching user transactions:', err);\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const getTeamLogo = (teamName) => {\n        const team = teams.find(team => team.name === teamName);\n        return team ? `${API_BASE_URL}/${team.logo}` : null;\n    };\n\n    const getDefaultAvatar = () => {\n        return \"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='80' height='80' viewBox='0 0 80 80'%3E%3Ccircle cx='40' cy='40' r='40' fill='%23e5e7eb'/%3E%3Cpath d='M40 40c6.6 0 12-5.4 12-12s-5.4-12-12-12-12 5.4-12 12 5.4 12 12 12zm0 6c-8 0-24 4-24 12v6h48v-6c0-8-16-12-24-12z' fill='%23374151'/%3E%3C/svg%3E\";\n    };\n\n    const handleSuspendUser = async () => {\n        if (window.confirm('Are you sure you want to suspend this user?')) {\n            try {\n                const response = await axios.post(`${API_BASE_URL}/handlers/suspend_user.php`, {\n                    user_id: userId,\n                    action: 'suspend'\n                });\n                if (response.data.success) {\n                    setSuccess('User suspended successfully');\n                    fetchUserDetails();\n                } else {\n                    setError(response.data.message || 'Failed to suspend user');\n                }\n            } catch (err) {\n                setError('Failed to suspend user');\n            }\n        }\n    };\n\n    const handleBanUser = async () => {\n        if (window.confirm('Are you sure you want to ban this user? This action cannot be undone.')) {\n            try {\n                const response = await axios.post(`${API_BASE_URL}/handlers/ban_user.php`, {\n                    user_id: userId,\n                    action: 'ban'\n                });\n                if (response.data.success) {\n                    setSuccess('User banned successfully');\n                    fetchUserDetails();\n                } else {\n                    setError(response.data.message || 'Failed to ban user');\n                }\n            } catch (err) {\n                setError('Failed to ban user');\n            }\n        }\n    };\n\n    if (loading) {\n        return (\n            <div className=\"flex justify-center items-center min-h-screen\">\n                <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n            </div>\n        );\n    }\n\n    if (!user) {\n        return (\n            <div className=\"p-6 bg-gray-50 min-h-screen\">\n                <div className=\"text-center\">\n                    <h1 className=\"text-2xl font-bold text-gray-800 mb-4\">User Not Found</h1>\n                    <button\n                        onClick={() => navigate('/admin/users')}\n                        className=\"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700\"\n                    >\n                        Back to Users\n                    </button>\n                </div>\n            </div>\n        );\n    }\n\n    return (\n        <div className=\"p-6 bg-gray-50 min-h-screen\">\n            {/* Header */}\n            <div className=\"mb-8\">\n                <div className=\"flex items-center justify-between\">\n                    <div className=\"flex items-center space-x-4\">\n                        <button\n                            onClick={() => navigate('/admin/users')}\n                            className=\"flex items-center text-blue-600 hover:text-blue-800\"\n                        >\n                            <FaArrowLeft className=\"mr-2\" />\n                            Back to Users\n                        </button>\n                        <h1 className=\"text-2xl font-bold text-gray-800\">User Details</h1>\n                    </div>\n                    <div className=\"flex space-x-2\">\n                        <button\n                            onClick={() => navigate(`/admin/users/edit/${userId}`)}\n                            className=\"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700\"\n                        >\n                            <FaEdit className=\"mr-2\" />\n                            Edit User\n                        </button>\n                        <button\n                            onClick={handleSuspendUser}\n                            className=\"flex items-center px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700\"\n                        >\n                            <FaUserSlash className=\"mr-2\" />\n                            Suspend\n                        </button>\n                        <button\n                            onClick={handleBanUser}\n                            className=\"flex items-center px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700\"\n                        >\n                            <FaBan className=\"mr-2\" />\n                            Ban User\n                        </button>\n                    </div>\n                </div>\n            </div>\n\n            {/* Notification Messages */}\n            {error && (\n                <div className=\"mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative\">\n                    {error}\n                </div>\n            )}\n            {success && (\n                <div className=\"mb-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative\">\n                    {success}\n                </div>\n            )}\n\n            {/* User Profile Card */}\n            <div className=\"bg-white rounded-lg shadow-sm p-6 mb-8\">\n                <div className=\"flex items-center space-x-6\">\n                    <div className=\"flex-shrink-0\">\n                        <img\n                            src={getTeamLogo(user.favorite_team) || getDefaultAvatar()}\n                            alt={user.favorite_team || 'User Avatar'}\n                            className=\"w-20 h-20 rounded-full object-contain border-2 border-gray-200\"\n                            onError={(e) => {\n                                e.target.src = getDefaultAvatar();\n                            }}\n                        />\n                    </div>\n                    <div className=\"flex-1\">\n                        <h2 className=\"text-2xl font-bold text-gray-900\">{user.full_name}</h2>\n                        <p className=\"text-gray-600\">@{user.username}</p>\n                        <div className=\"flex items-center space-x-4 mt-2\">\n                            <div className=\"flex items-center text-gray-500\">\n                                <FaEnvelope className=\"mr-1\" />\n                                {user.email}\n                            </div>\n                            <div className=\"flex items-center text-gray-500\">\n                                <FaCalendarAlt className=\"mr-1\" />\n                                Joined {new Date(user.created_at).toLocaleDateString()}\n                            </div>\n                        </div>\n                    </div>\n                    <div className=\"text-right\">\n                        <div className=\"text-3xl font-bold text-green-600\">{user.balance}</div>\n                        <div className=\"text-gray-500\">FanCoins</div>\n                    </div>\n                </div>\n            </div>\n\n            {/* Stats Grid */}\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n                <div className=\"bg-white rounded-lg shadow-sm p-6\">\n                    <div className=\"flex items-center\">\n                        <div className=\"rounded-full bg-blue-100 p-3 mr-4\">\n                            <FaGamepad className=\"text-blue-500 text-xl\" />\n                        </div>\n                        <div>\n                            <p className=\"text-sm text-gray-500 uppercase tracking-wider\">Total Bets</p>\n                            <h3 className=\"text-2xl font-bold text-gray-800\">{user.total_bets || 0}</h3>\n                        </div>\n                    </div>\n                </div>\n\n                <div className=\"bg-white rounded-lg shadow-sm p-6\">\n                    <div className=\"flex items-center\">\n                        <div className=\"rounded-full bg-green-100 p-3 mr-4\">\n                            <FaTrophy className=\"text-green-500 text-xl\" />\n                        </div>\n                        <div>\n                            <p className=\"text-sm text-gray-500 uppercase tracking-wider\">Wins</p>\n                            <h3 className=\"text-2xl font-bold text-gray-800\">{user.wins || 0}</h3>\n                        </div>\n                    </div>\n                </div>\n\n                <div className=\"bg-white rounded-lg shadow-sm p-6\">\n                    <div className=\"flex items-center\">\n                        <div className=\"rounded-full bg-yellow-100 p-3 mr-4\">\n                            <FaChartLine className=\"text-yellow-500 text-xl\" />\n                        </div>\n                        <div>\n                            <p className=\"text-sm text-gray-500 uppercase tracking-wider\">Current Streak</p>\n                            <h3 className=\"text-2xl font-bold text-gray-800\">{user.current_streak || 0}</h3>\n                        </div>\n                    </div>\n                </div>\n\n                <div className=\"bg-white rounded-lg shadow-sm p-6\">\n                    <div className=\"flex items-center\">\n                        <div className=\"rounded-full bg-purple-100 p-3 mr-4\">\n                            <FaCoins className=\"text-purple-500 text-xl\" />\n                        </div>\n                        <div>\n                            <p className=\"text-sm text-gray-500 uppercase tracking-wider\">Total Points</p>\n                            <h3 className=\"text-2xl font-bold text-gray-800\">{user.total_points || 0}</h3>\n                        </div>\n                    </div>\n                </div>\n            </div>\n\n            {/* Recent Activity */}\n            <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n                {/* Recent Bets */}\n                <div className=\"bg-white rounded-lg shadow-sm p-6\">\n                    <h3 className=\"text-lg font-semibold text-gray-800 mb-4 flex items-center\">\n                        <FaGamepad className=\"mr-2\" />\n                        Recent Bets\n                    </h3>\n                    <div className=\"space-y-4\">\n                        {userBets.length > 0 ? (\n                            userBets.slice(0, 5).map((bet) => (\n                                <div key={bet.bet_id} className=\"border-l-4 border-blue-500 pl-4 py-2\">\n                                    <div className=\"flex justify-between items-start\">\n                                        <div>\n                                            <p className=\"font-medium text-gray-900\">\n                                                {bet.team_a} vs {bet.team_b}\n                                            </p>\n                                            <p className=\"text-sm text-gray-500\">\n                                                Amount: {bet.amount_user1} FC\n                                            </p>\n                                        </div>\n                                        <span className={`px-2 py-1 text-xs rounded-full ${\n                                            bet.bet_status === 'completed' ? 'bg-green-100 text-green-800' :\n                                            bet.bet_status === 'open' ? 'bg-yellow-100 text-yellow-800' :\n                                            'bg-blue-100 text-blue-800'\n                                        }`}>\n                                            {bet.bet_status}\n                                        </span>\n                                    </div>\n                                    <p className=\"text-xs text-gray-400 mt-1\">\n                                        {new Date(bet.created_at).toLocaleDateString()}\n                                    </p>\n                                </div>\n                            ))\n                        ) : (\n                            <p className=\"text-gray-500 text-center py-4\">No bets found</p>\n                        )}\n                    </div>\n                </div>\n\n                {/* Recent Transactions */}\n                <div className=\"bg-white rounded-lg shadow-sm p-6\">\n                    <h3 className=\"text-lg font-semibold text-gray-800 mb-4 flex items-center\">\n                        <FaHistory className=\"mr-2\" />\n                        Recent Transactions\n                    </h3>\n                    <div className=\"space-y-4\">\n                        {userTransactions.length > 0 ? (\n                            userTransactions.slice(0, 5).map((transaction) => (\n                                <div key={transaction.transaction_id} className=\"flex justify-between items-center py-2 border-b border-gray-100\">\n                                    <div>\n                                        <p className=\"font-medium text-gray-900 capitalize\">\n                                            {transaction.type}\n                                        </p>\n                                        <p className=\"text-xs text-gray-400\">\n                                            {new Date(transaction.created_at).toLocaleDateString()}\n                                        </p>\n                                    </div>\n                                    <div className=\"text-right\">\n                                        <p className={`font-medium ${\n                                            transaction.amount > 0 ? 'text-green-600' : 'text-red-600'\n                                        }`}>\n                                            {transaction.amount > 0 ? '+' : ''}{transaction.amount} FC\n                                        </p>\n                                        <p className=\"text-xs text-gray-500 capitalize\">\n                                            {transaction.status}\n                                        </p>\n                                    </div>\n                                </div>\n                            ))\n                        ) : (\n                            <p className=\"text-gray-500 text-center py-4\">No transactions found</p>\n                        )}\n                    </div>\n                </div>\n            </div>\n        </div>\n    );\n}\n\nexport default UserDetails;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,SAAS,CAAEC,WAAW,KAAQ,kBAAkB,CACzD,MAAO,CAAAC,KAAK,KAAM,OAAO,CACzB,OACIC,MAAM,CACNC,UAAU,CACVC,aAAa,CACbC,OAAO,CACPC,WAAW,CACXC,QAAQ,CACRC,WAAW,CACXC,MAAM,CACNC,KAAK,CACLC,WAAW,CACXC,SAAS,CACTC,SAAS,KACN,gBAAgB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAExB,KAAM,CAAAC,YAAY,CAAG,UAAU,CAE/B,QAAS,CAAAC,WAAWA,CAAA,CAAG,CACnB,KAAM,CAAEC,MAAO,CAAC,CAAGrB,SAAS,CAAC,CAAC,CAC9B,KAAM,CAAAsB,QAAQ,CAAGrB,WAAW,CAAC,CAAC,CAC9B,KAAM,CAACsB,IAAI,CAAEC,OAAO,CAAC,CAAG1B,QAAQ,CAAC,IAAI,CAAC,CACtC,KAAM,CAAC2B,KAAK,CAAEC,QAAQ,CAAC,CAAG5B,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAAC6B,QAAQ,CAAEC,WAAW,CAAC,CAAG9B,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAAC+B,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGhC,QAAQ,CAAC,EAAE,CAAC,CAC5D,KAAM,CAACiC,OAAO,CAAEC,UAAU,CAAC,CAAGlC,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACmC,KAAK,CAAEC,QAAQ,CAAC,CAAGpC,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAACqC,OAAO,CAAEC,UAAU,CAAC,CAAGtC,QAAQ,CAAC,EAAE,CAAC,CAE1CC,SAAS,CAAC,IAAM,CACZ,GAAIsB,MAAM,CAAE,CACRgB,gBAAgB,CAAC,CAAC,CAClBC,UAAU,CAAC,CAAC,CACZC,aAAa,CAAC,CAAC,CACfC,qBAAqB,CAAC,CAAC,CAC3B,CACJ,CAAC,CAAE,CAACnB,MAAM,CAAC,CAAC,CAEZ,KAAM,CAAAgB,gBAAgB,CAAG,KAAAA,CAAA,GAAY,CACjC,GAAI,CACA,KAAM,CAAAI,QAAQ,CAAG,KAAM,CAAAvC,KAAK,CAACwC,GAAG,CAAC,GAAGvB,YAAY,iCAAiCE,MAAM,EAAE,CAAC,CAC1F,GAAIoB,QAAQ,CAACE,IAAI,CAACR,OAAO,CAAE,CACvBX,OAAO,CAACiB,QAAQ,CAACE,IAAI,CAACpB,IAAI,CAAC,CAC/B,CAAC,IAAM,CACHW,QAAQ,CAAC,gBAAgB,CAAC,CAC9B,CACJ,CAAE,MAAOU,GAAG,CAAE,CACVV,QAAQ,CAAC,8BAA8B,CAAC,CACxCW,OAAO,CAACZ,KAAK,CAAC,8BAA8B,CAAEW,GAAG,CAAC,CACtD,CACJ,CAAC,CAED,KAAM,CAAAN,UAAU,CAAG,KAAAA,CAAA,GAAY,CAC3B,GAAI,CACA,KAAM,CAAAG,QAAQ,CAAG,KAAM,CAAAvC,KAAK,CAACwC,GAAG,CAAC,GAAGvB,YAAY,+BAA+B,CAAC,CAChFO,QAAQ,CAACe,QAAQ,CAACE,IAAI,CAACA,IAAI,EAAI,EAAE,CAAC,CACtC,CAAE,MAAOC,GAAG,CAAE,CACVC,OAAO,CAACZ,KAAK,CAAC,uBAAuB,CAAEW,GAAG,CAAC,CAC/C,CACJ,CAAC,CAED,KAAM,CAAAL,aAAa,CAAG,KAAAA,CAAA,GAAY,CAC9B,GAAI,CACA,KAAM,CAAAE,QAAQ,CAAG,KAAM,CAAAvC,KAAK,CAACwC,GAAG,CAAC,GAAGvB,YAAY,mCAAmCE,MAAM,EAAE,CAAC,CAC5F,GAAIoB,QAAQ,CAACE,IAAI,CAACR,OAAO,CAAE,CACvBP,WAAW,CAACa,QAAQ,CAACE,IAAI,CAACG,IAAI,EAAI,EAAE,CAAC,CACzC,CACJ,CAAE,MAAOF,GAAG,CAAE,CACVC,OAAO,CAACZ,KAAK,CAAC,2BAA2B,CAAEW,GAAG,CAAC,CACnD,CACJ,CAAC,CAED,KAAM,CAAAJ,qBAAqB,CAAG,KAAAA,CAAA,GAAY,CACtC,GAAI,CACA,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAvC,KAAK,CAACwC,GAAG,CAAC,GAAGvB,YAAY,2CAA2CE,MAAM,EAAE,CAAC,CACpG,GAAIoB,QAAQ,CAACE,IAAI,CAACR,OAAO,CAAE,CACvBL,mBAAmB,CAACW,QAAQ,CAACE,IAAI,CAACI,YAAY,EAAI,EAAE,CAAC,CACzD,CACJ,CAAE,MAAOH,GAAG,CAAE,CACVC,OAAO,CAACZ,KAAK,CAAC,mCAAmC,CAAEW,GAAG,CAAC,CAC3D,CAAC,OAAS,CACNZ,UAAU,CAAC,KAAK,CAAC,CACrB,CACJ,CAAC,CAED,KAAM,CAAAgB,WAAW,CAAIC,QAAQ,EAAK,CAC9B,KAAM,CAAAC,IAAI,CAAGzB,KAAK,CAAC0B,IAAI,CAACD,IAAI,EAAIA,IAAI,CAACE,IAAI,GAAKH,QAAQ,CAAC,CACvD,MAAO,CAAAC,IAAI,CAAG,GAAG/B,YAAY,IAAI+B,IAAI,CAACG,IAAI,EAAE,CAAG,IAAI,CACvD,CAAC,CAED,KAAM,CAAAC,gBAAgB,CAAGA,CAAA,GAAM,CAC3B,MAAO,2TAA2T,CACtU,CAAC,CAED,KAAM,CAAAC,iBAAiB,CAAG,KAAAA,CAAA,GAAY,CAClC,GAAIC,MAAM,CAACC,OAAO,CAAC,6CAA6C,CAAC,CAAE,CAC/D,GAAI,CACA,KAAM,CAAAhB,QAAQ,CAAG,KAAM,CAAAvC,KAAK,CAACwD,IAAI,CAAC,GAAGvC,YAAY,4BAA4B,CAAE,CAC3EwC,OAAO,CAAEtC,MAAM,CACfuC,MAAM,CAAE,SACZ,CAAC,CAAC,CACF,GAAInB,QAAQ,CAACE,IAAI,CAACR,OAAO,CAAE,CACvBC,UAAU,CAAC,6BAA6B,CAAC,CACzCC,gBAAgB,CAAC,CAAC,CACtB,CAAC,IAAM,CACHH,QAAQ,CAACO,QAAQ,CAACE,IAAI,CAACkB,OAAO,EAAI,wBAAwB,CAAC,CAC/D,CACJ,CAAE,MAAOjB,GAAG,CAAE,CACVV,QAAQ,CAAC,wBAAwB,CAAC,CACtC,CACJ,CACJ,CAAC,CAED,KAAM,CAAA4B,aAAa,CAAG,KAAAA,CAAA,GAAY,CAC9B,GAAIN,MAAM,CAACC,OAAO,CAAC,uEAAuE,CAAC,CAAE,CACzF,GAAI,CACA,KAAM,CAAAhB,QAAQ,CAAG,KAAM,CAAAvC,KAAK,CAACwD,IAAI,CAAC,GAAGvC,YAAY,wBAAwB,CAAE,CACvEwC,OAAO,CAAEtC,MAAM,CACfuC,MAAM,CAAE,KACZ,CAAC,CAAC,CACF,GAAInB,QAAQ,CAACE,IAAI,CAACR,OAAO,CAAE,CACvBC,UAAU,CAAC,0BAA0B,CAAC,CACtCC,gBAAgB,CAAC,CAAC,CACtB,CAAC,IAAM,CACHH,QAAQ,CAACO,QAAQ,CAACE,IAAI,CAACkB,OAAO,EAAI,oBAAoB,CAAC,CAC3D,CACJ,CAAE,MAAOjB,GAAG,CAAE,CACVV,QAAQ,CAAC,oBAAoB,CAAC,CAClC,CACJ,CACJ,CAAC,CAED,GAAIH,OAAO,CAAE,CACT,mBACIf,IAAA,QAAK+C,SAAS,CAAC,+CAA+C,CAAAC,QAAA,cAC1DhD,IAAA,QAAK+C,SAAS,CAAC,gEAAgE,CAAM,CAAC,CACrF,CAAC,CAEd,CAEA,GAAI,CAACxC,IAAI,CAAE,CACP,mBACIP,IAAA,QAAK+C,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cACxC9C,KAAA,QAAK6C,SAAS,CAAC,aAAa,CAAAC,QAAA,eACxBhD,IAAA,OAAI+C,SAAS,CAAC,uCAAuC,CAAAC,QAAA,CAAC,gBAAc,CAAI,CAAC,cACzEhD,IAAA,WACIiD,OAAO,CAAEA,CAAA,GAAM3C,QAAQ,CAAC,cAAc,CAAE,CACxCyC,SAAS,CAAC,+DAA+D,CAAAC,QAAA,CAC5E,eAED,CAAQ,CAAC,EACR,CAAC,CACL,CAAC,CAEd,CAEA,mBACI9C,KAAA,QAAK6C,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAExChD,IAAA,QAAK+C,SAAS,CAAC,MAAM,CAAAC,QAAA,cACjB9C,KAAA,QAAK6C,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAC9C9C,KAAA,QAAK6C,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eACxC9C,KAAA,WACI+C,OAAO,CAAEA,CAAA,GAAM3C,QAAQ,CAAC,cAAc,CAAE,CACxCyC,SAAS,CAAC,qDAAqD,CAAAC,QAAA,eAE/DhD,IAAA,CAACP,WAAW,EAACsD,SAAS,CAAC,MAAM,CAAE,CAAC,gBAEpC,EAAQ,CAAC,cACT/C,IAAA,OAAI+C,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,cAAY,CAAI,CAAC,EACjE,CAAC,cACN9C,KAAA,QAAK6C,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC3B9C,KAAA,WACI+C,OAAO,CAAEA,CAAA,GAAM3C,QAAQ,CAAC,qBAAqBD,MAAM,EAAE,CAAE,CACvD0C,SAAS,CAAC,iFAAiF,CAAAC,QAAA,eAE3FhD,IAAA,CAACN,MAAM,EAACqD,SAAS,CAAC,MAAM,CAAE,CAAC,YAE/B,EAAQ,CAAC,cACT7C,KAAA,WACI+C,OAAO,CAAEV,iBAAkB,CAC3BQ,SAAS,CAAC,qFAAqF,CAAAC,QAAA,eAE/FhD,IAAA,CAACJ,WAAW,EAACmD,SAAS,CAAC,MAAM,CAAE,CAAC,UAEpC,EAAQ,CAAC,cACT7C,KAAA,WACI+C,OAAO,CAAEH,aAAc,CACvBC,SAAS,CAAC,+EAA+E,CAAAC,QAAA,eAEzFhD,IAAA,CAACL,KAAK,EAACoD,SAAS,CAAC,MAAM,CAAE,CAAC,WAE9B,EAAQ,CAAC,EACR,CAAC,EACL,CAAC,CACL,CAAC,CAGL9B,KAAK,eACFjB,IAAA,QAAK+C,SAAS,CAAC,+EAA+E,CAAAC,QAAA,CACzF/B,KAAK,CACL,CACR,CACAE,OAAO,eACJnB,IAAA,QAAK+C,SAAS,CAAC,qFAAqF,CAAAC,QAAA,CAC/F7B,OAAO,CACP,CACR,cAGDnB,IAAA,QAAK+C,SAAS,CAAC,wCAAwC,CAAAC,QAAA,cACnD9C,KAAA,QAAK6C,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eACxChD,IAAA,QAAK+C,SAAS,CAAC,eAAe,CAAAC,QAAA,cAC1BhD,IAAA,QACIkD,GAAG,CAAElB,WAAW,CAACzB,IAAI,CAAC4C,aAAa,CAAC,EAAIb,gBAAgB,CAAC,CAAE,CAC3Dc,GAAG,CAAE7C,IAAI,CAAC4C,aAAa,EAAI,aAAc,CACzCJ,SAAS,CAAC,gEAAgE,CAC1EM,OAAO,CAAGC,CAAC,EAAK,CACZA,CAAC,CAACC,MAAM,CAACL,GAAG,CAAGZ,gBAAgB,CAAC,CAAC,CACrC,CAAE,CACL,CAAC,CACD,CAAC,cACNpC,KAAA,QAAK6C,SAAS,CAAC,QAAQ,CAAAC,QAAA,eACnBhD,IAAA,OAAI+C,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAEzC,IAAI,CAACiD,SAAS,CAAK,CAAC,cACtEtD,KAAA,MAAG6C,SAAS,CAAC,eAAe,CAAAC,QAAA,EAAC,GAAC,CAACzC,IAAI,CAACkD,QAAQ,EAAI,CAAC,cACjDvD,KAAA,QAAK6C,SAAS,CAAC,kCAAkC,CAAAC,QAAA,eAC7C9C,KAAA,QAAK6C,SAAS,CAAC,iCAAiC,CAAAC,QAAA,eAC5ChD,IAAA,CAACZ,UAAU,EAAC2D,SAAS,CAAC,MAAM,CAAE,CAAC,CAC9BxC,IAAI,CAACmD,KAAK,EACV,CAAC,cACNxD,KAAA,QAAK6C,SAAS,CAAC,iCAAiC,CAAAC,QAAA,eAC5ChD,IAAA,CAACX,aAAa,EAAC0D,SAAS,CAAC,MAAM,CAAE,CAAC,UAC3B,CAAC,GAAI,CAAAY,IAAI,CAACpD,IAAI,CAACqD,UAAU,CAAC,CAACC,kBAAkB,CAAC,CAAC,EACrD,CAAC,EACL,CAAC,EACL,CAAC,cACN3D,KAAA,QAAK6C,SAAS,CAAC,YAAY,CAAAC,QAAA,eACvBhD,IAAA,QAAK+C,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAEzC,IAAI,CAACuD,OAAO,CAAM,CAAC,cACvE9D,IAAA,QAAK+C,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,UAAQ,CAAK,CAAC,EAC5C,CAAC,EACL,CAAC,CACL,CAAC,cAGN9C,KAAA,QAAK6C,SAAS,CAAC,2DAA2D,CAAAC,QAAA,eACtEhD,IAAA,QAAK+C,SAAS,CAAC,mCAAmC,CAAAC,QAAA,cAC9C9C,KAAA,QAAK6C,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAC9BhD,IAAA,QAAK+C,SAAS,CAAC,mCAAmC,CAAAC,QAAA,cAC9ChD,IAAA,CAACF,SAAS,EAACiD,SAAS,CAAC,uBAAuB,CAAE,CAAC,CAC9C,CAAC,cACN7C,KAAA,QAAA8C,QAAA,eACIhD,IAAA,MAAG+C,SAAS,CAAC,gDAAgD,CAAAC,QAAA,CAAC,YAAU,CAAG,CAAC,cAC5EhD,IAAA,OAAI+C,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAEzC,IAAI,CAACwD,UAAU,EAAI,CAAC,CAAK,CAAC,EAC3E,CAAC,EACL,CAAC,CACL,CAAC,cAEN/D,IAAA,QAAK+C,SAAS,CAAC,mCAAmC,CAAAC,QAAA,cAC9C9C,KAAA,QAAK6C,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAC9BhD,IAAA,QAAK+C,SAAS,CAAC,oCAAoC,CAAAC,QAAA,cAC/ChD,IAAA,CAACR,QAAQ,EAACuD,SAAS,CAAC,wBAAwB,CAAE,CAAC,CAC9C,CAAC,cACN7C,KAAA,QAAA8C,QAAA,eACIhD,IAAA,MAAG+C,SAAS,CAAC,gDAAgD,CAAAC,QAAA,CAAC,MAAI,CAAG,CAAC,cACtEhD,IAAA,OAAI+C,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAEzC,IAAI,CAACyD,IAAI,EAAI,CAAC,CAAK,CAAC,EACrE,CAAC,EACL,CAAC,CACL,CAAC,cAENhE,IAAA,QAAK+C,SAAS,CAAC,mCAAmC,CAAAC,QAAA,cAC9C9C,KAAA,QAAK6C,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAC9BhD,IAAA,QAAK+C,SAAS,CAAC,qCAAqC,CAAAC,QAAA,cAChDhD,IAAA,CAACT,WAAW,EAACwD,SAAS,CAAC,yBAAyB,CAAE,CAAC,CAClD,CAAC,cACN7C,KAAA,QAAA8C,QAAA,eACIhD,IAAA,MAAG+C,SAAS,CAAC,gDAAgD,CAAAC,QAAA,CAAC,gBAAc,CAAG,CAAC,cAChFhD,IAAA,OAAI+C,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAEzC,IAAI,CAAC0D,cAAc,EAAI,CAAC,CAAK,CAAC,EAC/E,CAAC,EACL,CAAC,CACL,CAAC,cAENjE,IAAA,QAAK+C,SAAS,CAAC,mCAAmC,CAAAC,QAAA,cAC9C9C,KAAA,QAAK6C,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAC9BhD,IAAA,QAAK+C,SAAS,CAAC,qCAAqC,CAAAC,QAAA,cAChDhD,IAAA,CAACV,OAAO,EAACyD,SAAS,CAAC,yBAAyB,CAAE,CAAC,CAC9C,CAAC,cACN7C,KAAA,QAAA8C,QAAA,eACIhD,IAAA,MAAG+C,SAAS,CAAC,gDAAgD,CAAAC,QAAA,CAAC,cAAY,CAAG,CAAC,cAC9EhD,IAAA,OAAI+C,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAEzC,IAAI,CAAC2D,YAAY,EAAI,CAAC,CAAK,CAAC,EAC7E,CAAC,EACL,CAAC,CACL,CAAC,EACL,CAAC,cAGNhE,KAAA,QAAK6C,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eAElD9C,KAAA,QAAK6C,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAC9C9C,KAAA,OAAI6C,SAAS,CAAC,4DAA4D,CAAAC,QAAA,eACtEhD,IAAA,CAACF,SAAS,EAACiD,SAAS,CAAC,MAAM,CAAE,CAAC,cAElC,EAAI,CAAC,cACL/C,IAAA,QAAK+C,SAAS,CAAC,WAAW,CAAAC,QAAA,CACrBrC,QAAQ,CAACwD,MAAM,CAAG,CAAC,CAChBxD,QAAQ,CAACyD,KAAK,CAAC,CAAC,CAAE,CAAC,CAAC,CAACC,GAAG,CAAEC,GAAG,eACzBpE,KAAA,QAAsB6C,SAAS,CAAC,sCAAsC,CAAAC,QAAA,eAClE9C,KAAA,QAAK6C,SAAS,CAAC,kCAAkC,CAAAC,QAAA,eAC7C9C,KAAA,QAAA8C,QAAA,eACI9C,KAAA,MAAG6C,SAAS,CAAC,2BAA2B,CAAAC,QAAA,EACnCsB,GAAG,CAACC,MAAM,CAAC,MAAI,CAACD,GAAG,CAACE,MAAM,EAC5B,CAAC,cACJtE,KAAA,MAAG6C,SAAS,CAAC,uBAAuB,CAAAC,QAAA,EAAC,UACzB,CAACsB,GAAG,CAACG,YAAY,CAAC,KAC9B,EAAG,CAAC,EACH,CAAC,cACNzE,IAAA,SAAM+C,SAAS,CAAE,kCACbuB,GAAG,CAACI,UAAU,GAAK,WAAW,CAAG,6BAA6B,CAC9DJ,GAAG,CAACI,UAAU,GAAK,MAAM,CAAG,+BAA+B,CAC3D,2BAA2B,EAC5B,CAAA1B,QAAA,CACEsB,GAAG,CAACI,UAAU,CACb,CAAC,EACN,CAAC,cACN1E,IAAA,MAAG+C,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CACpC,GAAI,CAAAW,IAAI,CAACW,GAAG,CAACV,UAAU,CAAC,CAACC,kBAAkB,CAAC,CAAC,CAC/C,CAAC,GApBES,GAAG,CAACK,MAqBT,CACR,CAAC,cAEF3E,IAAA,MAAG+C,SAAS,CAAC,gCAAgC,CAAAC,QAAA,CAAC,eAAa,CAAG,CACjE,CACA,CAAC,EACL,CAAC,cAGN9C,KAAA,QAAK6C,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAC9C9C,KAAA,OAAI6C,SAAS,CAAC,4DAA4D,CAAAC,QAAA,eACtEhD,IAAA,CAACH,SAAS,EAACkD,SAAS,CAAC,MAAM,CAAE,CAAC,sBAElC,EAAI,CAAC,cACL/C,IAAA,QAAK+C,SAAS,CAAC,WAAW,CAAAC,QAAA,CACrBnC,gBAAgB,CAACsD,MAAM,CAAG,CAAC,CACxBtD,gBAAgB,CAACuD,KAAK,CAAC,CAAC,CAAE,CAAC,CAAC,CAACC,GAAG,CAAEO,WAAW,eACzC1E,KAAA,QAAsC6C,SAAS,CAAC,iEAAiE,CAAAC,QAAA,eAC7G9C,KAAA,QAAA8C,QAAA,eACIhD,IAAA,MAAG+C,SAAS,CAAC,sCAAsC,CAAAC,QAAA,CAC9C4B,WAAW,CAACC,IAAI,CAClB,CAAC,cACJ7E,IAAA,MAAG+C,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAC/B,GAAI,CAAAW,IAAI,CAACiB,WAAW,CAAChB,UAAU,CAAC,CAACC,kBAAkB,CAAC,CAAC,CACvD,CAAC,EACH,CAAC,cACN3D,KAAA,QAAK6C,SAAS,CAAC,YAAY,CAAAC,QAAA,eACvB9C,KAAA,MAAG6C,SAAS,CAAE,eACV6B,WAAW,CAACE,MAAM,CAAG,CAAC,CAAG,gBAAgB,CAAG,cAAc,EAC3D,CAAA9B,QAAA,EACE4B,WAAW,CAACE,MAAM,CAAG,CAAC,CAAG,GAAG,CAAG,EAAE,CAAEF,WAAW,CAACE,MAAM,CAAC,KAC3D,EAAG,CAAC,cACJ9E,IAAA,MAAG+C,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAC1C4B,WAAW,CAACG,MAAM,CACpB,CAAC,EACH,CAAC,GAlBAH,WAAW,CAACI,cAmBjB,CACR,CAAC,cAEFhF,IAAA,MAAG+C,SAAS,CAAC,gCAAgC,CAAAC,QAAA,CAAC,uBAAqB,CAAG,CACzE,CACA,CAAC,EACL,CAAC,EACL,CAAC,EACL,CAAC,CAEd,CAEA,cAAe,CAAA5C,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}