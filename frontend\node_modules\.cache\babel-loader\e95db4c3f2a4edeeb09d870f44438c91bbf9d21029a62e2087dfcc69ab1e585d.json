{"ast": null, "code": "var _jsxFileName = \"C:\\\\MAMP\\\\htdocs\\\\FanBet247\\\\frontend\\\\src\\\\pages\\\\UserManagement.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { FaUsers, FaUserPlus, FaUserCheck, FaUserClock, FaEdit, FaSearch } from 'react-icons/fa';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst API_BASE_URL = '/backend';\nfunction UserManagement() {\n  _s();\n  const [users, setUsers] = useState([]);\n  const [teams, setTeams] = useState([]);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  const [editingUserId, setEditingUserId] = useState(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [currentPage, setCurrentPage] = useState(1);\n  const [usersPerPage] = useState(10);\n  const [stats, setStats] = useState({\n    totalUsers: 0,\n    activeUsers: 0,\n    newUsers: 0,\n    pendingUsers: 0\n  });\n  const [editingUser, setEditingUser] = useState({\n    username: '',\n    full_name: '',\n    email: '',\n    favorite_team: '',\n    balance: 0\n  });\n  useEffect(() => {\n    fetchUsers();\n    fetchTeams();\n  }, []);\n  const fetchUsers = async () => {\n    try {\n      const response = await axios.get(`${API_BASE_URL}/handlers/user_management.php`);\n      if (response.data.success) {\n        const userData = response.data.data || [];\n        setUsers(userData);\n\n        // Calculate stats\n        const now = new Date();\n        const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);\n\n        // For demo purposes, we'll simulate some stats\n        const totalUsers = userData.length;\n        const activeUsers = userData.filter(user => user.last_login && new Date(user.last_login) > oneWeekAgo).length || Math.floor(totalUsers * 0.7);\n        const newUsers = userData.filter(user => user.created_at && new Date(user.created_at) > oneWeekAgo).length || Math.floor(totalUsers * 0.2);\n        const pendingUsers = userData.filter(user => user.status === 'pending').length || Math.floor(totalUsers * 0.1);\n        setStats({\n          totalUsers,\n          activeUsers,\n          newUsers,\n          pendingUsers\n        });\n      } else {\n        setError(response.data.message || 'Failed to fetch users');\n      }\n    } catch (err) {\n      setError('Failed to fetch users. Please check your network connection and try again.');\n      console.error('Error fetching users:', err);\n    }\n  };\n  const fetchTeams = async () => {\n    try {\n      const response = await axios.get(`${API_BASE_URL}/handlers/team_management.php`);\n      setTeams(response.data.data || []);\n    } catch (err) {\n      setError('Failed to fetch teams');\n    }\n  };\n  const getTeamLogo = teamName => {\n    const team = teams.find(team => team.name === teamName);\n    return team ? `${API_BASE_URL}/${team.logo}` : null;\n  };\n  const getDefaultAvatar = () => {\n    return \"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='40' height='40' viewBox='0 0 40 40'%3E%3Ccircle cx='20' cy='20' r='20' fill='%23e5e7eb'/%3E%3Cpath d='M20 20c3.3 0 6-2.7 6-6s-2.7-6-6-6-6 2.7-6 6 2.7 6 6 6zm0 3c-4 0-12 2-12 6v3h24v-3c0-4-8-6-12-6z' fill='%23374151'/%3E%3C/svg%3E\";\n  };\n  const handleEdit = user => {\n    setEditingUserId(user.user_id);\n    setEditingUser(user);\n  };\n  const handleUpdate = async e => {\n    e.preventDefault();\n    try {\n      const response = await axios.put(`${API_BASE_URL}/handlers/user_management.php?id=${editingUserId}`, editingUser);\n      if (response.data.success) {\n        setSuccess('User updated successfully!');\n        fetchUsers();\n        setEditingUserId(null);\n      } else {\n        setError(response.data.message || 'Failed to update user');\n      }\n    } catch (err) {\n      setError('Failed to update user');\n    }\n  };\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setEditingUser(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  // Search functionality\n  const handleSearch = e => {\n    setSearchTerm(e.target.value);\n    setCurrentPage(1); // Reset to first page when searching\n  };\n\n  // Filter users based on search term\n  const filteredUsers = users.filter(user => user.username.toLowerCase().includes(searchTerm.toLowerCase()) || user.full_name.toLowerCase().includes(searchTerm.toLowerCase()) || user.email.toLowerCase().includes(searchTerm.toLowerCase()));\n\n  // Pagination\n  const indexOfLastUser = currentPage * usersPerPage;\n  const indexOfFirstUser = indexOfLastUser - usersPerPage;\n  const currentUsers = filteredUsers.slice(indexOfFirstUser, indexOfLastUser);\n  const totalPages = Math.ceil(filteredUsers.length / usersPerPage);\n  const paginate = pageNumber => setCurrentPage(pageNumber);\n\n  // Generate page numbers\n  const pageNumbers = [];\n  for (let i = 1; i <= totalPages; i++) {\n    pageNumbers.push(i);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"p-6 bg-gray-50 min-h-screen\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-2xl font-bold text-gray-800\",\n        children: \"User Management\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 142,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-600\",\n        children: \"Manage all users in the system\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 141,\n      columnNumber: 13\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative\",\n      role: \"alert\",\n      children: /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"block sm:inline\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 149,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 148,\n      columnNumber: 17\n    }, this), success && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative\",\n      role: \"alert\",\n      children: /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"block sm:inline\",\n        children: success\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 154,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 153,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm p-6 flex items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"rounded-full bg-blue-100 p-3 mr-4\",\n          children: /*#__PURE__*/_jsxDEV(FaUsers, {\n            className: \"text-blue-500 text-xl\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-500 uppercase tracking-wider\",\n            children: \"Total Users\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-2xl font-bold text-gray-800\",\n            children: stats.totalUsers\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 161,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm p-6 flex items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"rounded-full bg-green-100 p-3 mr-4\",\n          children: /*#__PURE__*/_jsxDEV(FaUserCheck, {\n            className: \"text-green-500 text-xl\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-500 uppercase tracking-wider\",\n            children: \"Active Users\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-2xl font-bold text-gray-800\",\n            children: stats.activeUsers\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 172,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm p-6 flex items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"rounded-full bg-yellow-100 p-3 mr-4\",\n          children: /*#__PURE__*/_jsxDEV(FaUserPlus, {\n            className: \"text-yellow-500 text-xl\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-500 uppercase tracking-wider\",\n            children: \"New Users (7d)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-2xl font-bold text-gray-800\",\n            children: stats.newUsers\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 183,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm p-6 flex items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"rounded-full bg-purple-100 p-3 mr-4\",\n          children: /*#__PURE__*/_jsxDEV(FaUserClock, {\n            className: \"text-purple-500 text-xl\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-500 uppercase tracking-wider\",\n            children: \"Pending Users\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-2xl font-bold text-gray-800\",\n            children: stats.pendingUsers\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 194,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 159,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg shadow-sm p-6 mb-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col md:flex-row md:items-center md:justify-between mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-lg font-semibold text-white bg-blue-600 px-4 py-2 rounded mb-4 md:mb-0\",\n          children: \"User List\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative w-full md:w-64\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n            children: /*#__PURE__*/_jsxDEV(FaSearch, {\n              className: \"h-4 w-4 text-gray-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            className: \"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm\",\n            placeholder: \"Search users...\",\n            value: searchTerm,\n            onChange: handleSearch\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"overflow-x-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"table\", {\n          className: \"min-w-full divide-y divide-gray-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            className: \"bg-blue-600\",\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                scope: \"col\",\n                className: \"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider\",\n                children: \"#\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                scope: \"col\",\n                className: \"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider\",\n                children: \"Username\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 231,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                scope: \"col\",\n                className: \"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider\",\n                children: \"Full Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 234,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                scope: \"col\",\n                className: \"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider\",\n                children: \"Email\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 237,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                scope: \"col\",\n                className: \"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider\",\n                children: \"Favorite Team\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 240,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                scope: \"col\",\n                className: \"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider\",\n                children: \"Balance\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 243,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                scope: \"col\",\n                className: \"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider\",\n                children: \"Actions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 246,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            className: \"bg-white divide-y divide-gray-200\",\n            children: currentUsers.map((user, index) => /*#__PURE__*/_jsxDEV(\"tr\", {\n              className: \"hover:bg-gray-50\",\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                children: indexOfFirstUser + index + 1\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 254,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm font-medium text-gray-900\",\n                  children: user.username\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 258,\n                  columnNumber: 41\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 257,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm text-gray-500\",\n                  children: user.full_name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 261,\n                  columnNumber: 41\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 260,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm text-gray-500\",\n                  children: user.email\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 264,\n                  columnNumber: 41\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 263,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm text-gray-500\",\n                  children: user.favorite_team\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 267,\n                  columnNumber: 41\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 266,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm font-medium text-gray-900\",\n                  children: [user.balance, \" FanCoins\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 270,\n                  columnNumber: 41\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 269,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => handleEdit(user),\n                  className: \"text-blue-600 hover:text-blue-900 flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(FaEdit, {\n                    className: \"mr-1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 277,\n                    columnNumber: 45\n                  }, this), \" Edit\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 273,\n                  columnNumber: 41\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 272,\n                columnNumber: 37\n              }, this)]\n            }, user.user_id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 253,\n              columnNumber: 33\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 225,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 224,\n        columnNumber: 17\n      }, this), totalPages > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between border-t border-gray-200 px-4 py-3 sm:px-6 mt-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-700\",\n              children: [\"Showing \", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-medium\",\n                children: indexOfFirstUser + 1\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 292,\n                columnNumber: 45\n              }, this), \" to\", ' ', /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-medium\",\n                children: indexOfLastUser > filteredUsers.length ? filteredUsers.length : indexOfLastUser\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 293,\n                columnNumber: 37\n              }, this), ' ', \"of \", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-medium\",\n                children: filteredUsers.length\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 296,\n                columnNumber: 40\n              }, this), \" results\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 291,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 290,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: /*#__PURE__*/_jsxDEV(\"nav\", {\n              className: \"relative z-0 inline-flex rounded-md shadow-sm -space-x-px\",\n              \"aria-label\": \"Pagination\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => paginate(currentPage > 1 ? currentPage - 1 : 1),\n                disabled: currentPage === 1,\n                className: `relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium ${currentPage === 1 ? 'text-gray-300 cursor-not-allowed' : 'text-gray-500 hover:bg-gray-50'}`,\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"sr-only\",\n                  children: \"Previous\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 308,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"h-5 w-5\",\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  viewBox: \"0 0 20 20\",\n                  fill: \"currentColor\",\n                  \"aria-hidden\": \"true\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    fillRule: \"evenodd\",\n                    d: \"M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z\",\n                    clipRule: \"evenodd\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 310,\n                    columnNumber: 45\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 309,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 301,\n                columnNumber: 37\n              }, this), pageNumbers.map(number => /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => paginate(number),\n                className: `relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium ${currentPage === number ? 'z-10 bg-blue-50 border-blue-500 text-blue-600' : 'text-gray-500 hover:bg-gray-50'}`,\n                children: number\n              }, number, false, {\n                fileName: _jsxFileName,\n                lineNumber: 315,\n                columnNumber: 41\n              }, this)), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => paginate(currentPage < totalPages ? currentPage + 1 : totalPages),\n                disabled: currentPage === totalPages,\n                className: `relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium ${currentPage === totalPages ? 'text-gray-300 cursor-not-allowed' : 'text-gray-500 hover:bg-gray-50'}`,\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"sr-only\",\n                  children: \"Next\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 335,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"h-5 w-5\",\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  viewBox: \"0 0 20 20\",\n                  fill: \"currentColor\",\n                  \"aria-hidden\": \"true\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    fillRule: \"evenodd\",\n                    d: \"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z\",\n                    clipRule: \"evenodd\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 337,\n                    columnNumber: 45\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 336,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 328,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 300,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 299,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 289,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 288,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 206,\n      columnNumber: 13\n    }, this), editingUserId && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-xl w-full max-w-md\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-between items-center p-4 border-b\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-gray-800\",\n            children: \"Edit User\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 352,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"text-gray-500 hover:text-gray-700 text-2xl focus:outline-none\",\n            onClick: () => setEditingUserId(null),\n            children: \"\\xD7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 353,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 351,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleUpdate,\n          className: \"p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Username\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 364,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                name: \"username\",\n                value: editingUser.username,\n                onChange: handleInputChange,\n                className: \"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 365,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 363,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Full Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 376,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                name: \"full_name\",\n                value: editingUser.full_name,\n                onChange: handleInputChange,\n                className: \"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 377,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 375,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Email\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 388,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"email\",\n                name: \"email\",\n                value: editingUser.email,\n                onChange: handleInputChange,\n                className: \"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 389,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 387,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Favorite Team\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 400,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                name: \"favorite_team\",\n                value: editingUser.favorite_team,\n                onChange: handleInputChange,\n                className: \"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\",\n                required: true,\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Select Favorite Team\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 408,\n                  columnNumber: 41\n                }, this), teams.map(team => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: team.name,\n                  children: team.name\n                }, team.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 410,\n                  columnNumber: 45\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 401,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 399,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Balance (FanCoins)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 416,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                name: \"balance\",\n                value: editingUser.balance,\n                onChange: handleInputChange,\n                className: \"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 417,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 415,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 362,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-6 flex justify-end space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              onClick: () => setEditingUserId(null),\n              className: \"px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 429,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n              children: \"Update User\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 436,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 428,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 361,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 350,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 349,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 139,\n    columnNumber: 9\n  }, this);\n}\n_s(UserManagement, \"TsMbciHqXWnuuDoeIhf6N2CZ7vM=\");\n_c = UserManagement;\nexport default UserManagement;\nvar _c;\n$RefreshReg$(_c, \"UserManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "FaUsers", "FaUserPlus", "FaUserCheck", "FaUserClock", "FaEdit", "FaSearch", "jsxDEV", "_jsxDEV", "API_BASE_URL", "UserManagement", "_s", "users", "setUsers", "teams", "setTeams", "error", "setError", "success", "setSuccess", "editingUserId", "setEditingUserId", "searchTerm", "setSearchTerm", "currentPage", "setCurrentPage", "usersPerPage", "stats", "setStats", "totalUsers", "activeUsers", "newUsers", "pendingUsers", "editingUser", "setEditingUser", "username", "full_name", "email", "favorite_team", "balance", "fetchUsers", "fetchTeams", "response", "get", "data", "userData", "now", "Date", "oneWeekAgo", "getTime", "length", "filter", "user", "last_login", "Math", "floor", "created_at", "status", "message", "err", "console", "getTeamLogo", "teamName", "team", "find", "name", "logo", "getDefaultAvatar", "handleEdit", "user_id", "handleUpdate", "e", "preventDefault", "put", "handleInputChange", "value", "target", "prev", "handleSearch", "filteredUsers", "toLowerCase", "includes", "indexOfLastUser", "indexOfFirstUser", "currentUsers", "slice", "totalPages", "ceil", "paginate", "pageNumber", "pageNumbers", "i", "push", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "role", "type", "placeholder", "onChange", "scope", "map", "index", "onClick", "disabled", "xmlns", "viewBox", "fill", "fillRule", "d", "clipRule", "number", "onSubmit", "required", "id", "_c", "$RefreshReg$"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/pages/UserManagement.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport axios from 'axios';\r\nimport { FaU<PERSON>s, FaUserPlus, FaUser<PERSON>heck, FaUser<PERSON>lock, FaEdit, FaSearch } from 'react-icons/fa';\r\n\r\nconst API_BASE_URL = '/backend';\r\n\r\nfunction UserManagement() {\r\n    const [users, setUsers] = useState([]);\r\n    const [teams, setTeams] = useState([]);\r\n    const [error, setError] = useState('');\r\n    const [success, setSuccess] = useState('');\r\n    const [editingUserId, setEditingUserId] = useState(null);\r\n    const [searchTerm, setSearchTerm] = useState('');\r\n    const [currentPage, setCurrentPage] = useState(1);\r\n    const [usersPerPage] = useState(10);\r\n    const [stats, setStats] = useState({\r\n        totalUsers: 0,\r\n        activeUsers: 0,\r\n        newUsers: 0,\r\n        pendingUsers: 0\r\n    });\r\n    const [editingUser, setEditingUser] = useState({\r\n        username: '',\r\n        full_name: '',\r\n        email: '',\r\n        favorite_team: '',\r\n        balance: 0\r\n    });\r\n\r\n    useEffect(() => {\r\n        fetchUsers();\r\n        fetchTeams();\r\n    }, []);\r\n\r\n    const fetchUsers = async () => {\r\n        try {\r\n            const response = await axios.get(`${API_BASE_URL}/handlers/user_management.php`);\r\n            if (response.data.success) {\r\n                const userData = response.data.data || [];\r\n                setUsers(userData);\r\n\r\n                // Calculate stats\r\n                const now = new Date();\r\n                const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);\r\n\r\n                // For demo purposes, we'll simulate some stats\r\n                const totalUsers = userData.length;\r\n                const activeUsers = userData.filter(user => user.last_login && new Date(user.last_login) > oneWeekAgo).length || Math.floor(totalUsers * 0.7);\r\n                const newUsers = userData.filter(user => user.created_at && new Date(user.created_at) > oneWeekAgo).length || Math.floor(totalUsers * 0.2);\r\n                const pendingUsers = userData.filter(user => user.status === 'pending').length || Math.floor(totalUsers * 0.1);\r\n\r\n                setStats({\r\n                    totalUsers,\r\n                    activeUsers,\r\n                    newUsers,\r\n                    pendingUsers\r\n                });\r\n            } else {\r\n                setError(response.data.message || 'Failed to fetch users');\r\n            }\r\n        } catch (err) {\r\n            setError('Failed to fetch users. Please check your network connection and try again.');\r\n            console.error('Error fetching users:', err);\r\n        }\r\n    };\r\n\r\n    const fetchTeams = async () => {\r\n        try {\r\n            const response = await axios.get(`${API_BASE_URL}/handlers/team_management.php`);\r\n            setTeams(response.data.data || []);\r\n        } catch (err) {\r\n            setError('Failed to fetch teams');\r\n        }\r\n    };\r\n\r\n    const getTeamLogo = (teamName) => {\r\n        const team = teams.find(team => team.name === teamName);\r\n        return team ? `${API_BASE_URL}/${team.logo}` : null;\r\n    };\r\n\r\n    const getDefaultAvatar = () => {\r\n        return \"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='40' height='40' viewBox='0 0 40 40'%3E%3Ccircle cx='20' cy='20' r='20' fill='%23e5e7eb'/%3E%3Cpath d='M20 20c3.3 0 6-2.7 6-6s-2.7-6-6-6-6 2.7-6 6 2.7 6 6 6zm0 3c-4 0-12 2-12 6v3h24v-3c0-4-8-6-12-6z' fill='%23374151'/%3E%3C/svg%3E\";\r\n    };\r\n\r\n    const handleEdit = (user) => {\r\n        setEditingUserId(user.user_id);\r\n        setEditingUser(user);\r\n    };\r\n\r\n    const handleUpdate = async (e) => {\r\n        e.preventDefault();\r\n        try {\r\n            const response = await axios.put(`${API_BASE_URL}/handlers/user_management.php?id=${editingUserId}`, editingUser);\r\n            if (response.data.success) {\r\n                setSuccess('User updated successfully!');\r\n                fetchUsers();\r\n                setEditingUserId(null);\r\n            } else {\r\n                setError(response.data.message || 'Failed to update user');\r\n            }\r\n        } catch (err) {\r\n            setError('Failed to update user');\r\n        }\r\n    };\r\n\r\n    const handleInputChange = (e) => {\r\n        const { name, value } = e.target;\r\n        setEditingUser(prev => ({ ...prev, [name]: value }));\r\n    };\r\n\r\n    // Search functionality\r\n    const handleSearch = (e) => {\r\n        setSearchTerm(e.target.value);\r\n        setCurrentPage(1); // Reset to first page when searching\r\n    };\r\n\r\n    // Filter users based on search term\r\n    const filteredUsers = users.filter(user =>\r\n        user.username.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n        user.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n        user.email.toLowerCase().includes(searchTerm.toLowerCase())\r\n    );\r\n\r\n    // Pagination\r\n    const indexOfLastUser = currentPage * usersPerPage;\r\n    const indexOfFirstUser = indexOfLastUser - usersPerPage;\r\n    const currentUsers = filteredUsers.slice(indexOfFirstUser, indexOfLastUser);\r\n    const totalPages = Math.ceil(filteredUsers.length / usersPerPage);\r\n\r\n    const paginate = (pageNumber) => setCurrentPage(pageNumber);\r\n\r\n    // Generate page numbers\r\n    const pageNumbers = [];\r\n    for (let i = 1; i <= totalPages; i++) {\r\n        pageNumbers.push(i);\r\n    }\r\n\r\n    return (\r\n        <div className=\"p-6 bg-gray-50 min-h-screen\">\r\n            {/* Page Header */}\r\n            <div className=\"mb-8\">\r\n                <h1 className=\"text-2xl font-bold text-gray-800\">User Management</h1>\r\n                <p className=\"text-gray-600\">Manage all users in the system</p>\r\n            </div>\r\n\r\n            {/* Notification Messages */}\r\n            {error && (\r\n                <div className=\"mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative\" role=\"alert\">\r\n                    <span className=\"block sm:inline\">{error}</span>\r\n                </div>\r\n            )}\r\n            {success && (\r\n                <div className=\"mb-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative\" role=\"alert\">\r\n                    <span className=\"block sm:inline\">{success}</span>\r\n                </div>\r\n            )}\r\n\r\n            {/* Stats Cards */}\r\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\r\n                {/* Total Users */}\r\n                <div className=\"bg-white rounded-lg shadow-sm p-6 flex items-center\">\r\n                    <div className=\"rounded-full bg-blue-100 p-3 mr-4\">\r\n                        <FaUsers className=\"text-blue-500 text-xl\" />\r\n                    </div>\r\n                    <div>\r\n                        <p className=\"text-sm text-gray-500 uppercase tracking-wider\">Total Users</p>\r\n                        <h3 className=\"text-2xl font-bold text-gray-800\">{stats.totalUsers}</h3>\r\n                    </div>\r\n                </div>\r\n\r\n                {/* Active Users */}\r\n                <div className=\"bg-white rounded-lg shadow-sm p-6 flex items-center\">\r\n                    <div className=\"rounded-full bg-green-100 p-3 mr-4\">\r\n                        <FaUserCheck className=\"text-green-500 text-xl\" />\r\n                    </div>\r\n                    <div>\r\n                        <p className=\"text-sm text-gray-500 uppercase tracking-wider\">Active Users</p>\r\n                        <h3 className=\"text-2xl font-bold text-gray-800\">{stats.activeUsers}</h3>\r\n                    </div>\r\n                </div>\r\n\r\n                {/* New Users */}\r\n                <div className=\"bg-white rounded-lg shadow-sm p-6 flex items-center\">\r\n                    <div className=\"rounded-full bg-yellow-100 p-3 mr-4\">\r\n                        <FaUserPlus className=\"text-yellow-500 text-xl\" />\r\n                    </div>\r\n                    <div>\r\n                        <p className=\"text-sm text-gray-500 uppercase tracking-wider\">New Users (7d)</p>\r\n                        <h3 className=\"text-2xl font-bold text-gray-800\">{stats.newUsers}</h3>\r\n                    </div>\r\n                </div>\r\n\r\n                {/* Pending Users */}\r\n                <div className=\"bg-white rounded-lg shadow-sm p-6 flex items-center\">\r\n                    <div className=\"rounded-full bg-purple-100 p-3 mr-4\">\r\n                        <FaUserClock className=\"text-purple-500 text-xl\" />\r\n                    </div>\r\n                    <div>\r\n                        <p className=\"text-sm text-gray-500 uppercase tracking-wider\">Pending Users</p>\r\n                        <h3 className=\"text-2xl font-bold text-gray-800\">{stats.pendingUsers}</h3>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            {/* Search and Filter */}\r\n            <div className=\"bg-white rounded-lg shadow-sm p-6 mb-8\">\r\n                <div className=\"flex flex-col md:flex-row md:items-center md:justify-between mb-4\">\r\n                    <h2 className=\"text-lg font-semibold text-white bg-blue-600 px-4 py-2 rounded mb-4 md:mb-0\">User List</h2>\r\n                    <div className=\"relative w-full md:w-64\">\r\n                        <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n                            <FaSearch className=\"h-4 w-4 text-gray-400\" />\r\n                        </div>\r\n                        <input\r\n                            type=\"text\"\r\n                            className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm\"\r\n                            placeholder=\"Search users...\"\r\n                            value={searchTerm}\r\n                            onChange={handleSearch}\r\n                        />\r\n                    </div>\r\n                </div>\r\n\r\n                {/* Users Table */}\r\n                <div className=\"overflow-x-auto\">\r\n                    <table className=\"min-w-full divide-y divide-gray-200\">\r\n                        <thead className=\"bg-blue-600\">\r\n                            <tr>\r\n                                <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider\">\r\n                                    #\r\n                                </th>\r\n                                <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider\">\r\n                                    Username\r\n                                </th>\r\n                                <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider\">\r\n                                    Full Name\r\n                                </th>\r\n                                <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider\">\r\n                                    Email\r\n                                </th>\r\n                                <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider\">\r\n                                    Favorite Team\r\n                                </th>\r\n                                <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider\">\r\n                                    Balance\r\n                                </th>\r\n                                <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider\">\r\n                                    Actions\r\n                                </th>\r\n                            </tr>\r\n                        </thead>\r\n                        <tbody className=\"bg-white divide-y divide-gray-200\">\r\n                            {currentUsers.map((user, index) => (\r\n                                <tr key={user.user_id} className=\"hover:bg-gray-50\">\r\n                                    <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\r\n                                        {indexOfFirstUser + index + 1}\r\n                                    </td>\r\n                                    <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                                        <div className=\"text-sm font-medium text-gray-900\">{user.username}</div>\r\n                                    </td>\r\n                                    <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                                        <div className=\"text-sm text-gray-500\">{user.full_name}</div>\r\n                                    </td>\r\n                                    <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                                        <div className=\"text-sm text-gray-500\">{user.email}</div>\r\n                                    </td>\r\n                                    <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                                        <div className=\"text-sm text-gray-500\">{user.favorite_team}</div>\r\n                                    </td>\r\n                                    <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                                        <div className=\"text-sm font-medium text-gray-900\">{user.balance} FanCoins</div>\r\n                                    </td>\r\n                                    <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\r\n                                        <button\r\n                                            onClick={() => handleEdit(user)}\r\n                                            className=\"text-blue-600 hover:text-blue-900 flex items-center\"\r\n                                        >\r\n                                            <FaEdit className=\"mr-1\" /> Edit\r\n                                        </button>\r\n                                    </td>\r\n                                </tr>\r\n                            ))}\r\n                        </tbody>\r\n                    </table>\r\n                </div>\r\n\r\n                {/* Pagination */}\r\n                {totalPages > 1 && (\r\n                    <div className=\"flex items-center justify-between border-t border-gray-200 px-4 py-3 sm:px-6 mt-4\">\r\n                        <div className=\"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between\">\r\n                            <div>\r\n                                <p className=\"text-sm text-gray-700\">\r\n                                    Showing <span className=\"font-medium\">{indexOfFirstUser + 1}</span> to{' '}\r\n                                    <span className=\"font-medium\">\r\n                                        {indexOfLastUser > filteredUsers.length ? filteredUsers.length : indexOfLastUser}\r\n                                    </span>{' '}\r\n                                    of <span className=\"font-medium\">{filteredUsers.length}</span> results\r\n                                </p>\r\n                            </div>\r\n                            <div>\r\n                                <nav className=\"relative z-0 inline-flex rounded-md shadow-sm -space-x-px\" aria-label=\"Pagination\">\r\n                                    <button\r\n                                        onClick={() => paginate(currentPage > 1 ? currentPage - 1 : 1)}\r\n                                        disabled={currentPage === 1}\r\n                                        className={`relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium ${\r\n                                            currentPage === 1 ? 'text-gray-300 cursor-not-allowed' : 'text-gray-500 hover:bg-gray-50'\r\n                                        }`}\r\n                                    >\r\n                                        <span className=\"sr-only\">Previous</span>\r\n                                        <svg className=\"h-5 w-5\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 20 20\" fill=\"currentColor\" aria-hidden=\"true\">\r\n                                            <path fillRule=\"evenodd\" d=\"M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\r\n                                        </svg>\r\n                                    </button>\r\n\r\n                                    {pageNumbers.map(number => (\r\n                                        <button\r\n                                            key={number}\r\n                                            onClick={() => paginate(number)}\r\n                                            className={`relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium ${\r\n                                                currentPage === number\r\n                                                    ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'\r\n                                                    : 'text-gray-500 hover:bg-gray-50'\r\n                                            }`}\r\n                                        >\r\n                                            {number}\r\n                                        </button>\r\n                                    ))}\r\n\r\n                                    <button\r\n                                        onClick={() => paginate(currentPage < totalPages ? currentPage + 1 : totalPages)}\r\n                                        disabled={currentPage === totalPages}\r\n                                        className={`relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium ${\r\n                                            currentPage === totalPages ? 'text-gray-300 cursor-not-allowed' : 'text-gray-500 hover:bg-gray-50'\r\n                                        }`}\r\n                                    >\r\n                                        <span className=\"sr-only\">Next</span>\r\n                                        <svg className=\"h-5 w-5\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 20 20\" fill=\"currentColor\" aria-hidden=\"true\">\r\n                                            <path fillRule=\"evenodd\" d=\"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z\" clipRule=\"evenodd\" />\r\n                                        </svg>\r\n                                    </button>\r\n                                </nav>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                )}\r\n            </div>\r\n\r\n            {/* Edit User Modal */}\r\n            {editingUserId && (\r\n                <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\r\n                    <div className=\"bg-white rounded-lg shadow-xl w-full max-w-md\">\r\n                        <div className=\"flex justify-between items-center p-4 border-b\">\r\n                            <h3 className=\"text-lg font-semibold text-gray-800\">Edit User</h3>\r\n                            <button\r\n                                className=\"text-gray-500 hover:text-gray-700 text-2xl focus:outline-none\"\r\n                                onClick={() => setEditingUserId(null)}\r\n                            >\r\n                                ×\r\n                            </button>\r\n                        </div>\r\n\r\n                        <form onSubmit={handleUpdate} className=\"p-6\">\r\n                            <div className=\"space-y-4\">\r\n                                <div>\r\n                                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Username</label>\r\n                                    <input\r\n                                        type=\"text\"\r\n                                        name=\"username\"\r\n                                        value={editingUser.username}\r\n                                        onChange={handleInputChange}\r\n                                        className=\"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\r\n                                        required\r\n                                    />\r\n                                </div>\r\n\r\n                                <div>\r\n                                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Full Name</label>\r\n                                    <input\r\n                                        type=\"text\"\r\n                                        name=\"full_name\"\r\n                                        value={editingUser.full_name}\r\n                                        onChange={handleInputChange}\r\n                                        className=\"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\r\n                                        required\r\n                                    />\r\n                                </div>\r\n\r\n                                <div>\r\n                                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Email</label>\r\n                                    <input\r\n                                        type=\"email\"\r\n                                        name=\"email\"\r\n                                        value={editingUser.email}\r\n                                        onChange={handleInputChange}\r\n                                        className=\"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\r\n                                        required\r\n                                    />\r\n                                </div>\r\n\r\n                                <div>\r\n                                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Favorite Team</label>\r\n                                    <select\r\n                                        name=\"favorite_team\"\r\n                                        value={editingUser.favorite_team}\r\n                                        onChange={handleInputChange}\r\n                                        className=\"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\r\n                                        required\r\n                                    >\r\n                                        <option value=\"\">Select Favorite Team</option>\r\n                                        {teams.map(team => (\r\n                                            <option key={team.id} value={team.name}>{team.name}</option>\r\n                                        ))}\r\n                                    </select>\r\n                                </div>\r\n\r\n                                <div>\r\n                                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Balance (FanCoins)</label>\r\n                                    <input\r\n                                        type=\"number\"\r\n                                        name=\"balance\"\r\n                                        value={editingUser.balance}\r\n                                        onChange={handleInputChange}\r\n                                        className=\"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\r\n                                        required\r\n                                    />\r\n                                </div>\r\n                            </div>\r\n\r\n                            <div className=\"mt-6 flex justify-end space-x-3\">\r\n                                <button\r\n                                    type=\"button\"\r\n                                    onClick={() => setEditingUserId(null)}\r\n                                    className=\"px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\r\n                                >\r\n                                    Cancel\r\n                                </button>\r\n                                <button\r\n                                    type=\"submit\"\r\n                                    className=\"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\r\n                                >\r\n                                    Update User\r\n                                </button>\r\n                            </div>\r\n                        </form>\r\n                    </div>\r\n                </div>\r\n            )}\r\n        </div>\r\n    );\r\n}\r\nexport default UserManagement;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,EAAEC,UAAU,EAAEC,WAAW,EAAEC,WAAW,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjG,MAAMC,YAAY,GAAG,UAAU;AAE/B,SAASC,cAAcA,CAAA,EAAG;EAAAC,EAAA;EACtB,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACgB,KAAK,EAAEC,QAAQ,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACkB,KAAK,EAAEC,QAAQ,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACoB,OAAO,EAAEC,UAAU,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACsB,aAAa,EAAEC,gBAAgB,CAAC,GAAGvB,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACwB,UAAU,EAAEC,aAAa,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC0B,WAAW,EAAEC,cAAc,CAAC,GAAG3B,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAAC4B,YAAY,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EACnC,MAAM,CAAC6B,KAAK,EAAEC,QAAQ,CAAC,GAAG9B,QAAQ,CAAC;IAC/B+B,UAAU,EAAE,CAAC;IACbC,WAAW,EAAE,CAAC;IACdC,QAAQ,EAAE,CAAC;IACXC,YAAY,EAAE;EAClB,CAAC,CAAC;EACF,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGpC,QAAQ,CAAC;IAC3CqC,QAAQ,EAAE,EAAE;IACZC,SAAS,EAAE,EAAE;IACbC,KAAK,EAAE,EAAE;IACTC,aAAa,EAAE,EAAE;IACjBC,OAAO,EAAE;EACb,CAAC,CAAC;EAEFxC,SAAS,CAAC,MAAM;IACZyC,UAAU,CAAC,CAAC;IACZC,UAAU,CAAC,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMD,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI;MACA,MAAME,QAAQ,GAAG,MAAM1C,KAAK,CAAC2C,GAAG,CAAC,GAAGlC,YAAY,+BAA+B,CAAC;MAChF,IAAIiC,QAAQ,CAACE,IAAI,CAAC1B,OAAO,EAAE;QACvB,MAAM2B,QAAQ,GAAGH,QAAQ,CAACE,IAAI,CAACA,IAAI,IAAI,EAAE;QACzC/B,QAAQ,CAACgC,QAAQ,CAAC;;QAElB;QACA,MAAMC,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;QACtB,MAAMC,UAAU,GAAG,IAAID,IAAI,CAACD,GAAG,CAACG,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;;QAEpE;QACA,MAAMpB,UAAU,GAAGgB,QAAQ,CAACK,MAAM;QAClC,MAAMpB,WAAW,GAAGe,QAAQ,CAACM,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,UAAU,IAAI,IAAIN,IAAI,CAACK,IAAI,CAACC,UAAU,CAAC,GAAGL,UAAU,CAAC,CAACE,MAAM,IAAII,IAAI,CAACC,KAAK,CAAC1B,UAAU,GAAG,GAAG,CAAC;QAC7I,MAAME,QAAQ,GAAGc,QAAQ,CAACM,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACI,UAAU,IAAI,IAAIT,IAAI,CAACK,IAAI,CAACI,UAAU,CAAC,GAAGR,UAAU,CAAC,CAACE,MAAM,IAAII,IAAI,CAACC,KAAK,CAAC1B,UAAU,GAAG,GAAG,CAAC;QAC1I,MAAMG,YAAY,GAAGa,QAAQ,CAACM,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACK,MAAM,KAAK,SAAS,CAAC,CAACP,MAAM,IAAII,IAAI,CAACC,KAAK,CAAC1B,UAAU,GAAG,GAAG,CAAC;QAE9GD,QAAQ,CAAC;UACLC,UAAU;UACVC,WAAW;UACXC,QAAQ;UACRC;QACJ,CAAC,CAAC;MACN,CAAC,MAAM;QACHf,QAAQ,CAACyB,QAAQ,CAACE,IAAI,CAACc,OAAO,IAAI,uBAAuB,CAAC;MAC9D;IACJ,CAAC,CAAC,OAAOC,GAAG,EAAE;MACV1C,QAAQ,CAAC,4EAA4E,CAAC;MACtF2C,OAAO,CAAC5C,KAAK,CAAC,uBAAuB,EAAE2C,GAAG,CAAC;IAC/C;EACJ,CAAC;EAED,MAAMlB,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI;MACA,MAAMC,QAAQ,GAAG,MAAM1C,KAAK,CAAC2C,GAAG,CAAC,GAAGlC,YAAY,+BAA+B,CAAC;MAChFM,QAAQ,CAAC2B,QAAQ,CAACE,IAAI,CAACA,IAAI,IAAI,EAAE,CAAC;IACtC,CAAC,CAAC,OAAOe,GAAG,EAAE;MACV1C,QAAQ,CAAC,uBAAuB,CAAC;IACrC;EACJ,CAAC;EAED,MAAM4C,WAAW,GAAIC,QAAQ,IAAK;IAC9B,MAAMC,IAAI,GAAGjD,KAAK,CAACkD,IAAI,CAACD,IAAI,IAAIA,IAAI,CAACE,IAAI,KAAKH,QAAQ,CAAC;IACvD,OAAOC,IAAI,GAAG,GAAGtD,YAAY,IAAIsD,IAAI,CAACG,IAAI,EAAE,GAAG,IAAI;EACvD,CAAC;EAED,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC3B,OAAO,2SAA2S;EACtT,CAAC;EAED,MAAMC,UAAU,GAAIhB,IAAI,IAAK;IACzB/B,gBAAgB,CAAC+B,IAAI,CAACiB,OAAO,CAAC;IAC9BnC,cAAc,CAACkB,IAAI,CAAC;EACxB,CAAC;EAED,MAAMkB,YAAY,GAAG,MAAOC,CAAC,IAAK;IAC9BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAI;MACA,MAAM9B,QAAQ,GAAG,MAAM1C,KAAK,CAACyE,GAAG,CAAC,GAAGhE,YAAY,oCAAoCW,aAAa,EAAE,EAAEa,WAAW,CAAC;MACjH,IAAIS,QAAQ,CAACE,IAAI,CAAC1B,OAAO,EAAE;QACvBC,UAAU,CAAC,4BAA4B,CAAC;QACxCqB,UAAU,CAAC,CAAC;QACZnB,gBAAgB,CAAC,IAAI,CAAC;MAC1B,CAAC,MAAM;QACHJ,QAAQ,CAACyB,QAAQ,CAACE,IAAI,CAACc,OAAO,IAAI,uBAAuB,CAAC;MAC9D;IACJ,CAAC,CAAC,OAAOC,GAAG,EAAE;MACV1C,QAAQ,CAAC,uBAAuB,CAAC;IACrC;EACJ,CAAC;EAED,MAAMyD,iBAAiB,GAAIH,CAAC,IAAK;IAC7B,MAAM;MAAEN,IAAI;MAAEU;IAAM,CAAC,GAAGJ,CAAC,CAACK,MAAM;IAChC1C,cAAc,CAAC2C,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACZ,IAAI,GAAGU;IAAM,CAAC,CAAC,CAAC;EACxD,CAAC;;EAED;EACA,MAAMG,YAAY,GAAIP,CAAC,IAAK;IACxBhD,aAAa,CAACgD,CAAC,CAACK,MAAM,CAACD,KAAK,CAAC;IAC7BlD,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;EACvB,CAAC;;EAED;EACA,MAAMsD,aAAa,GAAGnE,KAAK,CAACuC,MAAM,CAACC,IAAI,IACnCA,IAAI,CAACjB,QAAQ,CAAC6C,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC3D,UAAU,CAAC0D,WAAW,CAAC,CAAC,CAAC,IAC9D5B,IAAI,CAAChB,SAAS,CAAC4C,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC3D,UAAU,CAAC0D,WAAW,CAAC,CAAC,CAAC,IAC/D5B,IAAI,CAACf,KAAK,CAAC2C,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC3D,UAAU,CAAC0D,WAAW,CAAC,CAAC,CAC9D,CAAC;;EAED;EACA,MAAME,eAAe,GAAG1D,WAAW,GAAGE,YAAY;EAClD,MAAMyD,gBAAgB,GAAGD,eAAe,GAAGxD,YAAY;EACvD,MAAM0D,YAAY,GAAGL,aAAa,CAACM,KAAK,CAACF,gBAAgB,EAAED,eAAe,CAAC;EAC3E,MAAMI,UAAU,GAAGhC,IAAI,CAACiC,IAAI,CAACR,aAAa,CAAC7B,MAAM,GAAGxB,YAAY,CAAC;EAEjE,MAAM8D,QAAQ,GAAIC,UAAU,IAAKhE,cAAc,CAACgE,UAAU,CAAC;;EAE3D;EACA,MAAMC,WAAW,GAAG,EAAE;EACtB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIL,UAAU,EAAEK,CAAC,EAAE,EAAE;IAClCD,WAAW,CAACE,IAAI,CAACD,CAAC,CAAC;EACvB;EAEA,oBACInF,OAAA;IAAKqF,SAAS,EAAC,6BAA6B;IAAAC,QAAA,gBAExCtF,OAAA;MAAKqF,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACjBtF,OAAA;QAAIqF,SAAS,EAAC,kCAAkC;QAAAC,QAAA,EAAC;MAAe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACrE1F,OAAA;QAAGqF,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAA8B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9D,CAAC,EAGLlF,KAAK,iBACFR,OAAA;MAAKqF,SAAS,EAAC,+EAA+E;MAACM,IAAI,EAAC,OAAO;MAAAL,QAAA,eACvGtF,OAAA;QAAMqF,SAAS,EAAC,iBAAiB;QAAAC,QAAA,EAAE9E;MAAK;QAAA+E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/C,CACR,EACAhF,OAAO,iBACJV,OAAA;MAAKqF,SAAS,EAAC,qFAAqF;MAACM,IAAI,EAAC,OAAO;MAAAL,QAAA,eAC7GtF,OAAA;QAAMqF,SAAS,EAAC,iBAAiB;QAAAC,QAAA,EAAE5E;MAAO;QAAA6E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjD,CACR,eAGD1F,OAAA;MAAKqF,SAAS,EAAC,2DAA2D;MAAAC,QAAA,gBAEtEtF,OAAA;QAAKqF,SAAS,EAAC,qDAAqD;QAAAC,QAAA,gBAChEtF,OAAA;UAAKqF,SAAS,EAAC,mCAAmC;UAAAC,QAAA,eAC9CtF,OAAA,CAACP,OAAO;YAAC4F,SAAS,EAAC;UAAuB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC,eACN1F,OAAA;UAAAsF,QAAA,gBACItF,OAAA;YAAGqF,SAAS,EAAC,gDAAgD;YAAAC,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC7E1F,OAAA;YAAIqF,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAEnE,KAAK,CAACE;UAAU;YAAAkE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGN1F,OAAA;QAAKqF,SAAS,EAAC,qDAAqD;QAAAC,QAAA,gBAChEtF,OAAA;UAAKqF,SAAS,EAAC,oCAAoC;UAAAC,QAAA,eAC/CtF,OAAA,CAACL,WAAW;YAAC0F,SAAS,EAAC;UAAwB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD,CAAC,eACN1F,OAAA;UAAAsF,QAAA,gBACItF,OAAA;YAAGqF,SAAS,EAAC,gDAAgD;YAAAC,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC9E1F,OAAA;YAAIqF,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAEnE,KAAK,CAACG;UAAW;YAAAiE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGN1F,OAAA;QAAKqF,SAAS,EAAC,qDAAqD;QAAAC,QAAA,gBAChEtF,OAAA;UAAKqF,SAAS,EAAC,qCAAqC;UAAAC,QAAA,eAChDtF,OAAA,CAACN,UAAU;YAAC2F,SAAS,EAAC;UAAyB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD,CAAC,eACN1F,OAAA;UAAAsF,QAAA,gBACItF,OAAA;YAAGqF,SAAS,EAAC,gDAAgD;YAAAC,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAChF1F,OAAA;YAAIqF,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAEnE,KAAK,CAACI;UAAQ;YAAAgE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGN1F,OAAA;QAAKqF,SAAS,EAAC,qDAAqD;QAAAC,QAAA,gBAChEtF,OAAA;UAAKqF,SAAS,EAAC,qCAAqC;UAAAC,QAAA,eAChDtF,OAAA,CAACJ,WAAW;YAACyF,SAAS,EAAC;UAAyB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD,CAAC,eACN1F,OAAA;UAAAsF,QAAA,gBACItF,OAAA;YAAGqF,SAAS,EAAC,gDAAgD;YAAAC,QAAA,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC/E1F,OAAA;YAAIqF,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAEnE,KAAK,CAACK;UAAY;YAAA+D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGN1F,OAAA;MAAKqF,SAAS,EAAC,wCAAwC;MAAAC,QAAA,gBACnDtF,OAAA;QAAKqF,SAAS,EAAC,mEAAmE;QAAAC,QAAA,gBAC9EtF,OAAA;UAAIqF,SAAS,EAAC,6EAA6E;UAAAC,QAAA,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1G1F,OAAA;UAAKqF,SAAS,EAAC,yBAAyB;UAAAC,QAAA,gBACpCtF,OAAA;YAAKqF,SAAS,EAAC,sEAAsE;YAAAC,QAAA,eACjFtF,OAAA,CAACF,QAAQ;cAACuF,SAAS,EAAC;YAAuB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC,eACN1F,OAAA;YACI4F,IAAI,EAAC,MAAM;YACXP,SAAS,EAAC,qJAAqJ;YAC/JQ,WAAW,EAAC,iBAAiB;YAC7B1B,KAAK,EAAErD,UAAW;YAClBgF,QAAQ,EAAExB;UAAa;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGN1F,OAAA;QAAKqF,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC5BtF,OAAA;UAAOqF,SAAS,EAAC,qCAAqC;UAAAC,QAAA,gBAClDtF,OAAA;YAAOqF,SAAS,EAAC,aAAa;YAAAC,QAAA,eAC1BtF,OAAA;cAAAsF,QAAA,gBACItF,OAAA;gBAAI+F,KAAK,EAAC,KAAK;gBAACV,SAAS,EAAC,6EAA6E;gBAAAC,QAAA,EAAC;cAExG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL1F,OAAA;gBAAI+F,KAAK,EAAC,KAAK;gBAACV,SAAS,EAAC,6EAA6E;gBAAAC,QAAA,EAAC;cAExG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL1F,OAAA;gBAAI+F,KAAK,EAAC,KAAK;gBAACV,SAAS,EAAC,6EAA6E;gBAAAC,QAAA,EAAC;cAExG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL1F,OAAA;gBAAI+F,KAAK,EAAC,KAAK;gBAACV,SAAS,EAAC,6EAA6E;gBAAAC,QAAA,EAAC;cAExG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL1F,OAAA;gBAAI+F,KAAK,EAAC,KAAK;gBAACV,SAAS,EAAC,6EAA6E;gBAAAC,QAAA,EAAC;cAExG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL1F,OAAA;gBAAI+F,KAAK,EAAC,KAAK;gBAACV,SAAS,EAAC,6EAA6E;gBAAAC,QAAA,EAAC;cAExG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL1F,OAAA;gBAAI+F,KAAK,EAAC,KAAK;gBAACV,SAAS,EAAC,6EAA6E;gBAAAC,QAAA,EAAC;cAExG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACR1F,OAAA;YAAOqF,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAC/CV,YAAY,CAACoB,GAAG,CAAC,CAACpD,IAAI,EAAEqD,KAAK,kBAC1BjG,OAAA;cAAuBqF,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/CtF,OAAA;gBAAIqF,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,EAC5DX,gBAAgB,GAAGsB,KAAK,GAAG;cAAC;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC,eACL1F,OAAA;gBAAIqF,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,eACvCtF,OAAA;kBAAKqF,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAE1C,IAAI,CAACjB;gBAAQ;kBAAA4D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxE,CAAC,eACL1F,OAAA;gBAAIqF,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,eACvCtF,OAAA;kBAAKqF,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAE1C,IAAI,CAAChB;gBAAS;kBAAA2D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7D,CAAC,eACL1F,OAAA;gBAAIqF,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,eACvCtF,OAAA;kBAAKqF,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAE1C,IAAI,CAACf;gBAAK;kBAAA0D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD,CAAC,eACL1F,OAAA;gBAAIqF,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,eACvCtF,OAAA;kBAAKqF,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAE1C,IAAI,CAACd;gBAAa;kBAAAyD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjE,CAAC,eACL1F,OAAA;gBAAIqF,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,eACvCtF,OAAA;kBAAKqF,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,GAAE1C,IAAI,CAACb,OAAO,EAAC,WAAS;gBAAA;kBAAAwD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChF,CAAC,eACL1F,OAAA;gBAAIqF,SAAS,EAAC,iDAAiD;gBAAAC,QAAA,eAC3DtF,OAAA;kBACIkG,OAAO,EAAEA,CAAA,KAAMtC,UAAU,CAAChB,IAAI,CAAE;kBAChCyC,SAAS,EAAC,qDAAqD;kBAAAC,QAAA,gBAE/DtF,OAAA,CAACH,MAAM;oBAACwF,SAAS,EAAC;kBAAM;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,SAC/B;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA,GA1BA9C,IAAI,CAACiB,OAAO;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA2BjB,CACP;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC,EAGLZ,UAAU,GAAG,CAAC,iBACX9E,OAAA;QAAKqF,SAAS,EAAC,mFAAmF;QAAAC,QAAA,eAC9FtF,OAAA;UAAKqF,SAAS,EAAC,6DAA6D;UAAAC,QAAA,gBACxEtF,OAAA;YAAAsF,QAAA,eACItF,OAAA;cAAGqF,SAAS,EAAC,uBAAuB;cAAAC,QAAA,GAAC,UACzB,eAAAtF,OAAA;gBAAMqF,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAEX,gBAAgB,GAAG;cAAC;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,OAAG,EAAC,GAAG,eAC1E1F,OAAA;gBAAMqF,SAAS,EAAC,aAAa;gBAAAC,QAAA,EACxBZ,eAAe,GAAGH,aAAa,CAAC7B,MAAM,GAAG6B,aAAa,CAAC7B,MAAM,GAAGgC;cAAe;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9E,CAAC,EAAC,GAAG,EAAC,KACT,eAAA1F,OAAA;gBAAMqF,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAEf,aAAa,CAAC7B;cAAM;gBAAA6C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,YAClE;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN1F,OAAA;YAAAsF,QAAA,eACItF,OAAA;cAAKqF,SAAS,EAAC,2DAA2D;cAAC,cAAW,YAAY;cAAAC,QAAA,gBAC9FtF,OAAA;gBACIkG,OAAO,EAAEA,CAAA,KAAMlB,QAAQ,CAAChE,WAAW,GAAG,CAAC,GAAGA,WAAW,GAAG,CAAC,GAAG,CAAC,CAAE;gBAC/DmF,QAAQ,EAAEnF,WAAW,KAAK,CAAE;gBAC5BqE,SAAS,EAAE,gHACPrE,WAAW,KAAK,CAAC,GAAG,kCAAkC,GAAG,gCAAgC,EAC1F;gBAAAsE,QAAA,gBAEHtF,OAAA;kBAAMqF,SAAS,EAAC,SAAS;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACzC1F,OAAA;kBAAKqF,SAAS,EAAC,SAAS;kBAACe,KAAK,EAAC,4BAA4B;kBAACC,OAAO,EAAC,WAAW;kBAACC,IAAI,EAAC,cAAc;kBAAC,eAAY,MAAM;kBAAAhB,QAAA,eAClHtF,OAAA;oBAAMuG,QAAQ,EAAC,SAAS;oBAACC,CAAC,EAAC,mHAAmH;oBAACC,QAAQ,EAAC;kBAAS;oBAAAlB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,EAERR,WAAW,CAACc,GAAG,CAACU,MAAM,iBACnB1G,OAAA;gBAEIkG,OAAO,EAAEA,CAAA,KAAMlB,QAAQ,CAAC0B,MAAM,CAAE;gBAChCrB,SAAS,EAAE,mGACPrE,WAAW,KAAK0F,MAAM,GAChB,+CAA+C,GAC/C,gCAAgC,EACvC;gBAAApB,QAAA,EAEFoB;cAAM,GARFA,MAAM;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OASP,CACX,CAAC,eAEF1F,OAAA;gBACIkG,OAAO,EAAEA,CAAA,KAAMlB,QAAQ,CAAChE,WAAW,GAAG8D,UAAU,GAAG9D,WAAW,GAAG,CAAC,GAAG8D,UAAU,CAAE;gBACjFqB,QAAQ,EAAEnF,WAAW,KAAK8D,UAAW;gBACrCO,SAAS,EAAE,gHACPrE,WAAW,KAAK8D,UAAU,GAAG,kCAAkC,GAAG,gCAAgC,EACnG;gBAAAQ,QAAA,gBAEHtF,OAAA;kBAAMqF,SAAS,EAAC,SAAS;kBAAAC,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACrC1F,OAAA;kBAAKqF,SAAS,EAAC,SAAS;kBAACe,KAAK,EAAC,4BAA4B;kBAACC,OAAO,EAAC,WAAW;kBAACC,IAAI,EAAC,cAAc;kBAAC,eAAY,MAAM;kBAAAhB,QAAA,eAClHtF,OAAA;oBAAMuG,QAAQ,EAAC,SAAS;oBAACC,CAAC,EAAC,oHAAoH;oBAACC,QAAQ,EAAC;kBAAS;oBAAAlB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CACR;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,EAGL9E,aAAa,iBACVZ,OAAA;MAAKqF,SAAS,EAAC,gFAAgF;MAAAC,QAAA,eAC3FtF,OAAA;QAAKqF,SAAS,EAAC,+CAA+C;QAAAC,QAAA,gBAC1DtF,OAAA;UAAKqF,SAAS,EAAC,gDAAgD;UAAAC,QAAA,gBAC3DtF,OAAA;YAAIqF,SAAS,EAAC,qCAAqC;YAAAC,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClE1F,OAAA;YACIqF,SAAS,EAAC,+DAA+D;YACzEa,OAAO,EAAEA,CAAA,KAAMrF,gBAAgB,CAAC,IAAI,CAAE;YAAAyE,QAAA,EACzC;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eAEN1F,OAAA;UAAM2G,QAAQ,EAAE7C,YAAa;UAACuB,SAAS,EAAC,KAAK;UAAAC,QAAA,gBACzCtF,OAAA;YAAKqF,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACtBtF,OAAA;cAAAsF,QAAA,gBACItF,OAAA;gBAAOqF,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAChF1F,OAAA;gBACI4F,IAAI,EAAC,MAAM;gBACXnC,IAAI,EAAC,UAAU;gBACfU,KAAK,EAAE1C,WAAW,CAACE,QAAS;gBAC5BmE,QAAQ,EAAE5B,iBAAkB;gBAC5BmB,SAAS,EAAC,4IAA4I;gBACtJuB,QAAQ;cAAA;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAEN1F,OAAA;cAAAsF,QAAA,gBACItF,OAAA;gBAAOqF,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACjF1F,OAAA;gBACI4F,IAAI,EAAC,MAAM;gBACXnC,IAAI,EAAC,WAAW;gBAChBU,KAAK,EAAE1C,WAAW,CAACG,SAAU;gBAC7BkE,QAAQ,EAAE5B,iBAAkB;gBAC5BmB,SAAS,EAAC,4IAA4I;gBACtJuB,QAAQ;cAAA;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAEN1F,OAAA;cAAAsF,QAAA,gBACItF,OAAA;gBAAOqF,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC7E1F,OAAA;gBACI4F,IAAI,EAAC,OAAO;gBACZnC,IAAI,EAAC,OAAO;gBACZU,KAAK,EAAE1C,WAAW,CAACI,KAAM;gBACzBiE,QAAQ,EAAE5B,iBAAkB;gBAC5BmB,SAAS,EAAC,4IAA4I;gBACtJuB,QAAQ;cAAA;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAEN1F,OAAA;cAAAsF,QAAA,gBACItF,OAAA;gBAAOqF,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACrF1F,OAAA;gBACIyD,IAAI,EAAC,eAAe;gBACpBU,KAAK,EAAE1C,WAAW,CAACK,aAAc;gBACjCgE,QAAQ,EAAE5B,iBAAkB;gBAC5BmB,SAAS,EAAC,4IAA4I;gBACtJuB,QAAQ;gBAAAtB,QAAA,gBAERtF,OAAA;kBAAQmE,KAAK,EAAC,EAAE;kBAAAmB,QAAA,EAAC;gBAAoB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EAC7CpF,KAAK,CAAC0F,GAAG,CAACzC,IAAI,iBACXvD,OAAA;kBAAsBmE,KAAK,EAAEZ,IAAI,CAACE,IAAK;kBAAA6B,QAAA,EAAE/B,IAAI,CAACE;gBAAI,GAArCF,IAAI,CAACsD,EAAE;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAuC,CAC9D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,eAEN1F,OAAA;cAAAsF,QAAA,gBACItF,OAAA;gBAAOqF,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAkB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1F1F,OAAA;gBACI4F,IAAI,EAAC,QAAQ;gBACbnC,IAAI,EAAC,SAAS;gBACdU,KAAK,EAAE1C,WAAW,CAACM,OAAQ;gBAC3B+D,QAAQ,EAAE5B,iBAAkB;gBAC5BmB,SAAS,EAAC,4IAA4I;gBACtJuB,QAAQ;cAAA;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAEN1F,OAAA;YAAKqF,SAAS,EAAC,iCAAiC;YAAAC,QAAA,gBAC5CtF,OAAA;cACI4F,IAAI,EAAC,QAAQ;cACbM,OAAO,EAAEA,CAAA,KAAMrF,gBAAgB,CAAC,IAAI,CAAE;cACtCwE,SAAS,EAAC,2LAA2L;cAAAC,QAAA,EACxM;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT1F,OAAA;cACI4F,IAAI,EAAC,QAAQ;cACbP,SAAS,EAAC,+LAA+L;cAAAC,QAAA,EAC5M;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACR;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd;AAACvF,EAAA,CA1bQD,cAAc;AAAA4G,EAAA,GAAd5G,cAAc;AA2bvB,eAAeA,cAAc;AAAC,IAAA4G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}