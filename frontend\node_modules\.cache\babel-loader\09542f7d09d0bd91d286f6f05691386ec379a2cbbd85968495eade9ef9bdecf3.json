{"ast": null, "code": "var _jsxFileName = \"C:\\\\MAMP\\\\htdocs\\\\FanBet247\\\\frontend\\\\src\\\\components\\\\Admin\\\\Admin2FAVerification.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport axios from 'axios';\nimport { FaShieldAlt, Fa<PERSON>ey, FaSpinner, FaExclamationTriangle, FaArrowLeft, FaQuestionCircle } from 'react-icons/fa';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst API_BASE_URL = '/backend';\nconst Admin2FAVerification = ({\n  adminId,\n  username,\n  onSuccess,\n  onBack\n}) => {\n  _s();\n  const [verificationCode, setVerificationCode] = useState('');\n  const [backupCode, setBackupCode] = useState('');\n  const [useBackupCode, setUseBackupCode] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  const verify2FA = async () => {\n    const code = useBackupCode ? backupCode : verificationCode;\n    if (!code || (useBackupCode ? code.length !== 8 : code.length !== 6)) {\n      setError(useBackupCode ? 'Please enter a valid 8-character backup code' : 'Please enter a valid 6-digit code');\n      return;\n    }\n    try {\n      setLoading(true);\n      setError('');\n      const response = await axios.post(`${API_BASE_URL}/handlers/admin_verify_2fa.php`, {\n        admin_id: adminId,\n        code: code,\n        is_backup_code: useBackupCode\n      });\n      if (response.data.success) {\n        setSuccess('2FA verification successful!');\n\n        // Show warning if backup codes are running low\n        if (response.data.backup_code_warning) {\n          setTimeout(() => {\n            alert(response.data.backup_code_warning);\n          }, 1000);\n        }\n        setTimeout(() => {\n          onSuccess({\n            admin_id: response.data.admin_id,\n            username: response.data.username,\n            role: response.data.role,\n            session_token: response.data.session_token,\n            auth_method: response.data.auth_method,\n            used_backup_code: response.data.used_backup_code,\n            remaining_backup_codes: response.data.remaining_backup_codes\n          });\n        }, 1000);\n      } else {\n        setError(response.data.message || 'Invalid verification code');\n        // Clear the input on error\n        if (useBackupCode) {\n          setBackupCode('');\n        } else {\n          setVerificationCode('');\n        }\n      }\n    } catch (err) {\n      setError('Failed to verify 2FA code. Please try again.');\n      console.error('2FA verification error:', err);\n      if (useBackupCode) {\n        setBackupCode('');\n      } else {\n        setVerificationCode('');\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleKeyPress = e => {\n    if (e.key === 'Enter') {\n      verify2FA();\n    }\n  };\n  const toggleBackupCode = () => {\n    setUseBackupCode(!useBackupCode);\n    setVerificationCode('');\n    setBackupCode('');\n    setError('');\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-md w-full space-y-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-green-100\",\n          children: /*#__PURE__*/_jsxDEV(FaShieldAlt, {\n            className: \"h-6 w-6 text-green-600\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 94,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"mt-6 text-center text-3xl font-extrabold text-gray-900\",\n          children: \"Two-Factor Authentication\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-2 text-center text-sm text-gray-600\",\n          children: useBackupCode ? 'Enter one of your backup codes' : 'Enter the code from your Google Authenticator app'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-center text-sm font-medium text-gray-900\",\n          children: username\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 92,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-8 space-y-6\",\n        children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-red-50 border border-red-200 rounded-md p-4 flex items-center gap-3\",\n          children: [/*#__PURE__*/_jsxDEV(FaExclamationTriangle, {\n            className: \"text-red-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 114,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-red-700 text-sm\",\n            children: error\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 25\n        }, this), success && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-green-50 border border-green-200 rounded-md p-4 flex items-center gap-3\",\n          children: [/*#__PURE__*/_jsxDEV(FaKey, {\n            className: \"text-green-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-green-700 text-sm\",\n            children: success\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 123,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: useBackupCode ? /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Backup Code\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              maxLength: \"8\",\n              value: backupCode,\n              onChange: e => setBackupCode(e.target.value.toUpperCase().replace(/[^A-Z0-9]/g, '')),\n              onKeyPress: handleKeyPress,\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500 text-center text-lg font-mono\",\n              placeholder: \"XXXXXXXX\",\n              disabled: loading\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs text-gray-500 mt-1\",\n              children: \"Enter one of your 8-character backup codes\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 29\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Authenticator Code\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              maxLength: \"6\",\n              value: verificationCode,\n              onChange: e => setVerificationCode(e.target.value.replace(/\\D/g, '')),\n              onKeyPress: handleKeyPress,\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500 text-center text-xl font-mono\",\n              placeholder: \"000000\",\n              disabled: loading\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs text-gray-500 mt-1\",\n              children: \"Enter the 6-digit code from Google Authenticator\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: verify2FA,\n            disabled: loading || (useBackupCode ? backupCode.length !== 8 : verificationCode.length !== 6),\n            className: \"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed\",\n            children: [loading && /*#__PURE__*/_jsxDEV(FaSpinner, {\n              className: \"animate-spin mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 41\n            }, this), loading ? 'Verifying...' : 'Verify Code']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: toggleBackupCode,\n            className: \"text-sm text-blue-600 hover:text-blue-500 flex items-center justify-center gap-2 mx-auto\",\n            disabled: loading,\n            children: [/*#__PURE__*/_jsxDEV(FaQuestionCircle, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 29\n            }, this), useBackupCode ? 'Use Authenticator App' : 'Use Backup Code']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-blue-50 border border-blue-200 rounded-md p-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-start gap-3\",\n            children: [/*#__PURE__*/_jsxDEV(FaQuestionCircle, {\n              className: \"text-blue-500 mt-0.5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm text-blue-700\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"font-medium mb-1\",\n                children: \"Need help?\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 199,\n                columnNumber: 33\n              }, this), useBackupCode ? /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Backup codes are 8-character codes you saved when setting up 2FA. Each code can only be used once.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 201,\n                columnNumber: 37\n              }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Open your Google Authenticator app and find the 6-digit code for FanBet247 Admin. The code changes every 30 seconds.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 206,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: onBack,\n            className: \"text-sm text-gray-600 hover:text-gray-500 flex items-center justify-center gap-2 mx-auto\",\n            disabled: loading,\n            children: [/*#__PURE__*/_jsxDEV(FaArrowLeft, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 29\n            }, this), \"Back to Login\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 91,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 90,\n    columnNumber: 9\n  }, this);\n};\n_s(Admin2FAVerification, \"bHNbUW6wTAsJStfuZDmvVCMqYag=\");\n_c = Admin2FAVerification;\nexport default Admin2FAVerification;\nvar _c;\n$RefreshReg$(_c, \"Admin2FAVerification\");", "map": {"version": 3, "names": ["React", "useState", "axios", "FaShieldAlt", "FaKey", "FaSpinner", "FaExclamationTriangle", "FaArrowLeft", "FaQuestionCircle", "jsxDEV", "_jsxDEV", "API_BASE_URL", "Admin2FAVerification", "adminId", "username", "onSuccess", "onBack", "_s", "verificationCode", "setVerificationCode", "backupCode", "setBackupCode", "useBackupCode", "setUseBackupCode", "loading", "setLoading", "error", "setError", "success", "setSuccess", "verify2FA", "code", "length", "response", "post", "admin_id", "is_backup_code", "data", "backup_code_warning", "setTimeout", "alert", "role", "session_token", "auth_method", "used_backup_code", "remaining_backup_codes", "message", "err", "console", "handleKeyPress", "e", "key", "toggleBackupCode", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "max<PERSON><PERSON><PERSON>", "value", "onChange", "target", "toUpperCase", "replace", "onKeyPress", "placeholder", "disabled", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/components/Admin/Admin2FAVerification.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport axios from 'axios';\nimport { FaS<PERSON>eld<PERSON>lt, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, FaEx<PERSON><PERSON><PERSON>gle, FaArrowLeft, FaQuestionCircle } from 'react-icons/fa';\n\nconst API_BASE_URL = '/backend';\n\nconst Admin2FAVerification = ({ adminId, username, onSuccess, onBack }) => {\n    const [verificationCode, setVerificationCode] = useState('');\n    const [backupCode, setBackupCode] = useState('');\n    const [useBackupCode, setUseBackupCode] = useState(false);\n    const [loading, setLoading] = useState(false);\n    const [error, setError] = useState('');\n    const [success, setSuccess] = useState('');\n\n    const verify2FA = async () => {\n        const code = useBackupCode ? backupCode : verificationCode;\n        \n        if (!code || (useBackupCode ? code.length !== 8 : code.length !== 6)) {\n            setError(useBackupCode ? 'Please enter a valid 8-character backup code' : 'Please enter a valid 6-digit code');\n            return;\n        }\n\n        try {\n            setLoading(true);\n            setError('');\n\n            const response = await axios.post(`${API_BASE_URL}/handlers/admin_verify_2fa.php`, {\n                admin_id: adminId,\n                code: code,\n                is_backup_code: useBackupCode\n            });\n\n            if (response.data.success) {\n                setSuccess('2FA verification successful!');\n                \n                // Show warning if backup codes are running low\n                if (response.data.backup_code_warning) {\n                    setTimeout(() => {\n                        alert(response.data.backup_code_warning);\n                    }, 1000);\n                }\n                \n                setTimeout(() => {\n                    onSuccess({\n                        admin_id: response.data.admin_id,\n                        username: response.data.username,\n                        role: response.data.role,\n                        session_token: response.data.session_token,\n                        auth_method: response.data.auth_method,\n                        used_backup_code: response.data.used_backup_code,\n                        remaining_backup_codes: response.data.remaining_backup_codes\n                    });\n                }, 1000);\n            } else {\n                setError(response.data.message || 'Invalid verification code');\n                // Clear the input on error\n                if (useBackupCode) {\n                    setBackupCode('');\n                } else {\n                    setVerificationCode('');\n                }\n            }\n        } catch (err) {\n            setError('Failed to verify 2FA code. Please try again.');\n            console.error('2FA verification error:', err);\n            if (useBackupCode) {\n                setBackupCode('');\n            } else {\n                setVerificationCode('');\n            }\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const handleKeyPress = (e) => {\n        if (e.key === 'Enter') {\n            verify2FA();\n        }\n    };\n\n    const toggleBackupCode = () => {\n        setUseBackupCode(!useBackupCode);\n        setVerificationCode('');\n        setBackupCode('');\n        setError('');\n    };\n\n    return (\n        <div className=\"min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8\">\n            <div className=\"max-w-md w-full space-y-8\">\n                <div>\n                    <div className=\"mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-green-100\">\n                        <FaShieldAlt className=\"h-6 w-6 text-green-600\" />\n                    </div>\n                    <h2 className=\"mt-6 text-center text-3xl font-extrabold text-gray-900\">\n                        Two-Factor Authentication\n                    </h2>\n                    <p className=\"mt-2 text-center text-sm text-gray-600\">\n                        {useBackupCode \n                            ? 'Enter one of your backup codes'\n                            : 'Enter the code from your Google Authenticator app'\n                        }\n                    </p>\n                    <p className=\"text-center text-sm font-medium text-gray-900\">\n                        {username}\n                    </p>\n                </div>\n\n                <div className=\"mt-8 space-y-6\">\n                    {/* Error Message */}\n                    {error && (\n                        <div className=\"bg-red-50 border border-red-200 rounded-md p-4 flex items-center gap-3\">\n                            <FaExclamationTriangle className=\"text-red-500\" />\n                            <span className=\"text-red-700 text-sm\">{error}</span>\n                        </div>\n                    )}\n\n                    {/* Success Message */}\n                    {success && (\n                        <div className=\"bg-green-50 border border-green-200 rounded-md p-4 flex items-center gap-3\">\n                            <FaKey className=\"text-green-500\" />\n                            <span className=\"text-green-700 text-sm\">{success}</span>\n                        </div>\n                    )}\n\n                    {/* Verification Input */}\n                    <div>\n                        {useBackupCode ? (\n                            <div>\n                                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                                    Backup Code\n                                </label>\n                                <input\n                                    type=\"text\"\n                                    maxLength=\"8\"\n                                    value={backupCode}\n                                    onChange={(e) => setBackupCode(e.target.value.toUpperCase().replace(/[^A-Z0-9]/g, ''))}\n                                    onKeyPress={handleKeyPress}\n                                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500 text-center text-lg font-mono\"\n                                    placeholder=\"XXXXXXXX\"\n                                    disabled={loading}\n                                />\n                                <p className=\"text-xs text-gray-500 mt-1\">\n                                    Enter one of your 8-character backup codes\n                                </p>\n                            </div>\n                        ) : (\n                            <div>\n                                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                                    Authenticator Code\n                                </label>\n                                <input\n                                    type=\"text\"\n                                    maxLength=\"6\"\n                                    value={verificationCode}\n                                    onChange={(e) => setVerificationCode(e.target.value.replace(/\\D/g, ''))}\n                                    onKeyPress={handleKeyPress}\n                                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500 text-center text-xl font-mono\"\n                                    placeholder=\"000000\"\n                                    disabled={loading}\n                                />\n                                <p className=\"text-xs text-gray-500 mt-1\">\n                                    Enter the 6-digit code from Google Authenticator\n                                </p>\n                            </div>\n                        )}\n                    </div>\n\n                    {/* Verify Button */}\n                    <div>\n                        <button\n                            onClick={verify2FA}\n                            disabled={loading || (useBackupCode ? backupCode.length !== 8 : verificationCode.length !== 6)}\n                            className=\"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed\"\n                        >\n                            {loading && <FaSpinner className=\"animate-spin mr-2\" />}\n                            {loading ? 'Verifying...' : 'Verify Code'}\n                        </button>\n                    </div>\n\n                    {/* Toggle Backup Code */}\n                    <div className=\"text-center\">\n                        <button\n                            onClick={toggleBackupCode}\n                            className=\"text-sm text-blue-600 hover:text-blue-500 flex items-center justify-center gap-2 mx-auto\"\n                            disabled={loading}\n                        >\n                            <FaQuestionCircle />\n                            {useBackupCode ? 'Use Authenticator App' : 'Use Backup Code'}\n                        </button>\n                    </div>\n\n                    {/* Help Text */}\n                    <div className=\"bg-blue-50 border border-blue-200 rounded-md p-4\">\n                        <div className=\"flex items-start gap-3\">\n                            <FaQuestionCircle className=\"text-blue-500 mt-0.5\" />\n                            <div className=\"text-sm text-blue-700\">\n                                <p className=\"font-medium mb-1\">Need help?</p>\n                                {useBackupCode ? (\n                                    <p>\n                                        Backup codes are 8-character codes you saved when setting up 2FA. \n                                        Each code can only be used once.\n                                    </p>\n                                ) : (\n                                    <p>\n                                        Open your Google Authenticator app and find the 6-digit code for FanBet247 Admin. \n                                        The code changes every 30 seconds.\n                                    </p>\n                                )}\n                            </div>\n                        </div>\n                    </div>\n\n                    {/* Back Button */}\n                    <div className=\"text-center\">\n                        <button\n                            onClick={onBack}\n                            className=\"text-sm text-gray-600 hover:text-gray-500 flex items-center justify-center gap-2 mx-auto\"\n                            disabled={loading}\n                        >\n                            <FaArrowLeft />\n                            Back to Login\n                        </button>\n                    </div>\n                </div>\n            </div>\n        </div>\n    );\n};\n\nexport default Admin2FAVerification;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,EAAEC,KAAK,EAAEC,SAAS,EAAEC,qBAAqB,EAAEC,WAAW,EAAEC,gBAAgB,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErH,MAAMC,YAAY,GAAG,UAAU;AAE/B,MAAMC,oBAAoB,GAAGA,CAAC;EAAEC,OAAO;EAAEC,QAAQ;EAAEC,SAAS;EAAEC;AAAO,CAAC,KAAK;EAAAC,EAAA;EACvE,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACmB,UAAU,EAAEC,aAAa,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACqB,aAAa,EAAEC,gBAAgB,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACuB,OAAO,EAAEC,UAAU,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACyB,KAAK,EAAEC,QAAQ,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC2B,OAAO,EAAEC,UAAU,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EAE1C,MAAM6B,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC1B,MAAMC,IAAI,GAAGT,aAAa,GAAGF,UAAU,GAAGF,gBAAgB;IAE1D,IAAI,CAACa,IAAI,KAAKT,aAAa,GAAGS,IAAI,CAACC,MAAM,KAAK,CAAC,GAAGD,IAAI,CAACC,MAAM,KAAK,CAAC,CAAC,EAAE;MAClEL,QAAQ,CAACL,aAAa,GAAG,8CAA8C,GAAG,mCAAmC,CAAC;MAC9G;IACJ;IAEA,IAAI;MACAG,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,EAAE,CAAC;MAEZ,MAAMM,QAAQ,GAAG,MAAM/B,KAAK,CAACgC,IAAI,CAAC,GAAGvB,YAAY,gCAAgC,EAAE;QAC/EwB,QAAQ,EAAEtB,OAAO;QACjBkB,IAAI,EAAEA,IAAI;QACVK,cAAc,EAAEd;MACpB,CAAC,CAAC;MAEF,IAAIW,QAAQ,CAACI,IAAI,CAACT,OAAO,EAAE;QACvBC,UAAU,CAAC,8BAA8B,CAAC;;QAE1C;QACA,IAAII,QAAQ,CAACI,IAAI,CAACC,mBAAmB,EAAE;UACnCC,UAAU,CAAC,MAAM;YACbC,KAAK,CAACP,QAAQ,CAACI,IAAI,CAACC,mBAAmB,CAAC;UAC5C,CAAC,EAAE,IAAI,CAAC;QACZ;QAEAC,UAAU,CAAC,MAAM;UACbxB,SAAS,CAAC;YACNoB,QAAQ,EAAEF,QAAQ,CAACI,IAAI,CAACF,QAAQ;YAChCrB,QAAQ,EAAEmB,QAAQ,CAACI,IAAI,CAACvB,QAAQ;YAChC2B,IAAI,EAAER,QAAQ,CAACI,IAAI,CAACI,IAAI;YACxBC,aAAa,EAAET,QAAQ,CAACI,IAAI,CAACK,aAAa;YAC1CC,WAAW,EAAEV,QAAQ,CAACI,IAAI,CAACM,WAAW;YACtCC,gBAAgB,EAAEX,QAAQ,CAACI,IAAI,CAACO,gBAAgB;YAChDC,sBAAsB,EAAEZ,QAAQ,CAACI,IAAI,CAACQ;UAC1C,CAAC,CAAC;QACN,CAAC,EAAE,IAAI,CAAC;MACZ,CAAC,MAAM;QACHlB,QAAQ,CAACM,QAAQ,CAACI,IAAI,CAACS,OAAO,IAAI,2BAA2B,CAAC;QAC9D;QACA,IAAIxB,aAAa,EAAE;UACfD,aAAa,CAAC,EAAE,CAAC;QACrB,CAAC,MAAM;UACHF,mBAAmB,CAAC,EAAE,CAAC;QAC3B;MACJ;IACJ,CAAC,CAAC,OAAO4B,GAAG,EAAE;MACVpB,QAAQ,CAAC,8CAA8C,CAAC;MACxDqB,OAAO,CAACtB,KAAK,CAAC,yBAAyB,EAAEqB,GAAG,CAAC;MAC7C,IAAIzB,aAAa,EAAE;QACfD,aAAa,CAAC,EAAE,CAAC;MACrB,CAAC,MAAM;QACHF,mBAAmB,CAAC,EAAE,CAAC;MAC3B;IACJ,CAAC,SAAS;MACNM,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,MAAMwB,cAAc,GAAIC,CAAC,IAAK;IAC1B,IAAIA,CAAC,CAACC,GAAG,KAAK,OAAO,EAAE;MACnBrB,SAAS,CAAC,CAAC;IACf;EACJ,CAAC;EAED,MAAMsB,gBAAgB,GAAGA,CAAA,KAAM;IAC3B7B,gBAAgB,CAAC,CAACD,aAAa,CAAC;IAChCH,mBAAmB,CAAC,EAAE,CAAC;IACvBE,aAAa,CAAC,EAAE,CAAC;IACjBM,QAAQ,CAAC,EAAE,CAAC;EAChB,CAAC;EAED,oBACIjB,OAAA;IAAK2C,SAAS,EAAC,qFAAqF;IAAAC,QAAA,eAChG5C,OAAA;MAAK2C,SAAS,EAAC,2BAA2B;MAAAC,QAAA,gBACtC5C,OAAA;QAAA4C,QAAA,gBACI5C,OAAA;UAAK2C,SAAS,EAAC,8EAA8E;UAAAC,QAAA,eACzF5C,OAAA,CAACP,WAAW;YAACkD,SAAS,EAAC;UAAwB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD,CAAC,eACNhD,OAAA;UAAI2C,SAAS,EAAC,wDAAwD;UAAAC,QAAA,EAAC;QAEvE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLhD,OAAA;UAAG2C,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EAChDhC,aAAa,GACR,gCAAgC,GAChC;QAAmD;UAAAiC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAE1D,CAAC,eACJhD,OAAA;UAAG2C,SAAS,EAAC,+CAA+C;UAAAC,QAAA,EACvDxC;QAAQ;UAAAyC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENhD,OAAA;QAAK2C,SAAS,EAAC,gBAAgB;QAAAC,QAAA,GAE1B5B,KAAK,iBACFhB,OAAA;UAAK2C,SAAS,EAAC,wEAAwE;UAAAC,QAAA,gBACnF5C,OAAA,CAACJ,qBAAqB;YAAC+C,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAClDhD,OAAA;YAAM2C,SAAS,EAAC,sBAAsB;YAAAC,QAAA,EAAE5B;UAAK;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpD,CACR,EAGA9B,OAAO,iBACJlB,OAAA;UAAK2C,SAAS,EAAC,4EAA4E;UAAAC,QAAA,gBACvF5C,OAAA,CAACN,KAAK;YAACiD,SAAS,EAAC;UAAgB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACpChD,OAAA;YAAM2C,SAAS,EAAC,wBAAwB;YAAAC,QAAA,EAAE1B;UAAO;YAAA2B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxD,CACR,eAGDhD,OAAA;UAAA4C,QAAA,EACKhC,aAAa,gBACVZ,OAAA;YAAA4C,QAAA,gBACI5C,OAAA;cAAO2C,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRhD,OAAA;cACIiD,IAAI,EAAC,MAAM;cACXC,SAAS,EAAC,GAAG;cACbC,KAAK,EAAEzC,UAAW;cAClB0C,QAAQ,EAAGZ,CAAC,IAAK7B,aAAa,CAAC6B,CAAC,CAACa,MAAM,CAACF,KAAK,CAACG,WAAW,CAAC,CAAC,CAACC,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,CAAE;cACvFC,UAAU,EAAEjB,cAAe;cAC3BI,SAAS,EAAC,2IAA2I;cACrJc,WAAW,EAAC,UAAU;cACtBC,QAAQ,EAAE5C;YAAQ;cAAA+B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CAAC,eACFhD,OAAA;cAAG2C,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAE1C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,gBAENhD,OAAA;YAAA4C,QAAA,gBACI5C,OAAA;cAAO2C,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRhD,OAAA;cACIiD,IAAI,EAAC,MAAM;cACXC,SAAS,EAAC,GAAG;cACbC,KAAK,EAAE3C,gBAAiB;cACxB4C,QAAQ,EAAGZ,CAAC,IAAK/B,mBAAmB,CAAC+B,CAAC,CAACa,MAAM,CAACF,KAAK,CAACI,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAE;cACxEC,UAAU,EAAEjB,cAAe;cAC3BI,SAAS,EAAC,2IAA2I;cACrJc,WAAW,EAAC,QAAQ;cACpBC,QAAQ,EAAE5C;YAAQ;cAAA+B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CAAC,eACFhD,OAAA;cAAG2C,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAE1C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QACR;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAGNhD,OAAA;UAAA4C,QAAA,eACI5C,OAAA;YACI2D,OAAO,EAAEvC,SAAU;YACnBsC,QAAQ,EAAE5C,OAAO,KAAKF,aAAa,GAAGF,UAAU,CAACY,MAAM,KAAK,CAAC,GAAGd,gBAAgB,CAACc,MAAM,KAAK,CAAC,CAAE;YAC/FqB,SAAS,EAAC,kRAAkR;YAAAC,QAAA,GAE3R9B,OAAO,iBAAId,OAAA,CAACL,SAAS;cAACgD,SAAS,EAAC;YAAmB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EACtDlC,OAAO,GAAG,cAAc,GAAG,aAAa;UAAA;YAAA+B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eAGNhD,OAAA;UAAK2C,SAAS,EAAC,aAAa;UAAAC,QAAA,eACxB5C,OAAA;YACI2D,OAAO,EAAEjB,gBAAiB;YAC1BC,SAAS,EAAC,0FAA0F;YACpGe,QAAQ,EAAE5C,OAAQ;YAAA8B,QAAA,gBAElB5C,OAAA,CAACF,gBAAgB;cAAA+C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EACnBpC,aAAa,GAAG,uBAAuB,GAAG,iBAAiB;UAAA;YAAAiC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eAGNhD,OAAA;UAAK2C,SAAS,EAAC,kDAAkD;UAAAC,QAAA,eAC7D5C,OAAA;YAAK2C,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBACnC5C,OAAA,CAACF,gBAAgB;cAAC6C,SAAS,EAAC;YAAsB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACrDhD,OAAA;cAAK2C,SAAS,EAAC,uBAAuB;cAAAC,QAAA,gBAClC5C,OAAA;gBAAG2C,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,EAC7CpC,aAAa,gBACVZ,OAAA;gBAAA4C,QAAA,EAAG;cAGH;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,gBAEJhD,OAAA;gBAAA4C,QAAA,EAAG;cAGH;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAGNhD,OAAA;UAAK2C,SAAS,EAAC,aAAa;UAAAC,QAAA,eACxB5C,OAAA;YACI2D,OAAO,EAAErD,MAAO;YAChBqC,SAAS,EAAC,0FAA0F;YACpGe,QAAQ,EAAE5C,OAAQ;YAAA8B,QAAA,gBAElB5C,OAAA,CAACH,WAAW;cAAAgD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,iBAEnB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAACzC,EAAA,CA/NIL,oBAAoB;AAAA0D,EAAA,GAApB1D,oBAAoB;AAiO1B,eAAeA,oBAAoB;AAAC,IAAA0D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}