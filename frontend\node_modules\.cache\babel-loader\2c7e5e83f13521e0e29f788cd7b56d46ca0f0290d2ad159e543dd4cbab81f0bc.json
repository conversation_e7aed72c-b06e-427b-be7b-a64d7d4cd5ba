{"ast": null, "code": "var _jsxFileName = \"C:\\\\MAMP\\\\htdocs\\\\FanBet247\\\\frontend\\\\src\\\\pages\\\\ChallengeManagement.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport Countdown from 'react-countdown';\nimport { FaEye, FaEdit, FaTrash, FaChevronLeft, FaChevronRight } from 'react-icons/fa';\nimport './ChallengeManagement.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst API_BASE_URL = '/backend';\nfunction ChallengeManagement() {\n  _s();\n  const [challenges, setChallenges] = useState([]);\n  const [teams, setTeams] = useState([]);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  const [showEditModal, setShowEditModal] = useState(false);\n  const [showPreviewModal, setShowPreviewModal] = useState(false);\n  const [selectedChallenge, setSelectedChallenge] = useState(null);\n  const [currentPage, setCurrentPage] = useState(1);\n  const [editingChallenge, setEditingChallenge] = useState({\n    team_a: '',\n    team_b: '',\n    odds_team_a: 1.80,\n    odds_team_b: 1.80,\n    start_time: '',\n    end_time: '',\n    match_date: '',\n    status: 'Open'\n  });\n  const [showDeleteModal, setShowDeleteModal] = useState(false);\n  const [challengeToDelete, setChallengeToDelete] = useState(null);\n  const ITEMS_PER_PAGE = 10;\n  useEffect(() => {\n    fetchChallenges();\n    fetchTeams();\n\n    // Set up more frequent challenge status check (every 5 seconds)\n    const statusCheckInterval = setInterval(() => {\n      checkChallengeStatus();\n    }, 5000); // Check every 5 seconds\n\n    return () => clearInterval(statusCheckInterval);\n  }, []);\n  const fetchTeams = async () => {\n    try {\n      const response = await axios.get(`${API_BASE_URL}/handlers/team_management.php`);\n      if (response.data.status === 200) {\n        setTeams(response.data.data);\n      }\n    } catch (err) {\n      console.error('Error fetching teams:', err);\n    }\n  };\n  const fetchChallenges = async () => {\n    try {\n      const response = await axios.get(`${API_BASE_URL}/handlers/challenge_management.php`);\n      if (response.data.success) {\n        setChallenges(response.data.challenges);\n      }\n    } catch (err) {\n      setError('Failed to fetch challenges');\n    }\n  };\n  const checkChallengeStatus = async () => {\n    try {\n      const response = await axios.get(`${API_BASE_URL}/handlers/check_challenge_status.php`);\n      if (response.data.remaining_expired > 0) {\n        console.log(`Found ${response.data.remaining_expired} expired challenges that need closing`);\n      }\n      if (response.data.affected_rows > 0) {\n        console.log(`Closed ${response.data.affected_rows} expired challenges`);\n        fetchChallenges(); // Refresh only if changes were made\n      }\n    } catch (err) {\n      console.error('Error checking challenge status:', err);\n    }\n  };\n  const getTeamLogo = teamName => {\n    const team = teams.find(team => team.name === teamName);\n    return team ? `${API_BASE_URL}/${team.logo}` : '';\n  };\n  const handleEdit = challenge => {\n    setEditingChallenge(challenge);\n    setShowEditModal(true);\n  };\n  const handleUpdate = async () => {\n    try {\n      const response = await axios.post(`${API_BASE_URL}/handlers/challenge_management.php`, editingChallenge);\n      if (response.data.success) {\n        setSuccess('Challenge updated successfully!');\n        fetchChallenges();\n        setShowEditModal(false);\n      } else {\n        setError(response.data.message || 'Failed to update challenge');\n      }\n    } catch (err) {\n      setError('Failed to update challenge');\n      console.error('Update error:', err);\n    }\n  };\n  const handleDeleteClick = challenge => {\n    setChallengeToDelete(challenge);\n    setShowDeleteModal(true);\n  };\n  const handleDeleteConfirm = async () => {\n    if (!challengeToDelete) return;\n    try {\n      const response = await axios.delete(`${API_BASE_URL}/handlers/challenge_management.php?challenge_id=${challengeToDelete.challenge_id}`);\n      if (response.data.success) {\n        setSuccess('Challenge deleted successfully!');\n        fetchChallenges();\n      } else {\n        setError(response.data.message || 'Failed to delete challenge');\n      }\n    } catch (err) {\n      setError('Failed to delete challenge');\n      console.error('Delete error:', err);\n    }\n    setShowDeleteModal(false);\n    setChallengeToDelete(null);\n  };\n  const isExpired = challenge => {\n    return new Date(challenge.end_time) < new Date() || challenge.status === 'Expired';\n  };\n  const totalPages = Math.ceil(challenges.length / ITEMS_PER_PAGE);\n  const indexOfLastItem = currentPage * ITEMS_PER_PAGE;\n  const indexOfFirstItem = indexOfLastItem - ITEMS_PER_PAGE;\n  const currentChallenges = challenges.slice(indexOfFirstItem, indexOfLastItem);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"challenge-management\",\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      children: \"Challenge Management\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 150,\n      columnNumber: 13\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"error-message\",\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 151,\n      columnNumber: 23\n    }, this), success && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"success-message\",\n      children: success\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 152,\n      columnNumber: 25\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"challenges-list\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"table-container\",\n        children: /*#__PURE__*/_jsxDEV(\"table\", {\n          className: \"challenge-management-table\",\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"#\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 159,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Matchup\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 160,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Time/Date\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 161,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Status\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 162,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                style: {\n                  width: '100px'\n                },\n                children: \"Actions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 163,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            children: currentChallenges.map((challenge, index) => /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"index-column\",\n                children: indexOfFirstItem + index + 1\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 169,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"matchup-grid\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"team-block\",\n                    children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                      src: getTeamLogo(challenge.team_a),\n                      alt: challenge.team_a,\n                      className: \"team-logo\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 173,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"team-name\",\n                      children: challenge.team_a\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 176,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"team-odds\",\n                      children: [\"Odds: \", challenge.odds_team_a]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 177,\n                      columnNumber: 49\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 172,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"vs-center\",\n                    children: \"VS\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 180,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"team-block\",\n                    children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                      src: getTeamLogo(challenge.team_b),\n                      alt: challenge.team_b,\n                      className: \"team-logo\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 183,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"team-name\",\n                      children: challenge.team_b\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 186,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"team-odds\",\n                      children: [\"Odds: \", challenge.odds_team_b]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 187,\n                      columnNumber: 49\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 182,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 171,\n                  columnNumber: 41\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 170,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"time-cell\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"match-time-display\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"match-time\",\n                    children: [\"Match: \", new Date(challenge.match_date).toLocaleString('en-US', {\n                      month: 'short',\n                      day: 'numeric',\n                      hour: 'numeric',\n                      minute: '2-digit',\n                      hour12: true\n                    })]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 193,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"end-time\",\n                    children: [\"Time Left: \", /*#__PURE__*/_jsxDEV(Countdown, {\n                      date: new Date(challenge.end_time),\n                      renderer: ({\n                        days,\n                        hours,\n                        minutes,\n                        seconds,\n                        completed\n                      }) => /*#__PURE__*/_jsxDEV(CountdownRenderer, {\n                        days: days,\n                        hours: hours,\n                        minutes: minutes,\n                        seconds: seconds,\n                        completed: completed,\n                        date: challenge.end_time\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 206,\n                        columnNumber: 57\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 203,\n                      columnNumber: 60\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 202,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 192,\n                  columnNumber: 41\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"status-cell\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `status-badge ${challenge.status}`,\n                  children: challenge.status\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 220,\n                  columnNumber: 41\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 219,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"action-buttons\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"icon-btn btn-preview\",\n                    title: \"Preview\",\n                    onClick: () => {\n                      setSelectedChallenge(challenge);\n                      setShowPreviewModal(true);\n                    },\n                    children: /*#__PURE__*/_jsxDEV(FaEye, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 233,\n                      columnNumber: 49\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 226,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"icon-btn btn-edit\",\n                    title: \"Edit\",\n                    onClick: () => handleEdit(challenge),\n                    children: /*#__PURE__*/_jsxDEV(FaEdit, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 239,\n                      columnNumber: 49\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 235,\n                    columnNumber: 45\n                  }, this), isExpired(challenge) && /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"icon-btn btn-delete\",\n                    title: \"Delete\",\n                    onClick: () => handleDeleteClick(challenge),\n                    children: /*#__PURE__*/_jsxDEV(FaTrash, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 246,\n                      columnNumber: 53\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 242,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 225,\n                  columnNumber: 41\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 224,\n                columnNumber: 37\n              }, this)]\n            }, challenge.challenge_id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 33\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 155,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"pagination\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"icon-btn pagination-btn\",\n          onClick: () => setCurrentPage(prev => Math.max(prev - 1, 1)),\n          disabled: currentPage === 1,\n          title: \"Previous Page\",\n          children: /*#__PURE__*/_jsxDEV(FaChevronLeft, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"page-info\",\n          children: [\"Page \", currentPage, \" of \", totalPages]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"icon-btn pagination-btn\",\n          onClick: () => setCurrentPage(prev => Math.min(prev + 1, totalPages)),\n          disabled: currentPage === totalPages,\n          title: \"Next Page\",\n          children: /*#__PURE__*/_jsxDEV(FaChevronRight, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 274,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 268,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 256,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 154,\n      columnNumber: 13\n    }, this), showPreviewModal && selectedChallenge && /*#__PURE__*/_jsxDEV(PreviewModal, {\n      challenge: selectedChallenge,\n      onClose: () => setShowPreviewModal(false),\n      teams: teams,\n      getTeamLogo: getTeamLogo\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 280,\n      columnNumber: 17\n    }, this), showEditModal && /*#__PURE__*/_jsxDEV(EditModal, {\n      challenge: editingChallenge,\n      onClose: () => setShowEditModal(false),\n      onUpdate: handleUpdate,\n      onChange: (field, value) => setEditingChallenge(prev => ({\n        ...prev,\n        [field]: value\n      }))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 289,\n      columnNumber: 17\n    }, this), showDeleteModal && challengeToDelete && /*#__PURE__*/_jsxDEV(DeleteConfirmationModal, {\n      challenge: challengeToDelete,\n      onConfirm: handleDeleteConfirm,\n      onCancel: () => {\n        setShowDeleteModal(false);\n        setChallengeToDelete(null);\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 301,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 149,\n    columnNumber: 9\n  }, this);\n}\n_s(ChallengeManagement, \"hj34NRQ+hqBxtQKWZ1KeOCABnas=\");\n_c = ChallengeManagement;\nconst CountdownRenderer = ({\n  days,\n  hours,\n  minutes,\n  seconds,\n  completed,\n  date\n}) => {\n  if (completed) {\n    return /*#__PURE__*/_jsxDEV(\"span\", {\n      className: \"countdown-expired\",\n      children: \"Expired\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 316,\n      columnNumber: 16\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"countdown-container\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"countdown-time\",\n      children: [days > 0 ? `${days}d ` : '', hours, \"h \", minutes, \"m \", seconds, \"s\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 321,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 320,\n    columnNumber: 9\n  }, this);\n};\n_c2 = CountdownRenderer;\nconst PreviewModal = ({\n  challenge,\n  onClose,\n  teams,\n  getTeamLogo\n}) => /*#__PURE__*/_jsxDEV(\"div\", {\n  className: \"modal-overlay\",\n  children: /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"preview-modal\",\n    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n      className: \"close-button\",\n      onClick: onClose,\n      children: \"\\xD7\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 331,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"preview-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Match Preview\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 333,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"match-type-section\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"match-type-badge\",\n          children: challenge.match_type === 'full_time' ? 'Full Time' : 'Half Time'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 335,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 334,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 332,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"match-preview-display\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"match-team-preview left-team\",\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          src: getTeamLogo(challenge.team_a),\n          alt: challenge.team_a,\n          className: \"team-logo-large\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 342,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"team-name\",\n          children: challenge.team_a\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 343,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"team-odds\",\n          children: [\"Win: \", challenge.odds_team_a]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 344,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 341,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"match-vs-preview\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"vs-text\",\n          children: \"VS\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 347,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"match-odds\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"draw-odds\",\n            children: [\"Draw: \", challenge.odds_draw || '0.8']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 349,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"lost-odds\",\n            children: [\"Lost: \", challenge.odds_lost || '0.2']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 350,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 348,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 346,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"match-team-preview right-team\",\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          src: getTeamLogo(challenge.team_b),\n          alt: challenge.team_b,\n          className: \"team-logo-large\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 354,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"team-name\",\n          children: challenge.team_b\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 355,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"team-odds\",\n          children: [\"Win: \", challenge.odds_team_b]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 356,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 353,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 340,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"match-details\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"detail-row\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"detail-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"label\",\n            children: \"Start Time\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 362,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"value\",\n            children: new Date(challenge.start_time).toLocaleString()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 363,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 361,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"detail-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"label\",\n            children: \"End Time\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 366,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"value\",\n            children: new Date(challenge.end_time).toLocaleString()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 367,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 365,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 360,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"detail-row\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"detail-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"label\",\n            children: \"Match Date\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 372,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"value\",\n            children: new Date(challenge.match_date).toLocaleString()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 373,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 371,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"detail-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"label\",\n            children: \"Status\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 376,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"value status-badge\",\n            children: challenge.status\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 377,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 375,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 370,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 359,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 330,\n    columnNumber: 9\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 329,\n  columnNumber: 5\n}, this);\n_c3 = PreviewModal;\nconst EditModal = ({\n  challenge,\n  onClose,\n  onUpdate,\n  onChange\n}) => /*#__PURE__*/_jsxDEV(\"div\", {\n  className: \"modal-overlay\",\n  children: /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"edit-modal\",\n    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n      className: \"close-button\",\n      onClick: onClose,\n      children: \"\\xD7\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 388,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n      children: \"Edit Challenge\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 389,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"form-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Match Type\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 392,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          value: challenge.match_type || 'full_time',\n          onChange: e => onChange('match_type', e.target.value),\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"full_time\",\n            children: \"Full Time\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 397,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"half_time\",\n            children: \"Half Time\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 398,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 393,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 391,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Odds Team A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 402,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"number\",\n          step: \"0.01\",\n          value: challenge.odds_team_a,\n          onChange: e => onChange('odds_team_a', e.target.value)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 403,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 401,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Odds Team B\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 411,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"number\",\n          step: \"0.01\",\n          value: challenge.odds_team_b,\n          onChange: e => onChange('odds_team_b', e.target.value)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 412,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 410,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Draw Odds\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 420,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"number\",\n          step: \"0.01\",\n          value: challenge.odds_draw || 0.8,\n          onChange: e => onChange('odds_draw', e.target.value)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 421,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 419,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Lost Odds\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 429,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"number\",\n          step: \"0.01\",\n          value: challenge.odds_lost || 0.2,\n          onChange: e => onChange('odds_lost', e.target.value)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 430,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 428,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Status\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 438,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          value: challenge.status,\n          onChange: e => onChange('status', e.target.value),\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"Open\",\n            children: \"Open\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 443,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"Closed\",\n            children: \"Closed\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 444,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"Settled\",\n            children: \"Settled\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 445,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 439,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 437,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Start Time\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 449,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"datetime-local\",\n          value: challenge.start_time.slice(0, 16),\n          onChange: e => onChange('start_time', e.target.value)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 450,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 448,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"End Time\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 457,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"datetime-local\",\n          value: challenge.end_time.slice(0, 16),\n          onChange: e => onChange('end_time', e.target.value)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 458,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 456,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Match Date\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 465,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"datetime-local\",\n          value: challenge.match_date.slice(0, 16),\n          onChange: e => onChange('match_date', e.target.value)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 466,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 464,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 390,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"form-actions\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"cancel-button\",\n        onClick: onClose,\n        children: \"Cancel\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 474,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"save-button\",\n        onClick: onUpdate,\n        children: \"Save Changes\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 475,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 473,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 387,\n    columnNumber: 9\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 386,\n  columnNumber: 5\n}, this);\n_c4 = EditModal;\nconst DeleteConfirmationModal = ({\n  challenge,\n  onConfirm,\n  onCancel\n}) => /*#__PURE__*/_jsxDEV(\"div\", {\n  className: \"modal-overlay\",\n  children: /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"delete-confirmation-modal\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      children: \"Delete Challenge\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 484,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Are you sure you want to delete this expired challenge?\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 486,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"challenge-preview\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"teams\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: challenge.team_a\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 489,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"vs\",\n            children: \"vs\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 490,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: challenge.team_b\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 491,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 488,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"match-date\",\n          children: [\"Match Date: \", new Date(challenge.match_date).toLocaleString()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 493,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 487,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 485,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-actions\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"cancel-button\",\n        onClick: onCancel,\n        children: \"Cancel\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 499,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"confirm-delete-button\",\n        onClick: onConfirm,\n        children: \"Delete Challenge\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 500,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 498,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 483,\n    columnNumber: 9\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 482,\n  columnNumber: 5\n}, this);\n_c5 = DeleteConfirmationModal;\nexport default ChallengeManagement;\nvar _c, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"ChallengeManagement\");\n$RefreshReg$(_c2, \"CountdownRenderer\");\n$RefreshReg$(_c3, \"PreviewModal\");\n$RefreshReg$(_c4, \"EditModal\");\n$RefreshReg$(_c5, \"DeleteConfirmationModal\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "Countdown", "FaEye", "FaEdit", "FaTrash", "FaChevronLeft", "FaChevronRight", "jsxDEV", "_jsxDEV", "API_BASE_URL", "ChallengeManagement", "_s", "challenges", "setChallenges", "teams", "setTeams", "error", "setError", "success", "setSuccess", "showEditModal", "setShowEditModal", "showPreviewModal", "setShowPreviewModal", "selected<PERSON>hall<PERSON><PERSON>", "setSelectedChallenge", "currentPage", "setCurrentPage", "editingChallenge", "setEditingChallenge", "team_a", "team_b", "odds_team_a", "odds_team_b", "start_time", "end_time", "match_date", "status", "showDeleteModal", "setShowDeleteModal", "challengeToDelete", "setChallengeToDelete", "ITEMS_PER_PAGE", "fetchChallenges", "fetchTeams", "statusCheckInterval", "setInterval", "checkChallengeStatus", "clearInterval", "response", "get", "data", "err", "console", "remaining_expired", "log", "affected_rows", "getTeamLogo", "teamName", "team", "find", "name", "logo", "handleEdit", "challenge", "handleUpdate", "post", "message", "handleDeleteClick", "handleDeleteConfirm", "delete", "challenge_id", "isExpired", "Date", "totalPages", "Math", "ceil", "length", "indexOfLastItem", "indexOfFirstItem", "currentChallenges", "slice", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "width", "map", "index", "src", "alt", "toLocaleString", "month", "day", "hour", "minute", "hour12", "date", "renderer", "days", "hours", "minutes", "seconds", "completed", "CountdownRenderer", "title", "onClick", "prev", "max", "disabled", "min", "PreviewModal", "onClose", "EditModal", "onUpdate", "onChange", "field", "value", "DeleteConfirmationModal", "onConfirm", "onCancel", "_c", "_c2", "match_type", "odds_draw", "odds_lost", "_c3", "e", "target", "type", "step", "_c4", "_c5", "$RefreshReg$"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/pages/ChallengeManagement.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport Countdown from 'react-countdown';\nimport { FaEye, FaEdit, FaTrash, FaChevronLeft, FaChevronRight } from 'react-icons/fa';\nimport './ChallengeManagement.css';\n\nconst API_BASE_URL = '/backend';\n\nfunction ChallengeManagement() {\n    const [challenges, setChallenges] = useState([]);\n    const [teams, setTeams] = useState([]);\n    const [error, setError] = useState('');\n    const [success, setSuccess] = useState('');\n    const [showEditModal, setShowEditModal] = useState(false);\n    const [showPreviewModal, setShowPreviewModal] = useState(false);\n    const [selectedChallenge, setSelectedChallenge] = useState(null);\n    const [currentPage, setCurrentPage] = useState(1);\n    const [editingChallenge, setEditingChallenge] = useState({\n        team_a: '',\n        team_b: '',\n        odds_team_a: 1.80,\n        odds_team_b: 1.80,\n        start_time: '',\n        end_time: '',\n        match_date: '',\n        status: 'Open'\n    });\n    const [showDeleteModal, setShowDeleteModal] = useState(false);\n    const [challengeToDelete, setChallengeToDelete] = useState(null);\n\n    const ITEMS_PER_PAGE = 10;\n\n    useEffect(() => {\n        fetchChallenges();\n        fetchTeams();\n\n        // Set up more frequent challenge status check (every 5 seconds)\n        const statusCheckInterval = setInterval(() => {\n            checkChallengeStatus();\n        }, 5000); // Check every 5 seconds\n\n        return () => clearInterval(statusCheckInterval);\n    }, []);\n\n    const fetchTeams = async () => {\n        try {\n            const response = await axios.get(`${API_BASE_URL}/handlers/team_management.php`);\n            if (response.data.status === 200) {\n                setTeams(response.data.data);\n            }\n        } catch (err) {\n            console.error('Error fetching teams:', err);\n        }\n    };\n\n    const fetchChallenges = async () => {\n        try {\n            const response = await axios.get(`${API_BASE_URL}/handlers/challenge_management.php`);\n            if (response.data.success) {\n                setChallenges(response.data.challenges);\n            }\n        } catch (err) {\n            setError('Failed to fetch challenges');\n        }\n    };\n\n    const checkChallengeStatus = async () => {\n        try {\n            const response = await axios.get(`${API_BASE_URL}/handlers/check_challenge_status.php`);\n            if (response.data.remaining_expired > 0) {\n                console.log(`Found ${response.data.remaining_expired} expired challenges that need closing`);\n            }\n            if (response.data.affected_rows > 0) {\n                console.log(`Closed ${response.data.affected_rows} expired challenges`);\n                fetchChallenges(); // Refresh only if changes were made\n            }\n        } catch (err) {\n            console.error('Error checking challenge status:', err);\n        }\n    };\n\n    const getTeamLogo = (teamName) => {\n        const team = teams.find(team => team.name === teamName);\n        return team ? `${API_BASE_URL}/${team.logo}` : '';\n    };\n\n    const handleEdit = (challenge) => {\n        setEditingChallenge(challenge);\n        setShowEditModal(true);\n    };\n\n    const handleUpdate = async () => {\n        try {\n            const response = await axios.post(\n                `${API_BASE_URL}/handlers/challenge_management.php`,\n                editingChallenge\n            );\n\n            if (response.data.success) {\n                setSuccess('Challenge updated successfully!');\n                fetchChallenges();\n                setShowEditModal(false);\n            } else {\n                setError(response.data.message || 'Failed to update challenge');\n            }\n        } catch (err) {\n            setError('Failed to update challenge');\n            console.error('Update error:', err);\n        }\n    };\n\n    const handleDeleteClick = (challenge) => {\n        setChallengeToDelete(challenge);\n        setShowDeleteModal(true);\n    };\n\n    const handleDeleteConfirm = async () => {\n        if (!challengeToDelete) return;\n\n        try {\n            const response = await axios.delete(\n                `${API_BASE_URL}/handlers/challenge_management.php?challenge_id=${challengeToDelete.challenge_id}`\n            );\n\n            if (response.data.success) {\n                setSuccess('Challenge deleted successfully!');\n                fetchChallenges();\n            } else {\n                setError(response.data.message || 'Failed to delete challenge');\n            }\n        } catch (err) {\n            setError('Failed to delete challenge');\n            console.error('Delete error:', err);\n        }\n        setShowDeleteModal(false);\n        setChallengeToDelete(null);\n    };\n\n    const isExpired = (challenge) => {\n        return new Date(challenge.end_time) < new Date() || challenge.status === 'Expired';\n    };\n\n    const totalPages = Math.ceil(challenges.length / ITEMS_PER_PAGE);\n    const indexOfLastItem = currentPage * ITEMS_PER_PAGE;\n    const indexOfFirstItem = indexOfLastItem - ITEMS_PER_PAGE;\n    const currentChallenges = challenges.slice(indexOfFirstItem, indexOfLastItem);\n\n    return (\n        <div className=\"challenge-management\">\n            <h1>Challenge Management</h1>\n            {error && <div className=\"error-message\">{error}</div>}\n            {success && <div className=\"success-message\">{success}</div>}\n\n            <div className=\"challenges-list\">\n                <div className=\"table-container\">\n                    <table className=\"challenge-management-table\">\n                        <thead>\n                            <tr>\n                                <th>#</th>\n                                <th>Matchup</th>\n                                <th>Time/Date</th>\n                                <th>Status</th>\n                                <th style={{ width: '100px' }}>Actions</th>\n                            </tr>\n                        </thead>\n                        <tbody>\n                            {currentChallenges.map((challenge, index) => (\n                                <tr key={challenge.challenge_id}>\n                                    <td className=\"index-column\">{indexOfFirstItem + index + 1}</td>\n                                    <td>\n                                        <div className=\"matchup-grid\">\n                                            <div className=\"team-block\">\n                                                <img src={getTeamLogo(challenge.team_a)}\n                                                     alt={challenge.team_a}\n                                                     className=\"team-logo\" />\n                                                <div className=\"team-name\">{challenge.team_a}</div>\n                                                <div className=\"team-odds\">Odds: {challenge.odds_team_a}</div>\n                                            </div>\n\n                                            <div className=\"vs-center\">VS</div>\n\n                                            <div className=\"team-block\">\n                                                <img src={getTeamLogo(challenge.team_b)}\n                                                     alt={challenge.team_b}\n                                                     className=\"team-logo\" />\n                                                <div className=\"team-name\">{challenge.team_b}</div>\n                                                <div className=\"team-odds\">Odds: {challenge.odds_team_b}</div>\n                                            </div>\n                                        </div>\n                                    </td>\n                                    <td className=\"time-cell\">\n                                        <div className=\"match-time-display\">\n                                            <div className=\"match-time\">\n                                                Match: {new Date(challenge.match_date).toLocaleString('en-US', {\n                                                    month: 'short',\n                                                    day: 'numeric',\n                                                    hour: 'numeric',\n                                                    minute: '2-digit',\n                                                    hour12: true\n                                                })}\n                                            </div>\n                                            <div className=\"end-time\">\n                                                Time Left: <Countdown\n                                                    date={new Date(challenge.end_time)}\n                                                    renderer={({ days, hours, minutes, seconds, completed }) =>\n                                                        <CountdownRenderer\n                                                            days={days}\n                                                            hours={hours}\n                                                            minutes={minutes}\n                                                            seconds={seconds}\n                                                            completed={completed}\n                                                            date={challenge.end_time}\n                                                        />\n                                                    }\n                                                />\n                                            </div>\n                                        </div>\n                                    </td>\n                                    <td className=\"status-cell\">\n                                        <span className={`status-badge ${challenge.status}`}>\n                                            {challenge.status}\n                                        </span>\n                                    </td>\n                                    <td>\n                                        <div className=\"action-buttons\">\n                                            <button\n                                                className=\"icon-btn btn-preview\"\n                                                title=\"Preview\"\n                                                onClick={() => {\n                                                    setSelectedChallenge(challenge);\n                                                    setShowPreviewModal(true);\n                                                }}>\n                                                <FaEye />\n                                            </button>\n                                            <button\n                                                className=\"icon-btn btn-edit\"\n                                                title=\"Edit\"\n                                                onClick={() => handleEdit(challenge)}>\n                                                <FaEdit />\n                                            </button>\n                                            {isExpired(challenge) && (\n                                                <button\n                                                    className=\"icon-btn btn-delete\"\n                                                    title=\"Delete\"\n                                                    onClick={() => handleDeleteClick(challenge)}>\n                                                    <FaTrash />\n                                                </button>\n                                            )}\n                                        </div>\n                                    </td>\n                                </tr>\n                            ))}\n                        </tbody>\n                    </table>\n                </div>\n                <div className=\"pagination\">\n                    <button\n                        className=\"icon-btn pagination-btn\"\n                        onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}\n                        disabled={currentPage === 1}\n                        title=\"Previous Page\"\n                    >\n                        <FaChevronLeft />\n                    </button>\n                    <span className=\"page-info\">\n                        Page {currentPage} of {totalPages}\n                    </span>\n                    <button\n                        className=\"icon-btn pagination-btn\"\n                        onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}\n                        disabled={currentPage === totalPages}\n                        title=\"Next Page\"\n                    >\n                        <FaChevronRight />\n                    </button>\n                </div>\n            </div>\n\n            {showPreviewModal && selectedChallenge && (\n                <PreviewModal\n                    challenge={selectedChallenge}\n                    onClose={() => setShowPreviewModal(false)}\n                    teams={teams}\n                    getTeamLogo={getTeamLogo}\n                />\n            )}\n\n            {showEditModal && (\n                <EditModal\n                    challenge={editingChallenge}\n                    onClose={() => setShowEditModal(false)}\n                    onUpdate={handleUpdate}\n                    onChange={(field, value) => setEditingChallenge(prev => ({\n                        ...prev,\n                        [field]: value\n                    }))}\n                />\n            )}\n\n            {showDeleteModal && challengeToDelete && (\n                <DeleteConfirmationModal\n                    challenge={challengeToDelete}\n                    onConfirm={handleDeleteConfirm}\n                    onCancel={() => {\n                        setShowDeleteModal(false);\n                        setChallengeToDelete(null);\n                    }}\n                />\n            )}\n        </div>\n    );\n}\n\nconst CountdownRenderer = ({ days, hours, minutes, seconds, completed, date }) => {\n    if (completed) {\n        return <span className=\"countdown-expired\">Expired</span>;\n    }\n\n    return (\n        <div className=\"countdown-container\">\n            <div className=\"countdown-time\">\n                {days > 0 ? `${days}d ` : ''}{hours}h {minutes}m {seconds}s\n            </div>\n        </div>\n    );\n};\n\nconst PreviewModal = ({ challenge, onClose, teams, getTeamLogo }) => (\n    <div className=\"modal-overlay\">\n        <div className=\"preview-modal\">\n            <button className=\"close-button\" onClick={onClose}>×</button>\n            <div className=\"preview-header\">\n                <h2>Match Preview</h2>\n                <div className=\"match-type-section\">\n                    <span className=\"match-type-badge\">\n                        {challenge.match_type === 'full_time' ? 'Full Time' : 'Half Time'}\n                    </span>\n                </div>\n            </div>\n            <div className=\"match-preview-display\">\n                <div className=\"match-team-preview left-team\">\n                    <img src={getTeamLogo(challenge.team_a)} alt={challenge.team_a} className=\"team-logo-large\" />\n                    <h3 className=\"team-name\">{challenge.team_a}</h3>\n                    <p className=\"team-odds\">Win: {challenge.odds_team_a}</p>\n                </div>\n                <div className=\"match-vs-preview\">\n                    <span className=\"vs-text\">VS</span>\n                    <div className=\"match-odds\">\n                        <p className=\"draw-odds\">Draw: {challenge.odds_draw || '0.8'}</p>\n                        <p className=\"lost-odds\">Lost: {challenge.odds_lost || '0.2'}</p>\n                    </div>\n                </div>\n                <div className=\"match-team-preview right-team\">\n                    <img src={getTeamLogo(challenge.team_b)} alt={challenge.team_b} className=\"team-logo-large\" />\n                    <h3 className=\"team-name\">{challenge.team_b}</h3>\n                    <p className=\"team-odds\">Win: {challenge.odds_team_b}</p>\n                </div>\n            </div>\n            <div className=\"match-details\">\n                <div className=\"detail-row\">\n                    <div className=\"detail-item\">\n                        <span className=\"label\">Start Time</span>\n                        <span className=\"value\">{new Date(challenge.start_time).toLocaleString()}</span>\n                    </div>\n                    <div className=\"detail-item\">\n                        <span className=\"label\">End Time</span>\n                        <span className=\"value\">{new Date(challenge.end_time).toLocaleString()}</span>\n                    </div>\n                </div>\n                <div className=\"detail-row\">\n                    <div className=\"detail-item\">\n                        <span className=\"label\">Match Date</span>\n                        <span className=\"value\">{new Date(challenge.match_date).toLocaleString()}</span>\n                    </div>\n                    <div className=\"detail-item\">\n                        <span className=\"label\">Status</span>\n                        <span className=\"value status-badge\">{challenge.status}</span>\n                    </div>\n                </div>\n            </div>\n        </div>\n    </div>\n);\n\nconst EditModal = ({ challenge, onClose, onUpdate, onChange }) => (\n    <div className=\"modal-overlay\">\n        <div className=\"edit-modal\">\n            <button className=\"close-button\" onClick={onClose}>×</button>\n            <h2>Edit Challenge</h2>\n            <div className=\"form-content\">\n                <div className=\"form-group\">\n                    <label>Match Type</label>\n                    <select\n                        value={challenge.match_type || 'full_time'}\n                        onChange={(e) => onChange('match_type', e.target.value)}\n                    >\n                        <option value=\"full_time\">Full Time</option>\n                        <option value=\"half_time\">Half Time</option>\n                    </select>\n                </div>\n                <div className=\"form-group\">\n                    <label>Odds Team A</label>\n                    <input\n                        type=\"number\"\n                        step=\"0.01\"\n                        value={challenge.odds_team_a}\n                        onChange={(e) => onChange('odds_team_a', e.target.value)}\n                    />\n                </div>\n                <div className=\"form-group\">\n                    <label>Odds Team B</label>\n                    <input\n                        type=\"number\"\n                        step=\"0.01\"\n                        value={challenge.odds_team_b}\n                        onChange={(e) => onChange('odds_team_b', e.target.value)}\n                    />\n                </div>\n                <div className=\"form-group\">\n                    <label>Draw Odds</label>\n                    <input\n                        type=\"number\"\n                        step=\"0.01\"\n                        value={challenge.odds_draw || 0.8}\n                        onChange={(e) => onChange('odds_draw', e.target.value)}\n                    />\n                </div>\n                <div className=\"form-group\">\n                    <label>Lost Odds</label>\n                    <input\n                        type=\"number\"\n                        step=\"0.01\"\n                        value={challenge.odds_lost || 0.2}\n                        onChange={(e) => onChange('odds_lost', e.target.value)}\n                    />\n                </div>\n                <div className=\"form-group\">\n                    <label>Status</label>\n                    <select\n                        value={challenge.status}\n                        onChange={(e) => onChange('status', e.target.value)}\n                    >\n                        <option value=\"Open\">Open</option>\n                        <option value=\"Closed\">Closed</option>\n                        <option value=\"Settled\">Settled</option>\n                    </select>\n                </div>\n                <div className=\"form-group\">\n                    <label>Start Time</label>\n                    <input\n                        type=\"datetime-local\"\n                        value={challenge.start_time.slice(0, 16)}\n                        onChange={(e) => onChange('start_time', e.target.value)}\n                    />\n                </div>\n                <div className=\"form-group\">\n                    <label>End Time</label>\n                    <input\n                        type=\"datetime-local\"\n                        value={challenge.end_time.slice(0, 16)}\n                        onChange={(e) => onChange('end_time', e.target.value)}\n                    />\n                </div>\n                <div className=\"form-group\">\n                    <label>Match Date</label>\n                    <input\n                        type=\"datetime-local\"\n                        value={challenge.match_date.slice(0, 16)}\n                        onChange={(e) => onChange('match_date', e.target.value)}\n                    />\n                </div>\n            </div>\n            <div className=\"form-actions\">\n                <button className=\"cancel-button\" onClick={onClose}>Cancel</button>\n                <button className=\"save-button\" onClick={onUpdate}>Save Changes</button>\n            </div>\n        </div>\n    </div>\n);\n\nconst DeleteConfirmationModal = ({ challenge, onConfirm, onCancel }) => (\n    <div className=\"modal-overlay\">\n        <div className=\"delete-confirmation-modal\">\n            <h2>Delete Challenge</h2>\n            <div className=\"modal-content\">\n                <p>Are you sure you want to delete this expired challenge?</p>\n                <div className=\"challenge-preview\">\n                    <div className=\"teams\">\n                        <span>{challenge.team_a}</span>\n                        <span className=\"vs\">vs</span>\n                        <span>{challenge.team_b}</span>\n                    </div>\n                    <div className=\"match-date\">\n                        Match Date: {new Date(challenge.match_date).toLocaleString()}\n                    </div>\n                </div>\n            </div>\n            <div className=\"modal-actions\">\n                <button className=\"cancel-button\" onClick={onCancel}>Cancel</button>\n                <button className=\"confirm-delete-button\" onClick={onConfirm}>Delete Challenge</button>\n            </div>\n        </div>\n    </div>\n);\n\nexport default ChallengeManagement;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,SAAS,MAAM,iBAAiB;AACvC,SAASC,KAAK,EAAEC,MAAM,EAAEC,OAAO,EAAEC,aAAa,EAAEC,cAAc,QAAQ,gBAAgB;AACtF,OAAO,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnC,MAAMC,YAAY,GAAG,UAAU;AAE/B,SAASC,mBAAmBA,CAAA,EAAG;EAAAC,EAAA;EAC3B,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACgB,KAAK,EAAEC,QAAQ,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACkB,KAAK,EAAEC,QAAQ,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACoB,OAAO,EAAEC,UAAU,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACsB,aAAa,EAAEC,gBAAgB,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACwB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC0B,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG3B,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAAC4B,WAAW,EAAEC,cAAc,CAAC,GAAG7B,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAAC8B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG/B,QAAQ,CAAC;IACrDgC,MAAM,EAAE,EAAE;IACVC,MAAM,EAAE,EAAE;IACVC,WAAW,EAAE,IAAI;IACjBC,WAAW,EAAE,IAAI;IACjBC,UAAU,EAAE,EAAE;IACdC,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,EAAE;IACdC,MAAM,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC0C,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG3C,QAAQ,CAAC,IAAI,CAAC;EAEhE,MAAM4C,cAAc,GAAG,EAAE;EAEzB3C,SAAS,CAAC,MAAM;IACZ4C,eAAe,CAAC,CAAC;IACjBC,UAAU,CAAC,CAAC;;IAEZ;IACA,MAAMC,mBAAmB,GAAGC,WAAW,CAAC,MAAM;MAC1CC,oBAAoB,CAAC,CAAC;IAC1B,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;IAEV,OAAO,MAAMC,aAAa,CAACH,mBAAmB,CAAC;EACnD,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMD,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI;MACA,MAAMK,QAAQ,GAAG,MAAMjD,KAAK,CAACkD,GAAG,CAAC,GAAGzC,YAAY,+BAA+B,CAAC;MAChF,IAAIwC,QAAQ,CAACE,IAAI,CAACd,MAAM,KAAK,GAAG,EAAE;QAC9BtB,QAAQ,CAACkC,QAAQ,CAACE,IAAI,CAACA,IAAI,CAAC;MAChC;IACJ,CAAC,CAAC,OAAOC,GAAG,EAAE;MACVC,OAAO,CAACrC,KAAK,CAAC,uBAAuB,EAAEoC,GAAG,CAAC;IAC/C;EACJ,CAAC;EAED,MAAMT,eAAe,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACA,MAAMM,QAAQ,GAAG,MAAMjD,KAAK,CAACkD,GAAG,CAAC,GAAGzC,YAAY,oCAAoC,CAAC;MACrF,IAAIwC,QAAQ,CAACE,IAAI,CAACjC,OAAO,EAAE;QACvBL,aAAa,CAACoC,QAAQ,CAACE,IAAI,CAACvC,UAAU,CAAC;MAC3C;IACJ,CAAC,CAAC,OAAOwC,GAAG,EAAE;MACVnC,QAAQ,CAAC,4BAA4B,CAAC;IAC1C;EACJ,CAAC;EAED,MAAM8B,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACA,MAAME,QAAQ,GAAG,MAAMjD,KAAK,CAACkD,GAAG,CAAC,GAAGzC,YAAY,sCAAsC,CAAC;MACvF,IAAIwC,QAAQ,CAACE,IAAI,CAACG,iBAAiB,GAAG,CAAC,EAAE;QACrCD,OAAO,CAACE,GAAG,CAAC,SAASN,QAAQ,CAACE,IAAI,CAACG,iBAAiB,uCAAuC,CAAC;MAChG;MACA,IAAIL,QAAQ,CAACE,IAAI,CAACK,aAAa,GAAG,CAAC,EAAE;QACjCH,OAAO,CAACE,GAAG,CAAC,UAAUN,QAAQ,CAACE,IAAI,CAACK,aAAa,qBAAqB,CAAC;QACvEb,eAAe,CAAC,CAAC,CAAC,CAAC;MACvB;IACJ,CAAC,CAAC,OAAOS,GAAG,EAAE;MACVC,OAAO,CAACrC,KAAK,CAAC,kCAAkC,EAAEoC,GAAG,CAAC;IAC1D;EACJ,CAAC;EAED,MAAMK,WAAW,GAAIC,QAAQ,IAAK;IAC9B,MAAMC,IAAI,GAAG7C,KAAK,CAAC8C,IAAI,CAACD,IAAI,IAAIA,IAAI,CAACE,IAAI,KAAKH,QAAQ,CAAC;IACvD,OAAOC,IAAI,GAAG,GAAGlD,YAAY,IAAIkD,IAAI,CAACG,IAAI,EAAE,GAAG,EAAE;EACrD,CAAC;EAED,MAAMC,UAAU,GAAIC,SAAS,IAAK;IAC9BnC,mBAAmB,CAACmC,SAAS,CAAC;IAC9B3C,gBAAgB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAM4C,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACA,MAAMhB,QAAQ,GAAG,MAAMjD,KAAK,CAACkE,IAAI,CAC7B,GAAGzD,YAAY,oCAAoC,EACnDmB,gBACJ,CAAC;MAED,IAAIqB,QAAQ,CAACE,IAAI,CAACjC,OAAO,EAAE;QACvBC,UAAU,CAAC,iCAAiC,CAAC;QAC7CwB,eAAe,CAAC,CAAC;QACjBtB,gBAAgB,CAAC,KAAK,CAAC;MAC3B,CAAC,MAAM;QACHJ,QAAQ,CAACgC,QAAQ,CAACE,IAAI,CAACgB,OAAO,IAAI,4BAA4B,CAAC;MACnE;IACJ,CAAC,CAAC,OAAOf,GAAG,EAAE;MACVnC,QAAQ,CAAC,4BAA4B,CAAC;MACtCoC,OAAO,CAACrC,KAAK,CAAC,eAAe,EAAEoC,GAAG,CAAC;IACvC;EACJ,CAAC;EAED,MAAMgB,iBAAiB,GAAIJ,SAAS,IAAK;IACrCvB,oBAAoB,CAACuB,SAAS,CAAC;IAC/BzB,kBAAkB,CAAC,IAAI,CAAC;EAC5B,CAAC;EAED,MAAM8B,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI,CAAC7B,iBAAiB,EAAE;IAExB,IAAI;MACA,MAAMS,QAAQ,GAAG,MAAMjD,KAAK,CAACsE,MAAM,CAC/B,GAAG7D,YAAY,mDAAmD+B,iBAAiB,CAAC+B,YAAY,EACpG,CAAC;MAED,IAAItB,QAAQ,CAACE,IAAI,CAACjC,OAAO,EAAE;QACvBC,UAAU,CAAC,iCAAiC,CAAC;QAC7CwB,eAAe,CAAC,CAAC;MACrB,CAAC,MAAM;QACH1B,QAAQ,CAACgC,QAAQ,CAACE,IAAI,CAACgB,OAAO,IAAI,4BAA4B,CAAC;MACnE;IACJ,CAAC,CAAC,OAAOf,GAAG,EAAE;MACVnC,QAAQ,CAAC,4BAA4B,CAAC;MACtCoC,OAAO,CAACrC,KAAK,CAAC,eAAe,EAAEoC,GAAG,CAAC;IACvC;IACAb,kBAAkB,CAAC,KAAK,CAAC;IACzBE,oBAAoB,CAAC,IAAI,CAAC;EAC9B,CAAC;EAED,MAAM+B,SAAS,GAAIR,SAAS,IAAK;IAC7B,OAAO,IAAIS,IAAI,CAACT,SAAS,CAAC7B,QAAQ,CAAC,GAAG,IAAIsC,IAAI,CAAC,CAAC,IAAIT,SAAS,CAAC3B,MAAM,KAAK,SAAS;EACtF,CAAC;EAED,MAAMqC,UAAU,GAAGC,IAAI,CAACC,IAAI,CAAChE,UAAU,CAACiE,MAAM,GAAGnC,cAAc,CAAC;EAChE,MAAMoC,eAAe,GAAGpD,WAAW,GAAGgB,cAAc;EACpD,MAAMqC,gBAAgB,GAAGD,eAAe,GAAGpC,cAAc;EACzD,MAAMsC,iBAAiB,GAAGpE,UAAU,CAACqE,KAAK,CAACF,gBAAgB,EAAED,eAAe,CAAC;EAE7E,oBACItE,OAAA;IAAK0E,SAAS,EAAC,sBAAsB;IAAAC,QAAA,gBACjC3E,OAAA;MAAA2E,QAAA,EAAI;IAAoB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,EAC5BvE,KAAK,iBAAIR,OAAA;MAAK0E,SAAS,EAAC,eAAe;MAAAC,QAAA,EAAEnE;IAAK;MAAAoE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,EACrDrE,OAAO,iBAAIV,OAAA;MAAK0E,SAAS,EAAC,iBAAiB;MAAAC,QAAA,EAAEjE;IAAO;MAAAkE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAE5D/E,OAAA;MAAK0E,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC5B3E,OAAA;QAAK0E,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC5B3E,OAAA;UAAO0E,SAAS,EAAC,4BAA4B;UAAAC,QAAA,gBACzC3E,OAAA;YAAA2E,QAAA,eACI3E,OAAA;cAAA2E,QAAA,gBACI3E,OAAA;gBAAA2E,QAAA,EAAI;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACV/E,OAAA;gBAAA2E,QAAA,EAAI;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChB/E,OAAA;gBAAA2E,QAAA,EAAI;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClB/E,OAAA;gBAAA2E,QAAA,EAAI;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACf/E,OAAA;gBAAIgF,KAAK,EAAE;kBAAEC,KAAK,EAAE;gBAAQ,CAAE;gBAAAN,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACR/E,OAAA;YAAA2E,QAAA,EACKH,iBAAiB,CAACU,GAAG,CAAC,CAAC1B,SAAS,EAAE2B,KAAK,kBACpCnF,OAAA;cAAA2E,QAAA,gBACI3E,OAAA;gBAAI0E,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAEJ,gBAAgB,GAAGY,KAAK,GAAG;cAAC;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAChE/E,OAAA;gBAAA2E,QAAA,eACI3E,OAAA;kBAAK0E,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBACzB3E,OAAA;oBAAK0E,SAAS,EAAC,YAAY;oBAAAC,QAAA,gBACvB3E,OAAA;sBAAKoF,GAAG,EAAEnC,WAAW,CAACO,SAAS,CAAClC,MAAM,CAAE;sBACnC+D,GAAG,EAAE7B,SAAS,CAAClC,MAAO;sBACtBoD,SAAS,EAAC;oBAAW;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC7B/E,OAAA;sBAAK0E,SAAS,EAAC,WAAW;sBAAAC,QAAA,EAAEnB,SAAS,CAAClC;oBAAM;sBAAAsD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACnD/E,OAAA;sBAAK0E,SAAS,EAAC,WAAW;sBAAAC,QAAA,GAAC,QAAM,EAACnB,SAAS,CAAChC,WAAW;oBAAA;sBAAAoD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7D,CAAC,eAEN/E,OAAA;oBAAK0E,SAAS,EAAC,WAAW;oBAAAC,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAEnC/E,OAAA;oBAAK0E,SAAS,EAAC,YAAY;oBAAAC,QAAA,gBACvB3E,OAAA;sBAAKoF,GAAG,EAAEnC,WAAW,CAACO,SAAS,CAACjC,MAAM,CAAE;sBACnC8D,GAAG,EAAE7B,SAAS,CAACjC,MAAO;sBACtBmD,SAAS,EAAC;oBAAW;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC7B/E,OAAA;sBAAK0E,SAAS,EAAC,WAAW;sBAAAC,QAAA,EAAEnB,SAAS,CAACjC;oBAAM;sBAAAqD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACnD/E,OAAA;sBAAK0E,SAAS,EAAC,WAAW;sBAAAC,QAAA,GAAC,QAAM,EAACnB,SAAS,CAAC/B,WAAW;oBAAA;sBAAAmD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7D,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACL/E,OAAA;gBAAI0E,SAAS,EAAC,WAAW;gBAAAC,QAAA,eACrB3E,OAAA;kBAAK0E,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,gBAC/B3E,OAAA;oBAAK0E,SAAS,EAAC,YAAY;oBAAAC,QAAA,GAAC,SACjB,EAAC,IAAIV,IAAI,CAACT,SAAS,CAAC5B,UAAU,CAAC,CAAC0D,cAAc,CAAC,OAAO,EAAE;sBAC3DC,KAAK,EAAE,OAAO;sBACdC,GAAG,EAAE,SAAS;sBACdC,IAAI,EAAE,SAAS;sBACfC,MAAM,EAAE,SAAS;sBACjBC,MAAM,EAAE;oBACZ,CAAC,CAAC;kBAAA;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC,eACN/E,OAAA;oBAAK0E,SAAS,EAAC,UAAU;oBAAAC,QAAA,GAAC,aACX,eAAA3E,OAAA,CAACP,SAAS;sBACjBmG,IAAI,EAAE,IAAI3B,IAAI,CAACT,SAAS,CAAC7B,QAAQ,CAAE;sBACnCkE,QAAQ,EAAEA,CAAC;wBAAEC,IAAI;wBAAEC,KAAK;wBAAEC,OAAO;wBAAEC,OAAO;wBAAEC;sBAAU,CAAC,kBACnDlG,OAAA,CAACmG,iBAAiB;wBACdL,IAAI,EAAEA,IAAK;wBACXC,KAAK,EAAEA,KAAM;wBACbC,OAAO,EAAEA,OAAQ;wBACjBC,OAAO,EAAEA,OAAQ;wBACjBC,SAAS,EAAEA,SAAU;wBACrBN,IAAI,EAAEpC,SAAS,CAAC7B;sBAAS;wBAAAiD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC5B;oBACJ;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACL/E,OAAA;gBAAI0E,SAAS,EAAC,aAAa;gBAAAC,QAAA,eACvB3E,OAAA;kBAAM0E,SAAS,EAAE,gBAAgBlB,SAAS,CAAC3B,MAAM,EAAG;kBAAA8C,QAAA,EAC/CnB,SAAS,CAAC3B;gBAAM;kBAAA+C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC,eACL/E,OAAA;gBAAA2E,QAAA,eACI3E,OAAA;kBAAK0E,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC3B3E,OAAA;oBACI0E,SAAS,EAAC,sBAAsB;oBAChC0B,KAAK,EAAC,SAAS;oBACfC,OAAO,EAAEA,CAAA,KAAM;sBACXpF,oBAAoB,CAACuC,SAAS,CAAC;sBAC/BzC,mBAAmB,CAAC,IAAI,CAAC;oBAC7B,CAAE;oBAAA4D,QAAA,eACF3E,OAAA,CAACN,KAAK;sBAAAkF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eACT/E,OAAA;oBACI0E,SAAS,EAAC,mBAAmB;oBAC7B0B,KAAK,EAAC,MAAM;oBACZC,OAAO,EAAEA,CAAA,KAAM9C,UAAU,CAACC,SAAS,CAAE;oBAAAmB,QAAA,eACrC3E,OAAA,CAACL,MAAM;sBAAAiF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,EACRf,SAAS,CAACR,SAAS,CAAC,iBACjBxD,OAAA;oBACI0E,SAAS,EAAC,qBAAqB;oBAC/B0B,KAAK,EAAC,QAAQ;oBACdC,OAAO,EAAEA,CAAA,KAAMzC,iBAAiB,CAACJ,SAAS,CAAE;oBAAAmB,QAAA,eAC5C3E,OAAA,CAACJ,OAAO;sBAAAgF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CACX;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA,GAlFAvB,SAAS,CAACO,YAAY;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAmF3B,CACP;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC,eACN/E,OAAA;QAAK0E,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACvB3E,OAAA;UACI0E,SAAS,EAAC,yBAAyB;UACnC2B,OAAO,EAAEA,CAAA,KAAMlF,cAAc,CAACmF,IAAI,IAAInC,IAAI,CAACoC,GAAG,CAACD,IAAI,GAAG,CAAC,EAAE,CAAC,CAAC,CAAE;UAC7DE,QAAQ,EAAEtF,WAAW,KAAK,CAAE;UAC5BkF,KAAK,EAAC,eAAe;UAAAzB,QAAA,eAErB3E,OAAA,CAACH,aAAa;YAAA+E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC,eACT/E,OAAA;UAAM0E,SAAS,EAAC,WAAW;UAAAC,QAAA,GAAC,OACnB,EAACzD,WAAW,EAAC,MAAI,EAACgD,UAAU;QAAA;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC,eACP/E,OAAA;UACI0E,SAAS,EAAC,yBAAyB;UACnC2B,OAAO,EAAEA,CAAA,KAAMlF,cAAc,CAACmF,IAAI,IAAInC,IAAI,CAACsC,GAAG,CAACH,IAAI,GAAG,CAAC,EAAEpC,UAAU,CAAC,CAAE;UACtEsC,QAAQ,EAAEtF,WAAW,KAAKgD,UAAW;UACrCkC,KAAK,EAAC,WAAW;UAAAzB,QAAA,eAEjB3E,OAAA,CAACF,cAAc;YAAA8E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,EAELjE,gBAAgB,IAAIE,iBAAiB,iBAClChB,OAAA,CAAC0G,YAAY;MACTlD,SAAS,EAAExC,iBAAkB;MAC7B2F,OAAO,EAAEA,CAAA,KAAM5F,mBAAmB,CAAC,KAAK,CAAE;MAC1CT,KAAK,EAAEA,KAAM;MACb2C,WAAW,EAAEA;IAAY;MAAA2B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5B,CACJ,EAEAnE,aAAa,iBACVZ,OAAA,CAAC4G,SAAS;MACNpD,SAAS,EAAEpC,gBAAiB;MAC5BuF,OAAO,EAAEA,CAAA,KAAM9F,gBAAgB,CAAC,KAAK,CAAE;MACvCgG,QAAQ,EAAEpD,YAAa;MACvBqD,QAAQ,EAAEA,CAACC,KAAK,EAAEC,KAAK,KAAK3F,mBAAmB,CAACiF,IAAI,KAAK;QACrD,GAAGA,IAAI;QACP,CAACS,KAAK,GAAGC;MACb,CAAC,CAAC;IAAE;MAAApC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CACJ,EAEAjD,eAAe,IAAIE,iBAAiB,iBACjChC,OAAA,CAACiH,uBAAuB;MACpBzD,SAAS,EAAExB,iBAAkB;MAC7BkF,SAAS,EAAErD,mBAAoB;MAC/BsD,QAAQ,EAAEA,CAAA,KAAM;QACZpF,kBAAkB,CAAC,KAAK,CAAC;QACzBE,oBAAoB,CAAC,IAAI,CAAC;MAC9B;IAAE;MAAA2C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACJ;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd;AAAC5E,EAAA,CA/SQD,mBAAmB;AAAAkH,EAAA,GAAnBlH,mBAAmB;AAiT5B,MAAMiG,iBAAiB,GAAGA,CAAC;EAAEL,IAAI;EAAEC,KAAK;EAAEC,OAAO;EAAEC,OAAO;EAAEC,SAAS;EAAEN;AAAK,CAAC,KAAK;EAC9E,IAAIM,SAAS,EAAE;IACX,oBAAOlG,OAAA;MAAM0E,SAAS,EAAC,mBAAmB;MAAAC,QAAA,EAAC;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EAC7D;EAEA,oBACI/E,OAAA;IAAK0E,SAAS,EAAC,qBAAqB;IAAAC,QAAA,eAChC3E,OAAA;MAAK0E,SAAS,EAAC,gBAAgB;MAAAC,QAAA,GAC1BmB,IAAI,GAAG,CAAC,GAAG,GAAGA,IAAI,IAAI,GAAG,EAAE,EAAEC,KAAK,EAAC,IAAE,EAACC,OAAO,EAAC,IAAE,EAACC,OAAO,EAAC,GAC9D;IAAA;MAAArB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAACsC,GAAA,GAZIlB,iBAAiB;AAcvB,MAAMO,YAAY,GAAGA,CAAC;EAAElD,SAAS;EAAEmD,OAAO;EAAErG,KAAK;EAAE2C;AAAY,CAAC,kBAC5DjD,OAAA;EAAK0E,SAAS,EAAC,eAAe;EAAAC,QAAA,eAC1B3E,OAAA;IAAK0E,SAAS,EAAC,eAAe;IAAAC,QAAA,gBAC1B3E,OAAA;MAAQ0E,SAAS,EAAC,cAAc;MAAC2B,OAAO,EAAEM,OAAQ;MAAAhC,QAAA,EAAC;IAAC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,eAC7D/E,OAAA;MAAK0E,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC3B3E,OAAA;QAAA2E,QAAA,EAAI;MAAa;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACtB/E,OAAA;QAAK0E,SAAS,EAAC,oBAAoB;QAAAC,QAAA,eAC/B3E,OAAA;UAAM0E,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAC7BnB,SAAS,CAAC8D,UAAU,KAAK,WAAW,GAAG,WAAW,GAAG;QAAW;UAAA1C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/D;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eACN/E,OAAA;MAAK0E,SAAS,EAAC,uBAAuB;MAAAC,QAAA,gBAClC3E,OAAA;QAAK0E,SAAS,EAAC,8BAA8B;QAAAC,QAAA,gBACzC3E,OAAA;UAAKoF,GAAG,EAAEnC,WAAW,CAACO,SAAS,CAAClC,MAAM,CAAE;UAAC+D,GAAG,EAAE7B,SAAS,CAAClC,MAAO;UAACoD,SAAS,EAAC;QAAiB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9F/E,OAAA;UAAI0E,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAEnB,SAAS,CAAClC;QAAM;UAAAsD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACjD/E,OAAA;UAAG0E,SAAS,EAAC,WAAW;UAAAC,QAAA,GAAC,OAAK,EAACnB,SAAS,CAAChC,WAAW;QAAA;UAAAoD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxD,CAAC,eACN/E,OAAA;QAAK0E,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC7B3E,OAAA;UAAM0E,SAAS,EAAC,SAAS;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACnC/E,OAAA;UAAK0E,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACvB3E,OAAA;YAAG0E,SAAS,EAAC,WAAW;YAAAC,QAAA,GAAC,QAAM,EAACnB,SAAS,CAAC+D,SAAS,IAAI,KAAK;UAAA;YAAA3C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjE/E,OAAA;YAAG0E,SAAS,EAAC,WAAW;YAAAC,QAAA,GAAC,QAAM,EAACnB,SAAS,CAACgE,SAAS,IAAI,KAAK;UAAA;YAAA5C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACN/E,OAAA;QAAK0E,SAAS,EAAC,+BAA+B;QAAAC,QAAA,gBAC1C3E,OAAA;UAAKoF,GAAG,EAAEnC,WAAW,CAACO,SAAS,CAACjC,MAAM,CAAE;UAAC8D,GAAG,EAAE7B,SAAS,CAACjC,MAAO;UAACmD,SAAS,EAAC;QAAiB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9F/E,OAAA;UAAI0E,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAEnB,SAAS,CAACjC;QAAM;UAAAqD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACjD/E,OAAA;UAAG0E,SAAS,EAAC,WAAW;UAAAC,QAAA,GAAC,OAAK,EAACnB,SAAS,CAAC/B,WAAW;QAAA;UAAAmD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eACN/E,OAAA;MAAK0E,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC1B3E,OAAA;QAAK0E,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACvB3E,OAAA;UAAK0E,SAAS,EAAC,aAAa;UAAAC,QAAA,gBACxB3E,OAAA;YAAM0E,SAAS,EAAC,OAAO;YAAAC,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzC/E,OAAA;YAAM0E,SAAS,EAAC,OAAO;YAAAC,QAAA,EAAE,IAAIV,IAAI,CAACT,SAAS,CAAC9B,UAAU,CAAC,CAAC4D,cAAc,CAAC;UAAC;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/E,CAAC,eACN/E,OAAA;UAAK0E,SAAS,EAAC,aAAa;UAAAC,QAAA,gBACxB3E,OAAA;YAAM0E,SAAS,EAAC,OAAO;YAAAC,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACvC/E,OAAA;YAAM0E,SAAS,EAAC,OAAO;YAAAC,QAAA,EAAE,IAAIV,IAAI,CAACT,SAAS,CAAC7B,QAAQ,CAAC,CAAC2D,cAAc,CAAC;UAAC;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7E,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACN/E,OAAA;QAAK0E,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACvB3E,OAAA;UAAK0E,SAAS,EAAC,aAAa;UAAAC,QAAA,gBACxB3E,OAAA;YAAM0E,SAAS,EAAC,OAAO;YAAAC,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzC/E,OAAA;YAAM0E,SAAS,EAAC,OAAO;YAAAC,QAAA,EAAE,IAAIV,IAAI,CAACT,SAAS,CAAC5B,UAAU,CAAC,CAAC0D,cAAc,CAAC;UAAC;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/E,CAAC,eACN/E,OAAA;UAAK0E,SAAS,EAAC,aAAa;UAAAC,QAAA,gBACxB3E,OAAA;YAAM0E,SAAS,EAAC,OAAO;YAAAC,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACrC/E,OAAA;YAAM0E,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAEnB,SAAS,CAAC3B;UAAM;YAAA+C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACL,CACR;AAAC0C,GAAA,GAvDIf,YAAY;AAyDlB,MAAME,SAAS,GAAGA,CAAC;EAAEpD,SAAS;EAAEmD,OAAO;EAAEE,QAAQ;EAAEC;AAAS,CAAC,kBACzD9G,OAAA;EAAK0E,SAAS,EAAC,eAAe;EAAAC,QAAA,eAC1B3E,OAAA;IAAK0E,SAAS,EAAC,YAAY;IAAAC,QAAA,gBACvB3E,OAAA;MAAQ0E,SAAS,EAAC,cAAc;MAAC2B,OAAO,EAAEM,OAAQ;MAAAhC,QAAA,EAAC;IAAC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,eAC7D/E,OAAA;MAAA2E,QAAA,EAAI;IAAc;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACvB/E,OAAA;MAAK0E,SAAS,EAAC,cAAc;MAAAC,QAAA,gBACzB3E,OAAA;QAAK0E,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACvB3E,OAAA;UAAA2E,QAAA,EAAO;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACzB/E,OAAA;UACIgH,KAAK,EAAExD,SAAS,CAAC8D,UAAU,IAAI,WAAY;UAC3CR,QAAQ,EAAGY,CAAC,IAAKZ,QAAQ,CAAC,YAAY,EAAEY,CAAC,CAACC,MAAM,CAACX,KAAK,CAAE;UAAArC,QAAA,gBAExD3E,OAAA;YAAQgH,KAAK,EAAC,WAAW;YAAArC,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC5C/E,OAAA;YAAQgH,KAAK,EAAC,WAAW;YAAArC,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,eACN/E,OAAA;QAAK0E,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACvB3E,OAAA;UAAA2E,QAAA,EAAO;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC1B/E,OAAA;UACI4H,IAAI,EAAC,QAAQ;UACbC,IAAI,EAAC,MAAM;UACXb,KAAK,EAAExD,SAAS,CAAChC,WAAY;UAC7BsF,QAAQ,EAAGY,CAAC,IAAKZ,QAAQ,CAAC,aAAa,EAAEY,CAAC,CAACC,MAAM,CAACX,KAAK;QAAE;UAAApC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eACN/E,OAAA;QAAK0E,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACvB3E,OAAA;UAAA2E,QAAA,EAAO;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC1B/E,OAAA;UACI4H,IAAI,EAAC,QAAQ;UACbC,IAAI,EAAC,MAAM;UACXb,KAAK,EAAExD,SAAS,CAAC/B,WAAY;UAC7BqF,QAAQ,EAAGY,CAAC,IAAKZ,QAAQ,CAAC,aAAa,EAAEY,CAAC,CAACC,MAAM,CAACX,KAAK;QAAE;UAAApC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eACN/E,OAAA;QAAK0E,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACvB3E,OAAA;UAAA2E,QAAA,EAAO;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACxB/E,OAAA;UACI4H,IAAI,EAAC,QAAQ;UACbC,IAAI,EAAC,MAAM;UACXb,KAAK,EAAExD,SAAS,CAAC+D,SAAS,IAAI,GAAI;UAClCT,QAAQ,EAAGY,CAAC,IAAKZ,QAAQ,CAAC,WAAW,EAAEY,CAAC,CAACC,MAAM,CAACX,KAAK;QAAE;UAAApC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eACN/E,OAAA;QAAK0E,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACvB3E,OAAA;UAAA2E,QAAA,EAAO;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACxB/E,OAAA;UACI4H,IAAI,EAAC,QAAQ;UACbC,IAAI,EAAC,MAAM;UACXb,KAAK,EAAExD,SAAS,CAACgE,SAAS,IAAI,GAAI;UAClCV,QAAQ,EAAGY,CAAC,IAAKZ,QAAQ,CAAC,WAAW,EAAEY,CAAC,CAACC,MAAM,CAACX,KAAK;QAAE;UAAApC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eACN/E,OAAA;QAAK0E,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACvB3E,OAAA;UAAA2E,QAAA,EAAO;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACrB/E,OAAA;UACIgH,KAAK,EAAExD,SAAS,CAAC3B,MAAO;UACxBiF,QAAQ,EAAGY,CAAC,IAAKZ,QAAQ,CAAC,QAAQ,EAAEY,CAAC,CAACC,MAAM,CAACX,KAAK,CAAE;UAAArC,QAAA,gBAEpD3E,OAAA;YAAQgH,KAAK,EAAC,MAAM;YAAArC,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAClC/E,OAAA;YAAQgH,KAAK,EAAC,QAAQ;YAAArC,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACtC/E,OAAA;YAAQgH,KAAK,EAAC,SAAS;YAAArC,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,eACN/E,OAAA;QAAK0E,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACvB3E,OAAA;UAAA2E,QAAA,EAAO;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACzB/E,OAAA;UACI4H,IAAI,EAAC,gBAAgB;UACrBZ,KAAK,EAAExD,SAAS,CAAC9B,UAAU,CAAC+C,KAAK,CAAC,CAAC,EAAE,EAAE,CAAE;UACzCqC,QAAQ,EAAGY,CAAC,IAAKZ,QAAQ,CAAC,YAAY,EAAEY,CAAC,CAACC,MAAM,CAACX,KAAK;QAAE;UAAApC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eACN/E,OAAA;QAAK0E,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACvB3E,OAAA;UAAA2E,QAAA,EAAO;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACvB/E,OAAA;UACI4H,IAAI,EAAC,gBAAgB;UACrBZ,KAAK,EAAExD,SAAS,CAAC7B,QAAQ,CAAC8C,KAAK,CAAC,CAAC,EAAE,EAAE,CAAE;UACvCqC,QAAQ,EAAGY,CAAC,IAAKZ,QAAQ,CAAC,UAAU,EAAEY,CAAC,CAACC,MAAM,CAACX,KAAK;QAAE;UAAApC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eACN/E,OAAA;QAAK0E,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACvB3E,OAAA;UAAA2E,QAAA,EAAO;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACzB/E,OAAA;UACI4H,IAAI,EAAC,gBAAgB;UACrBZ,KAAK,EAAExD,SAAS,CAAC5B,UAAU,CAAC6C,KAAK,CAAC,CAAC,EAAE,EAAE,CAAE;UACzCqC,QAAQ,EAAGY,CAAC,IAAKZ,QAAQ,CAAC,YAAY,EAAEY,CAAC,CAACC,MAAM,CAACX,KAAK;QAAE;UAAApC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eACN/E,OAAA;MAAK0E,SAAS,EAAC,cAAc;MAAAC,QAAA,gBACzB3E,OAAA;QAAQ0E,SAAS,EAAC,eAAe;QAAC2B,OAAO,EAAEM,OAAQ;QAAAhC,QAAA,EAAC;MAAM;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACnE/E,OAAA;QAAQ0E,SAAS,EAAC,aAAa;QAAC2B,OAAO,EAAEQ,QAAS;QAAAlC,QAAA,EAAC;MAAY;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACL,CACR;AAAC+C,GAAA,GA9FIlB,SAAS;AAgGf,MAAMK,uBAAuB,GAAGA,CAAC;EAAEzD,SAAS;EAAE0D,SAAS;EAAEC;AAAS,CAAC,kBAC/DnH,OAAA;EAAK0E,SAAS,EAAC,eAAe;EAAAC,QAAA,eAC1B3E,OAAA;IAAK0E,SAAS,EAAC,2BAA2B;IAAAC,QAAA,gBACtC3E,OAAA;MAAA2E,QAAA,EAAI;IAAgB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACzB/E,OAAA;MAAK0E,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC1B3E,OAAA;QAAA2E,QAAA,EAAG;MAAuD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAC9D/E,OAAA;QAAK0E,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAC9B3E,OAAA;UAAK0E,SAAS,EAAC,OAAO;UAAAC,QAAA,gBAClB3E,OAAA;YAAA2E,QAAA,EAAOnB,SAAS,CAAClC;UAAM;YAAAsD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC/B/E,OAAA;YAAM0E,SAAS,EAAC,IAAI;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC9B/E,OAAA;YAAA2E,QAAA,EAAOnB,SAAS,CAACjC;UAAM;YAAAqD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC,eACN/E,OAAA;UAAK0E,SAAS,EAAC,YAAY;UAAAC,QAAA,GAAC,cACZ,EAAC,IAAIV,IAAI,CAACT,SAAS,CAAC5B,UAAU,CAAC,CAAC0D,cAAc,CAAC,CAAC;QAAA;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eACN/E,OAAA;MAAK0E,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC1B3E,OAAA;QAAQ0E,SAAS,EAAC,eAAe;QAAC2B,OAAO,EAAEc,QAAS;QAAAxC,QAAA,EAAC;MAAM;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACpE/E,OAAA;QAAQ0E,SAAS,EAAC,uBAAuB;QAAC2B,OAAO,EAAEa,SAAU;QAAAvC,QAAA,EAAC;MAAgB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACL,CACR;AAACgD,GAAA,GAvBId,uBAAuB;AAyB7B,eAAe/G,mBAAmB;AAAC,IAAAkH,EAAA,EAAAC,GAAA,EAAAI,GAAA,EAAAK,GAAA,EAAAC,GAAA;AAAAC,YAAA,CAAAZ,EAAA;AAAAY,YAAA,CAAAX,GAAA;AAAAW,YAAA,CAAAP,GAAA;AAAAO,YAAA,CAAAF,GAAA;AAAAE,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}