/* Admin Sidebar Base */
.admin-sidebar {
    width: 320px;
    min-width: 320px;
    height: 100vh;
    background-color: #ffffff;
    color: #333333;
    position: fixed;
    left: 0;
    top: 0;
    z-index: 1000;
    overflow-x: visible;
    overflow-y: auto;
    border-right: 1px solid #e0e0e0;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
    display: flex;
    flex-direction: column;
}

/* Sidebar Header */
.sidebar-header {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1rem;
    min-height: 100px;
    border-bottom: 1px solid #e0e0e0;
    background-color: #ffffff;
}

/* Admin sidebar specific logo styles - override conflicts */
.admin-sidebar .logo {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    width: 100% !important;
    min-height: 80px !important;
    height: auto !important;
    padding: 15px 10px !important;
    font-size: 1.25rem !important;
    font-weight: bold !important;
    color: #166534 !important; /* dark green */
    text-align: center !important;
    letter-spacing: 0.5px !important;
    margin-bottom: 0 !important;
    border-bottom: none !important;
    background: transparent !important;
}

.admin-sidebar .logo-icon {
    width: 180px !important;
    height: 70px !important;
    max-width: 180px !important;
    max-height: 70px !important;
    object-fit: contain !important;
    display: block !important;
}

.admin-sidebar .logo-text {
    color: #166534 !important;
    font-weight: bold !important;
    display: block !important; /* Show when no logo */
    font-size: 1.25rem !important;
    margin-bottom: 0 !important;
    border-bottom: none !important;
    padding: 0 !important;
}

/* Sidebar Navigation */
.admin-sidebar-nav {
    padding: 0.25rem 0;
    display: flex;
    flex-direction: column;
    height: calc(100vh - 100px); /* Adjust based on header height */
    overflow-y: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

/* Hide scrollbar for admin sidebar */
.admin-sidebar-nav::-webkit-scrollbar {
    width: 0;
    height: 0;
    display: none;
}

.admin-sidebar-nav::-webkit-scrollbar-thumb {
    display: none;
}

.admin-sidebar-nav::-webkit-scrollbar-track {
    display: none;
}

/* Menu Section */
.admin-menu-section {
    margin-bottom: 0.75rem;
    width: 100%;
    box-sizing: border-box;
}

/* Menu Category */
.admin-menu-category {
    display: flex;
    align-items: center;
    padding: 0.5rem 1.25rem 0.25rem;
    font-weight: 600;
    color: #444;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-top: 0.25rem;
    white-space: nowrap;
    overflow: visible;
    width: 100%;
    box-sizing: border-box;
}

.admin-menu-icon {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 10px;
    color: #444;
    flex-shrink: 0;
}

.admin-menu-title {
    flex: 1;
    white-space: nowrap;
    overflow: visible;
    text-overflow: clip;
    display: inline-block;
}

/* Menu Items */
.admin-menu-items {
    margin-top: 0.1rem;
    margin-bottom: 0.25rem;
    width: 100%;
    box-sizing: border-box;
    padding-right: 0.5rem;
}

.admin-nav-item {
    display: flex;
    align-items: center;
    padding: 0.5rem 0.75rem 0.5rem 2.5rem;
    color: #555;
    text-decoration: none;
    transition: background-color 0.3s ease, color 0.3s ease;
    font-size: 1rem;
    border-left: 3px solid transparent;
    margin-bottom: 1px;
    white-space: nowrap;
    overflow: visible;
    width: 100%;
    box-sizing: border-box;
}

.admin-nav-item:hover {
    background-color: rgba(0, 0, 0, 0.03);
}

.admin-nav-item.active {
    background-color: rgba(22, 101, 52, 0.08);
    color: #166534; /* dark green */
    font-weight: 500;
    border-left: 3px solid #166534;
}

.admin-nav-item-icon {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 10px;
    color: #666;
    flex-shrink: 0;
}

.admin-nav-item.active .admin-nav-item-icon {
    color: #166534; /* dark green */
}

.admin-nav-item-text {
    white-space: nowrap;
    overflow: visible;
    text-overflow: clip;
    display: inline-block;
    max-width: 180px;
    font-size: 1rem;
}

/* Scrollbar Styling */
.admin-sidebar::-webkit-scrollbar {
    width: 6px;
}

.admin-sidebar::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.03);
}

.admin-sidebar::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 3px;
}

.admin-sidebar::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.2);
}

/* Sidebar footer with logout button */
.sidebar-footer {
    margin-top: auto;
    padding: 1rem;
    border-top: 1px solid #e0e0e0;
    background-color: #ffffff;
    position: sticky;
    bottom: 0;
}

.logout-button {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 0.75rem 1rem;
    background: #dc2626; /* red */
    border: none;
    border-radius: 8px;
    cursor: pointer;
    color: white !important;
    transition: all 0.2s ease;
    font-weight: 500;
}

.logout-button:hover {
    background-color: #b91c1c; /* darker red */
}

.logout-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    color: white !important;
}

.logout-text {
    flex: 1;
    text-align: left;
    color: white !important;
}

/* Simple Menu Styles */
.simple-menu {
    display: flex;
    flex-direction: column;
    padding: 1rem 0;
    overflow-y: auto;
}

.simple-nav-item {
    display: flex;
    align-items: center;
    padding: 0.75rem 1.5rem;
    color: #333;
    text-decoration: none;
    transition: all 0.2s ease;
    border-left: 3px solid transparent;
    margin-bottom: 2px;
}

.simple-nav-item:hover {
    background-color: rgba(22, 101, 52, 0.05);
    color: #166534;
}

.simple-nav-item.active {
    background-color: rgba(22, 101, 52, 0.1);
    color: #166534;
    font-weight: 500;
    border-left: 3px solid #166534;
}

.simple-nav-item-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    width: 20px;
    height: 20px;
    color: inherit;
}

.simple-nav-item-text {
    font-size: 1rem;
}

/* Mobile Toggle Button */
.mobile-sidebar-toggle {
    position: fixed;
    top: 20px;
    left: 20px;
    z-index: 1001;
    background: #166534;
    color: white;
    border: none;
    border-radius: 8px;
    padding: 12px;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
    display: none;
}

.mobile-sidebar-toggle:hover {
    background: #15803d;
    transform: scale(1.05);
}

/* Desktop Toggle Button */
.desktop-sidebar-toggle {
    position: absolute;
    top: 15px;
    right: 15px;
    background: #166534;
    color: white;
    border: none;
    border-radius: 8px;
    padding: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 10;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    font-size: 14px;
}

.desktop-sidebar-toggle:hover {
    background: #15803d;
    transform: scale(1.05);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.desktop-sidebar-toggle:active {
    transform: scale(0.95);
}

/* Collapsed Sidebar Styles */
.admin-sidebar.collapsed {
    width: 80px;
    min-width: 80px;
}

.admin-sidebar.collapsed .sidebar-header {
    padding: 1rem 0.5rem;
}

.admin-sidebar.collapsed .logo-collapsed {
    color: #166534 !important;
    font-weight: bold !important;
    font-size: 1.5rem !important;
    display: block !important;
}

.admin-sidebar.collapsed .simple-nav-item {
    padding: 0.75rem 0.5rem;
    justify-content: center;
    position: relative;
}

.admin-sidebar.collapsed .simple-nav-item-icon {
    margin-right: 0;
}

.admin-sidebar.collapsed .logout-button {
    justify-content: center;
    padding: 0.75rem 0.5rem;
}

.admin-sidebar.collapsed .logout-icon {
    margin-right: 0;
}

/* Tooltip for collapsed items */
.admin-sidebar.collapsed .simple-nav-item:hover::after {
    content: attr(title);
    position: absolute;
    left: 100%;
    top: 50%;
    transform: translateY(-50%);
    background: #333;
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 0.875rem;
    white-space: nowrap;
    z-index: 1000;
    margin-left: 10px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.admin-sidebar.collapsed .simple-nav-item:hover::before {
    content: '';
    position: absolute;
    left: 100%;
    top: 50%;
    transform: translateY(-50%);
    border: 6px solid transparent;
    border-right-color: #333;
    margin-left: 4px;
    z-index: 1000;
}

/* Mobile Responsive Styles */
@media (max-width: 1024px) {
    .mobile-sidebar-toggle {
        display: block;
    }

    .admin-sidebar.mobile {
        position: fixed;
        z-index: 1000;
        transform: translateX(-100%);
        transition: transform 0.3s ease;
        width: 280px;
        min-width: 280px;
    }

    .admin-sidebar.mobile:not(.collapsed) {
        transform: translateX(0);
    }

    .admin-sidebar.mobile.collapsed {
        transform: translateX(-100%);
    }

    .admin-sidebar.mobile .desktop-sidebar-toggle {
        display: none;
    }

    /* Overlay for mobile */
    .admin-sidebar.mobile:not(.collapsed)::before {
        content: '';
        position: fixed;
        top: 0;
        left: 280px;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        z-index: -1;
    }
}

/* Adjust main content for collapsed sidebar */
@media (min-width: 1025px) {
    .admin-sidebar.collapsed ~ .main-wrapper {
        margin-left: 80px;
    }
}

/* Responsive styles for smaller screens */
@media (max-width: 768px) {
    .mobile-sidebar-toggle {
        top: 15px;
        left: 15px;
        padding: 10px;
    }

    .admin-sidebar.mobile {
        width: 260px;
        min-width: 260px;
    }
}
