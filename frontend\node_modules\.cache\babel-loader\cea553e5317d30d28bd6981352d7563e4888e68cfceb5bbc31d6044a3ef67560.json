{"ast": null, "code": "var _jsxFileName = \"C:\\\\MAMP\\\\htdocs\\\\FanBet247\\\\frontend\\\\src\\\\pages\\\\SecuritySettings.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { FaShieldAlt, FaCheck, FaTimes, FaSave, FaKey, FaUserShield, FaExclamationTriangle, FaInfoCircle } from 'react-icons/fa';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst API_BASE_URL = '/backend';\nfunction SecuritySettings() {\n  _s();\n  const [settings, setSettings] = useState({\n    enable_2fa: 'false',\n    allowed_auth_methods: 'email_otp,google_auth',\n    otp_expiry_time: '300',\n    max_otp_attempts: '3',\n    lockout_time: '1800',\n    password_min_length: '8',\n    require_special_chars: 'true',\n    session_timeout: '3600',\n    max_login_attempts: '5'\n  });\n\n  // Admin authentication settings\n  const [adminAuthSettings, setAdminAuthSettings] = useState({\n    admin_auth_method: 'password_only',\n    admin_otp_enabled: 'false',\n    admin_2fa_enabled: 'false',\n    admin_otp_expiry_time: '300',\n    admin_max_otp_attempts: '3',\n    admin_max_login_attempts: '5',\n    admin_lockout_time: '1800',\n    admin_require_2fa_for: 'login,password_change',\n    admin_backup_codes_count: '10',\n    admin_session_timeout: '3600'\n  });\n  const [loading, setLoading] = useState(true);\n  const [saving, setSaving] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  const [adminAuthAvailable, setAdminAuthAvailable] = useState(false);\n  const [smtpConfigured, setSmtpConfigured] = useState(false);\n  const [adminStats, setAdminStats] = useState({});\n  useEffect(() => {\n    fetchSettings();\n  }, []);\n  const fetchSettings = async () => {\n    try {\n      setLoading(true);\n\n      // Fetch regular security settings\n      const response = await axios.get(`${API_BASE_URL}/handlers/get_security_settings.php`);\n      if (response.data.success && response.data.settings) {\n        const settingsData = {};\n        Object.keys(response.data.settings).forEach(key => {\n          settingsData[key] = response.data.settings[key].value;\n        });\n        setSettings(settingsData);\n\n        // Check if admin auth settings are available\n        if (response.data.admin_auth_available && response.data.admin_auth_settings) {\n          const adminSettingsData = {};\n          Object.keys(response.data.admin_auth_settings).forEach(key => {\n            adminSettingsData[key] = response.data.admin_auth_settings[key].value;\n          });\n          setAdminAuthSettings(adminSettingsData);\n          setAdminAuthAvailable(true);\n        } else {\n          // Try to fetch admin auth settings separately\n          try {\n            const adminResponse = await axios.get(`${API_BASE_URL}/handlers/get_admin_auth_settings.php`);\n            if (adminResponse.data.success) {\n              setAdminAuthSettings(adminResponse.data.settings);\n              setAdminAuthAvailable(adminResponse.data.table_exists);\n              setSmtpConfigured(adminResponse.data.smtp_configured);\n              setAdminStats(adminResponse.data.admin_stats || {});\n            }\n          } catch (adminErr) {\n            console.log('Admin auth settings not available yet');\n            setAdminAuthAvailable(false);\n          }\n        }\n      }\n    } catch (err) {\n      setError('Failed to load security settings');\n      console.error('Error fetching settings:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleInputChange = e => {\n    const {\n      name,\n      value,\n      type,\n      checked\n    } = e.target;\n    setSettings(prev => ({\n      ...prev,\n      [name]: type === 'checkbox' ? checked ? 'true' : 'false' : value\n    }));\n  };\n  const handleAdminAuthChange = e => {\n    const {\n      name,\n      value,\n      type,\n      checked\n    } = e.target;\n    setAdminAuthSettings(prev => ({\n      ...prev,\n      [name]: type === 'checkbox' ? checked ? 'true' : 'false' : value\n    }));\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setError('');\n    setSuccess('');\n    try {\n      setSaving(true);\n\n      // Prepare data for submission\n      const submitData = {\n        settings: settings\n      };\n\n      // Include admin auth settings if available\n      if (adminAuthAvailable) {\n        submitData.admin_auth_settings = adminAuthSettings;\n      }\n      const response = await axios.post(`${API_BASE_URL}/handlers/update_security_settings.php`, submitData);\n      if (response.data.success) {\n        setSuccess('Security settings saved successfully!');\n\n        // Show SMTP warning if OTP is enabled but SMTP not configured\n        if (response.data.smtp_warning) {\n          setError(response.data.smtp_warning);\n        }\n        setTimeout(() => {\n          setSuccess('');\n          setError('');\n        }, 5000);\n      } else {\n        setError(response.data.message || 'Failed to save security settings');\n      }\n    } catch (err) {\n      setError('Failed to save security settings');\n      console.error('Error saving settings:', err);\n    } finally {\n      setSaving(false);\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-center h-64\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-lg text-gray-600\",\n          children: \"Loading security settings...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 154,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 153,\n      columnNumber: 13\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"p-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-2xl font-bold text-gray-800 flex items-center gap-3\",\n        children: [/*#__PURE__*/_jsxDEV(FaShieldAlt, {\n          className: \"text-blue-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 21\n        }, this), \"Security Settings\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 165,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-600 mt-2\",\n        children: \"Configure two-factor authentication (2FA) options and security features for both users and administrators.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 169,\n        columnNumber: 17\n      }, this), !adminAuthAvailable && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-4 bg-blue-50 border border-blue-200 rounded-lg p-4 flex items-start gap-3\",\n        children: [/*#__PURE__*/_jsxDEV(FaInfoCircle, {\n          className: \"text-blue-500 mt-0.5\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-blue-800 font-medium\",\n            children: \"Enhanced Admin Authentication Available\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-blue-700 text-sm mt-1\",\n            children: \"To enable advanced admin authentication features (OTP & 2FA), please run the database setup first.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 173,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 164,\n      columnNumber: 13\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-6 bg-red-50 border border-red-200 rounded-lg p-4 flex items-center gap-3\",\n      children: [/*#__PURE__*/_jsxDEV(FaTimes, {\n        className: \"text-red-500\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 188,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"text-red-700\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 189,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 187,\n      columnNumber: 17\n    }, this), success && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-6 bg-green-50 border border-green-200 rounded-lg p-4 flex items-center gap-3\",\n      children: [/*#__PURE__*/_jsxDEV(FaCheck, {\n        className: \"text-green-500\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 195,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"text-green-700\",\n        children: success\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 196,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 194,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg shadow-sm border\",\n      children: /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        className: \"p-6 space-y-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-lg font-semibold text-gray-800 mb-4\",\n            children: \"Two-Factor Authentication\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                id: \"enable_2fa\",\n                name: \"enable_2fa\",\n                checked: settings.enable_2fa === 'true',\n                onChange: handleInputChange,\n                className: \"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 208,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"enable_2fa\",\n                className: \"ml-2 block text-sm text-gray-900\",\n                children: \"Enable Two-Factor Authentication for users\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 216,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Allowed Authentication Methods\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 222,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                name: \"allowed_auth_methods\",\n                value: settings.allowed_auth_methods,\n                onChange: handleInputChange,\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                placeholder: \"email_otp,google_auth\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 225,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-gray-500 mt-1\",\n                children: \"Comma-separated list of allowed methods\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 221,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-lg font-semibold text-gray-800 mb-4\",\n            children: \"OTP Settings\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"OTP Expiry Time (seconds)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 243,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                name: \"otp_expiry_time\",\n                value: settings.otp_expiry_time,\n                onChange: handleInputChange,\n                min: \"60\",\n                max: \"3600\",\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 246,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Max OTP Attempts\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 258,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                name: \"max_otp_attempts\",\n                value: settings.max_otp_attempts,\n                onChange: handleInputChange,\n                min: \"1\",\n                max: \"10\",\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 261,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 257,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-lg font-semibold text-gray-800 mb-4\",\n            children: \"Password Security\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 276,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Minimum Password Length\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 279,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                name: \"password_min_length\",\n                value: settings.password_min_length,\n                onChange: handleInputChange,\n                min: \"6\",\n                max: \"50\",\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 282,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 278,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                id: \"require_special_chars\",\n                name: \"require_special_chars\",\n                checked: settings.require_special_chars === 'true',\n                onChange: handleInputChange,\n                className: \"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 294,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"require_special_chars\",\n                className: \"ml-2 block text-sm text-gray-900\",\n                children: \"Require special characters in passwords\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 302,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 293,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 275,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-lg font-semibold text-gray-800 mb-4\",\n            children: \"Login Security\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 311,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Max Login Attempts\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 314,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                name: \"max_login_attempts\",\n                value: settings.max_login_attempts,\n                onChange: handleInputChange,\n                min: \"1\",\n                max: \"20\",\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 317,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 313,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Lockout Time (seconds)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 329,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                name: \"lockout_time\",\n                value: settings.lockout_time,\n                onChange: handleInputChange,\n                min: \"300\",\n                max: \"86400\",\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 332,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 328,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Session Timeout (seconds)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 344,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                name: \"session_timeout\",\n                value: settings.session_timeout,\n                onChange: handleInputChange,\n                min: \"300\",\n                max: \"86400\",\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 347,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 343,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 312,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 310,\n          columnNumber: 21\n        }, this), adminAuthAvailable && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"border-t pt-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-3 mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(FaUserShield, {\n              className: \"text-red-500 text-xl\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 364,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-lg font-semibold text-gray-800\",\n                children: \"Admin Authentication Security\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 366,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600\",\n                children: \"Configure enhanced authentication methods for admin accounts\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 367,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 365,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 363,\n            columnNumber: 29\n          }, this), !smtpConfigured && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-6 bg-yellow-50 border border-yellow-200 rounded-lg p-4 flex items-start gap-3\",\n            children: [/*#__PURE__*/_jsxDEV(FaExclamationTriangle, {\n              className: \"text-yellow-500 mt-0.5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 374,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-yellow-800 font-medium\",\n                children: \"SMTP Configuration Required\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 376,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-yellow-700 text-sm mt-1\",\n                children: \"OTP authentication requires SMTP to be configured. Please set up email settings first.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 377,\n                columnNumber: 41\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 375,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 373,\n            columnNumber: 33\n          }, this), adminStats.total_admins && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-6 bg-blue-50 border border-blue-200 rounded-lg p-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center gap-2 mb-2\",\n              children: [/*#__PURE__*/_jsxDEV(FaInfoCircle, {\n                className: \"text-blue-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 388,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-medium text-blue-800\",\n                children: \"Current Admin Status\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 389,\n                columnNumber: 41\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 387,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-blue-600\",\n                  children: \"Total Admins:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 393,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"ml-2 font-medium\",\n                  children: adminStats.total_admins\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 394,\n                  columnNumber: 45\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 392,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-blue-600\",\n                  children: \"Password Only:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 397,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"ml-2 font-medium\",\n                  children: adminStats.password_only || 0\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 398,\n                  columnNumber: 45\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 396,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-blue-600\",\n                  children: \"OTP Enabled:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 401,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"ml-2 font-medium\",\n                  children: adminStats.otp_enabled || 0\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 402,\n                  columnNumber: 45\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 400,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-blue-600\",\n                  children: \"2FA Enabled:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 405,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"ml-2 font-medium\",\n                  children: adminStats.tfa_enabled || 0\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 406,\n                  columnNumber: 45\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 404,\n                columnNumber: 41\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 391,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 386,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-md font-medium text-gray-800 mb-4\",\n                children: \"Authentication Methods\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 415,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"checkbox\",\n                    id: \"admin_otp_enabled\",\n                    name: \"admin_otp_enabled\",\n                    checked: adminAuthSettings.admin_otp_enabled === 'true',\n                    onChange: handleAdminAuthChange,\n                    disabled: !smtpConfigured,\n                    className: \"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded disabled:opacity-50\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 418,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                    htmlFor: \"admin_otp_enabled\",\n                    className: \"ml-2 block text-sm text-gray-900\",\n                    children: [\"Enable OTP (One-Time Password) via Email\", !smtpConfigured && /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-red-500 ml-1\",\n                      children: \"(Requires SMTP)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 429,\n                      columnNumber: 69\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 427,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 417,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"checkbox\",\n                    id: \"admin_2fa_enabled\",\n                    name: \"admin_2fa_enabled\",\n                    checked: adminAuthSettings.admin_2fa_enabled === 'true',\n                    onChange: handleAdminAuthChange,\n                    className: \"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 434,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                    htmlFor: \"admin_2fa_enabled\",\n                    className: \"ml-2 block text-sm text-gray-900\",\n                    children: \"Enable 2FA (Two-Factor Authentication) via Google Authenticator\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 442,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 433,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 416,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 414,\n              columnNumber: 33\n            }, this), adminAuthSettings.admin_otp_enabled === 'true' && /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-md font-medium text-gray-800 mb-4\",\n                children: \"OTP Configuration\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 452,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                    children: \"OTP Expiry Time (seconds)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 455,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"number\",\n                    name: \"admin_otp_expiry_time\",\n                    value: adminAuthSettings.admin_otp_expiry_time,\n                    onChange: handleAdminAuthChange,\n                    min: \"60\",\n                    max: \"3600\",\n                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 458,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-gray-500 mt-1\",\n                    children: \"Default: 300 seconds (5 minutes)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 467,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 454,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                    children: \"Max OTP Attempts\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 471,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"number\",\n                    name: \"admin_max_otp_attempts\",\n                    value: adminAuthSettings.admin_max_otp_attempts,\n                    onChange: handleAdminAuthChange,\n                    min: \"1\",\n                    max: \"10\",\n                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 474,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-gray-500 mt-1\",\n                    children: \"Attempts before account lockout\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 483,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 470,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                    children: \"Lockout Time (seconds)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 487,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"number\",\n                    name: \"admin_lockout_time\",\n                    value: adminAuthSettings.admin_lockout_time,\n                    onChange: handleAdminAuthChange,\n                    min: \"300\",\n                    max: \"86400\",\n                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 490,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-gray-500 mt-1\",\n                    children: \"Default: 1800 seconds (30 minutes)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 499,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 486,\n                  columnNumber: 45\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 453,\n                columnNumber: 41\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 451,\n              columnNumber: 37\n            }, this), adminAuthSettings.admin_2fa_enabled === 'true' && /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-md font-medium text-gray-800 mb-4\",\n                children: \"2FA Configuration\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 508,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                    children: \"Backup Codes Count\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 511,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"number\",\n                    name: \"admin_backup_codes_count\",\n                    value: adminAuthSettings.admin_backup_codes_count,\n                    onChange: handleAdminAuthChange,\n                    min: \"5\",\n                    max: \"20\",\n                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 514,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-gray-500 mt-1\",\n                    children: \"Number of backup codes to generate\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 523,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 510,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                    children: \"Require 2FA For\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 527,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    name: \"admin_require_2fa_for\",\n                    value: adminAuthSettings.admin_require_2fa_for,\n                    onChange: handleAdminAuthChange,\n                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                    placeholder: \"login,password_change\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 530,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-gray-500 mt-1\",\n                    children: \"Comma-separated list of actions\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 538,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 526,\n                  columnNumber: 45\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 509,\n                columnNumber: 41\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 507,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-md font-medium text-gray-800 mb-4\",\n                children: \"General Admin Security\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 546,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                    children: \"Max Login Attempts\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 549,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"number\",\n                    name: \"admin_max_login_attempts\",\n                    value: adminAuthSettings.admin_max_login_attempts,\n                    onChange: handleAdminAuthChange,\n                    min: \"1\",\n                    max: \"20\",\n                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 552,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-gray-500 mt-1\",\n                    children: \"Failed attempts before lockout\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 561,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 548,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                    children: \"Session Timeout (seconds)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 565,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"number\",\n                    name: \"admin_session_timeout\",\n                    value: adminAuthSettings.admin_session_timeout,\n                    onChange: handleAdminAuthChange,\n                    min: \"300\",\n                    max: \"86400\",\n                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 568,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-gray-500 mt-1\",\n                    children: \"Default: 3600 seconds (1 hour)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 577,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 564,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 547,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 545,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gray-50 rounded-lg p-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-md font-medium text-gray-800 mb-3 flex items-center gap-2\",\n                children: [/*#__PURE__*/_jsxDEV(FaKey, {\n                  className: \"text-gray-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 585,\n                  columnNumber: 41\n                }, this), \"Setup Instructions\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 584,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-2 text-sm text-gray-700\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"For OTP Authentication:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 589,\n                    columnNumber: 44\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 589,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                  className: \"list-disc list-inside ml-4 space-y-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                    children: \"Ensure SMTP settings are configured in Email Settings\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 591,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    children: \"Enable OTP authentication above\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 592,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    children: \"Admins will receive OTP codes via email during login\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 593,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 590,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"mt-3\",\n                  children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"For 2FA Authentication:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 596,\n                    columnNumber: 61\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 596,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                  className: \"list-disc list-inside ml-4 space-y-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                    children: \"Enable 2FA authentication above\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 598,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    children: \"Each admin must set up Google Authenticator individually\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 599,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    children: \"Admins can access 2FA setup from their profile settings\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 600,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    children: \"Backup codes will be generated for account recovery\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 601,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 597,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"mt-3 text-yellow-700 bg-yellow-50 p-2 rounded\",\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Important:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 605,\n                    columnNumber: 45\n                  }, this), \" Test the authentication methods thoroughly before enforcing them for all admins.\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 604,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 588,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 583,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 413,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 362,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-end pt-6 border-t\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            disabled: saving,\n            className: \"flex items-center gap-2 px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n            children: [/*#__PURE__*/_jsxDEV(FaSave, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 620,\n              columnNumber: 29\n            }, this), saving ? 'Saving...' : 'Save Security Settings']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 615,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 614,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 202,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 201,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 162,\n    columnNumber: 9\n  }, this);\n}\n_s(SecuritySettings, \"5X8WKQH2TEbUhD71Ev9yed7+0Lo=\");\n_c = SecuritySettings;\nexport default SecuritySettings;\nvar _c;\n$RefreshReg$(_c, \"SecuritySettings\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "FaShieldAlt", "FaCheck", "FaTimes", "FaSave", "FaKey", "FaUserShield", "FaExclamationTriangle", "FaInfoCircle", "jsxDEV", "_jsxDEV", "API_BASE_URL", "SecuritySettings", "_s", "settings", "setSettings", "enable_2fa", "allowed_auth_methods", "otp_expiry_time", "max_otp_attempts", "lockout_time", "password_min_length", "require_special_chars", "session_timeout", "max_login_attempts", "adminAuthSettings", "setAdminAuthSettings", "admin_auth_method", "admin_otp_enabled", "admin_2fa_enabled", "admin_otp_expiry_time", "admin_max_otp_attempts", "admin_max_login_attempts", "admin_lockout_time", "admin_require_2fa_for", "admin_backup_codes_count", "admin_session_timeout", "loading", "setLoading", "saving", "setSaving", "error", "setError", "success", "setSuccess", "adminAuthAvailable", "setAdminAuthAvailable", "smtpConfigured", "setSmtpConfigured", "adminStats", "setAdminStats", "fetchSettings", "response", "get", "data", "settingsData", "Object", "keys", "for<PERSON>ach", "key", "value", "admin_auth_available", "admin_auth_settings", "adminSettingsData", "adminResponse", "table_exists", "smtp_configured", "admin_stats", "adminErr", "console", "log", "err", "handleInputChange", "e", "name", "type", "checked", "target", "prev", "handleAdminAuthChange", "handleSubmit", "preventDefault", "submitData", "post", "smtp_warning", "setTimeout", "message", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "id", "onChange", "htmlFor", "placeholder", "min", "max", "total_admins", "password_only", "otp_enabled", "tfa_enabled", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/pages/SecuritySettings.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { FaShieldAlt, FaCheck, FaTimes, FaSave, FaKey, FaUserShield, FaExclamationTriangle, FaInfoCircle } from 'react-icons/fa';\n\nconst API_BASE_URL = '/backend';\n\nfunction SecuritySettings() {\n    const [settings, setSettings] = useState({\n        enable_2fa: 'false',\n        allowed_auth_methods: 'email_otp,google_auth',\n        otp_expiry_time: '300',\n        max_otp_attempts: '3',\n        lockout_time: '1800',\n        password_min_length: '8',\n        require_special_chars: 'true',\n        session_timeout: '3600',\n        max_login_attempts: '5'\n    });\n\n    // Admin authentication settings\n    const [adminAuthSettings, setAdminAuthSettings] = useState({\n        admin_auth_method: 'password_only',\n        admin_otp_enabled: 'false',\n        admin_2fa_enabled: 'false',\n        admin_otp_expiry_time: '300',\n        admin_max_otp_attempts: '3',\n        admin_max_login_attempts: '5',\n        admin_lockout_time: '1800',\n        admin_require_2fa_for: 'login,password_change',\n        admin_backup_codes_count: '10',\n        admin_session_timeout: '3600'\n    });\n\n    const [loading, setLoading] = useState(true);\n    const [saving, setSaving] = useState(false);\n    const [error, setError] = useState('');\n    const [success, setSuccess] = useState('');\n    const [adminAuthAvailable, setAdminAuthAvailable] = useState(false);\n    const [smtpConfigured, setSmtpConfigured] = useState(false);\n    const [adminStats, setAdminStats] = useState({});\n\n    useEffect(() => {\n        fetchSettings();\n    }, []);\n\n    const fetchSettings = async () => {\n        try {\n            setLoading(true);\n\n            // Fetch regular security settings\n            const response = await axios.get(`${API_BASE_URL}/handlers/get_security_settings.php`);\n\n            if (response.data.success && response.data.settings) {\n                const settingsData = {};\n                Object.keys(response.data.settings).forEach(key => {\n                    settingsData[key] = response.data.settings[key].value;\n                });\n                setSettings(settingsData);\n\n                // Check if admin auth settings are available\n                if (response.data.admin_auth_available && response.data.admin_auth_settings) {\n                    const adminSettingsData = {};\n                    Object.keys(response.data.admin_auth_settings).forEach(key => {\n                        adminSettingsData[key] = response.data.admin_auth_settings[key].value;\n                    });\n                    setAdminAuthSettings(adminSettingsData);\n                    setAdminAuthAvailable(true);\n                } else {\n                    // Try to fetch admin auth settings separately\n                    try {\n                        const adminResponse = await axios.get(`${API_BASE_URL}/handlers/get_admin_auth_settings.php`);\n                        if (adminResponse.data.success) {\n                            setAdminAuthSettings(adminResponse.data.settings);\n                            setAdminAuthAvailable(adminResponse.data.table_exists);\n                            setSmtpConfigured(adminResponse.data.smtp_configured);\n                            setAdminStats(adminResponse.data.admin_stats || {});\n                        }\n                    } catch (adminErr) {\n                        console.log('Admin auth settings not available yet');\n                        setAdminAuthAvailable(false);\n                    }\n                }\n            }\n        } catch (err) {\n            setError('Failed to load security settings');\n            console.error('Error fetching settings:', err);\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const handleInputChange = (e) => {\n        const { name, value, type, checked } = e.target;\n        setSettings(prev => ({\n            ...prev,\n            [name]: type === 'checkbox' ? (checked ? 'true' : 'false') : value\n        }));\n    };\n\n    const handleAdminAuthChange = (e) => {\n        const { name, value, type, checked } = e.target;\n        setAdminAuthSettings(prev => ({\n            ...prev,\n            [name]: type === 'checkbox' ? (checked ? 'true' : 'false') : value\n        }));\n    };\n\n    const handleSubmit = async (e) => {\n        e.preventDefault();\n        setError('');\n        setSuccess('');\n\n        try {\n            setSaving(true);\n\n            // Prepare data for submission\n            const submitData = {\n                settings: settings\n            };\n\n            // Include admin auth settings if available\n            if (adminAuthAvailable) {\n                submitData.admin_auth_settings = adminAuthSettings;\n            }\n\n            const response = await axios.post(`${API_BASE_URL}/handlers/update_security_settings.php`, submitData);\n\n            if (response.data.success) {\n                setSuccess('Security settings saved successfully!');\n\n                // Show SMTP warning if OTP is enabled but SMTP not configured\n                if (response.data.smtp_warning) {\n                    setError(response.data.smtp_warning);\n                }\n\n                setTimeout(() => {\n                    setSuccess('');\n                    setError('');\n                }, 5000);\n            } else {\n                setError(response.data.message || 'Failed to save security settings');\n            }\n        } catch (err) {\n            setError('Failed to save security settings');\n            console.error('Error saving settings:', err);\n        } finally {\n            setSaving(false);\n        }\n    };\n\n    if (loading) {\n        return (\n            <div className=\"p-6\">\n                <div className=\"flex items-center justify-center h-64\">\n                    <div className=\"text-lg text-gray-600\">Loading security settings...</div>\n                </div>\n            </div>\n        );\n    }\n\n    return (\n        <div className=\"p-6\">\n            {/* Header */}\n            <div className=\"mb-8\">\n                <h1 className=\"text-2xl font-bold text-gray-800 flex items-center gap-3\">\n                    <FaShieldAlt className=\"text-blue-500\" />\n                    Security Settings\n                </h1>\n                <p className=\"text-gray-600 mt-2\">\n                    Configure two-factor authentication (2FA) options and security features for both users and administrators.\n                </p>\n                {!adminAuthAvailable && (\n                    <div className=\"mt-4 bg-blue-50 border border-blue-200 rounded-lg p-4 flex items-start gap-3\">\n                        <FaInfoCircle className=\"text-blue-500 mt-0.5\" />\n                        <div>\n                            <p className=\"text-blue-800 font-medium\">Enhanced Admin Authentication Available</p>\n                            <p className=\"text-blue-700 text-sm mt-1\">\n                                To enable advanced admin authentication features (OTP & 2FA), please run the database setup first.\n                            </p>\n                        </div>\n                    </div>\n                )}\n            </div>\n\n            {/* Alerts */}\n            {error && (\n                <div className=\"mb-6 bg-red-50 border border-red-200 rounded-lg p-4 flex items-center gap-3\">\n                    <FaTimes className=\"text-red-500\" />\n                    <span className=\"text-red-700\">{error}</span>\n                </div>\n            )}\n\n            {success && (\n                <div className=\"mb-6 bg-green-50 border border-green-200 rounded-lg p-4 flex items-center gap-3\">\n                    <FaCheck className=\"text-green-500\" />\n                    <span className=\"text-green-700\">{success}</span>\n                </div>\n            )}\n\n            {/* Settings Form */}\n            <div className=\"bg-white rounded-lg shadow-sm border\">\n                <form onSubmit={handleSubmit} className=\"p-6 space-y-6\">\n                    {/* Two-Factor Authentication */}\n                    <div>\n                        <h2 className=\"text-lg font-semibold text-gray-800 mb-4\">Two-Factor Authentication</h2>\n                        <div className=\"space-y-4\">\n                            <div className=\"flex items-center\">\n                                <input\n                                    type=\"checkbox\"\n                                    id=\"enable_2fa\"\n                                    name=\"enable_2fa\"\n                                    checked={settings.enable_2fa === 'true'}\n                                    onChange={handleInputChange}\n                                    className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                                />\n                                <label htmlFor=\"enable_2fa\" className=\"ml-2 block text-sm text-gray-900\">\n                                    Enable Two-Factor Authentication for users\n                                </label>\n                            </div>\n                            \n                            <div>\n                                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                                    Allowed Authentication Methods\n                                </label>\n                                <input\n                                    type=\"text\"\n                                    name=\"allowed_auth_methods\"\n                                    value={settings.allowed_auth_methods}\n                                    onChange={handleInputChange}\n                                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                                    placeholder=\"email_otp,google_auth\"\n                                />\n                                <p className=\"text-xs text-gray-500 mt-1\">Comma-separated list of allowed methods</p>\n                            </div>\n                        </div>\n                    </div>\n\n                    {/* OTP Settings */}\n                    <div>\n                        <h2 className=\"text-lg font-semibold text-gray-800 mb-4\">OTP Settings</h2>\n                        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                            <div>\n                                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                                    OTP Expiry Time (seconds)\n                                </label>\n                                <input\n                                    type=\"number\"\n                                    name=\"otp_expiry_time\"\n                                    value={settings.otp_expiry_time}\n                                    onChange={handleInputChange}\n                                    min=\"60\"\n                                    max=\"3600\"\n                                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                                />\n                            </div>\n                            \n                            <div>\n                                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                                    Max OTP Attempts\n                                </label>\n                                <input\n                                    type=\"number\"\n                                    name=\"max_otp_attempts\"\n                                    value={settings.max_otp_attempts}\n                                    onChange={handleInputChange}\n                                    min=\"1\"\n                                    max=\"10\"\n                                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                                />\n                            </div>\n                        </div>\n                    </div>\n\n                    {/* Password Security */}\n                    <div>\n                        <h2 className=\"text-lg font-semibold text-gray-800 mb-4\">Password Security</h2>\n                        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                            <div>\n                                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                                    Minimum Password Length\n                                </label>\n                                <input\n                                    type=\"number\"\n                                    name=\"password_min_length\"\n                                    value={settings.password_min_length}\n                                    onChange={handleInputChange}\n                                    min=\"6\"\n                                    max=\"50\"\n                                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                                />\n                            </div>\n                            \n                            <div className=\"flex items-center\">\n                                <input\n                                    type=\"checkbox\"\n                                    id=\"require_special_chars\"\n                                    name=\"require_special_chars\"\n                                    checked={settings.require_special_chars === 'true'}\n                                    onChange={handleInputChange}\n                                    className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                                />\n                                <label htmlFor=\"require_special_chars\" className=\"ml-2 block text-sm text-gray-900\">\n                                    Require special characters in passwords\n                                </label>\n                            </div>\n                        </div>\n                    </div>\n\n                    {/* Login Security */}\n                    <div>\n                        <h2 className=\"text-lg font-semibold text-gray-800 mb-4\">Login Security</h2>\n                        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n                            <div>\n                                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                                    Max Login Attempts\n                                </label>\n                                <input\n                                    type=\"number\"\n                                    name=\"max_login_attempts\"\n                                    value={settings.max_login_attempts}\n                                    onChange={handleInputChange}\n                                    min=\"1\"\n                                    max=\"20\"\n                                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                                />\n                            </div>\n\n                            <div>\n                                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                                    Lockout Time (seconds)\n                                </label>\n                                <input\n                                    type=\"number\"\n                                    name=\"lockout_time\"\n                                    value={settings.lockout_time}\n                                    onChange={handleInputChange}\n                                    min=\"300\"\n                                    max=\"86400\"\n                                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                                />\n                            </div>\n\n                            <div>\n                                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                                    Session Timeout (seconds)\n                                </label>\n                                <input\n                                    type=\"number\"\n                                    name=\"session_timeout\"\n                                    value={settings.session_timeout}\n                                    onChange={handleInputChange}\n                                    min=\"300\"\n                                    max=\"86400\"\n                                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                                />\n                            </div>\n                        </div>\n                    </div>\n\n                    {/* Admin Authentication Security */}\n                    {adminAuthAvailable && (\n                        <div className=\"border-t pt-6\">\n                            <div className=\"flex items-center gap-3 mb-6\">\n                                <FaUserShield className=\"text-red-500 text-xl\" />\n                                <div>\n                                    <h2 className=\"text-lg font-semibold text-gray-800\">Admin Authentication Security</h2>\n                                    <p className=\"text-sm text-gray-600\">Configure enhanced authentication methods for admin accounts</p>\n                                </div>\n                            </div>\n\n                            {/* SMTP Warning */}\n                            {!smtpConfigured && (\n                                <div className=\"mb-6 bg-yellow-50 border border-yellow-200 rounded-lg p-4 flex items-start gap-3\">\n                                    <FaExclamationTriangle className=\"text-yellow-500 mt-0.5\" />\n                                    <div>\n                                        <p className=\"text-yellow-800 font-medium\">SMTP Configuration Required</p>\n                                        <p className=\"text-yellow-700 text-sm mt-1\">\n                                            OTP authentication requires SMTP to be configured. Please set up email settings first.\n                                        </p>\n                                    </div>\n                                </div>\n                            )}\n\n                            {/* Admin Stats */}\n                            {adminStats.total_admins && (\n                                <div className=\"mb-6 bg-blue-50 border border-blue-200 rounded-lg p-4\">\n                                    <div className=\"flex items-center gap-2 mb-2\">\n                                        <FaInfoCircle className=\"text-blue-500\" />\n                                        <span className=\"font-medium text-blue-800\">Current Admin Status</span>\n                                    </div>\n                                    <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm\">\n                                        <div>\n                                            <span className=\"text-blue-600\">Total Admins:</span>\n                                            <span className=\"ml-2 font-medium\">{adminStats.total_admins}</span>\n                                        </div>\n                                        <div>\n                                            <span className=\"text-blue-600\">Password Only:</span>\n                                            <span className=\"ml-2 font-medium\">{adminStats.password_only || 0}</span>\n                                        </div>\n                                        <div>\n                                            <span className=\"text-blue-600\">OTP Enabled:</span>\n                                            <span className=\"ml-2 font-medium\">{adminStats.otp_enabled || 0}</span>\n                                        </div>\n                                        <div>\n                                            <span className=\"text-blue-600\">2FA Enabled:</span>\n                                            <span className=\"ml-2 font-medium\">{adminStats.tfa_enabled || 0}</span>\n                                        </div>\n                                    </div>\n                                </div>\n                            )}\n\n                            {/* Authentication Method Selection */}\n                            <div className=\"space-y-6\">\n                                <div>\n                                    <h3 className=\"text-md font-medium text-gray-800 mb-4\">Authentication Methods</h3>\n                                    <div className=\"space-y-4\">\n                                        <div className=\"flex items-center\">\n                                            <input\n                                                type=\"checkbox\"\n                                                id=\"admin_otp_enabled\"\n                                                name=\"admin_otp_enabled\"\n                                                checked={adminAuthSettings.admin_otp_enabled === 'true'}\n                                                onChange={handleAdminAuthChange}\n                                                disabled={!smtpConfigured}\n                                                className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded disabled:opacity-50\"\n                                            />\n                                            <label htmlFor=\"admin_otp_enabled\" className=\"ml-2 block text-sm text-gray-900\">\n                                                Enable OTP (One-Time Password) via Email\n                                                {!smtpConfigured && <span className=\"text-red-500 ml-1\">(Requires SMTP)</span>}\n                                            </label>\n                                        </div>\n\n                                        <div className=\"flex items-center\">\n                                            <input\n                                                type=\"checkbox\"\n                                                id=\"admin_2fa_enabled\"\n                                                name=\"admin_2fa_enabled\"\n                                                checked={adminAuthSettings.admin_2fa_enabled === 'true'}\n                                                onChange={handleAdminAuthChange}\n                                                className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                                            />\n                                            <label htmlFor=\"admin_2fa_enabled\" className=\"ml-2 block text-sm text-gray-900\">\n                                                Enable 2FA (Two-Factor Authentication) via Google Authenticator\n                                            </label>\n                                        </div>\n                                    </div>\n                                </div>\n\n                                {/* OTP Configuration */}\n                                {adminAuthSettings.admin_otp_enabled === 'true' && (\n                                    <div>\n                                        <h3 className=\"text-md font-medium text-gray-800 mb-4\">OTP Configuration</h3>\n                                        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n                                            <div>\n                                                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                                                    OTP Expiry Time (seconds)\n                                                </label>\n                                                <input\n                                                    type=\"number\"\n                                                    name=\"admin_otp_expiry_time\"\n                                                    value={adminAuthSettings.admin_otp_expiry_time}\n                                                    onChange={handleAdminAuthChange}\n                                                    min=\"60\"\n                                                    max=\"3600\"\n                                                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                                                />\n                                                <p className=\"text-xs text-gray-500 mt-1\">Default: 300 seconds (5 minutes)</p>\n                                            </div>\n\n                                            <div>\n                                                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                                                    Max OTP Attempts\n                                                </label>\n                                                <input\n                                                    type=\"number\"\n                                                    name=\"admin_max_otp_attempts\"\n                                                    value={adminAuthSettings.admin_max_otp_attempts}\n                                                    onChange={handleAdminAuthChange}\n                                                    min=\"1\"\n                                                    max=\"10\"\n                                                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                                                />\n                                                <p className=\"text-xs text-gray-500 mt-1\">Attempts before account lockout</p>\n                                            </div>\n\n                                            <div>\n                                                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                                                    Lockout Time (seconds)\n                                                </label>\n                                                <input\n                                                    type=\"number\"\n                                                    name=\"admin_lockout_time\"\n                                                    value={adminAuthSettings.admin_lockout_time}\n                                                    onChange={handleAdminAuthChange}\n                                                    min=\"300\"\n                                                    max=\"86400\"\n                                                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                                                />\n                                                <p className=\"text-xs text-gray-500 mt-1\">Default: 1800 seconds (30 minutes)</p>\n                                            </div>\n                                        </div>\n                                    </div>\n                                )}\n\n                                {/* 2FA Configuration */}\n                                {adminAuthSettings.admin_2fa_enabled === 'true' && (\n                                    <div>\n                                        <h3 className=\"text-md font-medium text-gray-800 mb-4\">2FA Configuration</h3>\n                                        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                                            <div>\n                                                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                                                    Backup Codes Count\n                                                </label>\n                                                <input\n                                                    type=\"number\"\n                                                    name=\"admin_backup_codes_count\"\n                                                    value={adminAuthSettings.admin_backup_codes_count}\n                                                    onChange={handleAdminAuthChange}\n                                                    min=\"5\"\n                                                    max=\"20\"\n                                                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                                                />\n                                                <p className=\"text-xs text-gray-500 mt-1\">Number of backup codes to generate</p>\n                                            </div>\n\n                                            <div>\n                                                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                                                    Require 2FA For\n                                                </label>\n                                                <input\n                                                    type=\"text\"\n                                                    name=\"admin_require_2fa_for\"\n                                                    value={adminAuthSettings.admin_require_2fa_for}\n                                                    onChange={handleAdminAuthChange}\n                                                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                                                    placeholder=\"login,password_change\"\n                                                />\n                                                <p className=\"text-xs text-gray-500 mt-1\">Comma-separated list of actions</p>\n                                            </div>\n                                        </div>\n                                    </div>\n                                )}\n\n                                {/* General Admin Security */}\n                                <div>\n                                    <h3 className=\"text-md font-medium text-gray-800 mb-4\">General Admin Security</h3>\n                                    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                                        <div>\n                                            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                                                Max Login Attempts\n                                            </label>\n                                            <input\n                                                type=\"number\"\n                                                name=\"admin_max_login_attempts\"\n                                                value={adminAuthSettings.admin_max_login_attempts}\n                                                onChange={handleAdminAuthChange}\n                                                min=\"1\"\n                                                max=\"20\"\n                                                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                                            />\n                                            <p className=\"text-xs text-gray-500 mt-1\">Failed attempts before lockout</p>\n                                        </div>\n\n                                        <div>\n                                            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                                                Session Timeout (seconds)\n                                            </label>\n                                            <input\n                                                type=\"number\"\n                                                name=\"admin_session_timeout\"\n                                                value={adminAuthSettings.admin_session_timeout}\n                                                onChange={handleAdminAuthChange}\n                                                min=\"300\"\n                                                max=\"86400\"\n                                                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                                            />\n                                            <p className=\"text-xs text-gray-500 mt-1\">Default: 3600 seconds (1 hour)</p>\n                                        </div>\n                                    </div>\n                                </div>\n\n                                {/* Setup Instructions */}\n                                <div className=\"bg-gray-50 rounded-lg p-4\">\n                                    <h3 className=\"text-md font-medium text-gray-800 mb-3 flex items-center gap-2\">\n                                        <FaKey className=\"text-gray-600\" />\n                                        Setup Instructions\n                                    </h3>\n                                    <div className=\"space-y-2 text-sm text-gray-700\">\n                                        <p><strong>For OTP Authentication:</strong></p>\n                                        <ul className=\"list-disc list-inside ml-4 space-y-1\">\n                                            <li>Ensure SMTP settings are configured in Email Settings</li>\n                                            <li>Enable OTP authentication above</li>\n                                            <li>Admins will receive OTP codes via email during login</li>\n                                        </ul>\n\n                                        <p className=\"mt-3\"><strong>For 2FA Authentication:</strong></p>\n                                        <ul className=\"list-disc list-inside ml-4 space-y-1\">\n                                            <li>Enable 2FA authentication above</li>\n                                            <li>Each admin must set up Google Authenticator individually</li>\n                                            <li>Admins can access 2FA setup from their profile settings</li>\n                                            <li>Backup codes will be generated for account recovery</li>\n                                        </ul>\n\n                                        <p className=\"mt-3 text-yellow-700 bg-yellow-50 p-2 rounded\">\n                                            <strong>Important:</strong> Test the authentication methods thoroughly before enforcing them for all admins.\n                                        </p>\n                                    </div>\n                                </div>\n                            </div>\n                        </div>\n                    )}\n\n                    {/* Submit Button */}\n                    <div className=\"flex justify-end pt-6 border-t\">\n                        <button\n                            type=\"submit\"\n                            disabled={saving}\n                            className=\"flex items-center gap-2 px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n                        >\n                            <FaSave />\n                            {saving ? 'Saving...' : 'Save Security Settings'}\n                        </button>\n                    </div>\n                </form>\n            </div>\n        </div>\n    );\n}\n\nexport default SecuritySettings;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,EAAEC,OAAO,EAAEC,OAAO,EAAEC,MAAM,EAAEC,KAAK,EAAEC,YAAY,EAAEC,qBAAqB,EAAEC,YAAY,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjI,MAAMC,YAAY,GAAG,UAAU;AAE/B,SAASC,gBAAgBA,CAAA,EAAG;EAAAC,EAAA;EACxB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGjB,QAAQ,CAAC;IACrCkB,UAAU,EAAE,OAAO;IACnBC,oBAAoB,EAAE,uBAAuB;IAC7CC,eAAe,EAAE,KAAK;IACtBC,gBAAgB,EAAE,GAAG;IACrBC,YAAY,EAAE,MAAM;IACpBC,mBAAmB,EAAE,GAAG;IACxBC,qBAAqB,EAAE,MAAM;IAC7BC,eAAe,EAAE,MAAM;IACvBC,kBAAkB,EAAE;EACxB,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG5B,QAAQ,CAAC;IACvD6B,iBAAiB,EAAE,eAAe;IAClCC,iBAAiB,EAAE,OAAO;IAC1BC,iBAAiB,EAAE,OAAO;IAC1BC,qBAAqB,EAAE,KAAK;IAC5BC,sBAAsB,EAAE,GAAG;IAC3BC,wBAAwB,EAAE,GAAG;IAC7BC,kBAAkB,EAAE,MAAM;IAC1BC,qBAAqB,EAAE,uBAAuB;IAC9CC,wBAAwB,EAAE,IAAI;IAC9BC,qBAAqB,EAAE;EAC3B,CAAC,CAAC;EAEF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGxC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACyC,MAAM,EAAEC,SAAS,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAAC2C,KAAK,EAAEC,QAAQ,CAAC,GAAG5C,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC6C,OAAO,EAAEC,UAAU,CAAC,GAAG9C,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC+C,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGhD,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACiD,cAAc,EAAEC,iBAAiB,CAAC,GAAGlD,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACmD,UAAU,EAAEC,aAAa,CAAC,GAAGpD,QAAQ,CAAC,CAAC,CAAC,CAAC;EAEhDC,SAAS,CAAC,MAAM;IACZoD,aAAa,CAAC,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,aAAa,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACAb,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAMc,QAAQ,GAAG,MAAMpD,KAAK,CAACqD,GAAG,CAAC,GAAG1C,YAAY,qCAAqC,CAAC;MAEtF,IAAIyC,QAAQ,CAACE,IAAI,CAACX,OAAO,IAAIS,QAAQ,CAACE,IAAI,CAACxC,QAAQ,EAAE;QACjD,MAAMyC,YAAY,GAAG,CAAC,CAAC;QACvBC,MAAM,CAACC,IAAI,CAACL,QAAQ,CAACE,IAAI,CAACxC,QAAQ,CAAC,CAAC4C,OAAO,CAACC,GAAG,IAAI;UAC/CJ,YAAY,CAACI,GAAG,CAAC,GAAGP,QAAQ,CAACE,IAAI,CAACxC,QAAQ,CAAC6C,GAAG,CAAC,CAACC,KAAK;QACzD,CAAC,CAAC;QACF7C,WAAW,CAACwC,YAAY,CAAC;;QAEzB;QACA,IAAIH,QAAQ,CAACE,IAAI,CAACO,oBAAoB,IAAIT,QAAQ,CAACE,IAAI,CAACQ,mBAAmB,EAAE;UACzE,MAAMC,iBAAiB,GAAG,CAAC,CAAC;UAC5BP,MAAM,CAACC,IAAI,CAACL,QAAQ,CAACE,IAAI,CAACQ,mBAAmB,CAAC,CAACJ,OAAO,CAACC,GAAG,IAAI;YAC1DI,iBAAiB,CAACJ,GAAG,CAAC,GAAGP,QAAQ,CAACE,IAAI,CAACQ,mBAAmB,CAACH,GAAG,CAAC,CAACC,KAAK;UACzE,CAAC,CAAC;UACFlC,oBAAoB,CAACqC,iBAAiB,CAAC;UACvCjB,qBAAqB,CAAC,IAAI,CAAC;QAC/B,CAAC,MAAM;UACH;UACA,IAAI;YACA,MAAMkB,aAAa,GAAG,MAAMhE,KAAK,CAACqD,GAAG,CAAC,GAAG1C,YAAY,uCAAuC,CAAC;YAC7F,IAAIqD,aAAa,CAACV,IAAI,CAACX,OAAO,EAAE;cAC5BjB,oBAAoB,CAACsC,aAAa,CAACV,IAAI,CAACxC,QAAQ,CAAC;cACjDgC,qBAAqB,CAACkB,aAAa,CAACV,IAAI,CAACW,YAAY,CAAC;cACtDjB,iBAAiB,CAACgB,aAAa,CAACV,IAAI,CAACY,eAAe,CAAC;cACrDhB,aAAa,CAACc,aAAa,CAACV,IAAI,CAACa,WAAW,IAAI,CAAC,CAAC,CAAC;YACvD;UACJ,CAAC,CAAC,OAAOC,QAAQ,EAAE;YACfC,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;YACpDxB,qBAAqB,CAAC,KAAK,CAAC;UAChC;QACJ;MACJ;IACJ,CAAC,CAAC,OAAOyB,GAAG,EAAE;MACV7B,QAAQ,CAAC,kCAAkC,CAAC;MAC5C2B,OAAO,CAAC5B,KAAK,CAAC,0BAA0B,EAAE8B,GAAG,CAAC;IAClD,CAAC,SAAS;MACNjC,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,MAAMkC,iBAAiB,GAAIC,CAAC,IAAK;IAC7B,MAAM;MAAEC,IAAI;MAAEd,KAAK;MAAEe,IAAI;MAAEC;IAAQ,CAAC,GAAGH,CAAC,CAACI,MAAM;IAC/C9D,WAAW,CAAC+D,IAAI,KAAK;MACjB,GAAGA,IAAI;MACP,CAACJ,IAAI,GAAGC,IAAI,KAAK,UAAU,GAAIC,OAAO,GAAG,MAAM,GAAG,OAAO,GAAIhB;IACjE,CAAC,CAAC,CAAC;EACP,CAAC;EAED,MAAMmB,qBAAqB,GAAIN,CAAC,IAAK;IACjC,MAAM;MAAEC,IAAI;MAAEd,KAAK;MAAEe,IAAI;MAAEC;IAAQ,CAAC,GAAGH,CAAC,CAACI,MAAM;IAC/CnD,oBAAoB,CAACoD,IAAI,KAAK;MAC1B,GAAGA,IAAI;MACP,CAACJ,IAAI,GAAGC,IAAI,KAAK,UAAU,GAAIC,OAAO,GAAG,MAAM,GAAG,OAAO,GAAIhB;IACjE,CAAC,CAAC,CAAC;EACP,CAAC;EAED,MAAMoB,YAAY,GAAG,MAAOP,CAAC,IAAK;IAC9BA,CAAC,CAACQ,cAAc,CAAC,CAAC;IAClBvC,QAAQ,CAAC,EAAE,CAAC;IACZE,UAAU,CAAC,EAAE,CAAC;IAEd,IAAI;MACAJ,SAAS,CAAC,IAAI,CAAC;;MAEf;MACA,MAAM0C,UAAU,GAAG;QACfpE,QAAQ,EAAEA;MACd,CAAC;;MAED;MACA,IAAI+B,kBAAkB,EAAE;QACpBqC,UAAU,CAACpB,mBAAmB,GAAGrC,iBAAiB;MACtD;MAEA,MAAM2B,QAAQ,GAAG,MAAMpD,KAAK,CAACmF,IAAI,CAAC,GAAGxE,YAAY,wCAAwC,EAAEuE,UAAU,CAAC;MAEtG,IAAI9B,QAAQ,CAACE,IAAI,CAACX,OAAO,EAAE;QACvBC,UAAU,CAAC,uCAAuC,CAAC;;QAEnD;QACA,IAAIQ,QAAQ,CAACE,IAAI,CAAC8B,YAAY,EAAE;UAC5B1C,QAAQ,CAACU,QAAQ,CAACE,IAAI,CAAC8B,YAAY,CAAC;QACxC;QAEAC,UAAU,CAAC,MAAM;UACbzC,UAAU,CAAC,EAAE,CAAC;UACdF,QAAQ,CAAC,EAAE,CAAC;QAChB,CAAC,EAAE,IAAI,CAAC;MACZ,CAAC,MAAM;QACHA,QAAQ,CAACU,QAAQ,CAACE,IAAI,CAACgC,OAAO,IAAI,kCAAkC,CAAC;MACzE;IACJ,CAAC,CAAC,OAAOf,GAAG,EAAE;MACV7B,QAAQ,CAAC,kCAAkC,CAAC;MAC5C2B,OAAO,CAAC5B,KAAK,CAAC,wBAAwB,EAAE8B,GAAG,CAAC;IAChD,CAAC,SAAS;MACN/B,SAAS,CAAC,KAAK,CAAC;IACpB;EACJ,CAAC;EAED,IAAIH,OAAO,EAAE;IACT,oBACI3B,OAAA;MAAK6E,SAAS,EAAC,KAAK;MAAAC,QAAA,eAChB9E,OAAA;QAAK6E,SAAS,EAAC,uCAAuC;QAAAC,QAAA,eAClD9E,OAAA;UAAK6E,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAA4B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd;EAEA,oBACIlF,OAAA;IAAK6E,SAAS,EAAC,KAAK;IAAAC,QAAA,gBAEhB9E,OAAA;MAAK6E,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACjB9E,OAAA;QAAI6E,SAAS,EAAC,0DAA0D;QAAAC,QAAA,gBACpE9E,OAAA,CAACT,WAAW;UAACsF,SAAS,EAAC;QAAe;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,qBAE7C;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLlF,OAAA;QAAG6E,SAAS,EAAC,oBAAoB;QAAAC,QAAA,EAAC;MAElC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,EACH,CAAC/C,kBAAkB,iBAChBnC,OAAA;QAAK6E,SAAS,EAAC,8EAA8E;QAAAC,QAAA,gBACzF9E,OAAA,CAACF,YAAY;UAAC+E,SAAS,EAAC;QAAsB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACjDlF,OAAA;UAAA8E,QAAA,gBACI9E,OAAA;YAAG6E,SAAS,EAAC,2BAA2B;YAAAC,QAAA,EAAC;UAAuC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACpFlF,OAAA;YAAG6E,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAE1C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CACR;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,EAGLnD,KAAK,iBACF/B,OAAA;MAAK6E,SAAS,EAAC,6EAA6E;MAAAC,QAAA,gBACxF9E,OAAA,CAACP,OAAO;QAACoF,SAAS,EAAC;MAAc;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACpClF,OAAA;QAAM6E,SAAS,EAAC,cAAc;QAAAC,QAAA,EAAE/C;MAAK;QAAAgD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5C,CACR,EAEAjD,OAAO,iBACJjC,OAAA;MAAK6E,SAAS,EAAC,iFAAiF;MAAAC,QAAA,gBAC5F9E,OAAA,CAACR,OAAO;QAACqF,SAAS,EAAC;MAAgB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACtClF,OAAA;QAAM6E,SAAS,EAAC,gBAAgB;QAAAC,QAAA,EAAE7C;MAAO;QAAA8C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChD,CACR,eAGDlF,OAAA;MAAK6E,SAAS,EAAC,sCAAsC;MAAAC,QAAA,eACjD9E,OAAA;QAAMmF,QAAQ,EAAEb,YAAa;QAACO,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAEnD9E,OAAA;UAAA8E,QAAA,gBACI9E,OAAA;YAAI6E,SAAS,EAAC,0CAA0C;YAAAC,QAAA,EAAC;UAAyB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvFlF,OAAA;YAAK6E,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACtB9E,OAAA;cAAK6E,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAC9B9E,OAAA;gBACIiE,IAAI,EAAC,UAAU;gBACfmB,EAAE,EAAC,YAAY;gBACfpB,IAAI,EAAC,YAAY;gBACjBE,OAAO,EAAE9D,QAAQ,CAACE,UAAU,KAAK,MAAO;gBACxC+E,QAAQ,EAAEvB,iBAAkB;gBAC5Be,SAAS,EAAC;cAAmE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChF,CAAC,eACFlF,OAAA;gBAAOsF,OAAO,EAAC,YAAY;gBAACT,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAAC;cAEzE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC,eAENlF,OAAA;cAAA8E,QAAA,gBACI9E,OAAA;gBAAO6E,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRlF,OAAA;gBACIiE,IAAI,EAAC,MAAM;gBACXD,IAAI,EAAC,sBAAsB;gBAC3Bd,KAAK,EAAE9C,QAAQ,CAACG,oBAAqB;gBACrC8E,QAAQ,EAAEvB,iBAAkB;gBAC5Be,SAAS,EAAC,2GAA2G;gBACrHU,WAAW,EAAC;cAAuB;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC,eACFlF,OAAA;gBAAG6E,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAAuC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAGNlF,OAAA;UAAA8E,QAAA,gBACI9E,OAAA;YAAI6E,SAAS,EAAC,0CAA0C;YAAAC,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1ElF,OAAA;YAAK6E,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBAClD9E,OAAA;cAAA8E,QAAA,gBACI9E,OAAA;gBAAO6E,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRlF,OAAA;gBACIiE,IAAI,EAAC,QAAQ;gBACbD,IAAI,EAAC,iBAAiB;gBACtBd,KAAK,EAAE9C,QAAQ,CAACI,eAAgB;gBAChC6E,QAAQ,EAAEvB,iBAAkB;gBAC5B0B,GAAG,EAAC,IAAI;gBACRC,GAAG,EAAC,MAAM;gBACVZ,SAAS,EAAC;cAA2G;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAENlF,OAAA;cAAA8E,QAAA,gBACI9E,OAAA;gBAAO6E,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRlF,OAAA;gBACIiE,IAAI,EAAC,QAAQ;gBACbD,IAAI,EAAC,kBAAkB;gBACvBd,KAAK,EAAE9C,QAAQ,CAACK,gBAAiB;gBACjC4E,QAAQ,EAAEvB,iBAAkB;gBAC5B0B,GAAG,EAAC,GAAG;gBACPC,GAAG,EAAC,IAAI;gBACRZ,SAAS,EAAC;cAA2G;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAGNlF,OAAA;UAAA8E,QAAA,gBACI9E,OAAA;YAAI6E,SAAS,EAAC,0CAA0C;YAAAC,QAAA,EAAC;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC/ElF,OAAA;YAAK6E,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBAClD9E,OAAA;cAAA8E,QAAA,gBACI9E,OAAA;gBAAO6E,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRlF,OAAA;gBACIiE,IAAI,EAAC,QAAQ;gBACbD,IAAI,EAAC,qBAAqB;gBAC1Bd,KAAK,EAAE9C,QAAQ,CAACO,mBAAoB;gBACpC0E,QAAQ,EAAEvB,iBAAkB;gBAC5B0B,GAAG,EAAC,GAAG;gBACPC,GAAG,EAAC,IAAI;gBACRZ,SAAS,EAAC;cAA2G;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAENlF,OAAA;cAAK6E,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAC9B9E,OAAA;gBACIiE,IAAI,EAAC,UAAU;gBACfmB,EAAE,EAAC,uBAAuB;gBAC1BpB,IAAI,EAAC,uBAAuB;gBAC5BE,OAAO,EAAE9D,QAAQ,CAACQ,qBAAqB,KAAK,MAAO;gBACnDyE,QAAQ,EAAEvB,iBAAkB;gBAC5Be,SAAS,EAAC;cAAmE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChF,CAAC,eACFlF,OAAA;gBAAOsF,OAAO,EAAC,uBAAuB;gBAACT,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAAC;cAEpF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAGNlF,OAAA;UAAA8E,QAAA,gBACI9E,OAAA;YAAI6E,SAAS,EAAC,0CAA0C;YAAAC,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5ElF,OAAA;YAAK6E,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBAClD9E,OAAA;cAAA8E,QAAA,gBACI9E,OAAA;gBAAO6E,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRlF,OAAA;gBACIiE,IAAI,EAAC,QAAQ;gBACbD,IAAI,EAAC,oBAAoB;gBACzBd,KAAK,EAAE9C,QAAQ,CAACU,kBAAmB;gBACnCuE,QAAQ,EAAEvB,iBAAkB;gBAC5B0B,GAAG,EAAC,GAAG;gBACPC,GAAG,EAAC,IAAI;gBACRZ,SAAS,EAAC;cAA2G;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAENlF,OAAA;cAAA8E,QAAA,gBACI9E,OAAA;gBAAO6E,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRlF,OAAA;gBACIiE,IAAI,EAAC,QAAQ;gBACbD,IAAI,EAAC,cAAc;gBACnBd,KAAK,EAAE9C,QAAQ,CAACM,YAAa;gBAC7B2E,QAAQ,EAAEvB,iBAAkB;gBAC5B0B,GAAG,EAAC,KAAK;gBACTC,GAAG,EAAC,OAAO;gBACXZ,SAAS,EAAC;cAA2G;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAENlF,OAAA;cAAA8E,QAAA,gBACI9E,OAAA;gBAAO6E,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRlF,OAAA;gBACIiE,IAAI,EAAC,QAAQ;gBACbD,IAAI,EAAC,iBAAiB;gBACtBd,KAAK,EAAE9C,QAAQ,CAACS,eAAgB;gBAChCwE,QAAQ,EAAEvB,iBAAkB;gBAC5B0B,GAAG,EAAC,KAAK;gBACTC,GAAG,EAAC,OAAO;gBACXZ,SAAS,EAAC;cAA2G;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,EAGL/C,kBAAkB,iBACfnC,OAAA;UAAK6E,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC1B9E,OAAA;YAAK6E,SAAS,EAAC,8BAA8B;YAAAC,QAAA,gBACzC9E,OAAA,CAACJ,YAAY;cAACiF,SAAS,EAAC;YAAsB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjDlF,OAAA;cAAA8E,QAAA,gBACI9E,OAAA;gBAAI6E,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,EAAC;cAA6B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACtFlF,OAAA;gBAAG6E,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAA4D;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,EAGL,CAAC7C,cAAc,iBACZrC,OAAA;YAAK6E,SAAS,EAAC,kFAAkF;YAAAC,QAAA,gBAC7F9E,OAAA,CAACH,qBAAqB;cAACgF,SAAS,EAAC;YAAwB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC5DlF,OAAA;cAAA8E,QAAA,gBACI9E,OAAA;gBAAG6E,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,EAAC;cAA2B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC1ElF,OAAA;gBAAG6E,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,EAAC;cAE5C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CACR,EAGA3C,UAAU,CAACmD,YAAY,iBACpB1F,OAAA;YAAK6E,SAAS,EAAC,uDAAuD;YAAAC,QAAA,gBAClE9E,OAAA;cAAK6E,SAAS,EAAC,8BAA8B;cAAAC,QAAA,gBACzC9E,OAAA,CAACF,YAAY;gBAAC+E,SAAS,EAAC;cAAe;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC1ClF,OAAA;gBAAM6E,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EAAC;cAAoB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtE,CAAC,eACNlF,OAAA;cAAK6E,SAAS,EAAC,+CAA+C;cAAAC,QAAA,gBAC1D9E,OAAA;gBAAA8E,QAAA,gBACI9E,OAAA;kBAAM6E,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACpDlF,OAAA;kBAAM6E,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,EAAEvC,UAAU,CAACmD;gBAAY;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClE,CAAC,eACNlF,OAAA;gBAAA8E,QAAA,gBACI9E,OAAA;kBAAM6E,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACrDlF,OAAA;kBAAM6E,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,EAAEvC,UAAU,CAACoD,aAAa,IAAI;gBAAC;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxE,CAAC,eACNlF,OAAA;gBAAA8E,QAAA,gBACI9E,OAAA;kBAAM6E,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACnDlF,OAAA;kBAAM6E,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,EAAEvC,UAAU,CAACqD,WAAW,IAAI;gBAAC;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtE,CAAC,eACNlF,OAAA;gBAAA8E,QAAA,gBACI9E,OAAA;kBAAM6E,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACnDlF,OAAA;kBAAM6E,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,EAAEvC,UAAU,CAACsD,WAAW,IAAI;gBAAC;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CACR,eAGDlF,OAAA;YAAK6E,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACtB9E,OAAA;cAAA8E,QAAA,gBACI9E,OAAA;gBAAI6E,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,EAAC;cAAsB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClFlF,OAAA;gBAAK6E,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACtB9E,OAAA;kBAAK6E,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAC9B9E,OAAA;oBACIiE,IAAI,EAAC,UAAU;oBACfmB,EAAE,EAAC,mBAAmB;oBACtBpB,IAAI,EAAC,mBAAmB;oBACxBE,OAAO,EAAEnD,iBAAiB,CAACG,iBAAiB,KAAK,MAAO;oBACxDmE,QAAQ,EAAEhB,qBAAsB;oBAChCyB,QAAQ,EAAE,CAACzD,cAAe;oBAC1BwC,SAAS,EAAC;kBAAuF;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpG,CAAC,eACFlF,OAAA;oBAAOsF,OAAO,EAAC,mBAAmB;oBAACT,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,GAAC,0CAE5E,EAAC,CAACzC,cAAc,iBAAIrC,OAAA;sBAAM6E,SAAS,EAAC,mBAAmB;sBAAAC,QAAA,EAAC;oBAAe;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3E,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC,eAENlF,OAAA;kBAAK6E,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAC9B9E,OAAA;oBACIiE,IAAI,EAAC,UAAU;oBACfmB,EAAE,EAAC,mBAAmB;oBACtBpB,IAAI,EAAC,mBAAmB;oBACxBE,OAAO,EAAEnD,iBAAiB,CAACI,iBAAiB,KAAK,MAAO;oBACxDkE,QAAQ,EAAEhB,qBAAsB;oBAChCQ,SAAS,EAAC;kBAAmE;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChF,CAAC,eACFlF,OAAA;oBAAOsF,OAAO,EAAC,mBAAmB;oBAACT,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAAC;kBAEhF;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,EAGLnE,iBAAiB,CAACG,iBAAiB,KAAK,MAAM,iBAC3ClB,OAAA;cAAA8E,QAAA,gBACI9E,OAAA;gBAAI6E,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,EAAC;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC7ElF,OAAA;gBAAK6E,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,gBAClD9E,OAAA;kBAAA8E,QAAA,gBACI9E,OAAA;oBAAO6E,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAEhE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRlF,OAAA;oBACIiE,IAAI,EAAC,QAAQ;oBACbD,IAAI,EAAC,uBAAuB;oBAC5Bd,KAAK,EAAEnC,iBAAiB,CAACK,qBAAsB;oBAC/CiE,QAAQ,EAAEhB,qBAAsB;oBAChCmB,GAAG,EAAC,IAAI;oBACRC,GAAG,EAAC,MAAM;oBACVZ,SAAS,EAAC;kBAA2G;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxH,CAAC,eACFlF,OAAA;oBAAG6E,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,EAAC;kBAAgC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7E,CAAC,eAENlF,OAAA;kBAAA8E,QAAA,gBACI9E,OAAA;oBAAO6E,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAEhE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRlF,OAAA;oBACIiE,IAAI,EAAC,QAAQ;oBACbD,IAAI,EAAC,wBAAwB;oBAC7Bd,KAAK,EAAEnC,iBAAiB,CAACM,sBAAuB;oBAChDgE,QAAQ,EAAEhB,qBAAsB;oBAChCmB,GAAG,EAAC,GAAG;oBACPC,GAAG,EAAC,IAAI;oBACRZ,SAAS,EAAC;kBAA2G;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxH,CAAC,eACFlF,OAAA;oBAAG6E,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,EAAC;kBAA+B;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5E,CAAC,eAENlF,OAAA;kBAAA8E,QAAA,gBACI9E,OAAA;oBAAO6E,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAEhE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRlF,OAAA;oBACIiE,IAAI,EAAC,QAAQ;oBACbD,IAAI,EAAC,oBAAoB;oBACzBd,KAAK,EAAEnC,iBAAiB,CAACQ,kBAAmB;oBAC5C8D,QAAQ,EAAEhB,qBAAsB;oBAChCmB,GAAG,EAAC,KAAK;oBACTC,GAAG,EAAC,OAAO;oBACXZ,SAAS,EAAC;kBAA2G;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxH,CAAC,eACFlF,OAAA;oBAAG6E,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,EAAC;kBAAkC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/E,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CACR,EAGAnE,iBAAiB,CAACI,iBAAiB,KAAK,MAAM,iBAC3CnB,OAAA;cAAA8E,QAAA,gBACI9E,OAAA;gBAAI6E,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,EAAC;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC7ElF,OAAA;gBAAK6E,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,gBAClD9E,OAAA;kBAAA8E,QAAA,gBACI9E,OAAA;oBAAO6E,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAEhE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRlF,OAAA;oBACIiE,IAAI,EAAC,QAAQ;oBACbD,IAAI,EAAC,0BAA0B;oBAC/Bd,KAAK,EAAEnC,iBAAiB,CAACU,wBAAyB;oBAClD4D,QAAQ,EAAEhB,qBAAsB;oBAChCmB,GAAG,EAAC,GAAG;oBACPC,GAAG,EAAC,IAAI;oBACRZ,SAAS,EAAC;kBAA2G;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxH,CAAC,eACFlF,OAAA;oBAAG6E,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,EAAC;kBAAkC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/E,CAAC,eAENlF,OAAA;kBAAA8E,QAAA,gBACI9E,OAAA;oBAAO6E,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAEhE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRlF,OAAA;oBACIiE,IAAI,EAAC,MAAM;oBACXD,IAAI,EAAC,uBAAuB;oBAC5Bd,KAAK,EAAEnC,iBAAiB,CAACS,qBAAsB;oBAC/C6D,QAAQ,EAAEhB,qBAAsB;oBAChCQ,SAAS,EAAC,2GAA2G;oBACrHU,WAAW,EAAC;kBAAuB;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtC,CAAC,eACFlF,OAAA;oBAAG6E,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,EAAC;kBAA+B;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5E,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CACR,eAGDlF,OAAA;cAAA8E,QAAA,gBACI9E,OAAA;gBAAI6E,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,EAAC;cAAsB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClFlF,OAAA;gBAAK6E,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,gBAClD9E,OAAA;kBAAA8E,QAAA,gBACI9E,OAAA;oBAAO6E,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAEhE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRlF,OAAA;oBACIiE,IAAI,EAAC,QAAQ;oBACbD,IAAI,EAAC,0BAA0B;oBAC/Bd,KAAK,EAAEnC,iBAAiB,CAACO,wBAAyB;oBAClD+D,QAAQ,EAAEhB,qBAAsB;oBAChCmB,GAAG,EAAC,GAAG;oBACPC,GAAG,EAAC,IAAI;oBACRZ,SAAS,EAAC;kBAA2G;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxH,CAAC,eACFlF,OAAA;oBAAG6E,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,EAAC;kBAA8B;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3E,CAAC,eAENlF,OAAA;kBAAA8E,QAAA,gBACI9E,OAAA;oBAAO6E,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAEhE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRlF,OAAA;oBACIiE,IAAI,EAAC,QAAQ;oBACbD,IAAI,EAAC,uBAAuB;oBAC5Bd,KAAK,EAAEnC,iBAAiB,CAACW,qBAAsB;oBAC/C2D,QAAQ,EAAEhB,qBAAsB;oBAChCmB,GAAG,EAAC,KAAK;oBACTC,GAAG,EAAC,OAAO;oBACXZ,SAAS,EAAC;kBAA2G;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxH,CAAC,eACFlF,OAAA;oBAAG6E,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,EAAC;kBAA8B;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3E,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAGNlF,OAAA;cAAK6E,SAAS,EAAC,2BAA2B;cAAAC,QAAA,gBACtC9E,OAAA;gBAAI6E,SAAS,EAAC,gEAAgE;gBAAAC,QAAA,gBAC1E9E,OAAA,CAACL,KAAK;kBAACkF,SAAS,EAAC;gBAAe;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,sBAEvC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLlF,OAAA;gBAAK6E,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,gBAC5C9E,OAAA;kBAAA8E,QAAA,eAAG9E,OAAA;oBAAA8E,QAAA,EAAQ;kBAAuB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAC/ClF,OAAA;kBAAI6E,SAAS,EAAC,sCAAsC;kBAAAC,QAAA,gBAChD9E,OAAA;oBAAA8E,QAAA,EAAI;kBAAqD;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC9DlF,OAAA;oBAAA8E,QAAA,EAAI;kBAA+B;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACxClF,OAAA;oBAAA8E,QAAA,EAAI;kBAAoD;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7D,CAAC,eAELlF,OAAA;kBAAG6E,SAAS,EAAC,MAAM;kBAAAC,QAAA,eAAC9E,OAAA;oBAAA8E,QAAA,EAAQ;kBAAuB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAChElF,OAAA;kBAAI6E,SAAS,EAAC,sCAAsC;kBAAAC,QAAA,gBAChD9E,OAAA;oBAAA8E,QAAA,EAAI;kBAA+B;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACxClF,OAAA;oBAAA8E,QAAA,EAAI;kBAAwD;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACjElF,OAAA;oBAAA8E,QAAA,EAAI;kBAAuD;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAChElF,OAAA;oBAAA8E,QAAA,EAAI;kBAAmD;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5D,CAAC,eAELlF,OAAA;kBAAG6E,SAAS,EAAC,+CAA+C;kBAAAC,QAAA,gBACxD9E,OAAA;oBAAA8E,QAAA,EAAQ;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,qFAC/B;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CACR,eAGDlF,OAAA;UAAK6E,SAAS,EAAC,gCAAgC;UAAAC,QAAA,eAC3C9E,OAAA;YACIiE,IAAI,EAAC,QAAQ;YACb6B,QAAQ,EAAEjE,MAAO;YACjBgD,SAAS,EAAC,yJAAyJ;YAAAC,QAAA,gBAEnK9E,OAAA,CAACN,MAAM;cAAAqF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EACTrD,MAAM,GAAG,WAAW,GAAG,wBAAwB;UAAA;YAAAkD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd;AAAC/E,EAAA,CA7mBQD,gBAAgB;AAAA6F,EAAA,GAAhB7F,gBAAgB;AA+mBzB,eAAeA,gBAAgB;AAAC,IAAA6F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}