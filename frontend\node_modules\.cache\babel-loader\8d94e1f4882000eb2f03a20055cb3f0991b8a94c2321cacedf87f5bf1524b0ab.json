{"ast": null, "code": "var _jsxFileName = \"C:\\\\MAMP\\\\htdocs\\\\FanBet247\\\\frontend\\\\src\\\\components\\\\Admin\\\\Admin2FASetup.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { FaShieldAlt, FaQrcode, FaKey, FaCopy, FaCheck, FaSpinner, FaExclamationTriangle, FaArrowLeft, FaDownload } from 'react-icons/fa';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst API_BASE_URL = '/backend';\nconst Admin2FASetup = ({\n  adminId,\n  username,\n  onSuccess,\n  onBack\n}) => {\n  _s();\n  const [step, setStep] = useState(1); // 1: Setup, 2: Verify, 3: Backup Codes\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n\n  // Setup data\n  const [secretKey, setSecretKey] = useState('');\n  const [qrCodeUrl, setQrCodeUrl] = useState('');\n  const [backupCodes, setBackupCodes] = useState([]);\n  const [manualEntryKey, setManualEntryKey] = useState('');\n\n  // Verification\n  const [verificationCode, setVerificationCode] = useState('');\n  const [verifying, setVerifying] = useState(false);\n\n  // UI state\n  const [copied, setCopied] = useState(false);\n  const [backupCodesCopied, setBackupCodesCopied] = useState(false);\n  useEffect(() => {\n    initiate2FASetup();\n  }, []);\n  const initiate2FASetup = async () => {\n    try {\n      setLoading(true);\n      setError('');\n      const response = await axios.get(`${API_BASE_URL}/handlers/admin_setup_2fa.php?adminId=${adminId}`);\n      if (response.data.success) {\n        setSecretKey(response.data.secret_key);\n        setQrCodeUrl(response.data.qr_code_url);\n        setBackupCodes(response.data.backup_codes);\n        setManualEntryKey(response.data.manual_entry_key);\n        setSuccess('2FA setup initiated successfully');\n        setTimeout(() => setSuccess(''), 3000);\n      } else {\n        setError(response.data.message || 'Failed to initiate 2FA setup');\n      }\n    } catch (err) {\n      setError('Failed to initiate 2FA setup. Please try again.');\n      console.error('2FA setup error:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const verify2FASetup = async () => {\n    if (!verificationCode || verificationCode.length !== 6) {\n      setError('Please enter a valid 6-digit code from your authenticator app');\n      return;\n    }\n    try {\n      setVerifying(true);\n      setError('');\n      const response = await axios.post(`${API_BASE_URL}/handlers/admin_setup_2fa.php?adminId=${adminId}`, {\n        verification_code: verificationCode\n      });\n      if (response.data.success) {\n        setSuccess('2FA setup completed successfully!');\n        setStep(3); // Show backup codes\n      } else {\n        setError(response.data.message || 'Invalid verification code');\n      }\n    } catch (err) {\n      setError('Failed to verify 2FA setup. Please try again.');\n      console.error('2FA verification error:', err);\n    } finally {\n      setVerifying(false);\n    }\n  };\n  const copyToClipboard = async (text, type = 'key') => {\n    try {\n      await navigator.clipboard.writeText(text);\n      if (type === 'key') {\n        setCopied(true);\n        setTimeout(() => setCopied(false), 2000);\n      } else {\n        setBackupCodesCopied(true);\n        setTimeout(() => setBackupCodesCopied(false), 2000);\n      }\n    } catch (err) {\n      console.error('Failed to copy to clipboard:', err);\n    }\n  };\n  const downloadBackupCodes = () => {\n    const content = `FanBet247 Admin 2FA Backup Codes\\nGenerated: ${new Date().toLocaleString()}\\nAdmin: ${username}\\n\\n${backupCodes.join('\\n')}\\n\\nKeep these codes safe and secure. Each code can only be used once.`;\n    const blob = new Blob([content], {\n      type: 'text/plain'\n    });\n    const url = URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = `fanbet247-backup-codes-${username}.txt`;\n    document.body.appendChild(a);\n    a.click();\n    document.body.removeChild(a);\n    URL.revokeObjectURL(url);\n  };\n  const complete2FASetup = () => {\n    onSuccess({\n      message: '2FA has been successfully enabled for your account',\n      auth_method: '2fa',\n      backup_codes: backupCodes\n    });\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(FaSpinner, {\n          className: \"animate-spin text-4xl text-blue-600 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Setting up 2FA...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 123,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 122,\n      columnNumber: 13\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-md w-full space-y-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-green-100\",\n          children: /*#__PURE__*/_jsxDEV(FaShieldAlt, {\n            className: \"h-6 w-6 text-green-600\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"mt-6 text-center text-3xl font-extrabold text-gray-900\",\n          children: \"Setup Two-Factor Authentication\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-2 text-center text-sm text-gray-600\",\n          children: \"Secure your admin account with Google Authenticator\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-center text-sm font-medium text-gray-900\",\n          children: username\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 134,\n        columnNumber: 17\n      }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-red-50 border border-red-200 rounded-md p-4 flex items-center gap-3\",\n        children: [/*#__PURE__*/_jsxDEV(FaExclamationTriangle, {\n          className: \"text-red-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-red-700 text-sm\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 151,\n        columnNumber: 21\n      }, this), success && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-green-50 border border-green-200 rounded-md p-4 flex items-center gap-3\",\n        children: [/*#__PURE__*/_jsxDEV(FaCheck, {\n          className: \"text-green-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-green-700 text-sm\",\n          children: success\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 159,\n        columnNumber: 21\n      }, this), step === 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-medium text-gray-900 mb-4\",\n            children: \"Step 1: Scan QR Code\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-600 mb-6\",\n            children: \"Scan this QR code with your Google Authenticator app\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 29\n          }, this), qrCodeUrl && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white p-4 rounded-lg border inline-block\",\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: qrCodeUrl,\n              alt: \"2FA QR Code\",\n              className: \"w-48 h-48 mx-auto\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 37\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"text-md font-medium text-gray-900 mb-2\",\n            children: \"Manual Entry\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-600 mb-3\",\n            children: \"If you can't scan the QR code, enter this key manually:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 183,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: manualEntryKey,\n              readOnly: true,\n              className: \"flex-1 px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-sm font-mono\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => copyToClipboard(manualEntryKey),\n              className: \"px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center gap-2\",\n              children: [copied ? /*#__PURE__*/_jsxDEV(FaCheck, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 197,\n                columnNumber: 47\n              }, this) : /*#__PURE__*/_jsxDEV(FaCopy, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 197,\n                columnNumber: 61\n              }, this), copied ? 'Copied!' : 'Copy']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setStep(2),\n          className: \"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n          children: \"Next: Verify Setup\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 167,\n        columnNumber: 21\n      }, this), step === 2 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-medium text-gray-900 mb-4\",\n            children: \"Step 2: Verify Setup\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-600 mb-6\",\n            children: \"Enter the 6-digit code from your Google Authenticator app\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-700 mb-2\",\n            children: \"Verification Code\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            maxLength: \"6\",\n            value: verificationCode,\n            onChange: e => setVerificationCode(e.target.value.replace(/\\D/g, '')),\n            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-center text-xl font-mono\",\n            placeholder: \"000000\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 222,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex gap-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setStep(1),\n            className: \"flex-1 py-2 px-4 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50\",\n            children: \"Back\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: verify2FASetup,\n            disabled: verifying || verificationCode.length !== 6,\n            className: \"flex-1 flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed\",\n            children: [verifying && /*#__PURE__*/_jsxDEV(FaSpinner, {\n              className: \"animate-spin mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 248,\n              columnNumber: 47\n            }, this), verifying ? 'Verifying...' : 'Verify & Enable 2FA']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 243,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 214,\n        columnNumber: 21\n      }, this), step === 3 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-medium text-gray-900 mb-4\",\n            children: \"Step 3: Save Backup Codes\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 259,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-600 mb-6\",\n            children: \"Save these backup codes in a secure location. Each code can only be used once.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gray-50 rounded-lg p-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-2 gap-2 font-mono text-sm\",\n            children: backupCodes.map((code, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white p-2 rounded border text-center\",\n              children: code\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 268,\n              columnNumber: 37\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex gap-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => copyToClipboard(backupCodes.join('\\n'), 'codes'),\n            className: \"flex-1 flex justify-center items-center gap-2 py-2 px-4 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50\",\n            children: [backupCodesCopied ? /*#__PURE__*/_jsxDEV(FaCheck, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 54\n            }, this) : /*#__PURE__*/_jsxDEV(FaCopy, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 68\n            }, this), backupCodesCopied ? 'Copied!' : 'Copy Codes']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 276,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: downloadBackupCodes,\n            className: \"flex-1 flex justify-center items-center gap-2 py-2 px-4 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50\",\n            children: [/*#__PURE__*/_jsxDEV(FaDownload, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 287,\n              columnNumber: 33\n            }, this), \"Download\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 275,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: complete2FASetup,\n          className: \"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500\",\n          children: \"Complete Setup\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 292,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 257,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onBack,\n          className: \"text-sm text-gray-600 hover:text-gray-500 flex items-center justify-center gap-2 mx-auto\",\n          children: [/*#__PURE__*/_jsxDEV(FaArrowLeft, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 307,\n            columnNumber: 25\n          }, this), \"Back to Login\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 303,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 302,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 133,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 132,\n    columnNumber: 9\n  }, this);\n};\n_s(Admin2FASetup, \"pmghmqAD1v45BhYsJ3VnknTb1BU=\");\n_c = Admin2FASetup;\nexport default Admin2FASetup;\nvar _c;\n$RefreshReg$(_c, \"Admin2FASetup\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "FaShieldAlt", "FaQrcode", "FaKey", "FaCopy", "FaCheck", "FaSpinner", "FaExclamationTriangle", "FaArrowLeft", "FaDownload", "jsxDEV", "_jsxDEV", "API_BASE_URL", "Admin2FASetup", "adminId", "username", "onSuccess", "onBack", "_s", "step", "setStep", "loading", "setLoading", "error", "setError", "success", "setSuccess", "secret<PERSON>ey", "set<PERSON>ec<PERSON><PERSON>ey", "qrCodeUrl", "setQrCodeUrl", "backupCodes", "setBackupCodes", "manualEntryKey", "setManual<PERSON>ntry<PERSON>ey", "verificationCode", "setVerificationCode", "verifying", "setVerifying", "copied", "setCopied", "backupCodesCopied", "setBackupCodesCopied", "initiate2FASetup", "response", "get", "data", "secret_key", "qr_code_url", "backup_codes", "manual_entry_key", "setTimeout", "message", "err", "console", "verify2FASetup", "length", "post", "verification_code", "copyToClipboard", "text", "type", "navigator", "clipboard", "writeText", "downloadBackupCodes", "content", "Date", "toLocaleString", "join", "blob", "Blob", "url", "URL", "createObjectURL", "a", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "complete2FASetup", "auth_method", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "src", "alt", "value", "readOnly", "onClick", "max<PERSON><PERSON><PERSON>", "onChange", "e", "target", "replace", "placeholder", "disabled", "map", "code", "index", "_c", "$RefreshReg$"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/components/Admin/Admin2FASetup.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { FaShieldAlt, FaQrcode, Fa<PERSON>ey, <PERSON>a<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>a<PERSON><PERSON><PERSON>, FaEx<PERSON><PERSON>riangle, FaArrowLeft, FaDownload } from 'react-icons/fa';\n\nconst API_BASE_URL = '/backend';\n\nconst Admin2FASetup = ({ adminId, username, onSuccess, onBack }) => {\n    const [step, setStep] = useState(1); // 1: Setup, 2: Verify, 3: Backup Codes\n    const [loading, setLoading] = useState(false);\n    const [error, setError] = useState('');\n    const [success, setSuccess] = useState('');\n    \n    // Setup data\n    const [secretKey, setSecretKey] = useState('');\n    const [qrCodeUrl, setQrCodeUrl] = useState('');\n    const [backupCodes, setBackupCodes] = useState([]);\n    const [manualEntry<PERSON><PERSON>, setManualEntryKey] = useState('');\n    \n    // Verification\n    const [verificationCode, setVerificationCode] = useState('');\n    const [verifying, setVerifying] = useState(false);\n    \n    // UI state\n    const [copied, setCopied] = useState(false);\n    const [backupCodesCopied, setBackupCodesCopied] = useState(false);\n\n    useEffect(() => {\n        initiate2FASetup();\n    }, []);\n\n    const initiate2FASetup = async () => {\n        try {\n            setLoading(true);\n            setError('');\n\n            const response = await axios.get(`${API_BASE_URL}/handlers/admin_setup_2fa.php?adminId=${adminId}`);\n\n            if (response.data.success) {\n                setSecretKey(response.data.secret_key);\n                setQrCodeUrl(response.data.qr_code_url);\n                setBackupCodes(response.data.backup_codes);\n                setManualEntryKey(response.data.manual_entry_key);\n                setSuccess('2FA setup initiated successfully');\n                setTimeout(() => setSuccess(''), 3000);\n            } else {\n                setError(response.data.message || 'Failed to initiate 2FA setup');\n            }\n        } catch (err) {\n            setError('Failed to initiate 2FA setup. Please try again.');\n            console.error('2FA setup error:', err);\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const verify2FASetup = async () => {\n        if (!verificationCode || verificationCode.length !== 6) {\n            setError('Please enter a valid 6-digit code from your authenticator app');\n            return;\n        }\n\n        try {\n            setVerifying(true);\n            setError('');\n\n            const response = await axios.post(`${API_BASE_URL}/handlers/admin_setup_2fa.php?adminId=${adminId}`, {\n                verification_code: verificationCode\n            });\n\n            if (response.data.success) {\n                setSuccess('2FA setup completed successfully!');\n                setStep(3); // Show backup codes\n            } else {\n                setError(response.data.message || 'Invalid verification code');\n            }\n        } catch (err) {\n            setError('Failed to verify 2FA setup. Please try again.');\n            console.error('2FA verification error:', err);\n        } finally {\n            setVerifying(false);\n        }\n    };\n\n    const copyToClipboard = async (text, type = 'key') => {\n        try {\n            await navigator.clipboard.writeText(text);\n            if (type === 'key') {\n                setCopied(true);\n                setTimeout(() => setCopied(false), 2000);\n            } else {\n                setBackupCodesCopied(true);\n                setTimeout(() => setBackupCodesCopied(false), 2000);\n            }\n        } catch (err) {\n            console.error('Failed to copy to clipboard:', err);\n        }\n    };\n\n    const downloadBackupCodes = () => {\n        const content = `FanBet247 Admin 2FA Backup Codes\\nGenerated: ${new Date().toLocaleString()}\\nAdmin: ${username}\\n\\n${backupCodes.join('\\n')}\\n\\nKeep these codes safe and secure. Each code can only be used once.`;\n        const blob = new Blob([content], { type: 'text/plain' });\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement('a');\n        a.href = url;\n        a.download = `fanbet247-backup-codes-${username}.txt`;\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n        URL.revokeObjectURL(url);\n    };\n\n    const complete2FASetup = () => {\n        onSuccess({\n            message: '2FA has been successfully enabled for your account',\n            auth_method: '2fa',\n            backup_codes: backupCodes\n        });\n    };\n\n    if (loading) {\n        return (\n            <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n                <div className=\"text-center\">\n                    <FaSpinner className=\"animate-spin text-4xl text-blue-600 mx-auto mb-4\" />\n                    <p className=\"text-gray-600\">Setting up 2FA...</p>\n                </div>\n            </div>\n        );\n    }\n\n    return (\n        <div className=\"min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8\">\n            <div className=\"max-w-md w-full space-y-8\">\n                <div>\n                    <div className=\"mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-green-100\">\n                        <FaShieldAlt className=\"h-6 w-6 text-green-600\" />\n                    </div>\n                    <h2 className=\"mt-6 text-center text-3xl font-extrabold text-gray-900\">\n                        Setup Two-Factor Authentication\n                    </h2>\n                    <p className=\"mt-2 text-center text-sm text-gray-600\">\n                        Secure your admin account with Google Authenticator\n                    </p>\n                    <p className=\"text-center text-sm font-medium text-gray-900\">\n                        {username}\n                    </p>\n                </div>\n\n                {/* Error Message */}\n                {error && (\n                    <div className=\"bg-red-50 border border-red-200 rounded-md p-4 flex items-center gap-3\">\n                        <FaExclamationTriangle className=\"text-red-500\" />\n                        <span className=\"text-red-700 text-sm\">{error}</span>\n                    </div>\n                )}\n\n                {/* Success Message */}\n                {success && (\n                    <div className=\"bg-green-50 border border-green-200 rounded-md p-4 flex items-center gap-3\">\n                        <FaCheck className=\"text-green-500\" />\n                        <span className=\"text-green-700 text-sm\">{success}</span>\n                    </div>\n                )}\n\n                {/* Step 1: QR Code Setup */}\n                {step === 1 && (\n                    <div className=\"space-y-6\">\n                        <div className=\"text-center\">\n                            <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Step 1: Scan QR Code</h3>\n                            <p className=\"text-sm text-gray-600 mb-6\">\n                                Scan this QR code with your Google Authenticator app\n                            </p>\n                            \n                            {qrCodeUrl && (\n                                <div className=\"bg-white p-4 rounded-lg border inline-block\">\n                                    <img src={qrCodeUrl} alt=\"2FA QR Code\" className=\"w-48 h-48 mx-auto\" />\n                                </div>\n                            )}\n                        </div>\n\n                        <div>\n                            <h4 className=\"text-md font-medium text-gray-900 mb-2\">Manual Entry</h4>\n                            <p className=\"text-sm text-gray-600 mb-3\">\n                                If you can't scan the QR code, enter this key manually:\n                            </p>\n                            <div className=\"flex items-center gap-2\">\n                                <input\n                                    type=\"text\"\n                                    value={manualEntryKey}\n                                    readOnly\n                                    className=\"flex-1 px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-sm font-mono\"\n                                />\n                                <button\n                                    onClick={() => copyToClipboard(manualEntryKey)}\n                                    className=\"px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center gap-2\"\n                                >\n                                    {copied ? <FaCheck /> : <FaCopy />}\n                                    {copied ? 'Copied!' : 'Copy'}\n                                </button>\n                            </div>\n                        </div>\n\n                        <button\n                            onClick={() => setStep(2)}\n                            className=\"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n                        >\n                            Next: Verify Setup\n                        </button>\n                    </div>\n                )}\n\n                {/* Step 2: Verification */}\n                {step === 2 && (\n                    <div className=\"space-y-6\">\n                        <div className=\"text-center\">\n                            <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Step 2: Verify Setup</h3>\n                            <p className=\"text-sm text-gray-600 mb-6\">\n                                Enter the 6-digit code from your Google Authenticator app\n                            </p>\n                        </div>\n\n                        <div>\n                            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                                Verification Code\n                            </label>\n                            <input\n                                type=\"text\"\n                                maxLength=\"6\"\n                                value={verificationCode}\n                                onChange={(e) => setVerificationCode(e.target.value.replace(/\\D/g, ''))}\n                                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-center text-xl font-mono\"\n                                placeholder=\"000000\"\n                            />\n                        </div>\n\n                        <div className=\"flex gap-3\">\n                            <button\n                                onClick={() => setStep(1)}\n                                className=\"flex-1 py-2 px-4 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50\"\n                            >\n                                Back\n                            </button>\n                            <button\n                                onClick={verify2FASetup}\n                                disabled={verifying || verificationCode.length !== 6}\n                                className=\"flex-1 flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed\"\n                            >\n                                {verifying && <FaSpinner className=\"animate-spin mr-2\" />}\n                                {verifying ? 'Verifying...' : 'Verify & Enable 2FA'}\n                            </button>\n                        </div>\n                    </div>\n                )}\n\n                {/* Step 3: Backup Codes */}\n                {step === 3 && (\n                    <div className=\"space-y-6\">\n                        <div className=\"text-center\">\n                            <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Step 3: Save Backup Codes</h3>\n                            <p className=\"text-sm text-gray-600 mb-6\">\n                                Save these backup codes in a secure location. Each code can only be used once.\n                            </p>\n                        </div>\n\n                        <div className=\"bg-gray-50 rounded-lg p-4\">\n                            <div className=\"grid grid-cols-2 gap-2 font-mono text-sm\">\n                                {backupCodes.map((code, index) => (\n                                    <div key={index} className=\"bg-white p-2 rounded border text-center\">\n                                        {code}\n                                    </div>\n                                ))}\n                            </div>\n                        </div>\n\n                        <div className=\"flex gap-3\">\n                            <button\n                                onClick={() => copyToClipboard(backupCodes.join('\\n'), 'codes')}\n                                className=\"flex-1 flex justify-center items-center gap-2 py-2 px-4 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50\"\n                            >\n                                {backupCodesCopied ? <FaCheck /> : <FaCopy />}\n                                {backupCodesCopied ? 'Copied!' : 'Copy Codes'}\n                            </button>\n                            <button\n                                onClick={downloadBackupCodes}\n                                className=\"flex-1 flex justify-center items-center gap-2 py-2 px-4 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50\"\n                            >\n                                <FaDownload />\n                                Download\n                            </button>\n                        </div>\n\n                        <button\n                            onClick={complete2FASetup}\n                            className=\"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500\"\n                        >\n                            Complete Setup\n                        </button>\n                    </div>\n                )}\n\n                {/* Back to Login */}\n                <div className=\"text-center\">\n                    <button\n                        onClick={onBack}\n                        className=\"text-sm text-gray-600 hover:text-gray-500 flex items-center justify-center gap-2 mx-auto\"\n                    >\n                        <FaArrowLeft />\n                        Back to Login\n                    </button>\n                </div>\n            </div>\n        </div>\n    );\n};\n\nexport default Admin2FASetup;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,MAAM,EAAEC,OAAO,EAAEC,SAAS,EAAEC,qBAAqB,EAAEC,WAAW,EAAEC,UAAU,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1I,MAAMC,YAAY,GAAG,UAAU;AAE/B,MAAMC,aAAa,GAAGA,CAAC;EAAEC,OAAO;EAAEC,QAAQ;EAAEC,SAAS;EAAEC;AAAO,CAAC,KAAK;EAAAC,EAAA;EAChE,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGtB,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;EACrC,MAAM,CAACuB,OAAO,EAAEC,UAAU,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACyB,KAAK,EAAEC,QAAQ,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC2B,OAAO,EAAEC,UAAU,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;;EAE1C;EACA,MAAM,CAAC6B,SAAS,EAAEC,YAAY,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC+B,SAAS,EAAEC,YAAY,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACiC,WAAW,EAAEC,cAAc,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACmC,cAAc,EAAEC,iBAAiB,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;;EAExD;EACA,MAAM,CAACqC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGtC,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACuC,SAAS,EAAEC,YAAY,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;;EAEjD;EACA,MAAM,CAACyC,MAAM,EAAEC,SAAS,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAAC2C,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG5C,QAAQ,CAAC,KAAK,CAAC;EAEjEC,SAAS,CAAC,MAAM;IACZ4C,gBAAgB,CAAC,CAAC;EACtB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACArB,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,EAAE,CAAC;MAEZ,MAAMoB,QAAQ,GAAG,MAAM5C,KAAK,CAAC6C,GAAG,CAAC,GAAGjC,YAAY,yCAAyCE,OAAO,EAAE,CAAC;MAEnG,IAAI8B,QAAQ,CAACE,IAAI,CAACrB,OAAO,EAAE;QACvBG,YAAY,CAACgB,QAAQ,CAACE,IAAI,CAACC,UAAU,CAAC;QACtCjB,YAAY,CAACc,QAAQ,CAACE,IAAI,CAACE,WAAW,CAAC;QACvChB,cAAc,CAACY,QAAQ,CAACE,IAAI,CAACG,YAAY,CAAC;QAC1Cf,iBAAiB,CAACU,QAAQ,CAACE,IAAI,CAACI,gBAAgB,CAAC;QACjDxB,UAAU,CAAC,kCAAkC,CAAC;QAC9CyB,UAAU,CAAC,MAAMzB,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;MAC1C,CAAC,MAAM;QACHF,QAAQ,CAACoB,QAAQ,CAACE,IAAI,CAACM,OAAO,IAAI,8BAA8B,CAAC;MACrE;IACJ,CAAC,CAAC,OAAOC,GAAG,EAAE;MACV7B,QAAQ,CAAC,iDAAiD,CAAC;MAC3D8B,OAAO,CAAC/B,KAAK,CAAC,kBAAkB,EAAE8B,GAAG,CAAC;IAC1C,CAAC,SAAS;MACN/B,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,MAAMiC,cAAc,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI,CAACpB,gBAAgB,IAAIA,gBAAgB,CAACqB,MAAM,KAAK,CAAC,EAAE;MACpDhC,QAAQ,CAAC,+DAA+D,CAAC;MACzE;IACJ;IAEA,IAAI;MACAc,YAAY,CAAC,IAAI,CAAC;MAClBd,QAAQ,CAAC,EAAE,CAAC;MAEZ,MAAMoB,QAAQ,GAAG,MAAM5C,KAAK,CAACyD,IAAI,CAAC,GAAG7C,YAAY,yCAAyCE,OAAO,EAAE,EAAE;QACjG4C,iBAAiB,EAAEvB;MACvB,CAAC,CAAC;MAEF,IAAIS,QAAQ,CAACE,IAAI,CAACrB,OAAO,EAAE;QACvBC,UAAU,CAAC,mCAAmC,CAAC;QAC/CN,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,MAAM;QACHI,QAAQ,CAACoB,QAAQ,CAACE,IAAI,CAACM,OAAO,IAAI,2BAA2B,CAAC;MAClE;IACJ,CAAC,CAAC,OAAOC,GAAG,EAAE;MACV7B,QAAQ,CAAC,+CAA+C,CAAC;MACzD8B,OAAO,CAAC/B,KAAK,CAAC,yBAAyB,EAAE8B,GAAG,CAAC;IACjD,CAAC,SAAS;MACNf,YAAY,CAAC,KAAK,CAAC;IACvB;EACJ,CAAC;EAED,MAAMqB,eAAe,GAAG,MAAAA,CAAOC,IAAI,EAAEC,IAAI,GAAG,KAAK,KAAK;IAClD,IAAI;MACA,MAAMC,SAAS,CAACC,SAAS,CAACC,SAAS,CAACJ,IAAI,CAAC;MACzC,IAAIC,IAAI,KAAK,KAAK,EAAE;QAChBrB,SAAS,CAAC,IAAI,CAAC;QACfW,UAAU,CAAC,MAAMX,SAAS,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC;MAC5C,CAAC,MAAM;QACHE,oBAAoB,CAAC,IAAI,CAAC;QAC1BS,UAAU,CAAC,MAAMT,oBAAoB,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC;MACvD;IACJ,CAAC,CAAC,OAAOW,GAAG,EAAE;MACVC,OAAO,CAAC/B,KAAK,CAAC,8BAA8B,EAAE8B,GAAG,CAAC;IACtD;EACJ,CAAC;EAED,MAAMY,mBAAmB,GAAGA,CAAA,KAAM;IAC9B,MAAMC,OAAO,GAAG,gDAAgD,IAAIC,IAAI,CAAC,CAAC,CAACC,cAAc,CAAC,CAAC,YAAYrD,QAAQ,OAAOgB,WAAW,CAACsC,IAAI,CAAC,IAAI,CAAC,wEAAwE;IACpN,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACL,OAAO,CAAC,EAAE;MAAEL,IAAI,EAAE;IAAa,CAAC,CAAC;IACxD,MAAMW,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;IACrC,MAAMK,CAAC,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACrCF,CAAC,CAACG,IAAI,GAAGN,GAAG;IACZG,CAAC,CAACI,QAAQ,GAAG,0BAA0BhE,QAAQ,MAAM;IACrD6D,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,CAAC,CAAC;IAC5BA,CAAC,CAACO,KAAK,CAAC,CAAC;IACTN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,CAAC,CAAC;IAC5BF,GAAG,CAACW,eAAe,CAACZ,GAAG,CAAC;EAC5B,CAAC;EAED,MAAMa,gBAAgB,GAAGA,CAAA,KAAM;IAC3BrE,SAAS,CAAC;MACNoC,OAAO,EAAE,oDAAoD;MAC7DkC,WAAW,EAAE,KAAK;MAClBrC,YAAY,EAAElB;IAClB,CAAC,CAAC;EACN,CAAC;EAED,IAAIV,OAAO,EAAE;IACT,oBACIV,OAAA;MAAK4E,SAAS,EAAC,0DAA0D;MAAAC,QAAA,eACrE7E,OAAA;QAAK4E,SAAS,EAAC,aAAa;QAAAC,QAAA,gBACxB7E,OAAA,CAACL,SAAS;UAACiF,SAAS,EAAC;QAAkD;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1EjF,OAAA;UAAG4E,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd;EAEA,oBACIjF,OAAA;IAAK4E,SAAS,EAAC,qFAAqF;IAAAC,QAAA,eAChG7E,OAAA;MAAK4E,SAAS,EAAC,2BAA2B;MAAAC,QAAA,gBACtC7E,OAAA;QAAA6E,QAAA,gBACI7E,OAAA;UAAK4E,SAAS,EAAC,8EAA8E;UAAAC,QAAA,eACzF7E,OAAA,CAACV,WAAW;YAACsF,SAAS,EAAC;UAAwB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD,CAAC,eACNjF,OAAA;UAAI4E,SAAS,EAAC,wDAAwD;UAAAC,QAAA,EAAC;QAEvE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLjF,OAAA;UAAG4E,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EAAC;QAEtD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJjF,OAAA;UAAG4E,SAAS,EAAC,+CAA+C;UAAAC,QAAA,EACvDzE;QAAQ;UAAA0E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLrE,KAAK,iBACFZ,OAAA;QAAK4E,SAAS,EAAC,wEAAwE;QAAAC,QAAA,gBACnF7E,OAAA,CAACJ,qBAAqB;UAACgF,SAAS,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAClDjF,OAAA;UAAM4E,SAAS,EAAC,sBAAsB;UAAAC,QAAA,EAAEjE;QAAK;UAAAkE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpD,CACR,EAGAnE,OAAO,iBACJd,OAAA;QAAK4E,SAAS,EAAC,4EAA4E;QAAAC,QAAA,gBACvF7E,OAAA,CAACN,OAAO;UAACkF,SAAS,EAAC;QAAgB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACtCjF,OAAA;UAAM4E,SAAS,EAAC,wBAAwB;UAAAC,QAAA,EAAE/D;QAAO;UAAAgE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxD,CACR,EAGAzE,IAAI,KAAK,CAAC,iBACPR,OAAA;QAAK4E,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACtB7E,OAAA;UAAK4E,SAAS,EAAC,aAAa;UAAAC,QAAA,gBACxB7E,OAAA;YAAI4E,SAAS,EAAC,wCAAwC;YAAAC,QAAA,EAAC;UAAoB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChFjF,OAAA;YAAG4E,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAE1C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,EAEH/D,SAAS,iBACNlB,OAAA;YAAK4E,SAAS,EAAC,6CAA6C;YAAAC,QAAA,eACxD7E,OAAA;cAAKkF,GAAG,EAAEhE,SAAU;cAACiE,GAAG,EAAC,aAAa;cAACP,SAAS,EAAC;YAAmB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtE,CACR;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAENjF,OAAA;UAAA6E,QAAA,gBACI7E,OAAA;YAAI4E,SAAS,EAAC,wCAAwC;YAAAC,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxEjF,OAAA;YAAG4E,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAE1C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJjF,OAAA;YAAK4E,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACpC7E,OAAA;cACIkD,IAAI,EAAC,MAAM;cACXkC,KAAK,EAAE9D,cAAe;cACtB+D,QAAQ;cACRT,SAAS,EAAC;YAAiF;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9F,CAAC,eACFjF,OAAA;cACIsF,OAAO,EAAEA,CAAA,KAAMtC,eAAe,CAAC1B,cAAc,CAAE;cAC/CsD,SAAS,EAAC,uFAAuF;cAAAC,QAAA,GAEhGjD,MAAM,gBAAG5B,OAAA,CAACN,OAAO;gBAAAoF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAGjF,OAAA,CAACP,MAAM;gBAAAqF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,EACjCrD,MAAM,GAAG,SAAS,GAAG,MAAM;YAAA;cAAAkD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAENjF,OAAA;UACIsF,OAAO,EAAEA,CAAA,KAAM7E,OAAO,CAAC,CAAC,CAAE;UAC1BmE,SAAS,EAAC,0NAA0N;UAAAC,QAAA,EACvO;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CACR,EAGAzE,IAAI,KAAK,CAAC,iBACPR,OAAA;QAAK4E,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACtB7E,OAAA;UAAK4E,SAAS,EAAC,aAAa;UAAAC,QAAA,gBACxB7E,OAAA;YAAI4E,SAAS,EAAC,wCAAwC;YAAAC,QAAA,EAAC;UAAoB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChFjF,OAAA;YAAG4E,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAE1C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENjF,OAAA;UAAA6E,QAAA,gBACI7E,OAAA;YAAO4E,SAAS,EAAC,8CAA8C;YAAAC,QAAA,EAAC;UAEhE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRjF,OAAA;YACIkD,IAAI,EAAC,MAAM;YACXqC,SAAS,EAAC,GAAG;YACbH,KAAK,EAAE5D,gBAAiB;YACxBgE,QAAQ,EAAGC,CAAC,IAAKhE,mBAAmB,CAACgE,CAAC,CAACC,MAAM,CAACN,KAAK,CAACO,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAE;YACxEf,SAAS,EAAC,yIAAyI;YACnJgB,WAAW,EAAC;UAAQ;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAENjF,OAAA;UAAK4E,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACvB7E,OAAA;YACIsF,OAAO,EAAEA,CAAA,KAAM7E,OAAO,CAAC,CAAC,CAAE;YAC1BmE,SAAS,EAAC,gHAAgH;YAAAC,QAAA,EAC7H;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTjF,OAAA;YACIsF,OAAO,EAAE1C,cAAe;YACxBiD,QAAQ,EAAEnE,SAAS,IAAIF,gBAAgB,CAACqB,MAAM,KAAK,CAAE;YACrD+B,SAAS,EAAC,6QAA6Q;YAAAC,QAAA,GAEtRnD,SAAS,iBAAI1B,OAAA,CAACL,SAAS;cAACiF,SAAS,EAAC;YAAmB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EACxDvD,SAAS,GAAG,cAAc,GAAG,qBAAqB;UAAA;YAAAoD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CACR,EAGAzE,IAAI,KAAK,CAAC,iBACPR,OAAA;QAAK4E,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACtB7E,OAAA;UAAK4E,SAAS,EAAC,aAAa;UAAAC,QAAA,gBACxB7E,OAAA;YAAI4E,SAAS,EAAC,wCAAwC;YAAAC,QAAA,EAAC;UAAyB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrFjF,OAAA;YAAG4E,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAE1C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENjF,OAAA;UAAK4E,SAAS,EAAC,2BAA2B;UAAAC,QAAA,eACtC7E,OAAA;YAAK4E,SAAS,EAAC,0CAA0C;YAAAC,QAAA,EACpDzD,WAAW,CAAC0E,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACzBhG,OAAA;cAAiB4E,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAC/DkB;YAAI,GADCC,KAAK;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEV,CACR;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAENjF,OAAA;UAAK4E,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACvB7E,OAAA;YACIsF,OAAO,EAAEA,CAAA,KAAMtC,eAAe,CAAC5B,WAAW,CAACsC,IAAI,CAAC,IAAI,CAAC,EAAE,OAAO,CAAE;YAChEkB,SAAS,EAAC,uJAAuJ;YAAAC,QAAA,GAEhK/C,iBAAiB,gBAAG9B,OAAA,CAACN,OAAO;cAAAoF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGjF,OAAA,CAACP,MAAM;cAAAqF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EAC5CnD,iBAAiB,GAAG,SAAS,GAAG,YAAY;UAAA;YAAAgD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC,eACTjF,OAAA;YACIsF,OAAO,EAAEhC,mBAAoB;YAC7BsB,SAAS,EAAC,uJAAuJ;YAAAC,QAAA,gBAEjK7E,OAAA,CAACF,UAAU;cAAAgF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,YAElB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eAENjF,OAAA;UACIsF,OAAO,EAAEZ,gBAAiB;UAC1BE,SAAS,EAAC,6NAA6N;UAAAC,QAAA,EAC1O;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CACR,eAGDjF,OAAA;QAAK4E,SAAS,EAAC,aAAa;QAAAC,QAAA,eACxB7E,OAAA;UACIsF,OAAO,EAAEhF,MAAO;UAChBsE,SAAS,EAAC,0FAA0F;UAAAC,QAAA,gBAEpG7E,OAAA,CAACH,WAAW;YAAAiF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,iBAEnB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAAC1E,EAAA,CAnTIL,aAAa;AAAA+F,EAAA,GAAb/F,aAAa;AAqTnB,eAAeA,aAAa;AAAC,IAAA+F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}