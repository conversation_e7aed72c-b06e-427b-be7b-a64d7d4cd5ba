{"ast": null, "code": "import React,{useState,useEffect,useCallback,useRef}from'react';import axios from'axios';import{useNavigate,useLocation}from'react-router-dom';import'./ViewBets.css';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const API_BASE_URL='/backend';function ViewBets(){var _location$state;const[outgoingBets,setOutgoingBets]=useState([]);const[teams,setTeams]=useState([]);const[loading,setLoading]=useState(true);const[error,setError]=useState(null);const[currentPage,setCurrentPage]=useState(1);const[totalPages,setTotalPages]=useState(1);const[itemsPerPage]=useState(20);const[searchTerm,setSearchTerm]=useState('');const userId=localStorage.getItem('userId');const navigate=useNavigate();const location=useLocation();const newBetRef=useRef(null);const[showLinkModal,setShowLinkModal]=useState(false);const[selectedBetLink,setSelectedBetLink]=useState('');const[showBetDetailsModal,setShowBetDetailsModal]=useState(false);const[selectedBet,setSelectedBet]=useState(null);const[newBetId,setNewBetId]=useState(((_location$state=location.state)===null||_location$state===void 0?void 0:_location$state.newBetId)||null);const fetchBets=useCallback(async()=>{try{console.log('Fetching bets...');const response=await axios.get(`${API_BASE_URL}/handlers/get_bets.php`,{params:{userId:userId,page:currentPage,limit:itemsPerPage,search:searchTerm}});console.log('Bets response:',response.data);if(response.data.success){setOutgoingBets(response.data.bets||[]);setTotalPages(response.data.pagination.totalPages);}else{console.error('Failed to fetch bets:',response.data.message);setError(response.data.message||'Failed to fetch bets');}}catch(error){var _error$response,_error$response$data;console.error('Error fetching bets:',error);setError(((_error$response=error.response)===null||_error$response===void 0?void 0:(_error$response$data=_error$response.data)===null||_error$response$data===void 0?void 0:_error$response$data.message)||'An error occurred while fetching bets.');}finally{setLoading(false);}},[userId,currentPage,itemsPerPage,searchTerm]);const fetchTeams=useCallback(async()=>{try{console.log('Fetching teams...');const response=await axios.get(`${API_BASE_URL}/handlers/team_management.php`);console.log('Teams response:',response.data);if(response.data.status===200){setTeams(response.data.data||[]);}else{console.error('Failed to fetch teams:',response.data.message);setError(response.data.message||'Failed to fetch teams');}}catch(error){var _error$response2,_error$response2$data;console.error('Error fetching teams:',error);setError(((_error$response2=error.response)===null||_error$response2===void 0?void 0:(_error$response2$data=_error$response2.data)===null||_error$response2$data===void 0?void 0:_error$response2$data.message)||'An error occurred while fetching teams.');}},[]);useEffect(()=>{if(!userId){navigate('/login');return;}console.log('Initializing ViewBets with userId:',userId);const initializeData=async()=>{try{setError(null);setLoading(true);await Promise.all([fetchBets(),fetchTeams()]);}catch(err){console.error('ViewBets initialization error:',err);setError('Failed to initialize bets view. Please try again later.');}};initializeData();},[userId,fetchBets,fetchTeams,navigate]);useEffect(()=>{if(newBetId&&newBetRef.current){newBetRef.current.scrollIntoView({behavior:'smooth',block:'center'});const timer=setTimeout(()=>{setNewBetId(null);},5000);return()=>clearTimeout(timer);}},[outgoingBets,newBetId]);const handlePageChange=newPage=>{setCurrentPage(newPage);};const handleSearch=e=>{setSearchTerm(e.target.value);setCurrentPage(1);// Reset to first page when searching\n};const renderPagination=()=>{const pages=[];for(let i=1;i<=totalPages;i++){pages.push(/*#__PURE__*/_jsx(\"button\",{onClick:()=>handlePageChange(i),className:`pagination-button ${currentPage===i?'active':''}`,children:i},i));}return/*#__PURE__*/_jsxs(\"div\",{className:\"pagination\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>handlePageChange(currentPage-1),disabled:currentPage===1,className:\"pagination-button\",children:\"Previous\"}),pages,/*#__PURE__*/_jsx(\"button\",{onClick:()=>handlePageChange(currentPage+1),disabled:currentPage===totalPages,className:\"pagination-button\",children:\"Next\"})]});};const calculateOdds=bet=>{const totalPot=parseFloat(bet.amount_user1)*2;const winReturn=parseFloat(bet.potential_return_win_user1);const lossReturn=parseFloat(bet.potential_return_loss_user1);const drawReturn=parseFloat(bet.potential_return_draw_user1);const winOdds=(winReturn/totalPot*100).toFixed(1);const lossOdds=(lossReturn/totalPot*100).toFixed(1);const drawOdds=(drawReturn/totalPot*100).toFixed(1);return{win:{odds:winOdds,return:winReturn},loss:{odds:lossOdds,return:lossReturn},draw:{odds:drawOdds,return:drawReturn}};};const getUserStatus=(bet,isCreator)=>{if(bet.bet_status.toLowerCase()==='joined'||bet.bet_status.toLowerCase()==='completed'){return isCreator?'Creator':'Opponent';}if(isCreator){return'Creator';}return'Waiting for Opponent';};if(loading){return/*#__PURE__*/_jsx(\"div\",{className:\"loading\",children:\"Loading bets...\"});}if(error){return/*#__PURE__*/_jsx(\"div\",{className:\"error\",children:error});}const getTeamLogo=teamName=>{const team=teams.find(team=>team.name===teamName);return team?`${API_BASE_URL}/${team.logo}`:'';};const handleGenerateLink=bet=>{const link=`${window.location.origin}/user/join-challenge2/${bet.challenge_id}/${bet.bet_id}/${bet.unique_code}/${bet.user1_id}`;setSelectedBetLink(link);setShowLinkModal(true);};const handleCloseModal=()=>{setShowLinkModal(false);};const getReference=bet=>{return(bet.unique_code||`${bet.bet_id}DNRBKCC`).toUpperCase();};const handleShowBetDetails=bet=>{setSelectedBet(bet);setShowBetDetailsModal(true);};return/*#__PURE__*/_jsxs(\"div\",{className:\"view-bets-container\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"title-section\",children:[/*#__PURE__*/_jsx(\"h2\",{children:\"Outgoing Bets\"}),/*#__PURE__*/_jsx(\"div\",{className:\"title-line\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"search-container\",children:/*#__PURE__*/_jsx(\"input\",{type:\"text\",placeholder:\"Search by reference number...\",value:searchTerm,onChange:handleSearch,className:\"search-input\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"table-responsive\",children:/*#__PURE__*/_jsxs(\"table\",{className:\"bets-table\",children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{children:\"#\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Ref\"}),/*#__PURE__*/_jsx(\"th\",{colSpan:\"3\",className:\"teams-header compact\",children:\"Teams\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Amount\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Return\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Status\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Match Date\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Actions\"})]})}),/*#__PURE__*/_jsx(\"tbody\",{children:outgoingBets.map((bet,index)=>/*#__PURE__*/_jsxs(\"tr\",{ref:bet.bet_id===newBetId?newBetRef:null,className:bet.bet_id===newBetId?'highlight-new-bet':'',children:[/*#__PURE__*/_jsx(\"td\",{children:(currentPage-1)*itemsPerPage+index+1}),/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsx(\"span\",{className:\"reference\",onClick:()=>handleShowBetDetails(bet),children:getReference(bet)})}),/*#__PURE__*/_jsx(\"td\",{className:\"team-cell compact\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"team-info\",children:[/*#__PURE__*/_jsx(\"img\",{src:getTeamLogo(bet.team_a),alt:bet.team_a,className:\"team-logo\"}),/*#__PURE__*/_jsx(\"span\",{children:bet.team_a}),bet.bet_choice_user1==='team_a_win'&&/*#__PURE__*/_jsx(\"span\",{className:\"pick-badge\",children:\"Your Pick\"})]})}),/*#__PURE__*/_jsx(\"td\",{className:\"vs-cell compact\",children:/*#__PURE__*/_jsx(\"div\",{className:\"vs-indicator\",children:\"VS\"})}),/*#__PURE__*/_jsx(\"td\",{className:\"team-cell compact\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"team-info\",children:[/*#__PURE__*/_jsx(\"img\",{src:getTeamLogo(bet.team_b),alt:bet.team_b,className:\"team-logo\"}),/*#__PURE__*/_jsx(\"span\",{children:bet.team_b}),bet.bet_choice_user1!=='team_a_win'&&/*#__PURE__*/_jsx(\"span\",{className:\"pick-badge\",children:\"Your Pick\"})]})}),/*#__PURE__*/_jsxs(\"td\",{className:\"amount-cell\",children:[bet.amount_user1,\" \",/*#__PURE__*/_jsx(\"span\",{className:\"currency\",children:\"FanCoins\"})]}),/*#__PURE__*/_jsxs(\"td\",{className:\"return-cell\",children:[bet.potential_return_win_user1,\" \",/*#__PURE__*/_jsx(\"span\",{className:\"currency\",children:\"FanCoins\"})]}),/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsx(\"div\",{className:\"status-container\",children:/*#__PURE__*/_jsxs(\"div\",{className:`status-badge ${bet.bet_status}`,children:[/*#__PURE__*/_jsx(\"span\",{className:\"status-dot\"}),/*#__PURE__*/_jsxs(\"span\",{className:\"status-text\",children:[bet.bet_status==='open'&&'Open',bet.bet_status==='joined'&&'Joined',bet.bet_status==='completed'&&'Completed']})]})})}),/*#__PURE__*/_jsx(\"td\",{className:\"date-cell\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"date-display\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"date-line\",children:new Date(bet.match_date).toLocaleDateString()}),/*#__PURE__*/_jsx(\"div\",{className:\"time-line\",children:new Date(bet.match_date).toLocaleTimeString([],{hour:'2-digit',minute:'2-digit'})})]})}),/*#__PURE__*/_jsx(\"td\",{children:bet.bet_status==='open'&&/*#__PURE__*/_jsx(\"button\",{onClick:()=>handleGenerateLink(bet),className:\"generate-link-btn\",children:\"Generate Link\"})})]},bet.bet_id))})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"mobile-bets-grid\",children:outgoingBets.map((bet,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"mobile-bet-card\",ref:bet.bet_id===newBetId?newBetRef:null,\"data-status\":bet.bet_status.toLowerCase(),children:[/*#__PURE__*/_jsxs(\"div\",{className:\"mobile-bet-header\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"mobile-bet-ref\",onClick:()=>handleShowBetDetails(bet),children:getReference(bet)}),/*#__PURE__*/_jsxs(\"div\",{className:`status-badge ${bet.bet_status.toLowerCase()}`,children:[/*#__PURE__*/_jsx(\"span\",{className:\"status-dot\"}),bet.bet_status]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mobile-users-section\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"mobile-user-info\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"mobile-user-name\",children:bet.user1_username}),/*#__PURE__*/_jsx(\"span\",{className:\"mobile-user-status creator\",children:getUserStatus(bet,true)})]}),/*#__PURE__*/_jsx(\"div\",{className:\"mobile-vs-divider\",children:\"VS\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"mobile-user-info\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"mobile-user-name\",children:bet.user2_username||'---'}),/*#__PURE__*/_jsx(\"span\",{className:`mobile-user-status ${bet.user2_username?'opponent':'waiting'}`,children:getUserStatus(bet,false)})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mobile-teams-container\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"mobile-team\",children:[/*#__PURE__*/_jsx(\"img\",{src:getTeamLogo(bet.team_a),alt:bet.team_a,className:\"mobile-team-logo\"}),/*#__PURE__*/_jsx(\"span\",{children:bet.team_a})]}),/*#__PURE__*/_jsx(\"div\",{className:\"mobile-vs\",children:\"VS\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"mobile-team\",children:[/*#__PURE__*/_jsx(\"img\",{src:getTeamLogo(bet.team_b),alt:bet.team_b,className:\"mobile-team-logo\"}),/*#__PURE__*/_jsx(\"span\",{children:bet.team_b})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mobile-bet-details\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"mobile-detail-item\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"mobile-detail-label\",children:\"Amount\"}),/*#__PURE__*/_jsxs(\"span\",{className:\"mobile-detail-value\",children:[bet.amount_user1,\" FanCoins\"]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mobile-detail-item\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"mobile-detail-label\",children:\"Potential Return\"}),/*#__PURE__*/_jsxs(\"span\",{className:\"mobile-detail-value\",children:[bet.potential_return_win_user1,\" FanCoins\"]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mobile-detail-item full-width\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"mobile-detail-label\",children:\"Match Date\"}),/*#__PURE__*/_jsx(\"span\",{className:\"mobile-detail-value\",children:new Date(bet.match_date).toLocaleString()})]})]}),bet.bet_status==='open'&&/*#__PURE__*/_jsx(\"div\",{className:\"mobile-bet-actions\",children:/*#__PURE__*/_jsx(\"button\",{className:\"mobile-action-button\",onClick:()=>handleGenerateLink(bet),children:\"Generate Link\"})})]},bet.bet_id))}),window.innerWidth>768&&showBetDetailsModal&&selectedBet&&/*#__PURE__*/_jsx(\"div\",{className:\"modal-overlay\",onClick:()=>setShowBetDetailsModal(false),children:/*#__PURE__*/_jsxs(\"div\",{className:\"bet-details-modal\",onClick:e=>e.stopPropagation(),children:[/*#__PURE__*/_jsx(\"button\",{className:\"close-button\",onClick:()=>setShowBetDetailsModal(false),children:\"\\xD7\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"modal-left\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"modal-header\",children:[/*#__PURE__*/_jsxs(\"h3\",{className:\"reference-title\",children:[\"Bet Reference: \",getReference(selectedBet)]}),/*#__PURE__*/_jsxs(\"div\",{className:\"status-badges\",children:[/*#__PURE__*/_jsxs(\"div\",{className:`status-badge-large ${selectedBet.bet_status}`,children:[selectedBet.bet_status==='open'&&'OPEN FOR BETS',selectedBet.bet_status==='joined'&&'BET MATCHED',selectedBet.bet_status==='completed'&&'COMPLETED']}),/*#__PURE__*/_jsx(\"div\",{className:`match-type-badge-large ${selectedBet.match_type}`,children:selectedBet.match_type==='half_time'?'HALF TIME':'FULL TIME'})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"teams-match\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"team-card\",children:[selectedBet.bet_choice_user1==='team_a_win'&&/*#__PURE__*/_jsx(\"div\",{className:\"selected-badge\",children:\"Selected\"}),/*#__PURE__*/_jsx(\"img\",{src:getTeamLogo(selectedBet.team_a),alt:selectedBet.team_a}),/*#__PURE__*/_jsx(\"div\",{className:\"team-name\",children:selectedBet.team_a}),/*#__PURE__*/_jsx(\"div\",{className:\"team-username\",children:selectedBet.user1_username}),/*#__PURE__*/_jsxs(\"div\",{className:\"team-odds\",children:[selectedBet.odds_team_a,\"x\"]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"vs-badge\",children:\"VS\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"team-card\",children:[selectedBet.bet_choice_user1==='team_b_win'&&/*#__PURE__*/_jsx(\"div\",{className:\"selected-badge\",children:\"Selected\"}),/*#__PURE__*/_jsx(\"img\",{src:getTeamLogo(selectedBet.team_b),alt:selectedBet.team_b}),/*#__PURE__*/_jsx(\"div\",{className:\"team-name\",children:selectedBet.team_b}),/*#__PURE__*/_jsx(\"div\",{className:\"team-username\",children:selectedBet.user2_username||'Waiting for opponent'}),/*#__PURE__*/_jsxs(\"div\",{className:\"team-odds\",children:[selectedBet.odds_team_b,\"x\"]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"match-details-grid\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"details-section schedule-section\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"section-title\",children:\"MATCH SCHEDULE\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"schedule-grid\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"schedule-item\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"schedule-label\",children:\"MATCH DATE\"}),/*#__PURE__*/_jsx(\"span\",{className:\"schedule-value\",children:new Date(selectedBet.match_date).toLocaleString()})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"schedule-item\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"schedule-label\",children:\"START TIME\"}),/*#__PURE__*/_jsx(\"span\",{className:\"schedule-value\",children:new Date(selectedBet.start_time).toLocaleTimeString()})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"schedule-item\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"schedule-label\",children:\"END TIME\"}),/*#__PURE__*/_jsx(\"span\",{className:\"schedule-value\",children:new Date(selectedBet.end_time).toLocaleTimeString()})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"schedule-item\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"schedule-label\",children:\"CHALLENGE CREATED\"}),/*#__PURE__*/_jsx(\"span\",{className:\"schedule-value\",children:new Date(selectedBet.challenge_date).toLocaleString()})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"details-section odds-section\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"section-title\",children:\"ODDS INFORMATION\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"odds-grid\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"odds-item\",children:[/*#__PURE__*/_jsxs(\"span\",{className:\"odds-label\",children:[selectedBet.team_a,\" WIN\"]}),/*#__PURE__*/_jsxs(\"span\",{className:\"odds-value\",children:[selectedBet.odds_team_a,\"x\"]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"odds-item\",children:[/*#__PURE__*/_jsxs(\"span\",{className:\"odds-label\",children:[selectedBet.team_b,\" WIN\"]}),/*#__PURE__*/_jsxs(\"span\",{className:\"odds-value\",children:[selectedBet.odds_team_b,\"x\"]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"odds-item\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"odds-label\",children:\"DRAW\"}),/*#__PURE__*/_jsxs(\"span\",{className:\"odds-value\",children:[selectedBet.odds_draw,\"x\"]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"odds-item\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"odds-label\",children:\"LOSS MULTIPLIER\"}),/*#__PURE__*/_jsxs(\"span\",{className:\"odds-value\",children:[selectedBet.odds_lost,\"x\"]})]})]})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"modal-right\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"details-section\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"section-title\",children:\"BET STATUS\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"detail-row\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"detail-label\",children:\"CREATED BY\"}),/*#__PURE__*/_jsx(\"span\",{className:\"detail-value created-by\",children:selectedBet.user1_username})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"detail-row\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"detail-label\",children:\"STATUS\"}),/*#__PURE__*/_jsx(\"span\",{className:\"detail-value status-value\",children:selectedBet.user2_username?`Joined by ${selectedBet.user2_username}`:'Waiting for opponent'})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"details-section\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"section-title\",children:\"FINANCIAL DETAILS\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"detail-row\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"detail-label\",children:\"YOUR BET\"}),/*#__PURE__*/_jsxs(\"span\",{className:\"detail-value amount\",children:[selectedBet.amount_user1,\" FanCoins\"]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"detail-row\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"detail-label\",children:\"POTENTIAL WIN\"}),/*#__PURE__*/_jsxs(\"span\",{className:\"detail-value return\",children:[selectedBet.potential_return_win_user1,\" FanCoins\"]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"detail-row\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"detail-label\",children:\"POTENTIAL LOSS\"}),/*#__PURE__*/_jsxs(\"span\",{className:\"detail-value amount\",children:[selectedBet.potential_loss_user1,\" FanCoins\"]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"detail-row\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"detail-label\",children:\"DRAW OUTCOME\"}),/*#__PURE__*/_jsxs(\"span\",{className:\"detail-value\",children:[selectedBet.potential_draw_win_user1,\" FanCoins\"]})]}),selectedBet.user2_username&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"detail-row\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"detail-label\",children:\"OPPONENT BET\"}),/*#__PURE__*/_jsxs(\"span\",{className:\"detail-value amount\",children:[selectedBet.amount_user2,\" FanCoins\"]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"detail-row\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"detail-label\",children:\"OPPONENT WIN\"}),/*#__PURE__*/_jsxs(\"span\",{className:\"detail-value return\",children:[selectedBet.potential_return_win_user2,\" FanCoins\"]})]})]})]})]})]})}),window.innerWidth<=768&&showBetDetailsModal&&selectedBet&&/*#__PURE__*/_jsx(\"div\",{className:\"modal-overlay\",onClick:()=>setShowBetDetailsModal(false),children:/*#__PURE__*/_jsxs(\"div\",{className:\"mobile-modal-content\",onClick:e=>e.stopPropagation(),children:[/*#__PURE__*/_jsxs(\"div\",{className:\"mobile-modal-header\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"Bet Details\"}),/*#__PURE__*/_jsx(\"button\",{className:\"mobile-modal-close\",onClick:()=>setShowBetDetailsModal(false),children:\"\\xD7\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mobile-modal-body\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"mobile-ref-section\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"mobile-ref-number\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"mobile-ref-label\",children:\"Reference Number\"}),/*#__PURE__*/_jsx(\"span\",{className:\"mobile-ref-value\",children:getReference(selectedBet)})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mobile-status-badges\",children:[/*#__PURE__*/_jsxs(\"span\",{className:`mobile-status-badge ${selectedBet.bet_status.toLowerCase()}`,children:[selectedBet.bet_status==='open'&&'OPEN FOR BETS',selectedBet.bet_status==='joined'&&'BET MATCHED',selectedBet.bet_status==='completed'&&'COMPLETED']}),/*#__PURE__*/_jsx(\"span\",{className:\"mobile-status-badge mobile-match-type\",children:selectedBet.match_type==='half_time'?'HALF TIME':'FULL TIME'})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mobile-users-section\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"mobile-user-info\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"mobile-user-name\",children:selectedBet.user1_username}),/*#__PURE__*/_jsx(\"span\",{className:\"mobile-user-status creator\",children:getUserStatus(selectedBet,true)})]}),/*#__PURE__*/_jsx(\"div\",{className:\"mobile-vs-divider\",children:\"VS\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"mobile-user-info\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"mobile-user-name\",children:selectedBet.user2_username||'---'}),/*#__PURE__*/_jsx(\"span\",{className:`mobile-user-status ${selectedBet.user2_username?'opponent':'waiting'}`,children:getUserStatus(selectedBet,false)})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mobile-teams-container\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"mobile-team\",children:[/*#__PURE__*/_jsx(\"img\",{src:getTeamLogo(selectedBet.team_a),alt:selectedBet.team_a,className:\"mobile-team-logo\"}),/*#__PURE__*/_jsx(\"span\",{children:selectedBet.team_a})]}),/*#__PURE__*/_jsx(\"div\",{className:\"mobile-vs\",children:\"VS\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"mobile-team\",children:[/*#__PURE__*/_jsx(\"img\",{src:getTeamLogo(selectedBet.team_b),alt:selectedBet.team_b,className:\"mobile-team-logo\"}),/*#__PURE__*/_jsx(\"span\",{children:selectedBet.team_b})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"mobile-section-title\",children:\"ODDS INFORMATION\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"mobile-odds-grid\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"mobile-odds-item\",children:[/*#__PURE__*/_jsxs(\"span\",{className:\"mobile-odds-label\",children:[selectedBet.team_a,\" WIN\"]}),/*#__PURE__*/_jsxs(\"span\",{className:\"mobile-odds-value\",children:[selectedBet.odds_team_a,\"x\"]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mobile-odds-item\",children:[/*#__PURE__*/_jsxs(\"span\",{className:\"mobile-odds-label\",children:[selectedBet.team_b,\" WIN\"]}),/*#__PURE__*/_jsxs(\"span\",{className:\"mobile-odds-value\",children:[selectedBet.odds_team_b,\"x\"]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mobile-odds-item\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"mobile-odds-label\",children:\"DRAW\"}),/*#__PURE__*/_jsxs(\"span\",{className:\"mobile-odds-value\",children:[selectedBet.odds_draw,\"x\"]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mobile-odds-item\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"mobile-odds-label\",children:\"LOSS\"}),/*#__PURE__*/_jsxs(\"span\",{className:\"mobile-odds-value\",children:[selectedBet.odds_lost,\"x\"]})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"mobile-section-title\",children:\"FINANCIAL DETAILS\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"mobile-financial-grid\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"mobile-financial-item\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"mobile-financial-label\",children:\"Your Bet\"}),/*#__PURE__*/_jsxs(\"span\",{className:\"mobile-financial-value\",children:[selectedBet.amount_user1,\" FC\"]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mobile-financial-item\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"mobile-financial-label\",children:\"Potential Win\"}),/*#__PURE__*/_jsxs(\"span\",{className:\"mobile-financial-value win\",children:[\"+\",selectedBet.potential_return_win_user1,\" FC\"]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mobile-financial-item\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"mobile-financial-label\",children:\"Potential Loss\"}),/*#__PURE__*/_jsxs(\"span\",{className:\"mobile-financial-value loss\",children:[\"-\",selectedBet.potential_return_loss_user1,\" FC\"]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mobile-financial-item\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"mobile-financial-label\",children:\"Draw Return\"}),/*#__PURE__*/_jsxs(\"span\",{className:\"mobile-financial-value draw\",children:[selectedBet.potential_return_draw_user1,\" FC\"]})]})]})]}),selectedBet.bet_status==='open'&&/*#__PURE__*/_jsx(\"div\",{className:\"mobile-modal-footer\",children:/*#__PURE__*/_jsx(\"button\",{className:\"mobile-modal-action-button\",onClick:()=>handleGenerateLink(selectedBet),children:\"Generate Link\"})})]})}),showLinkModal&&/*#__PURE__*/_jsx(\"div\",{className:\"modal-overlay\",onClick:handleCloseModal,children:/*#__PURE__*/_jsxs(\"div\",{className:\"link-modal\",onClick:e=>e.stopPropagation(),children:[/*#__PURE__*/_jsx(\"span\",{className:\"close\",onClick:handleCloseModal,children:\"\\xD7\"}),/*#__PURE__*/_jsx(\"h3\",{children:\"Share this link with your friend\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"link-container\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"text\",value:selectedBetLink,readOnly:true}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{navigator.clipboard.writeText(selectedBetLink);// Show a temporary success message instead of alert\nconst button=document.activeElement;const originalText=button.textContent;button.textContent='Copied!';button.style.backgroundColor='#16a34a';setTimeout(()=>{button.textContent=originalText;button.style.backgroundColor='';},2000);},children:\"Copy\"})]})]})}),/*#__PURE__*/_jsx(\"style\",{jsx:true,children:`\n        .teams-header.compact {\n          width: 20%;  /* Reduced from 30% */\n        }\n        \n        .team-cell.compact {\n          max-width: 80px;  /* Reduced from 100px */\n          padding: 4px;  /* Reduced padding */\n        }\n        \n        .vs-cell.compact {\n          padding: 0 4px;  /* Reduced padding */\n          width: 30px;  /* Fixed width */\n        }\n        \n        .team-info {\n          display: flex;\n          align-items: center;\n          gap: 4px;  /* Reduced gap between elements */\n          max-width: 100%;\n        }\n        \n        .team-info span {\n          overflow: hidden;\n          text-overflow: ellipsis;\n          white-space: nowrap;\n          font-size: 0.9rem;\n        }\n\n        .date-cell {\n          padding: 4px 8px;\n          min-width: 100px;\n        }\n\n        .date-display {\n          display: flex;\n          flex-direction: column;\n          gap: 2px;\n          font-size: 0.85rem;\n        }\n\n        .date-line {\n          color: #333;\n        }\n\n        .time-line {\n          color: #666;\n          font-size: 0.8rem;\n        }\n      `})]});}export default ViewBets;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useRef", "axios", "useNavigate", "useLocation", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "API_BASE_URL", "ViewBets", "_location$state", "outgoingBets", "setOutgoingBets", "teams", "setTeams", "loading", "setLoading", "error", "setError", "currentPage", "setCurrentPage", "totalPages", "setTotalPages", "itemsPerPage", "searchTerm", "setSearchTerm", "userId", "localStorage", "getItem", "navigate", "location", "newBetRef", "showLinkModal", "setShowLinkModal", "selectedBetLink", "setSelectedBetLink", "showBetDetailsModal", "setShowBetDetailsModal", "selectedBet", "setSelectedBet", "newBetId", "setNewBetId", "state", "fetchBets", "console", "log", "response", "get", "params", "page", "limit", "search", "data", "success", "bets", "pagination", "message", "_error$response", "_error$response$data", "fetchTeams", "status", "_error$response2", "_error$response2$data", "initializeData", "Promise", "all", "err", "current", "scrollIntoView", "behavior", "block", "timer", "setTimeout", "clearTimeout", "handlePageChange", "newPage", "handleSearch", "e", "target", "value", "renderPagination", "pages", "i", "push", "onClick", "className", "children", "disabled", "calculateOdds", "bet", "totalPot", "parseFloat", "amount_user1", "winReturn", "potential_return_win_user1", "lossReturn", "potential_return_loss_user1", "drawReturn", "potential_return_draw_user1", "winOdds", "toFixed", "lossOdds", "drawOdds", "win", "odds", "return", "loss", "draw", "getUserStatus", "isCreator", "bet_status", "toLowerCase", "getTeamLogo", "teamName", "team", "find", "name", "logo", "handleGenerateLink", "link", "window", "origin", "challenge_id", "bet_id", "unique_code", "user1_id", "handleCloseModal", "getReference", "toUpperCase", "handleShowBetDetails", "type", "placeholder", "onChange", "colSpan", "map", "index", "ref", "src", "team_a", "alt", "bet_choice_user1", "team_b", "Date", "match_date", "toLocaleDateString", "toLocaleTimeString", "hour", "minute", "user1_username", "user2_username", "toLocaleString", "innerWidth", "stopPropagation", "match_type", "odds_team_a", "odds_team_b", "start_time", "end_time", "challenge_date", "odds_draw", "odds_lost", "potential_loss_user1", "potential_draw_win_user1", "amount_user2", "potential_return_win_user2", "readOnly", "navigator", "clipboard", "writeText", "button", "document", "activeElement", "originalText", "textContent", "style", "backgroundColor"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/pages/ViewBets.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback, useRef } from 'react';\nimport axios from 'axios';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport './ViewBets.css';\n\nconst API_BASE_URL = '/backend';\n\nfunction ViewBets() {\n  const [outgoingBets, setOutgoingBets] = useState([]);\n  const [teams, setTeams] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [currentPage, setCurrentPage] = useState(1);\n  const [totalPages, setTotalPages] = useState(1);\n  const [itemsPerPage] = useState(20);\n  const [searchTerm, setSearchTerm] = useState('');\n  const userId = localStorage.getItem('userId');\n  const navigate = useNavigate();\n  const location = useLocation();\n  const newBetRef = useRef(null);\n\n  const [showLinkModal, setShowLinkModal] = useState(false);\n  const [selectedBetLink, setSelectedBetLink] = useState('');\n  const [showBetDetailsModal, setShowBetDetailsModal] = useState(false);\n  const [selectedBet, setSelectedBet] = useState(null);\n  const [newBetId, setNewBetId] = useState(location.state?.newBetId || null);\n\n  const fetchBets = useCallback(async () => {\n    try {\n      console.log('Fetching bets...');\n      const response = await axios.get(`${API_BASE_URL}/handlers/get_bets.php`, {\n        params: {\n          userId: userId,\n          page: currentPage,\n          limit: itemsPerPage,\n          search: searchTerm\n        }\n      });\n      console.log('Bets response:', response.data);\n\n      if (response.data.success) {\n        setOutgoingBets(response.data.bets || []);\n        setTotalPages(response.data.pagination.totalPages);\n      } else {\n        console.error('Failed to fetch bets:', response.data.message);\n        setError(response.data.message || 'Failed to fetch bets');\n      }\n    } catch (error) {\n      console.error('Error fetching bets:', error);\n      setError(error.response?.data?.message || 'An error occurred while fetching bets.');\n    } finally {\n      setLoading(false);\n    }\n  }, [userId, currentPage, itemsPerPage, searchTerm]);\n\n  const fetchTeams = useCallback(async () => {\n    try {\n      console.log('Fetching teams...');\n      const response = await axios.get(`${API_BASE_URL}/handlers/team_management.php`);\n      console.log('Teams response:', response.data);\n\n      if (response.data.status === 200) {\n        setTeams(response.data.data || []);\n      } else {\n        console.error('Failed to fetch teams:', response.data.message);\n        setError(response.data.message || 'Failed to fetch teams');\n      }\n    } catch (error) {\n      console.error('Error fetching teams:', error);\n      setError(error.response?.data?.message || 'An error occurred while fetching teams.');\n    }\n  }, []);\n\n  useEffect(() => {\n    if (!userId) {\n      navigate('/login');\n      return;\n    }\n\n    console.log('Initializing ViewBets with userId:', userId);\n    const initializeData = async () => {\n      try {\n        setError(null);\n        setLoading(true);\n        await Promise.all([fetchBets(), fetchTeams()]);\n      } catch (err) {\n        console.error('ViewBets initialization error:', err);\n        setError('Failed to initialize bets view. Please try again later.');\n      }\n    };\n\n    initializeData();\n  }, [userId, fetchBets, fetchTeams, navigate]);\n\n  useEffect(() => {\n    if (newBetId && newBetRef.current) {\n      newBetRef.current.scrollIntoView({ behavior: 'smooth', block: 'center' });\n      const timer = setTimeout(() => {\n        setNewBetId(null);\n      }, 5000);\n      return () => clearTimeout(timer);\n    }\n  }, [outgoingBets, newBetId]);\n\n  const handlePageChange = (newPage) => {\n    setCurrentPage(newPage);\n  };\n\n  const handleSearch = (e) => {\n    setSearchTerm(e.target.value);\n    setCurrentPage(1); // Reset to first page when searching\n  };\n\n  const renderPagination = () => {\n    const pages = [];\n    for (let i = 1; i <= totalPages; i++) {\n      pages.push(\n        <button\n          key={i}\n          onClick={() => handlePageChange(i)}\n          className={`pagination-button ${currentPage === i ? 'active' : ''}`}\n        >\n          {i}\n        </button>\n      );\n    }\n    return (\n      <div className=\"pagination\">\n        <button\n          onClick={() => handlePageChange(currentPage - 1)}\n          disabled={currentPage === 1}\n          className=\"pagination-button\"\n        >\n          Previous\n        </button>\n        {pages}\n        <button\n          onClick={() => handlePageChange(currentPage + 1)}\n          disabled={currentPage === totalPages}\n          className=\"pagination-button\"\n        >\n          Next\n        </button>\n      </div>\n    );\n  };\n\n  const calculateOdds = (bet) => {\n    const totalPot = parseFloat(bet.amount_user1) * 2;\n    const winReturn = parseFloat(bet.potential_return_win_user1);\n    const lossReturn = parseFloat(bet.potential_return_loss_user1);\n    const drawReturn = parseFloat(bet.potential_return_draw_user1);\n    \n    const winOdds = (winReturn / totalPot * 100).toFixed(1);\n    const lossOdds = (lossReturn / totalPot * 100).toFixed(1);\n    const drawOdds = (drawReturn / totalPot * 100).toFixed(1);\n    \n    return {\n      win: { odds: winOdds, return: winReturn },\n      loss: { odds: lossOdds, return: lossReturn },\n      draw: { odds: drawOdds, return: drawReturn }\n    };\n  };\n\n  const getUserStatus = (bet, isCreator) => {\n    if (bet.bet_status.toLowerCase() === 'joined' || bet.bet_status.toLowerCase() === 'completed') {\n      return isCreator ? 'Creator' : 'Opponent';\n    }\n    \n    if (isCreator) {\n      return 'Creator';\n    }\n    \n    return 'Waiting for Opponent';\n  };\n\n  if (loading) {\n    return <div className=\"loading\">Loading bets...</div>;\n  }\n\n  if (error) {\n    return <div className=\"error\">{error}</div>;\n  }\n\n  const getTeamLogo = (teamName) => {\n    const team = teams.find(team => team.name === teamName);\n    return team ? `${API_BASE_URL}/${team.logo}` : '';\n  };\n\n  const handleGenerateLink = (bet) => {\n    const link = `${window.location.origin}/user/join-challenge2/${bet.challenge_id}/${bet.bet_id}/${bet.unique_code}/${bet.user1_id}`;\n    setSelectedBetLink(link);\n    setShowLinkModal(true);\n  };\n\n  const handleCloseModal = () => {\n    setShowLinkModal(false);\n  };\n\n  const getReference = (bet) => {\n    return (bet.unique_code || `${bet.bet_id}DNRBKCC`).toUpperCase();\n  };\n\n  const handleShowBetDetails = (bet) => {\n    setSelectedBet(bet);\n    setShowBetDetailsModal(true);\n  };\n\n  return (\n    <div className=\"view-bets-container\">\n      <div className=\"title-section\">\n        <h2>Outgoing Bets</h2>\n        <div className=\"title-line\"></div>\n      </div>\n      \n      <div className=\"search-container\">\n        <input\n          type=\"text\"\n          placeholder=\"Search by reference number...\"\n          value={searchTerm}\n          onChange={handleSearch}\n          className=\"search-input\"\n        />\n      </div>\n\n      {/* Desktop Table View */}\n      <div className=\"table-responsive\">\n        <table className=\"bets-table\">\n          <thead>\n            <tr>\n              <th>#</th>\n              <th>Ref</th>\n              <th colSpan=\"3\" className=\"teams-header compact\">Teams</th>\n              <th>Amount</th>\n              <th>Return</th>\n              <th>Status</th>\n              <th>Match Date</th>\n              <th>Actions</th>\n            </tr>\n          </thead>\n          <tbody>\n            {outgoingBets.map((bet, index) => (\n              <tr \n                key={bet.bet_id} \n                ref={bet.bet_id === newBetId ? newBetRef : null}\n                className={bet.bet_id === newBetId ? 'highlight-new-bet' : ''}\n              >\n                <td>{(currentPage - 1) * itemsPerPage + index + 1}</td>\n                <td>\n                  <span className=\"reference\" onClick={() => handleShowBetDetails(bet)}>\n                    {getReference(bet)}\n                  </span>\n                </td>\n                <td className=\"team-cell compact\">\n                  <div className=\"team-info\">\n                    <img \n                      src={getTeamLogo(bet.team_a)} \n                      alt={bet.team_a}\n                      className=\"team-logo\"\n                    />\n                    <span>{bet.team_a}</span>\n                    {bet.bet_choice_user1 === 'team_a_win' && <span className=\"pick-badge\">Your Pick</span>}\n                  </div>\n                </td>\n                <td className=\"vs-cell compact\">\n                  <div className=\"vs-indicator\">VS</div>\n                </td>\n                <td className=\"team-cell compact\">\n                  <div className=\"team-info\">\n                    <img \n                      src={getTeamLogo(bet.team_b)} \n                      alt={bet.team_b}\n                      className=\"team-logo\"\n                    />\n                    <span>{bet.team_b}</span>\n                    {bet.bet_choice_user1 !== 'team_a_win' && <span className=\"pick-badge\">Your Pick</span>}\n                  </div>\n                </td>\n                <td className=\"amount-cell\">\n                  {bet.amount_user1} <span className=\"currency\">FanCoins</span>\n                </td>\n                <td className=\"return-cell\">\n                  {bet.potential_return_win_user1} <span className=\"currency\">FanCoins</span>\n                </td>\n                <td>\n                  <div className=\"status-container\">\n                    <div className={`status-badge ${bet.bet_status}`}>\n                      <span className=\"status-dot\"></span>\n                      <span className=\"status-text\">\n                        {bet.bet_status === 'open' && 'Open'}\n                        {bet.bet_status === 'joined' && 'Joined'}\n                        {bet.bet_status === 'completed' && 'Completed'}\n                      </span>\n                    </div>\n                  </div>\n                </td>\n                <td className=\"date-cell\">\n                  <div className=\"date-display\">\n                    <div className=\"date-line\">{new Date(bet.match_date).toLocaleDateString()}</div>\n                    <div className=\"time-line\">{new Date(bet.match_date).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}</div>\n                  </div>\n                </td>\n                <td>\n                  {bet.bet_status === 'open' && (\n                    <button onClick={() => handleGenerateLink(bet)} className=\"generate-link-btn\">\n                      Generate Link\n                    </button>\n                  )}\n                </td>\n              </tr>\n            ))}\n          </tbody>\n        </table>\n      </div>\n\n      {/* Mobile Card View */}\n      <div className=\"mobile-bets-grid\">\n        {outgoingBets.map((bet, index) => (\n          <div \n            key={bet.bet_id}\n            className=\"mobile-bet-card\"\n            ref={bet.bet_id === newBetId ? newBetRef : null}\n            data-status={bet.bet_status.toLowerCase()}\n          >\n            <div className=\"mobile-bet-header\">\n              <span className=\"mobile-bet-ref\" onClick={() => handleShowBetDetails(bet)}>\n                {getReference(bet)}\n              </span>\n              <div className={`status-badge ${bet.bet_status.toLowerCase()}`}>\n                <span className=\"status-dot\"></span>\n                {bet.bet_status}\n              </div>\n            </div>\n\n            <div className=\"mobile-users-section\">\n              <div className=\"mobile-user-info\">\n                <span className=\"mobile-user-name\">{bet.user1_username}</span>\n                <span className=\"mobile-user-status creator\">\n                  {getUserStatus(bet, true)}\n                </span>\n              </div>\n              <div className=\"mobile-vs-divider\">VS</div>\n              <div className=\"mobile-user-info\">\n                <span className=\"mobile-user-name\">\n                  {bet.user2_username || '---'}\n                </span>\n                <span className={`mobile-user-status ${bet.user2_username ? 'opponent' : 'waiting'}`}>\n                  {getUserStatus(bet, false)}\n                </span>\n              </div>\n            </div>\n\n            <div className=\"mobile-teams-container\">\n              <div className=\"mobile-team\">\n                <img \n                  src={getTeamLogo(bet.team_a)}\n                  alt={bet.team_a}\n                  className=\"mobile-team-logo\"\n                />\n                <span>{bet.team_a}</span>\n              </div>\n              <div className=\"mobile-vs\">VS</div>\n              <div className=\"mobile-team\">\n                <img \n                  src={getTeamLogo(bet.team_b)}\n                  alt={bet.team_b}\n                  className=\"mobile-team-logo\"\n                />\n                <span>{bet.team_b}</span>\n              </div>\n            </div>\n\n            <div className=\"mobile-bet-details\">\n              <div className=\"mobile-detail-item\">\n                <span className=\"mobile-detail-label\">Amount</span>\n                <span className=\"mobile-detail-value\">{bet.amount_user1} FanCoins</span>\n              </div>\n              <div className=\"mobile-detail-item\">\n                <span className=\"mobile-detail-label\">Potential Return</span>\n                <span className=\"mobile-detail-value\">{bet.potential_return_win_user1} FanCoins</span>\n              </div>\n              <div className=\"mobile-detail-item full-width\">\n                <span className=\"mobile-detail-label\">Match Date</span>\n                <span className=\"mobile-detail-value\">\n                  {new Date(bet.match_date).toLocaleString()}\n                </span>\n              </div>\n            </div>\n\n            {bet.bet_status === 'open' && (\n              <div className=\"mobile-bet-actions\">\n                <button \n                  className=\"mobile-action-button\"\n                  onClick={() => handleGenerateLink(bet)}\n                >\n                  Generate Link\n                </button>\n              </div>\n            )}\n          </div>\n        ))}\n      </div>\n\n      {/* Desktop Modal */}\n      {window.innerWidth > 768 && showBetDetailsModal && selectedBet && (\n        <div className=\"modal-overlay\" onClick={() => setShowBetDetailsModal(false)}>\n          <div className=\"bet-details-modal\" onClick={e => e.stopPropagation()}>\n            <button className=\"close-button\" onClick={() => setShowBetDetailsModal(false)}>×</button>\n            \n            <div className=\"modal-left\">\n              <div className=\"modal-header\">\n                <h3 className=\"reference-title\">Bet Reference: {getReference(selectedBet)}</h3>\n                <div className=\"status-badges\">\n                  <div className={`status-badge-large ${selectedBet.bet_status}`}>\n                    {selectedBet.bet_status === 'open' && 'OPEN FOR BETS'}\n                    {selectedBet.bet_status === 'joined' && 'BET MATCHED'}\n                    {selectedBet.bet_status === 'completed' && 'COMPLETED'}\n                  </div>\n                  <div className={`match-type-badge-large ${selectedBet.match_type}`}>\n                    {selectedBet.match_type === 'half_time' ? 'HALF TIME' : 'FULL TIME'}\n                  </div>\n                </div>\n              </div>\n              \n              <div className=\"teams-match\">\n                <div className=\"team-card\">\n                  {selectedBet.bet_choice_user1 === 'team_a_win' && (\n                    <div className=\"selected-badge\">Selected</div>\n                  )}\n                  <img src={getTeamLogo(selectedBet.team_a)} alt={selectedBet.team_a} />\n                  <div className=\"team-name\">{selectedBet.team_a}</div>\n                  <div className=\"team-username\">{selectedBet.user1_username}</div>\n                  <div className=\"team-odds\">{selectedBet.odds_team_a}x</div>\n                </div>\n                \n                <div className=\"vs-badge\">VS</div>\n                \n                <div className=\"team-card\">\n                  {selectedBet.bet_choice_user1 === 'team_b_win' && (\n                    <div className=\"selected-badge\">Selected</div>\n                  )}\n                  <img src={getTeamLogo(selectedBet.team_b)} alt={selectedBet.team_b} />\n                  <div className=\"team-name\">{selectedBet.team_b}</div>\n                  <div className=\"team-username\">{selectedBet.user2_username || 'Waiting for opponent'}</div>\n                  <div className=\"team-odds\">{selectedBet.odds_team_b}x</div>\n                </div>\n              </div>\n\n              <div className=\"match-details-grid\">\n                <div className=\"details-section schedule-section\">\n                  <div className=\"section-title\">MATCH SCHEDULE</div>\n                  <div className=\"schedule-grid\">\n                    <div className=\"schedule-item\">\n                      <span className=\"schedule-label\">MATCH DATE</span>\n                      <span className=\"schedule-value\">{new Date(selectedBet.match_date).toLocaleString()}</span>\n                    </div>\n                    <div className=\"schedule-item\">\n                      <span className=\"schedule-label\">START TIME</span>\n                      <span className=\"schedule-value\">{new Date(selectedBet.start_time).toLocaleTimeString()}</span>\n                    </div>\n                    <div className=\"schedule-item\">\n                      <span className=\"schedule-label\">END TIME</span>\n                      <span className=\"schedule-value\">{new Date(selectedBet.end_time).toLocaleTimeString()}</span>\n                    </div>\n                    <div className=\"schedule-item\">\n                      <span className=\"schedule-label\">CHALLENGE CREATED</span>\n                      <span className=\"schedule-value\">{new Date(selectedBet.challenge_date).toLocaleString()}</span>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"details-section odds-section\">\n                  <div className=\"section-title\">ODDS INFORMATION</div>\n                  <div className=\"odds-grid\">\n                    <div className=\"odds-item\">\n                      <span className=\"odds-label\">{selectedBet.team_a} WIN</span>\n                      <span className=\"odds-value\">{selectedBet.odds_team_a}x</span>\n                    </div>\n                    <div className=\"odds-item\">\n                      <span className=\"odds-label\">{selectedBet.team_b} WIN</span>\n                      <span className=\"odds-value\">{selectedBet.odds_team_b}x</span>\n                    </div>\n                    <div className=\"odds-item\">\n                      <span className=\"odds-label\">DRAW</span>\n                      <span className=\"odds-value\">{selectedBet.odds_draw}x</span>\n                    </div>\n                    <div className=\"odds-item\">\n                      <span className=\"odds-label\">LOSS MULTIPLIER</span>\n                      <span className=\"odds-value\">{selectedBet.odds_lost}x</span>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n            \n            <div className=\"modal-right\">\n              <div className=\"details-section\">\n                <div className=\"section-title\">BET STATUS</div>\n                <div className=\"detail-row\">\n                  <span className=\"detail-label\">CREATED BY</span>\n                  <span className=\"detail-value created-by\">{selectedBet.user1_username}</span>\n                </div>\n                <div className=\"detail-row\">\n                  <span className=\"detail-label\">STATUS</span>\n                  <span className=\"detail-value status-value\">\n                    {selectedBet.user2_username ? `Joined by ${selectedBet.user2_username}` : 'Waiting for opponent'}\n                  </span>\n                </div>\n              </div>\n\n              <div className=\"details-section\">\n                <div className=\"section-title\">FINANCIAL DETAILS</div>\n                <div className=\"detail-row\">\n                  <span className=\"detail-label\">YOUR BET</span>\n                  <span className=\"detail-value amount\">{selectedBet.amount_user1} FanCoins</span>\n                </div>\n                <div className=\"detail-row\">\n                  <span className=\"detail-label\">POTENTIAL WIN</span>\n                  <span className=\"detail-value return\">{selectedBet.potential_return_win_user1} FanCoins</span>\n                </div>\n                <div className=\"detail-row\">\n                  <span className=\"detail-label\">POTENTIAL LOSS</span>\n                  <span className=\"detail-value amount\">{selectedBet.potential_loss_user1} FanCoins</span>\n                </div>\n                <div className=\"detail-row\">\n                  <span className=\"detail-label\">DRAW OUTCOME</span>\n                  <span className=\"detail-value\">{selectedBet.potential_draw_win_user1} FanCoins</span>\n                </div>\n                {selectedBet.user2_username && (\n                  <>\n                    <div className=\"detail-row\">\n                      <span className=\"detail-label\">OPPONENT BET</span>\n                      <span className=\"detail-value amount\">{selectedBet.amount_user2} FanCoins</span>\n                    </div>\n                    <div className=\"detail-row\">\n                      <span className=\"detail-label\">OPPONENT WIN</span>\n                      <span className=\"detail-value return\">{selectedBet.potential_return_win_user2} FanCoins</span>\n                    </div>\n                  </>\n                )}\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Mobile Modal */}\n      {window.innerWidth <= 768 && showBetDetailsModal && selectedBet && (\n        <div className=\"modal-overlay\" onClick={() => setShowBetDetailsModal(false)}>\n          <div className=\"mobile-modal-content\" onClick={e => e.stopPropagation()}>\n            <div className=\"mobile-modal-header\">\n              <h3>Bet Details</h3>\n              <button className=\"mobile-modal-close\" onClick={() => setShowBetDetailsModal(false)}>×</button>\n            </div>\n\n            <div className=\"mobile-modal-body\">\n              <div className=\"mobile-ref-section\">\n                <div className=\"mobile-ref-number\">\n                  <span className=\"mobile-ref-label\">Reference Number</span>\n                  <span className=\"mobile-ref-value\">{getReference(selectedBet)}</span>\n                </div>\n                <div className=\"mobile-status-badges\">\n                  <span className={`mobile-status-badge ${selectedBet.bet_status.toLowerCase()}`}>\n                    {selectedBet.bet_status === 'open' && 'OPEN FOR BETS'}\n                    {selectedBet.bet_status === 'joined' && 'BET MATCHED'}\n                    {selectedBet.bet_status === 'completed' && 'COMPLETED'}\n                  </span>\n                  <span className=\"mobile-status-badge mobile-match-type\">\n                    {selectedBet.match_type === 'half_time' ? 'HALF TIME' : 'FULL TIME'}\n                  </span>\n                </div>\n              </div>\n\n              <div className=\"mobile-users-section\">\n                <div className=\"mobile-user-info\">\n                  <span className=\"mobile-user-name\">{selectedBet.user1_username}</span>\n                  <span className=\"mobile-user-status creator\">\n                    {getUserStatus(selectedBet, true)}\n                  </span>\n                </div>\n                <div className=\"mobile-vs-divider\">VS</div>\n                <div className=\"mobile-user-info\">\n                  <span className=\"mobile-user-name\">\n                    {selectedBet.user2_username || '---'}\n                  </span>\n                  <span className={`mobile-user-status ${selectedBet.user2_username ? 'opponent' : 'waiting'}`}>\n                    {getUserStatus(selectedBet, false)}\n                  </span>\n                </div>\n              </div>\n\n              <div className=\"mobile-teams-container\">\n                <div className=\"mobile-team\">\n                  <img \n                    src={getTeamLogo(selectedBet.team_a)}\n                    alt={selectedBet.team_a}\n                    className=\"mobile-team-logo\"\n                  />\n                  <span>{selectedBet.team_a}</span>\n                </div>\n                <div className=\"mobile-vs\">VS</div>\n                <div className=\"mobile-team\">\n                  <img \n                    src={getTeamLogo(selectedBet.team_b)}\n                    alt={selectedBet.team_b}\n                    className=\"mobile-team-logo\"\n                  />\n                  <span>{selectedBet.team_b}</span>\n                </div>\n              </div>\n\n              <div className=\"mobile-section-title\">ODDS INFORMATION</div>\n              <div className=\"mobile-odds-grid\">\n                <div className=\"mobile-odds-item\">\n                  <span className=\"mobile-odds-label\">{selectedBet.team_a} WIN</span>\n                  <span className=\"mobile-odds-value\">{selectedBet.odds_team_a}x</span>\n                </div>\n                <div className=\"mobile-odds-item\">\n                  <span className=\"mobile-odds-label\">{selectedBet.team_b} WIN</span>\n                  <span className=\"mobile-odds-value\">{selectedBet.odds_team_b}x</span>\n                </div>\n                <div className=\"mobile-odds-item\">\n                  <span className=\"mobile-odds-label\">DRAW</span>\n                  <span className=\"mobile-odds-value\">{selectedBet.odds_draw}x</span>\n                </div>\n                <div className=\"mobile-odds-item\">\n                  <span className=\"mobile-odds-label\">LOSS</span>\n                  <span className=\"mobile-odds-value\">{selectedBet.odds_lost}x</span>\n                </div>\n              </div>\n\n              <div className=\"mobile-section-title\">FINANCIAL DETAILS</div>\n              <div className=\"mobile-financial-grid\">\n                <div className=\"mobile-financial-item\">\n                  <span className=\"mobile-financial-label\">Your Bet</span>\n                  <span className=\"mobile-financial-value\">{selectedBet.amount_user1} FC</span>\n                </div>\n                <div className=\"mobile-financial-item\">\n                  <span className=\"mobile-financial-label\">Potential Win</span>\n                  <span className=\"mobile-financial-value win\">+{selectedBet.potential_return_win_user1} FC</span>\n                </div>\n                <div className=\"mobile-financial-item\">\n                  <span className=\"mobile-financial-label\">Potential Loss</span>\n                  <span className=\"mobile-financial-value loss\">-{selectedBet.potential_return_loss_user1} FC</span>\n                </div>\n                <div className=\"mobile-financial-item\">\n                  <span className=\"mobile-financial-label\">Draw Return</span>\n                  <span className=\"mobile-financial-value draw\">{selectedBet.potential_return_draw_user1} FC</span>\n                </div>\n              </div>\n            </div>\n\n            {selectedBet.bet_status === 'open' && (\n              <div className=\"mobile-modal-footer\">\n                <button \n                  className=\"mobile-modal-action-button\"\n                  onClick={() => handleGenerateLink(selectedBet)}\n                >\n                  Generate Link\n                </button>\n              </div>\n            )}\n          </div>\n        </div>\n      )}\n\n      {showLinkModal && (\n        <div className=\"modal-overlay\" onClick={handleCloseModal}>\n          <div className=\"link-modal\" onClick={e => e.stopPropagation()}>\n            <span className=\"close\" onClick={handleCloseModal}>&times;</span>\n            <h3>Share this link with your friend</h3>\n            <div className=\"link-container\">\n              <input type=\"text\" value={selectedBetLink} readOnly />\n              <button onClick={() => {\n                navigator.clipboard.writeText(selectedBetLink);\n                // Show a temporary success message instead of alert\n                const button = document.activeElement;\n                const originalText = button.textContent;\n                button.textContent = 'Copied!';\n                button.style.backgroundColor = '#16a34a';\n                setTimeout(() => {\n                  button.textContent = originalText;\n                  button.style.backgroundColor = '';\n                }, 2000);\n              }}>\n                Copy\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n\n      <style jsx>{`\n        .teams-header.compact {\n          width: 20%;  /* Reduced from 30% */\n        }\n        \n        .team-cell.compact {\n          max-width: 80px;  /* Reduced from 100px */\n          padding: 4px;  /* Reduced padding */\n        }\n        \n        .vs-cell.compact {\n          padding: 0 4px;  /* Reduced padding */\n          width: 30px;  /* Fixed width */\n        }\n        \n        .team-info {\n          display: flex;\n          align-items: center;\n          gap: 4px;  /* Reduced gap between elements */\n          max-width: 100%;\n        }\n        \n        .team-info span {\n          overflow: hidden;\n          text-overflow: ellipsis;\n          white-space: nowrap;\n          font-size: 0.9rem;\n        }\n\n        .date-cell {\n          padding: 4px 8px;\n          min-width: 100px;\n        }\n\n        .date-display {\n          display: flex;\n          flex-direction: column;\n          gap: 2px;\n          font-size: 0.85rem;\n        }\n\n        .date-line {\n          color: #333;\n        }\n\n        .time-line {\n          color: #666;\n          font-size: 0.8rem;\n        }\n      `}</style>\n    </div>\n  );\n}\n\nexport default ViewBets;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,CAAEC,WAAW,CAAEC,MAAM,KAAQ,OAAO,CACvE,MAAO,CAAAC,KAAK,KAAM,OAAO,CACzB,OAASC,WAAW,CAAEC,WAAW,KAAQ,kBAAkB,CAC3D,MAAO,gBAAgB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAExB,KAAM,CAAAC,YAAY,CAAG,UAAU,CAE/B,QAAS,CAAAC,QAAQA,CAAA,CAAG,KAAAC,eAAA,CAClB,KAAM,CAACC,YAAY,CAAEC,eAAe,CAAC,CAAGjB,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAACkB,KAAK,CAAEC,QAAQ,CAAC,CAAGnB,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAACoB,OAAO,CAAEC,UAAU,CAAC,CAAGrB,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACsB,KAAK,CAAEC,QAAQ,CAAC,CAAGvB,QAAQ,CAAC,IAAI,CAAC,CACxC,KAAM,CAACwB,WAAW,CAAEC,cAAc,CAAC,CAAGzB,QAAQ,CAAC,CAAC,CAAC,CACjD,KAAM,CAAC0B,UAAU,CAAEC,aAAa,CAAC,CAAG3B,QAAQ,CAAC,CAAC,CAAC,CAC/C,KAAM,CAAC4B,YAAY,CAAC,CAAG5B,QAAQ,CAAC,EAAE,CAAC,CACnC,KAAM,CAAC6B,UAAU,CAAEC,aAAa,CAAC,CAAG9B,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAAA+B,MAAM,CAAGC,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC,CAC7C,KAAM,CAAAC,QAAQ,CAAG7B,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAA8B,QAAQ,CAAG7B,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAA8B,SAAS,CAAGjC,MAAM,CAAC,IAAI,CAAC,CAE9B,KAAM,CAACkC,aAAa,CAAEC,gBAAgB,CAAC,CAAGtC,QAAQ,CAAC,KAAK,CAAC,CACzD,KAAM,CAACuC,eAAe,CAAEC,kBAAkB,CAAC,CAAGxC,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAACyC,mBAAmB,CAAEC,sBAAsB,CAAC,CAAG1C,QAAQ,CAAC,KAAK,CAAC,CACrE,KAAM,CAAC2C,WAAW,CAAEC,cAAc,CAAC,CAAG5C,QAAQ,CAAC,IAAI,CAAC,CACpD,KAAM,CAAC6C,QAAQ,CAAEC,WAAW,CAAC,CAAG9C,QAAQ,CAAC,EAAAe,eAAA,CAAAoB,QAAQ,CAACY,KAAK,UAAAhC,eAAA,iBAAdA,eAAA,CAAgB8B,QAAQ,GAAI,IAAI,CAAC,CAE1E,KAAM,CAAAG,SAAS,CAAG9C,WAAW,CAAC,SAAY,CACxC,GAAI,CACF+C,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC,CAC/B,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAA/C,KAAK,CAACgD,GAAG,CAAC,GAAGvC,YAAY,wBAAwB,CAAE,CACxEwC,MAAM,CAAE,CACNtB,MAAM,CAAEA,MAAM,CACduB,IAAI,CAAE9B,WAAW,CACjB+B,KAAK,CAAE3B,YAAY,CACnB4B,MAAM,CAAE3B,UACV,CACF,CAAC,CAAC,CACFoB,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAEC,QAAQ,CAACM,IAAI,CAAC,CAE5C,GAAIN,QAAQ,CAACM,IAAI,CAACC,OAAO,CAAE,CACzBzC,eAAe,CAACkC,QAAQ,CAACM,IAAI,CAACE,IAAI,EAAI,EAAE,CAAC,CACzChC,aAAa,CAACwB,QAAQ,CAACM,IAAI,CAACG,UAAU,CAAClC,UAAU,CAAC,CACpD,CAAC,IAAM,CACLuB,OAAO,CAAC3B,KAAK,CAAC,uBAAuB,CAAE6B,QAAQ,CAACM,IAAI,CAACI,OAAO,CAAC,CAC7DtC,QAAQ,CAAC4B,QAAQ,CAACM,IAAI,CAACI,OAAO,EAAI,sBAAsB,CAAC,CAC3D,CACF,CAAE,MAAOvC,KAAK,CAAE,KAAAwC,eAAA,CAAAC,oBAAA,CACdd,OAAO,CAAC3B,KAAK,CAAC,sBAAsB,CAAEA,KAAK,CAAC,CAC5CC,QAAQ,CAAC,EAAAuC,eAAA,CAAAxC,KAAK,CAAC6B,QAAQ,UAAAW,eAAA,kBAAAC,oBAAA,CAAdD,eAAA,CAAgBL,IAAI,UAAAM,oBAAA,iBAApBA,oBAAA,CAAsBF,OAAO,GAAI,wCAAwC,CAAC,CACrF,CAAC,OAAS,CACRxC,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAAE,CAACU,MAAM,CAAEP,WAAW,CAAEI,YAAY,CAAEC,UAAU,CAAC,CAAC,CAEnD,KAAM,CAAAmC,UAAU,CAAG9D,WAAW,CAAC,SAAY,CACzC,GAAI,CACF+C,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC,CAChC,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAA/C,KAAK,CAACgD,GAAG,CAAC,GAAGvC,YAAY,+BAA+B,CAAC,CAChFoC,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAEC,QAAQ,CAACM,IAAI,CAAC,CAE7C,GAAIN,QAAQ,CAACM,IAAI,CAACQ,MAAM,GAAK,GAAG,CAAE,CAChC9C,QAAQ,CAACgC,QAAQ,CAACM,IAAI,CAACA,IAAI,EAAI,EAAE,CAAC,CACpC,CAAC,IAAM,CACLR,OAAO,CAAC3B,KAAK,CAAC,wBAAwB,CAAE6B,QAAQ,CAACM,IAAI,CAACI,OAAO,CAAC,CAC9DtC,QAAQ,CAAC4B,QAAQ,CAACM,IAAI,CAACI,OAAO,EAAI,uBAAuB,CAAC,CAC5D,CACF,CAAE,MAAOvC,KAAK,CAAE,KAAA4C,gBAAA,CAAAC,qBAAA,CACdlB,OAAO,CAAC3B,KAAK,CAAC,uBAAuB,CAAEA,KAAK,CAAC,CAC7CC,QAAQ,CAAC,EAAA2C,gBAAA,CAAA5C,KAAK,CAAC6B,QAAQ,UAAAe,gBAAA,kBAAAC,qBAAA,CAAdD,gBAAA,CAAgBT,IAAI,UAAAU,qBAAA,iBAApBA,qBAAA,CAAsBN,OAAO,GAAI,yCAAyC,CAAC,CACtF,CACF,CAAC,CAAE,EAAE,CAAC,CAEN5D,SAAS,CAAC,IAAM,CACd,GAAI,CAAC8B,MAAM,CAAE,CACXG,QAAQ,CAAC,QAAQ,CAAC,CAClB,OACF,CAEAe,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAEnB,MAAM,CAAC,CACzD,KAAM,CAAAqC,cAAc,CAAG,KAAAA,CAAA,GAAY,CACjC,GAAI,CACF7C,QAAQ,CAAC,IAAI,CAAC,CACdF,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAAAgD,OAAO,CAACC,GAAG,CAAC,CAACtB,SAAS,CAAC,CAAC,CAAEgB,UAAU,CAAC,CAAC,CAAC,CAAC,CAChD,CAAE,MAAOO,GAAG,CAAE,CACZtB,OAAO,CAAC3B,KAAK,CAAC,gCAAgC,CAAEiD,GAAG,CAAC,CACpDhD,QAAQ,CAAC,yDAAyD,CAAC,CACrE,CACF,CAAC,CAED6C,cAAc,CAAC,CAAC,CAClB,CAAC,CAAE,CAACrC,MAAM,CAAEiB,SAAS,CAAEgB,UAAU,CAAE9B,QAAQ,CAAC,CAAC,CAE7CjC,SAAS,CAAC,IAAM,CACd,GAAI4C,QAAQ,EAAIT,SAAS,CAACoC,OAAO,CAAE,CACjCpC,SAAS,CAACoC,OAAO,CAACC,cAAc,CAAC,CAAEC,QAAQ,CAAE,QAAQ,CAAEC,KAAK,CAAE,QAAS,CAAC,CAAC,CACzE,KAAM,CAAAC,KAAK,CAAGC,UAAU,CAAC,IAAM,CAC7B/B,WAAW,CAAC,IAAI,CAAC,CACnB,CAAC,CAAE,IAAI,CAAC,CACR,MAAO,IAAMgC,YAAY,CAACF,KAAK,CAAC,CAClC,CACF,CAAC,CAAE,CAAC5D,YAAY,CAAE6B,QAAQ,CAAC,CAAC,CAE5B,KAAM,CAAAkC,gBAAgB,CAAIC,OAAO,EAAK,CACpCvD,cAAc,CAACuD,OAAO,CAAC,CACzB,CAAC,CAED,KAAM,CAAAC,YAAY,CAAIC,CAAC,EAAK,CAC1BpD,aAAa,CAACoD,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC,CAC7B3D,cAAc,CAAC,CAAC,CAAC,CAAE;AACrB,CAAC,CAED,KAAM,CAAA4D,gBAAgB,CAAGA,CAAA,GAAM,CAC7B,KAAM,CAAAC,KAAK,CAAG,EAAE,CAChB,IAAK,GAAI,CAAAC,CAAC,CAAG,CAAC,CAAEA,CAAC,EAAI7D,UAAU,CAAE6D,CAAC,EAAE,CAAE,CACpCD,KAAK,CAACE,IAAI,cACRhF,IAAA,WAEEiF,OAAO,CAAEA,CAAA,GAAMV,gBAAgB,CAACQ,CAAC,CAAE,CACnCG,SAAS,CAAE,qBAAqBlE,WAAW,GAAK+D,CAAC,CAAG,QAAQ,CAAG,EAAE,EAAG,CAAAI,QAAA,CAEnEJ,CAAC,EAJGA,CAKC,CACV,CAAC,CACH,CACA,mBACE7E,KAAA,QAAKgF,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzBnF,IAAA,WACEiF,OAAO,CAAEA,CAAA,GAAMV,gBAAgB,CAACvD,WAAW,CAAG,CAAC,CAAE,CACjDoE,QAAQ,CAAEpE,WAAW,GAAK,CAAE,CAC5BkE,SAAS,CAAC,mBAAmB,CAAAC,QAAA,CAC9B,UAED,CAAQ,CAAC,CACRL,KAAK,cACN9E,IAAA,WACEiF,OAAO,CAAEA,CAAA,GAAMV,gBAAgB,CAACvD,WAAW,CAAG,CAAC,CAAE,CACjDoE,QAAQ,CAAEpE,WAAW,GAAKE,UAAW,CACrCgE,SAAS,CAAC,mBAAmB,CAAAC,QAAA,CAC9B,MAED,CAAQ,CAAC,EACN,CAAC,CAEV,CAAC,CAED,KAAM,CAAAE,aAAa,CAAIC,GAAG,EAAK,CAC7B,KAAM,CAAAC,QAAQ,CAAGC,UAAU,CAACF,GAAG,CAACG,YAAY,CAAC,CAAG,CAAC,CACjD,KAAM,CAAAC,SAAS,CAAGF,UAAU,CAACF,GAAG,CAACK,0BAA0B,CAAC,CAC5D,KAAM,CAAAC,UAAU,CAAGJ,UAAU,CAACF,GAAG,CAACO,2BAA2B,CAAC,CAC9D,KAAM,CAAAC,UAAU,CAAGN,UAAU,CAACF,GAAG,CAACS,2BAA2B,CAAC,CAE9D,KAAM,CAAAC,OAAO,CAAG,CAACN,SAAS,CAAGH,QAAQ,CAAG,GAAG,EAAEU,OAAO,CAAC,CAAC,CAAC,CACvD,KAAM,CAAAC,QAAQ,CAAG,CAACN,UAAU,CAAGL,QAAQ,CAAG,GAAG,EAAEU,OAAO,CAAC,CAAC,CAAC,CACzD,KAAM,CAAAE,QAAQ,CAAG,CAACL,UAAU,CAAGP,QAAQ,CAAG,GAAG,EAAEU,OAAO,CAAC,CAAC,CAAC,CAEzD,MAAO,CACLG,GAAG,CAAE,CAAEC,IAAI,CAAEL,OAAO,CAAEM,MAAM,CAAEZ,SAAU,CAAC,CACzCa,IAAI,CAAE,CAAEF,IAAI,CAAEH,QAAQ,CAAEI,MAAM,CAAEV,UAAW,CAAC,CAC5CY,IAAI,CAAE,CAAEH,IAAI,CAAEF,QAAQ,CAAEG,MAAM,CAAER,UAAW,CAC7C,CAAC,CACH,CAAC,CAED,KAAM,CAAAW,aAAa,CAAGA,CAACnB,GAAG,CAAEoB,SAAS,GAAK,CACxC,GAAIpB,GAAG,CAACqB,UAAU,CAACC,WAAW,CAAC,CAAC,GAAK,QAAQ,EAAItB,GAAG,CAACqB,UAAU,CAACC,WAAW,CAAC,CAAC,GAAK,WAAW,CAAE,CAC7F,MAAO,CAAAF,SAAS,CAAG,SAAS,CAAG,UAAU,CAC3C,CAEA,GAAIA,SAAS,CAAE,CACb,MAAO,SAAS,CAClB,CAEA,MAAO,sBAAsB,CAC/B,CAAC,CAED,GAAI9F,OAAO,CAAE,CACX,mBAAOZ,IAAA,QAAKkF,SAAS,CAAC,SAAS,CAAAC,QAAA,CAAC,iBAAe,CAAK,CAAC,CACvD,CAEA,GAAIrE,KAAK,CAAE,CACT,mBAAOd,IAAA,QAAKkF,SAAS,CAAC,OAAO,CAAAC,QAAA,CAAErE,KAAK,CAAM,CAAC,CAC7C,CAEA,KAAM,CAAA+F,WAAW,CAAIC,QAAQ,EAAK,CAChC,KAAM,CAAAC,IAAI,CAAGrG,KAAK,CAACsG,IAAI,CAACD,IAAI,EAAIA,IAAI,CAACE,IAAI,GAAKH,QAAQ,CAAC,CACvD,MAAO,CAAAC,IAAI,CAAG,GAAG1G,YAAY,IAAI0G,IAAI,CAACG,IAAI,EAAE,CAAG,EAAE,CACnD,CAAC,CAED,KAAM,CAAAC,kBAAkB,CAAI7B,GAAG,EAAK,CAClC,KAAM,CAAA8B,IAAI,CAAG,GAAGC,MAAM,CAAC1F,QAAQ,CAAC2F,MAAM,yBAAyBhC,GAAG,CAACiC,YAAY,IAAIjC,GAAG,CAACkC,MAAM,IAAIlC,GAAG,CAACmC,WAAW,IAAInC,GAAG,CAACoC,QAAQ,EAAE,CAClI1F,kBAAkB,CAACoF,IAAI,CAAC,CACxBtF,gBAAgB,CAAC,IAAI,CAAC,CACxB,CAAC,CAED,KAAM,CAAA6F,gBAAgB,CAAGA,CAAA,GAAM,CAC7B7F,gBAAgB,CAAC,KAAK,CAAC,CACzB,CAAC,CAED,KAAM,CAAA8F,YAAY,CAAItC,GAAG,EAAK,CAC5B,MAAO,CAACA,GAAG,CAACmC,WAAW,EAAI,GAAGnC,GAAG,CAACkC,MAAM,SAAS,EAAEK,WAAW,CAAC,CAAC,CAClE,CAAC,CAED,KAAM,CAAAC,oBAAoB,CAAIxC,GAAG,EAAK,CACpClD,cAAc,CAACkD,GAAG,CAAC,CACnBpD,sBAAsB,CAAC,IAAI,CAAC,CAC9B,CAAC,CAED,mBACEhC,KAAA,QAAKgF,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eAClCjF,KAAA,QAAKgF,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BnF,IAAA,OAAAmF,QAAA,CAAI,eAAa,CAAI,CAAC,cACtBnF,IAAA,QAAKkF,SAAS,CAAC,YAAY,CAAM,CAAC,EAC/B,CAAC,cAENlF,IAAA,QAAKkF,SAAS,CAAC,kBAAkB,CAAAC,QAAA,cAC/BnF,IAAA,UACE+H,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,+BAA+B,CAC3CpD,KAAK,CAAEvD,UAAW,CAClB4G,QAAQ,CAAExD,YAAa,CACvBS,SAAS,CAAC,cAAc,CACzB,CAAC,CACC,CAAC,cAGNlF,IAAA,QAAKkF,SAAS,CAAC,kBAAkB,CAAAC,QAAA,cAC/BjF,KAAA,UAAOgF,SAAS,CAAC,YAAY,CAAAC,QAAA,eAC3BnF,IAAA,UAAAmF,QAAA,cACEjF,KAAA,OAAAiF,QAAA,eACEnF,IAAA,OAAAmF,QAAA,CAAI,GAAC,CAAI,CAAC,cACVnF,IAAA,OAAAmF,QAAA,CAAI,KAAG,CAAI,CAAC,cACZnF,IAAA,OAAIkI,OAAO,CAAC,GAAG,CAAChD,SAAS,CAAC,sBAAsB,CAAAC,QAAA,CAAC,OAAK,CAAI,CAAC,cAC3DnF,IAAA,OAAAmF,QAAA,CAAI,QAAM,CAAI,CAAC,cACfnF,IAAA,OAAAmF,QAAA,CAAI,QAAM,CAAI,CAAC,cACfnF,IAAA,OAAAmF,QAAA,CAAI,QAAM,CAAI,CAAC,cACfnF,IAAA,OAAAmF,QAAA,CAAI,YAAU,CAAI,CAAC,cACnBnF,IAAA,OAAAmF,QAAA,CAAI,SAAO,CAAI,CAAC,EACd,CAAC,CACA,CAAC,cACRnF,IAAA,UAAAmF,QAAA,CACG3E,YAAY,CAAC2H,GAAG,CAAC,CAAC7C,GAAG,CAAE8C,KAAK,gBAC3BlI,KAAA,OAEEmI,GAAG,CAAE/C,GAAG,CAACkC,MAAM,GAAKnF,QAAQ,CAAGT,SAAS,CAAG,IAAK,CAChDsD,SAAS,CAAEI,GAAG,CAACkC,MAAM,GAAKnF,QAAQ,CAAG,mBAAmB,CAAG,EAAG,CAAA8C,QAAA,eAE9DnF,IAAA,OAAAmF,QAAA,CAAK,CAACnE,WAAW,CAAG,CAAC,EAAII,YAAY,CAAGgH,KAAK,CAAG,CAAC,CAAK,CAAC,cACvDpI,IAAA,OAAAmF,QAAA,cACEnF,IAAA,SAAMkF,SAAS,CAAC,WAAW,CAACD,OAAO,CAAEA,CAAA,GAAM6C,oBAAoB,CAACxC,GAAG,CAAE,CAAAH,QAAA,CAClEyC,YAAY,CAACtC,GAAG,CAAC,CACd,CAAC,CACL,CAAC,cACLtF,IAAA,OAAIkF,SAAS,CAAC,mBAAmB,CAAAC,QAAA,cAC/BjF,KAAA,QAAKgF,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBnF,IAAA,QACEsI,GAAG,CAAEzB,WAAW,CAACvB,GAAG,CAACiD,MAAM,CAAE,CAC7BC,GAAG,CAAElD,GAAG,CAACiD,MAAO,CAChBrD,SAAS,CAAC,WAAW,CACtB,CAAC,cACFlF,IAAA,SAAAmF,QAAA,CAAOG,GAAG,CAACiD,MAAM,CAAO,CAAC,CACxBjD,GAAG,CAACmD,gBAAgB,GAAK,YAAY,eAAIzI,IAAA,SAAMkF,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,WAAS,CAAM,CAAC,EACpF,CAAC,CACJ,CAAC,cACLnF,IAAA,OAAIkF,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC7BnF,IAAA,QAAKkF,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,IAAE,CAAK,CAAC,CACpC,CAAC,cACLnF,IAAA,OAAIkF,SAAS,CAAC,mBAAmB,CAAAC,QAAA,cAC/BjF,KAAA,QAAKgF,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBnF,IAAA,QACEsI,GAAG,CAAEzB,WAAW,CAACvB,GAAG,CAACoD,MAAM,CAAE,CAC7BF,GAAG,CAAElD,GAAG,CAACoD,MAAO,CAChBxD,SAAS,CAAC,WAAW,CACtB,CAAC,cACFlF,IAAA,SAAAmF,QAAA,CAAOG,GAAG,CAACoD,MAAM,CAAO,CAAC,CACxBpD,GAAG,CAACmD,gBAAgB,GAAK,YAAY,eAAIzI,IAAA,SAAMkF,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,WAAS,CAAM,CAAC,EACpF,CAAC,CACJ,CAAC,cACLjF,KAAA,OAAIgF,SAAS,CAAC,aAAa,CAAAC,QAAA,EACxBG,GAAG,CAACG,YAAY,CAAC,GAAC,cAAAzF,IAAA,SAAMkF,SAAS,CAAC,UAAU,CAAAC,QAAA,CAAC,UAAQ,CAAM,CAAC,EAC3D,CAAC,cACLjF,KAAA,OAAIgF,SAAS,CAAC,aAAa,CAAAC,QAAA,EACxBG,GAAG,CAACK,0BAA0B,CAAC,GAAC,cAAA3F,IAAA,SAAMkF,SAAS,CAAC,UAAU,CAAAC,QAAA,CAAC,UAAQ,CAAM,CAAC,EACzE,CAAC,cACLnF,IAAA,OAAAmF,QAAA,cACEnF,IAAA,QAAKkF,SAAS,CAAC,kBAAkB,CAAAC,QAAA,cAC/BjF,KAAA,QAAKgF,SAAS,CAAE,gBAAgBI,GAAG,CAACqB,UAAU,EAAG,CAAAxB,QAAA,eAC/CnF,IAAA,SAAMkF,SAAS,CAAC,YAAY,CAAO,CAAC,cACpChF,KAAA,SAAMgF,SAAS,CAAC,aAAa,CAAAC,QAAA,EAC1BG,GAAG,CAACqB,UAAU,GAAK,MAAM,EAAI,MAAM,CACnCrB,GAAG,CAACqB,UAAU,GAAK,QAAQ,EAAI,QAAQ,CACvCrB,GAAG,CAACqB,UAAU,GAAK,WAAW,EAAI,WAAW,EAC1C,CAAC,EACJ,CAAC,CACH,CAAC,CACJ,CAAC,cACL3G,IAAA,OAAIkF,SAAS,CAAC,WAAW,CAAAC,QAAA,cACvBjF,KAAA,QAAKgF,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BnF,IAAA,QAAKkF,SAAS,CAAC,WAAW,CAAAC,QAAA,CAAE,GAAI,CAAAwD,IAAI,CAACrD,GAAG,CAACsD,UAAU,CAAC,CAACC,kBAAkB,CAAC,CAAC,CAAM,CAAC,cAChF7I,IAAA,QAAKkF,SAAS,CAAC,WAAW,CAAAC,QAAA,CAAE,GAAI,CAAAwD,IAAI,CAACrD,GAAG,CAACsD,UAAU,CAAC,CAACE,kBAAkB,CAAC,EAAE,CAAE,CAAEC,IAAI,CAAE,SAAS,CAAEC,MAAM,CAAE,SAAU,CAAC,CAAC,CAAM,CAAC,EACvH,CAAC,CACJ,CAAC,cACLhJ,IAAA,OAAAmF,QAAA,CACGG,GAAG,CAACqB,UAAU,GAAK,MAAM,eACxB3G,IAAA,WAAQiF,OAAO,CAAEA,CAAA,GAAMkC,kBAAkB,CAAC7B,GAAG,CAAE,CAACJ,SAAS,CAAC,mBAAmB,CAAAC,QAAA,CAAC,eAE9E,CAAQ,CACT,CACC,CAAC,GAjEAG,GAAG,CAACkC,MAkEP,CACL,CAAC,CACG,CAAC,EACH,CAAC,CACL,CAAC,cAGNxH,IAAA,QAAKkF,SAAS,CAAC,kBAAkB,CAAAC,QAAA,CAC9B3E,YAAY,CAAC2H,GAAG,CAAC,CAAC7C,GAAG,CAAE8C,KAAK,gBAC3BlI,KAAA,QAEEgF,SAAS,CAAC,iBAAiB,CAC3BmD,GAAG,CAAE/C,GAAG,CAACkC,MAAM,GAAKnF,QAAQ,CAAGT,SAAS,CAAG,IAAK,CAChD,cAAa0D,GAAG,CAACqB,UAAU,CAACC,WAAW,CAAC,CAAE,CAAAzB,QAAA,eAE1CjF,KAAA,QAAKgF,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCnF,IAAA,SAAMkF,SAAS,CAAC,gBAAgB,CAACD,OAAO,CAAEA,CAAA,GAAM6C,oBAAoB,CAACxC,GAAG,CAAE,CAAAH,QAAA,CACvEyC,YAAY,CAACtC,GAAG,CAAC,CACd,CAAC,cACPpF,KAAA,QAAKgF,SAAS,CAAE,gBAAgBI,GAAG,CAACqB,UAAU,CAACC,WAAW,CAAC,CAAC,EAAG,CAAAzB,QAAA,eAC7DnF,IAAA,SAAMkF,SAAS,CAAC,YAAY,CAAO,CAAC,CACnCI,GAAG,CAACqB,UAAU,EACZ,CAAC,EACH,CAAC,cAENzG,KAAA,QAAKgF,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACnCjF,KAAA,QAAKgF,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/BnF,IAAA,SAAMkF,SAAS,CAAC,kBAAkB,CAAAC,QAAA,CAAEG,GAAG,CAAC2D,cAAc,CAAO,CAAC,cAC9DjJ,IAAA,SAAMkF,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CACzCsB,aAAa,CAACnB,GAAG,CAAE,IAAI,CAAC,CACrB,CAAC,EACJ,CAAC,cACNtF,IAAA,QAAKkF,SAAS,CAAC,mBAAmB,CAAAC,QAAA,CAAC,IAAE,CAAK,CAAC,cAC3CjF,KAAA,QAAKgF,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/BnF,IAAA,SAAMkF,SAAS,CAAC,kBAAkB,CAAAC,QAAA,CAC/BG,GAAG,CAAC4D,cAAc,EAAI,KAAK,CACxB,CAAC,cACPlJ,IAAA,SAAMkF,SAAS,CAAE,sBAAsBI,GAAG,CAAC4D,cAAc,CAAG,UAAU,CAAG,SAAS,EAAG,CAAA/D,QAAA,CAClFsB,aAAa,CAACnB,GAAG,CAAE,KAAK,CAAC,CACtB,CAAC,EACJ,CAAC,EACH,CAAC,cAENpF,KAAA,QAAKgF,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eACrCjF,KAAA,QAAKgF,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BnF,IAAA,QACEsI,GAAG,CAAEzB,WAAW,CAACvB,GAAG,CAACiD,MAAM,CAAE,CAC7BC,GAAG,CAAElD,GAAG,CAACiD,MAAO,CAChBrD,SAAS,CAAC,kBAAkB,CAC7B,CAAC,cACFlF,IAAA,SAAAmF,QAAA,CAAOG,GAAG,CAACiD,MAAM,CAAO,CAAC,EACtB,CAAC,cACNvI,IAAA,QAAKkF,SAAS,CAAC,WAAW,CAAAC,QAAA,CAAC,IAAE,CAAK,CAAC,cACnCjF,KAAA,QAAKgF,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BnF,IAAA,QACEsI,GAAG,CAAEzB,WAAW,CAACvB,GAAG,CAACoD,MAAM,CAAE,CAC7BF,GAAG,CAAElD,GAAG,CAACoD,MAAO,CAChBxD,SAAS,CAAC,kBAAkB,CAC7B,CAAC,cACFlF,IAAA,SAAAmF,QAAA,CAAOG,GAAG,CAACoD,MAAM,CAAO,CAAC,EACtB,CAAC,EACH,CAAC,cAENxI,KAAA,QAAKgF,SAAS,CAAC,oBAAoB,CAAAC,QAAA,eACjCjF,KAAA,QAAKgF,SAAS,CAAC,oBAAoB,CAAAC,QAAA,eACjCnF,IAAA,SAAMkF,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAC,QAAM,CAAM,CAAC,cACnDjF,KAAA,SAAMgF,SAAS,CAAC,qBAAqB,CAAAC,QAAA,EAAEG,GAAG,CAACG,YAAY,CAAC,WAAS,EAAM,CAAC,EACrE,CAAC,cACNvF,KAAA,QAAKgF,SAAS,CAAC,oBAAoB,CAAAC,QAAA,eACjCnF,IAAA,SAAMkF,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAC,kBAAgB,CAAM,CAAC,cAC7DjF,KAAA,SAAMgF,SAAS,CAAC,qBAAqB,CAAAC,QAAA,EAAEG,GAAG,CAACK,0BAA0B,CAAC,WAAS,EAAM,CAAC,EACnF,CAAC,cACNzF,KAAA,QAAKgF,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5CnF,IAAA,SAAMkF,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAC,YAAU,CAAM,CAAC,cACvDnF,IAAA,SAAMkF,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAClC,GAAI,CAAAwD,IAAI,CAACrD,GAAG,CAACsD,UAAU,CAAC,CAACO,cAAc,CAAC,CAAC,CACtC,CAAC,EACJ,CAAC,EACH,CAAC,CAEL7D,GAAG,CAACqB,UAAU,GAAK,MAAM,eACxB3G,IAAA,QAAKkF,SAAS,CAAC,oBAAoB,CAAAC,QAAA,cACjCnF,IAAA,WACEkF,SAAS,CAAC,sBAAsB,CAChCD,OAAO,CAAEA,CAAA,GAAMkC,kBAAkB,CAAC7B,GAAG,CAAE,CAAAH,QAAA,CACxC,eAED,CAAQ,CAAC,CACN,CACN,GA/EIG,GAAG,CAACkC,MAgFN,CACN,CAAC,CACC,CAAC,CAGLH,MAAM,CAAC+B,UAAU,CAAG,GAAG,EAAInH,mBAAmB,EAAIE,WAAW,eAC5DnC,IAAA,QAAKkF,SAAS,CAAC,eAAe,CAACD,OAAO,CAAEA,CAAA,GAAM/C,sBAAsB,CAAC,KAAK,CAAE,CAAAiD,QAAA,cAC1EjF,KAAA,QAAKgF,SAAS,CAAC,mBAAmB,CAACD,OAAO,CAAEP,CAAC,EAAIA,CAAC,CAAC2E,eAAe,CAAC,CAAE,CAAAlE,QAAA,eACnEnF,IAAA,WAAQkF,SAAS,CAAC,cAAc,CAACD,OAAO,CAAEA,CAAA,GAAM/C,sBAAsB,CAAC,KAAK,CAAE,CAAAiD,QAAA,CAAC,MAAC,CAAQ,CAAC,cAEzFjF,KAAA,QAAKgF,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzBjF,KAAA,QAAKgF,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BjF,KAAA,OAAIgF,SAAS,CAAC,iBAAiB,CAAAC,QAAA,EAAC,iBAAe,CAACyC,YAAY,CAACzF,WAAW,CAAC,EAAK,CAAC,cAC/EjC,KAAA,QAAKgF,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BjF,KAAA,QAAKgF,SAAS,CAAE,sBAAsB/C,WAAW,CAACwE,UAAU,EAAG,CAAAxB,QAAA,EAC5DhD,WAAW,CAACwE,UAAU,GAAK,MAAM,EAAI,eAAe,CACpDxE,WAAW,CAACwE,UAAU,GAAK,QAAQ,EAAI,aAAa,CACpDxE,WAAW,CAACwE,UAAU,GAAK,WAAW,EAAI,WAAW,EACnD,CAAC,cACN3G,IAAA,QAAKkF,SAAS,CAAE,0BAA0B/C,WAAW,CAACmH,UAAU,EAAG,CAAAnE,QAAA,CAChEhD,WAAW,CAACmH,UAAU,GAAK,WAAW,CAAG,WAAW,CAAG,WAAW,CAChE,CAAC,EACH,CAAC,EACH,CAAC,cAENpJ,KAAA,QAAKgF,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BjF,KAAA,QAAKgF,SAAS,CAAC,WAAW,CAAAC,QAAA,EACvBhD,WAAW,CAACsG,gBAAgB,GAAK,YAAY,eAC5CzI,IAAA,QAAKkF,SAAS,CAAC,gBAAgB,CAAAC,QAAA,CAAC,UAAQ,CAAK,CAC9C,cACDnF,IAAA,QAAKsI,GAAG,CAAEzB,WAAW,CAAC1E,WAAW,CAACoG,MAAM,CAAE,CAACC,GAAG,CAAErG,WAAW,CAACoG,MAAO,CAAE,CAAC,cACtEvI,IAAA,QAAKkF,SAAS,CAAC,WAAW,CAAAC,QAAA,CAAEhD,WAAW,CAACoG,MAAM,CAAM,CAAC,cACrDvI,IAAA,QAAKkF,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAEhD,WAAW,CAAC8G,cAAc,CAAM,CAAC,cACjE/I,KAAA,QAAKgF,SAAS,CAAC,WAAW,CAAAC,QAAA,EAAEhD,WAAW,CAACoH,WAAW,CAAC,GAAC,EAAK,CAAC,EACxD,CAAC,cAENvJ,IAAA,QAAKkF,SAAS,CAAC,UAAU,CAAAC,QAAA,CAAC,IAAE,CAAK,CAAC,cAElCjF,KAAA,QAAKgF,SAAS,CAAC,WAAW,CAAAC,QAAA,EACvBhD,WAAW,CAACsG,gBAAgB,GAAK,YAAY,eAC5CzI,IAAA,QAAKkF,SAAS,CAAC,gBAAgB,CAAAC,QAAA,CAAC,UAAQ,CAAK,CAC9C,cACDnF,IAAA,QAAKsI,GAAG,CAAEzB,WAAW,CAAC1E,WAAW,CAACuG,MAAM,CAAE,CAACF,GAAG,CAAErG,WAAW,CAACuG,MAAO,CAAE,CAAC,cACtE1I,IAAA,QAAKkF,SAAS,CAAC,WAAW,CAAAC,QAAA,CAAEhD,WAAW,CAACuG,MAAM,CAAM,CAAC,cACrD1I,IAAA,QAAKkF,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAEhD,WAAW,CAAC+G,cAAc,EAAI,sBAAsB,CAAM,CAAC,cAC3FhJ,KAAA,QAAKgF,SAAS,CAAC,WAAW,CAAAC,QAAA,EAAEhD,WAAW,CAACqH,WAAW,CAAC,GAAC,EAAK,CAAC,EACxD,CAAC,EACH,CAAC,cAENtJ,KAAA,QAAKgF,SAAS,CAAC,oBAAoB,CAAAC,QAAA,eACjCjF,KAAA,QAAKgF,SAAS,CAAC,kCAAkC,CAAAC,QAAA,eAC/CnF,IAAA,QAAKkF,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,gBAAc,CAAK,CAAC,cACnDjF,KAAA,QAAKgF,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BjF,KAAA,QAAKgF,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BnF,IAAA,SAAMkF,SAAS,CAAC,gBAAgB,CAAAC,QAAA,CAAC,YAAU,CAAM,CAAC,cAClDnF,IAAA,SAAMkF,SAAS,CAAC,gBAAgB,CAAAC,QAAA,CAAE,GAAI,CAAAwD,IAAI,CAACxG,WAAW,CAACyG,UAAU,CAAC,CAACO,cAAc,CAAC,CAAC,CAAO,CAAC,EACxF,CAAC,cACNjJ,KAAA,QAAKgF,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BnF,IAAA,SAAMkF,SAAS,CAAC,gBAAgB,CAAAC,QAAA,CAAC,YAAU,CAAM,CAAC,cAClDnF,IAAA,SAAMkF,SAAS,CAAC,gBAAgB,CAAAC,QAAA,CAAE,GAAI,CAAAwD,IAAI,CAACxG,WAAW,CAACsH,UAAU,CAAC,CAACX,kBAAkB,CAAC,CAAC,CAAO,CAAC,EAC5F,CAAC,cACN5I,KAAA,QAAKgF,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BnF,IAAA,SAAMkF,SAAS,CAAC,gBAAgB,CAAAC,QAAA,CAAC,UAAQ,CAAM,CAAC,cAChDnF,IAAA,SAAMkF,SAAS,CAAC,gBAAgB,CAAAC,QAAA,CAAE,GAAI,CAAAwD,IAAI,CAACxG,WAAW,CAACuH,QAAQ,CAAC,CAACZ,kBAAkB,CAAC,CAAC,CAAO,CAAC,EAC1F,CAAC,cACN5I,KAAA,QAAKgF,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BnF,IAAA,SAAMkF,SAAS,CAAC,gBAAgB,CAAAC,QAAA,CAAC,mBAAiB,CAAM,CAAC,cACzDnF,IAAA,SAAMkF,SAAS,CAAC,gBAAgB,CAAAC,QAAA,CAAE,GAAI,CAAAwD,IAAI,CAACxG,WAAW,CAACwH,cAAc,CAAC,CAACR,cAAc,CAAC,CAAC,CAAO,CAAC,EAC5F,CAAC,EACH,CAAC,EACH,CAAC,cAENjJ,KAAA,QAAKgF,SAAS,CAAC,8BAA8B,CAAAC,QAAA,eAC3CnF,IAAA,QAAKkF,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,kBAAgB,CAAK,CAAC,cACrDjF,KAAA,QAAKgF,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBjF,KAAA,QAAKgF,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBjF,KAAA,SAAMgF,SAAS,CAAC,YAAY,CAAAC,QAAA,EAAEhD,WAAW,CAACoG,MAAM,CAAC,MAAI,EAAM,CAAC,cAC5DrI,KAAA,SAAMgF,SAAS,CAAC,YAAY,CAAAC,QAAA,EAAEhD,WAAW,CAACoH,WAAW,CAAC,GAAC,EAAM,CAAC,EAC3D,CAAC,cACNrJ,KAAA,QAAKgF,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBjF,KAAA,SAAMgF,SAAS,CAAC,YAAY,CAAAC,QAAA,EAAEhD,WAAW,CAACuG,MAAM,CAAC,MAAI,EAAM,CAAC,cAC5DxI,KAAA,SAAMgF,SAAS,CAAC,YAAY,CAAAC,QAAA,EAAEhD,WAAW,CAACqH,WAAW,CAAC,GAAC,EAAM,CAAC,EAC3D,CAAC,cACNtJ,KAAA,QAAKgF,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBnF,IAAA,SAAMkF,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,MAAI,CAAM,CAAC,cACxCjF,KAAA,SAAMgF,SAAS,CAAC,YAAY,CAAAC,QAAA,EAAEhD,WAAW,CAACyH,SAAS,CAAC,GAAC,EAAM,CAAC,EACzD,CAAC,cACN1J,KAAA,QAAKgF,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBnF,IAAA,SAAMkF,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,iBAAe,CAAM,CAAC,cACnDjF,KAAA,SAAMgF,SAAS,CAAC,YAAY,CAAAC,QAAA,EAAEhD,WAAW,CAAC0H,SAAS,CAAC,GAAC,EAAM,CAAC,EACzD,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cAEN3J,KAAA,QAAKgF,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BjF,KAAA,QAAKgF,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9BnF,IAAA,QAAKkF,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,YAAU,CAAK,CAAC,cAC/CjF,KAAA,QAAKgF,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzBnF,IAAA,SAAMkF,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,YAAU,CAAM,CAAC,cAChDnF,IAAA,SAAMkF,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CAAEhD,WAAW,CAAC8G,cAAc,CAAO,CAAC,EAC1E,CAAC,cACN/I,KAAA,QAAKgF,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzBnF,IAAA,SAAMkF,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,QAAM,CAAM,CAAC,cAC5CnF,IAAA,SAAMkF,SAAS,CAAC,2BAA2B,CAAAC,QAAA,CACxChD,WAAW,CAAC+G,cAAc,CAAG,aAAa/G,WAAW,CAAC+G,cAAc,EAAE,CAAG,sBAAsB,CAC5F,CAAC,EACJ,CAAC,EACH,CAAC,cAENhJ,KAAA,QAAKgF,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9BnF,IAAA,QAAKkF,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,mBAAiB,CAAK,CAAC,cACtDjF,KAAA,QAAKgF,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzBnF,IAAA,SAAMkF,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,UAAQ,CAAM,CAAC,cAC9CjF,KAAA,SAAMgF,SAAS,CAAC,qBAAqB,CAAAC,QAAA,EAAEhD,WAAW,CAACsD,YAAY,CAAC,WAAS,EAAM,CAAC,EAC7E,CAAC,cACNvF,KAAA,QAAKgF,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzBnF,IAAA,SAAMkF,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,eAAa,CAAM,CAAC,cACnDjF,KAAA,SAAMgF,SAAS,CAAC,qBAAqB,CAAAC,QAAA,EAAEhD,WAAW,CAACwD,0BAA0B,CAAC,WAAS,EAAM,CAAC,EAC3F,CAAC,cACNzF,KAAA,QAAKgF,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzBnF,IAAA,SAAMkF,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,gBAAc,CAAM,CAAC,cACpDjF,KAAA,SAAMgF,SAAS,CAAC,qBAAqB,CAAAC,QAAA,EAAEhD,WAAW,CAAC2H,oBAAoB,CAAC,WAAS,EAAM,CAAC,EACrF,CAAC,cACN5J,KAAA,QAAKgF,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzBnF,IAAA,SAAMkF,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,cAAY,CAAM,CAAC,cAClDjF,KAAA,SAAMgF,SAAS,CAAC,cAAc,CAAAC,QAAA,EAAEhD,WAAW,CAAC4H,wBAAwB,CAAC,WAAS,EAAM,CAAC,EAClF,CAAC,CACL5H,WAAW,CAAC+G,cAAc,eACzBhJ,KAAA,CAAAE,SAAA,EAAA+E,QAAA,eACEjF,KAAA,QAAKgF,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzBnF,IAAA,SAAMkF,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,cAAY,CAAM,CAAC,cAClDjF,KAAA,SAAMgF,SAAS,CAAC,qBAAqB,CAAAC,QAAA,EAAEhD,WAAW,CAAC6H,YAAY,CAAC,WAAS,EAAM,CAAC,EAC7E,CAAC,cACN9J,KAAA,QAAKgF,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzBnF,IAAA,SAAMkF,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,cAAY,CAAM,CAAC,cAClDjF,KAAA,SAAMgF,SAAS,CAAC,qBAAqB,CAAAC,QAAA,EAAEhD,WAAW,CAAC8H,0BAA0B,CAAC,WAAS,EAAM,CAAC,EAC3F,CAAC,EACN,CACH,EACE,CAAC,EACH,CAAC,EACH,CAAC,CACH,CACN,CAGA5C,MAAM,CAAC+B,UAAU,EAAI,GAAG,EAAInH,mBAAmB,EAAIE,WAAW,eAC7DnC,IAAA,QAAKkF,SAAS,CAAC,eAAe,CAACD,OAAO,CAAEA,CAAA,GAAM/C,sBAAsB,CAAC,KAAK,CAAE,CAAAiD,QAAA,cAC1EjF,KAAA,QAAKgF,SAAS,CAAC,sBAAsB,CAACD,OAAO,CAAEP,CAAC,EAAIA,CAAC,CAAC2E,eAAe,CAAC,CAAE,CAAAlE,QAAA,eACtEjF,KAAA,QAAKgF,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eAClCnF,IAAA,OAAAmF,QAAA,CAAI,aAAW,CAAI,CAAC,cACpBnF,IAAA,WAAQkF,SAAS,CAAC,oBAAoB,CAACD,OAAO,CAAEA,CAAA,GAAM/C,sBAAsB,CAAC,KAAK,CAAE,CAAAiD,QAAA,CAAC,MAAC,CAAQ,CAAC,EAC5F,CAAC,cAENjF,KAAA,QAAKgF,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCjF,KAAA,QAAKgF,SAAS,CAAC,oBAAoB,CAAAC,QAAA,eACjCjF,KAAA,QAAKgF,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCnF,IAAA,SAAMkF,SAAS,CAAC,kBAAkB,CAAAC,QAAA,CAAC,kBAAgB,CAAM,CAAC,cAC1DnF,IAAA,SAAMkF,SAAS,CAAC,kBAAkB,CAAAC,QAAA,CAAEyC,YAAY,CAACzF,WAAW,CAAC,CAAO,CAAC,EAClE,CAAC,cACNjC,KAAA,QAAKgF,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACnCjF,KAAA,SAAMgF,SAAS,CAAE,uBAAuB/C,WAAW,CAACwE,UAAU,CAACC,WAAW,CAAC,CAAC,EAAG,CAAAzB,QAAA,EAC5EhD,WAAW,CAACwE,UAAU,GAAK,MAAM,EAAI,eAAe,CACpDxE,WAAW,CAACwE,UAAU,GAAK,QAAQ,EAAI,aAAa,CACpDxE,WAAW,CAACwE,UAAU,GAAK,WAAW,EAAI,WAAW,EAClD,CAAC,cACP3G,IAAA,SAAMkF,SAAS,CAAC,uCAAuC,CAAAC,QAAA,CACpDhD,WAAW,CAACmH,UAAU,GAAK,WAAW,CAAG,WAAW,CAAG,WAAW,CAC/D,CAAC,EACJ,CAAC,EACH,CAAC,cAENpJ,KAAA,QAAKgF,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACnCjF,KAAA,QAAKgF,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/BnF,IAAA,SAAMkF,SAAS,CAAC,kBAAkB,CAAAC,QAAA,CAAEhD,WAAW,CAAC8G,cAAc,CAAO,CAAC,cACtEjJ,IAAA,SAAMkF,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CACzCsB,aAAa,CAACtE,WAAW,CAAE,IAAI,CAAC,CAC7B,CAAC,EACJ,CAAC,cACNnC,IAAA,QAAKkF,SAAS,CAAC,mBAAmB,CAAAC,QAAA,CAAC,IAAE,CAAK,CAAC,cAC3CjF,KAAA,QAAKgF,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/BnF,IAAA,SAAMkF,SAAS,CAAC,kBAAkB,CAAAC,QAAA,CAC/BhD,WAAW,CAAC+G,cAAc,EAAI,KAAK,CAChC,CAAC,cACPlJ,IAAA,SAAMkF,SAAS,CAAE,sBAAsB/C,WAAW,CAAC+G,cAAc,CAAG,UAAU,CAAG,SAAS,EAAG,CAAA/D,QAAA,CAC1FsB,aAAa,CAACtE,WAAW,CAAE,KAAK,CAAC,CAC9B,CAAC,EACJ,CAAC,EACH,CAAC,cAENjC,KAAA,QAAKgF,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eACrCjF,KAAA,QAAKgF,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BnF,IAAA,QACEsI,GAAG,CAAEzB,WAAW,CAAC1E,WAAW,CAACoG,MAAM,CAAE,CACrCC,GAAG,CAAErG,WAAW,CAACoG,MAAO,CACxBrD,SAAS,CAAC,kBAAkB,CAC7B,CAAC,cACFlF,IAAA,SAAAmF,QAAA,CAAOhD,WAAW,CAACoG,MAAM,CAAO,CAAC,EAC9B,CAAC,cACNvI,IAAA,QAAKkF,SAAS,CAAC,WAAW,CAAAC,QAAA,CAAC,IAAE,CAAK,CAAC,cACnCjF,KAAA,QAAKgF,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BnF,IAAA,QACEsI,GAAG,CAAEzB,WAAW,CAAC1E,WAAW,CAACuG,MAAM,CAAE,CACrCF,GAAG,CAAErG,WAAW,CAACuG,MAAO,CACxBxD,SAAS,CAAC,kBAAkB,CAC7B,CAAC,cACFlF,IAAA,SAAAmF,QAAA,CAAOhD,WAAW,CAACuG,MAAM,CAAO,CAAC,EAC9B,CAAC,EACH,CAAC,cAEN1I,IAAA,QAAKkF,SAAS,CAAC,sBAAsB,CAAAC,QAAA,CAAC,kBAAgB,CAAK,CAAC,cAC5DjF,KAAA,QAAKgF,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/BjF,KAAA,QAAKgF,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/BjF,KAAA,SAAMgF,SAAS,CAAC,mBAAmB,CAAAC,QAAA,EAAEhD,WAAW,CAACoG,MAAM,CAAC,MAAI,EAAM,CAAC,cACnErI,KAAA,SAAMgF,SAAS,CAAC,mBAAmB,CAAAC,QAAA,EAAEhD,WAAW,CAACoH,WAAW,CAAC,GAAC,EAAM,CAAC,EAClE,CAAC,cACNrJ,KAAA,QAAKgF,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/BjF,KAAA,SAAMgF,SAAS,CAAC,mBAAmB,CAAAC,QAAA,EAAEhD,WAAW,CAACuG,MAAM,CAAC,MAAI,EAAM,CAAC,cACnExI,KAAA,SAAMgF,SAAS,CAAC,mBAAmB,CAAAC,QAAA,EAAEhD,WAAW,CAACqH,WAAW,CAAC,GAAC,EAAM,CAAC,EAClE,CAAC,cACNtJ,KAAA,QAAKgF,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/BnF,IAAA,SAAMkF,SAAS,CAAC,mBAAmB,CAAAC,QAAA,CAAC,MAAI,CAAM,CAAC,cAC/CjF,KAAA,SAAMgF,SAAS,CAAC,mBAAmB,CAAAC,QAAA,EAAEhD,WAAW,CAACyH,SAAS,CAAC,GAAC,EAAM,CAAC,EAChE,CAAC,cACN1J,KAAA,QAAKgF,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/BnF,IAAA,SAAMkF,SAAS,CAAC,mBAAmB,CAAAC,QAAA,CAAC,MAAI,CAAM,CAAC,cAC/CjF,KAAA,SAAMgF,SAAS,CAAC,mBAAmB,CAAAC,QAAA,EAAEhD,WAAW,CAAC0H,SAAS,CAAC,GAAC,EAAM,CAAC,EAChE,CAAC,EACH,CAAC,cAEN7J,IAAA,QAAKkF,SAAS,CAAC,sBAAsB,CAAAC,QAAA,CAAC,mBAAiB,CAAK,CAAC,cAC7DjF,KAAA,QAAKgF,SAAS,CAAC,uBAAuB,CAAAC,QAAA,eACpCjF,KAAA,QAAKgF,SAAS,CAAC,uBAAuB,CAAAC,QAAA,eACpCnF,IAAA,SAAMkF,SAAS,CAAC,wBAAwB,CAAAC,QAAA,CAAC,UAAQ,CAAM,CAAC,cACxDjF,KAAA,SAAMgF,SAAS,CAAC,wBAAwB,CAAAC,QAAA,EAAEhD,WAAW,CAACsD,YAAY,CAAC,KAAG,EAAM,CAAC,EAC1E,CAAC,cACNvF,KAAA,QAAKgF,SAAS,CAAC,uBAAuB,CAAAC,QAAA,eACpCnF,IAAA,SAAMkF,SAAS,CAAC,wBAAwB,CAAAC,QAAA,CAAC,eAAa,CAAM,CAAC,cAC7DjF,KAAA,SAAMgF,SAAS,CAAC,4BAA4B,CAAAC,QAAA,EAAC,GAAC,CAAChD,WAAW,CAACwD,0BAA0B,CAAC,KAAG,EAAM,CAAC,EAC7F,CAAC,cACNzF,KAAA,QAAKgF,SAAS,CAAC,uBAAuB,CAAAC,QAAA,eACpCnF,IAAA,SAAMkF,SAAS,CAAC,wBAAwB,CAAAC,QAAA,CAAC,gBAAc,CAAM,CAAC,cAC9DjF,KAAA,SAAMgF,SAAS,CAAC,6BAA6B,CAAAC,QAAA,EAAC,GAAC,CAAChD,WAAW,CAAC0D,2BAA2B,CAAC,KAAG,EAAM,CAAC,EAC/F,CAAC,cACN3F,KAAA,QAAKgF,SAAS,CAAC,uBAAuB,CAAAC,QAAA,eACpCnF,IAAA,SAAMkF,SAAS,CAAC,wBAAwB,CAAAC,QAAA,CAAC,aAAW,CAAM,CAAC,cAC3DjF,KAAA,SAAMgF,SAAS,CAAC,6BAA6B,CAAAC,QAAA,EAAEhD,WAAW,CAAC4D,2BAA2B,CAAC,KAAG,EAAM,CAAC,EAC9F,CAAC,EACH,CAAC,EACH,CAAC,CAEL5D,WAAW,CAACwE,UAAU,GAAK,MAAM,eAChC3G,IAAA,QAAKkF,SAAS,CAAC,qBAAqB,CAAAC,QAAA,cAClCnF,IAAA,WACEkF,SAAS,CAAC,4BAA4B,CACtCD,OAAO,CAAEA,CAAA,GAAMkC,kBAAkB,CAAChF,WAAW,CAAE,CAAAgD,QAAA,CAChD,eAED,CAAQ,CAAC,CACN,CACN,EACE,CAAC,CACH,CACN,CAEAtD,aAAa,eACZ7B,IAAA,QAAKkF,SAAS,CAAC,eAAe,CAACD,OAAO,CAAE0C,gBAAiB,CAAAxC,QAAA,cACvDjF,KAAA,QAAKgF,SAAS,CAAC,YAAY,CAACD,OAAO,CAAEP,CAAC,EAAIA,CAAC,CAAC2E,eAAe,CAAC,CAAE,CAAAlE,QAAA,eAC5DnF,IAAA,SAAMkF,SAAS,CAAC,OAAO,CAACD,OAAO,CAAE0C,gBAAiB,CAAAxC,QAAA,CAAC,MAAO,CAAM,CAAC,cACjEnF,IAAA,OAAAmF,QAAA,CAAI,kCAAgC,CAAI,CAAC,cACzCjF,KAAA,QAAKgF,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BnF,IAAA,UAAO+H,IAAI,CAAC,MAAM,CAACnD,KAAK,CAAE7C,eAAgB,CAACmI,QAAQ,MAAE,CAAC,cACtDlK,IAAA,WAAQiF,OAAO,CAAEA,CAAA,GAAM,CACrBkF,SAAS,CAACC,SAAS,CAACC,SAAS,CAACtI,eAAe,CAAC,CAC9C;AACA,KAAM,CAAAuI,MAAM,CAAGC,QAAQ,CAACC,aAAa,CACrC,KAAM,CAAAC,YAAY,CAAGH,MAAM,CAACI,WAAW,CACvCJ,MAAM,CAACI,WAAW,CAAG,SAAS,CAC9BJ,MAAM,CAACK,KAAK,CAACC,eAAe,CAAG,SAAS,CACxCvG,UAAU,CAAC,IAAM,CACfiG,MAAM,CAACI,WAAW,CAAGD,YAAY,CACjCH,MAAM,CAACK,KAAK,CAACC,eAAe,CAAG,EAAE,CACnC,CAAC,CAAE,IAAI,CAAC,CACV,CAAE,CAAAzF,QAAA,CAAC,MAEH,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,CACH,CACN,cAEDnF,IAAA,UAAOD,GAAG,MAAAoF,QAAA,CAAE;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,CAAQ,CAAC,EACP,CAAC,CAEV,CAEA,cAAe,CAAA7E,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}