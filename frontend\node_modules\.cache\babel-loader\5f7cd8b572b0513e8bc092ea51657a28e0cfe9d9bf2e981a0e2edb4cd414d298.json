{"ast": null, "code": "var _jsxFileName = \"C:\\\\MAMP\\\\htdocs\\\\FanBet247\\\\frontend\\\\src\\\\pages\\\\UserManagement.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport axios from 'axios';\nimport { FaUsers, FaUserPlus, FaUserCheck, FaUserClock, FaEdit, FaSearch, FaEye, FaUserSlash, FaBan } from 'react-icons/fa';\nimport CustomModal from '../components/CustomModal';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst API_BASE_URL = '/backend';\nfunction UserManagement() {\n  _s();\n  const navigate = useNavigate();\n  const [users, setUsers] = useState([]);\n  const [teams, setTeams] = useState([]);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  const [editingUserId, setEditingUserId] = useState(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [currentPage, setCurrentPage] = useState(1);\n  const [usersPerPage] = useState(10);\n  const [stats, setStats] = useState({\n    totalUsers: 0,\n    activeUsers: 0,\n    newUsers: 0,\n    pendingUsers: 0\n  });\n  const [editingUser, setEditingUser] = useState({\n    username: '',\n    full_name: '',\n    email: '',\n    favorite_team: '',\n    balance: 0\n  });\n  const [showAddUserModal, setShowAddUserModal] = useState(false);\n  const [newUser, setNewUser] = useState({\n    username: '',\n    full_name: '',\n    email: '',\n    password: '',\n    favorite_team: '',\n    balance: 0\n  });\n\n  // Modal states\n  const [modalState, setModalState] = useState({\n    isOpen: false,\n    type: 'confirm',\n    title: '',\n    message: '',\n    onConfirm: null,\n    confirmText: 'Confirm',\n    confirmButtonColor: 'blue'\n  });\n  useEffect(() => {\n    fetchUsers();\n    fetchTeams();\n  }, []);\n  const fetchUsers = async () => {\n    try {\n      const response = await axios.get(`${API_BASE_URL}/handlers/user_management.php`);\n      if (response.data.success) {\n        const userData = response.data.data || [];\n        setUsers(userData);\n\n        // Calculate stats\n        const now = new Date();\n        const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);\n\n        // For demo purposes, we'll simulate some stats\n        const totalUsers = userData.length;\n        const activeUsers = userData.filter(user => user.last_login && new Date(user.last_login) > oneWeekAgo).length || Math.floor(totalUsers * 0.7);\n        const newUsers = userData.filter(user => user.created_at && new Date(user.created_at) > oneWeekAgo).length || Math.floor(totalUsers * 0.2);\n        const pendingUsers = userData.filter(user => user.status === 'pending').length || Math.floor(totalUsers * 0.1);\n        setStats({\n          totalUsers,\n          activeUsers,\n          newUsers,\n          pendingUsers\n        });\n      } else {\n        setError(response.data.message || 'Failed to fetch users');\n      }\n    } catch (err) {\n      setError('Failed to fetch users. Please check your network connection and try again.');\n      console.error('Error fetching users:', err);\n    }\n  };\n  const fetchTeams = async () => {\n    try {\n      const response = await axios.get(`${API_BASE_URL}/handlers/team_management.php`);\n      setTeams(response.data.data || []);\n    } catch (err) {\n      setError('Failed to fetch teams');\n    }\n  };\n  const getTeamLogo = teamName => {\n    const team = teams.find(team => team.name === teamName);\n    return team ? `${API_BASE_URL}/${team.logo}` : null;\n  };\n  const getDefaultAvatar = () => {\n    return \"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='40' height='40' viewBox='0 0 40 40'%3E%3Ccircle cx='20' cy='20' r='20' fill='%23e5e7eb'/%3E%3Cpath d='M20 20c3.3 0 6-2.7 6-6s-2.7-6-6-6-6 2.7-6 6 2.7 6 6 6zm0 3c-4 0-12 2-12 6v3h24v-3c0-4-8-6-12-6z' fill='%23374151'/%3E%3C/svg%3E\";\n  };\n  const handleEdit = user => {\n    setEditingUserId(user.user_id);\n    setEditingUser(user);\n  };\n  const handleUpdate = async e => {\n    e.preventDefault();\n    try {\n      const response = await axios.put(`${API_BASE_URL}/handlers/user_management.php?id=${editingUserId}`, editingUser);\n      if (response.data.success) {\n        setSuccess('User updated successfully!');\n        fetchUsers();\n        setEditingUserId(null);\n        setTimeout(() => setSuccess(''), 3000);\n      } else {\n        setError(response.data.message || 'Failed to update user');\n        setTimeout(() => setError(''), 3000);\n      }\n    } catch (err) {\n      setError('Failed to update user');\n    }\n  };\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setEditingUser(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n  const handleNewUserInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setNewUser(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n  const handleAddUser = async e => {\n    e.preventDefault();\n    try {\n      const response = await axios.post(`${API_BASE_URL}/handlers/add_user.php`, newUser);\n      if (response.data.success) {\n        setSuccess('User added successfully!');\n        fetchUsers();\n        setShowAddUserModal(false);\n        setNewUser({\n          username: '',\n          full_name: '',\n          email: '',\n          password: '',\n          favorite_team: '',\n          balance: 0\n        });\n        // Clear success message after 3 seconds\n        setTimeout(() => setSuccess(''), 3000);\n      } else {\n        setError(response.data.message || 'Failed to add user');\n        setTimeout(() => setError(''), 3000);\n      }\n    } catch (err) {\n      setError('Failed to add user');\n      setTimeout(() => setError(''), 3000);\n    }\n  };\n  const handleSuspendUser = user => {\n    setModalState({\n      isOpen: true,\n      type: 'confirm',\n      title: 'Suspend User',\n      message: `Are you sure you want to suspend user \"${user.username}\"? This will prevent them from accessing their account.`,\n      confirmText: 'Suspend User',\n      confirmButtonColor: 'yellow',\n      onConfirm: () => performSuspendUser(user)\n    });\n  };\n  const performSuspendUser = async user => {\n    try {\n      const response = await axios.post(`${API_BASE_URL}/handlers/suspend_user.php`, {\n        user_id: user.user_id,\n        action: 'suspend'\n      });\n      if (response.data.success) {\n        setSuccess(`User \"${user.username}\" suspended successfully!`);\n        fetchUsers();\n        setTimeout(() => setSuccess(''), 3000);\n      } else {\n        setError(response.data.message || 'Failed to suspend user');\n        setTimeout(() => setError(''), 3000);\n      }\n    } catch (err) {\n      setError('Failed to suspend user');\n      setTimeout(() => setError(''), 3000);\n    }\n  };\n  const handleBanUser = async user => {\n    if (window.confirm(`Are you sure you want to ban user \"${user.username}\"? This action cannot be undone.`)) {\n      try {\n        const response = await axios.post(`${API_BASE_URL}/handlers/ban_user.php`, {\n          user_id: user.user_id,\n          action: 'ban'\n        });\n        if (response.data.success) {\n          setSuccess(`User \"${user.username}\" banned successfully!`);\n          fetchUsers();\n          setTimeout(() => setSuccess(''), 3000);\n        } else {\n          setError(response.data.message || 'Failed to ban user');\n          setTimeout(() => setError(''), 3000);\n        }\n      } catch (err) {\n        setError('Failed to ban user');\n        setTimeout(() => setError(''), 3000);\n      }\n    }\n  };\n\n  // Search functionality\n  const handleSearch = e => {\n    setSearchTerm(e.target.value);\n    setCurrentPage(1); // Reset to first page when searching\n  };\n\n  // Filter users based on search term\n  const filteredUsers = users.filter(user => user.username.toLowerCase().includes(searchTerm.toLowerCase()) || user.full_name.toLowerCase().includes(searchTerm.toLowerCase()) || user.email.toLowerCase().includes(searchTerm.toLowerCase()));\n\n  // Pagination\n  const indexOfLastUser = currentPage * usersPerPage;\n  const indexOfFirstUser = indexOfLastUser - usersPerPage;\n  const currentUsers = filteredUsers.slice(indexOfFirstUser, indexOfLastUser);\n  const totalPages = Math.ceil(filteredUsers.length / usersPerPage);\n  const paginate = pageNumber => setCurrentPage(pageNumber);\n\n  // Generate page numbers\n  const pageNumbers = [];\n  for (let i = 1; i <= totalPages; i++) {\n    pageNumbers.push(i);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"p-6 bg-gray-50 min-h-screen\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-2xl font-bold text-gray-800\",\n        children: \"User Management\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 254,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-600\",\n        children: \"Manage all users in the system\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 255,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 253,\n      columnNumber: 13\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative\",\n      role: \"alert\",\n      children: /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"block sm:inline\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 261,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 260,\n      columnNumber: 17\n    }, this), success && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative\",\n      role: \"alert\",\n      children: /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"block sm:inline\",\n        children: success\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 266,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 265,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm p-6 flex items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"rounded-full bg-blue-100 p-3 mr-4\",\n          children: /*#__PURE__*/_jsxDEV(FaUsers, {\n            className: \"text-blue-500 text-xl\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 275,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 274,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-500 uppercase tracking-wider\",\n            children: \"Total Users\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-2xl font-bold text-gray-800\",\n            children: stats.totalUsers\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 279,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 277,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 273,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm p-6 flex items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"rounded-full bg-green-100 p-3 mr-4\",\n          children: /*#__PURE__*/_jsxDEV(FaUserCheck, {\n            className: \"text-green-500 text-xl\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 286,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 285,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-500 uppercase tracking-wider\",\n            children: \"Active Users\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 289,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-2xl font-bold text-gray-800\",\n            children: stats.activeUsers\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 290,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 288,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 284,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm p-6 flex items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"rounded-full bg-yellow-100 p-3 mr-4\",\n          children: /*#__PURE__*/_jsxDEV(FaUserPlus, {\n            className: \"text-yellow-500 text-xl\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 296,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-500 uppercase tracking-wider\",\n            children: \"New Users (7d)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 300,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-2xl font-bold text-gray-800\",\n            children: stats.newUsers\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 301,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 299,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 295,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm p-6 flex items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"rounded-full bg-purple-100 p-3 mr-4\",\n          children: /*#__PURE__*/_jsxDEV(FaUserClock, {\n            className: \"text-purple-500 text-xl\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 308,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 307,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-500 uppercase tracking-wider\",\n            children: \"Pending Users\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 311,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-2xl font-bold text-gray-800\",\n            children: stats.pendingUsers\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 312,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 310,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 306,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 271,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg shadow-sm p-6 mb-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col md:flex-row md:items-center md:justify-between mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-lg font-semibold text-white bg-blue-600 px-4 py-2 rounded mb-4 md:mb-0\",\n          children: \"User List\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 320,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col md:flex-row md:items-center space-y-2 md:space-y-0 md:space-x-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowAddUserModal(true),\n            className: \"flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700\",\n            children: [/*#__PURE__*/_jsxDEV(FaUserPlus, {\n              className: \"mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 326,\n              columnNumber: 29\n            }, this), \"Add User\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 322,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative w-full md:w-64\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n              children: /*#__PURE__*/_jsxDEV(FaSearch, {\n                className: \"h-4 w-4 text-gray-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 331,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 330,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              className: \"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm\",\n              placeholder: \"Search users...\",\n              value: searchTerm,\n              onChange: handleSearch\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 333,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 329,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 321,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 319,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"overflow-x-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"table\", {\n          className: \"min-w-full divide-y divide-gray-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            className: \"bg-blue-600\",\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                scope: \"col\",\n                className: \"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider\",\n                children: \"#\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 349,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                scope: \"col\",\n                className: \"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider\",\n                children: \"Avatar\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 352,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                scope: \"col\",\n                className: \"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider\",\n                children: \"Username\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 355,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                scope: \"col\",\n                className: \"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider\",\n                children: \"Full Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 358,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                scope: \"col\",\n                className: \"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider\",\n                children: \"Email\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 361,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                scope: \"col\",\n                className: \"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider\",\n                children: \"Favorite Team\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 364,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                scope: \"col\",\n                className: \"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider\",\n                children: \"Balance\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 367,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                scope: \"col\",\n                className: \"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider\",\n                children: \"Actions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 370,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 348,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 347,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            className: \"bg-white divide-y divide-gray-200\",\n            children: currentUsers.map((user, index) => /*#__PURE__*/_jsxDEV(\"tr\", {\n              className: \"hover:bg-gray-50\",\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                children: indexOfFirstUser + index + 1\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 378,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: getTeamLogo(user.favorite_team) || getDefaultAvatar(),\n                    alt: user.favorite_team || 'Default Avatar',\n                    className: \"w-10 h-10 rounded-full object-contain border border-gray-200\",\n                    onError: e => {\n                      e.target.src = getDefaultAvatar();\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 383,\n                    columnNumber: 45\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 382,\n                  columnNumber: 41\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 381,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm font-medium text-gray-900\",\n                  children: user.username\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 394,\n                  columnNumber: 41\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 393,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm text-gray-500\",\n                  children: user.full_name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 397,\n                  columnNumber: 41\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 396,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm text-gray-500\",\n                  children: user.email\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 400,\n                  columnNumber: 41\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 399,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2\",\n                  children: [user.favorite_team && getTeamLogo(user.favorite_team) && /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: getTeamLogo(user.favorite_team),\n                    alt: user.favorite_team,\n                    className: \"w-5 h-5 rounded-full object-contain\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 405,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm text-gray-500\",\n                    children: user.favorite_team || 'No team selected'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 411,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 403,\n                  columnNumber: 41\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 402,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm font-medium text-gray-900\",\n                  children: [user.balance, \" FanCoins\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 415,\n                  columnNumber: 41\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 414,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex space-x-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => navigate(`/admin/users/${user.user_id}`),\n                    className: \"text-green-600 hover:text-green-900 p-1\",\n                    title: \"View Details\",\n                    children: /*#__PURE__*/_jsxDEV(FaEye, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 424,\n                      columnNumber: 49\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 419,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleEdit(user),\n                    className: \"text-blue-600 hover:text-blue-900 p-1\",\n                    title: \"Edit User\",\n                    children: /*#__PURE__*/_jsxDEV(FaEdit, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 431,\n                      columnNumber: 49\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 426,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleSuspendUser(user),\n                    className: \"text-yellow-600 hover:text-yellow-900 p-1\",\n                    title: \"Suspend User\",\n                    children: /*#__PURE__*/_jsxDEV(FaUserSlash, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 438,\n                      columnNumber: 49\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 433,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleBanUser(user),\n                    className: \"text-red-600 hover:text-red-900 p-1\",\n                    title: \"Ban User\",\n                    children: /*#__PURE__*/_jsxDEV(FaBan, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 445,\n                      columnNumber: 49\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 440,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 418,\n                  columnNumber: 41\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 417,\n                columnNumber: 37\n              }, this)]\n            }, user.user_id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 377,\n              columnNumber: 33\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 375,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 346,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 345,\n        columnNumber: 17\n      }, this), totalPages > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between border-t border-gray-200 px-4 py-3 sm:px-6 mt-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-700\",\n              children: [\"Showing \", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-medium\",\n                children: indexOfFirstUser + 1\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 461,\n                columnNumber: 45\n              }, this), \" to\", ' ', /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-medium\",\n                children: indexOfLastUser > filteredUsers.length ? filteredUsers.length : indexOfLastUser\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 462,\n                columnNumber: 37\n              }, this), ' ', \"of \", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-medium\",\n                children: filteredUsers.length\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 465,\n                columnNumber: 40\n              }, this), \" results\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 460,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 459,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: /*#__PURE__*/_jsxDEV(\"nav\", {\n              className: \"relative z-0 inline-flex rounded-md shadow-sm -space-x-px\",\n              \"aria-label\": \"Pagination\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => paginate(currentPage > 1 ? currentPage - 1 : 1),\n                disabled: currentPage === 1,\n                className: `relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium ${currentPage === 1 ? 'text-gray-300 cursor-not-allowed' : 'text-gray-500 hover:bg-gray-50'}`,\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"sr-only\",\n                  children: \"Previous\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 477,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"h-5 w-5\",\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  viewBox: \"0 0 20 20\",\n                  fill: \"currentColor\",\n                  \"aria-hidden\": \"true\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    fillRule: \"evenodd\",\n                    d: \"M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z\",\n                    clipRule: \"evenodd\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 479,\n                    columnNumber: 45\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 478,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 470,\n                columnNumber: 37\n              }, this), pageNumbers.map(number => /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => paginate(number),\n                className: `relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium ${currentPage === number ? 'z-10 bg-blue-50 border-blue-500 text-blue-600' : 'text-gray-500 hover:bg-gray-50'}`,\n                children: number\n              }, number, false, {\n                fileName: _jsxFileName,\n                lineNumber: 484,\n                columnNumber: 41\n              }, this)), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => paginate(currentPage < totalPages ? currentPage + 1 : totalPages),\n                disabled: currentPage === totalPages,\n                className: `relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium ${currentPage === totalPages ? 'text-gray-300 cursor-not-allowed' : 'text-gray-500 hover:bg-gray-50'}`,\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"sr-only\",\n                  children: \"Next\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 504,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"h-5 w-5\",\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  viewBox: \"0 0 20 20\",\n                  fill: \"currentColor\",\n                  \"aria-hidden\": \"true\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    fillRule: \"evenodd\",\n                    d: \"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z\",\n                    clipRule: \"evenodd\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 506,\n                    columnNumber: 45\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 505,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 497,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 469,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 468,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 458,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 457,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 318,\n      columnNumber: 13\n    }, this), editingUserId && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-xl w-full max-w-md\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-between items-center p-4 border-b\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-gray-800\",\n            children: \"Edit User\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 521,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"text-gray-500 hover:text-gray-700 text-2xl focus:outline-none\",\n            onClick: () => setEditingUserId(null),\n            children: \"\\xD7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 522,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 520,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleUpdate,\n          className: \"p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Username\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 533,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                name: \"username\",\n                value: editingUser.username,\n                onChange: handleInputChange,\n                className: \"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 534,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 532,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Full Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 545,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                name: \"full_name\",\n                value: editingUser.full_name,\n                onChange: handleInputChange,\n                className: \"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 546,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 544,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Email\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 557,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"email\",\n                name: \"email\",\n                value: editingUser.email,\n                onChange: handleInputChange,\n                className: \"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 558,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 556,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Favorite Team\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 569,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                name: \"favorite_team\",\n                value: editingUser.favorite_team,\n                onChange: handleInputChange,\n                className: \"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\",\n                required: true,\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Select Favorite Team\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 577,\n                  columnNumber: 41\n                }, this), teams.map(team => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: team.name,\n                  children: team.name\n                }, team.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 579,\n                  columnNumber: 45\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 570,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 568,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Balance (FanCoins)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 585,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                name: \"balance\",\n                value: editingUser.balance,\n                onChange: handleInputChange,\n                className: \"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 586,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 584,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 531,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-6 flex justify-end space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              onClick: () => setEditingUserId(null),\n              className: \"px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 598,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n              children: \"Update User\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 605,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 597,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 530,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 519,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 518,\n      columnNumber: 17\n    }, this), showAddUserModal && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-xl w-full max-w-md\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-between items-center p-4 border-b\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-gray-800\",\n            children: \"Add New User\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 622,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"text-gray-500 hover:text-gray-700 text-2xl focus:outline-none\",\n            onClick: () => setShowAddUserModal(false),\n            children: \"\\xD7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 623,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 621,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleAddUser,\n          className: \"p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Username\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 634,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                name: \"username\",\n                value: newUser.username,\n                onChange: handleNewUserInputChange,\n                className: \"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 635,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 633,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Full Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 646,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                name: \"full_name\",\n                value: newUser.full_name,\n                onChange: handleNewUserInputChange,\n                className: \"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 647,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 645,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Email\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 658,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"email\",\n                name: \"email\",\n                value: newUser.email,\n                onChange: handleNewUserInputChange,\n                className: \"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 659,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 657,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Password\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 670,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"password\",\n                name: \"password\",\n                value: newUser.password,\n                onChange: handleNewUserInputChange,\n                className: \"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 671,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 669,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Favorite Team\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 682,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                name: \"favorite_team\",\n                value: newUser.favorite_team,\n                onChange: handleNewUserInputChange,\n                className: \"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Select Favorite Team\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 689,\n                  columnNumber: 41\n                }, this), teams.map(team => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: team.name,\n                  children: team.name\n                }, team.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 691,\n                  columnNumber: 45\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 683,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 681,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Initial Balance (FanCoins)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 697,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                name: \"balance\",\n                value: newUser.balance,\n                onChange: handleNewUserInputChange,\n                className: \"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm\",\n                min: \"0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 698,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 696,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 632,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-6 flex justify-end space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              onClick: () => setShowAddUserModal(false),\n              className: \"px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500\",\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 710,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500\",\n              children: \"Add User\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 717,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 709,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 631,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 620,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 619,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 251,\n    columnNumber: 9\n  }, this);\n}\n_s(UserManagement, \"6+L35aFhh9VOtRW/+hq9Jiwui3s=\", false, function () {\n  return [useNavigate];\n});\n_c = UserManagement;\nexport default UserManagement;\nvar _c;\n$RefreshReg$(_c, \"UserManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "axios", "FaUsers", "FaUserPlus", "FaUserCheck", "FaUserClock", "FaEdit", "FaSearch", "FaEye", "FaUserSlash", "FaBan", "CustomModal", "jsxDEV", "_jsxDEV", "API_BASE_URL", "UserManagement", "_s", "navigate", "users", "setUsers", "teams", "setTeams", "error", "setError", "success", "setSuccess", "editingUserId", "setEditingUserId", "searchTerm", "setSearchTerm", "currentPage", "setCurrentPage", "usersPerPage", "stats", "setStats", "totalUsers", "activeUsers", "newUsers", "pendingUsers", "editingUser", "setEditingUser", "username", "full_name", "email", "favorite_team", "balance", "showAddUserModal", "setShowAddUserModal", "newUser", "setNewUser", "password", "modalState", "setModalState", "isOpen", "type", "title", "message", "onConfirm", "confirmText", "confirmButtonColor", "fetchUsers", "fetchTeams", "response", "get", "data", "userData", "now", "Date", "oneWeekAgo", "getTime", "length", "filter", "user", "last_login", "Math", "floor", "created_at", "status", "err", "console", "getTeamLogo", "teamName", "team", "find", "name", "logo", "getDefaultAvatar", "handleEdit", "user_id", "handleUpdate", "e", "preventDefault", "put", "setTimeout", "handleInputChange", "value", "target", "prev", "handleNewUserInputChange", "handleAddUser", "post", "handleSuspendUser", "performSuspendUser", "action", "handleBanUser", "window", "confirm", "handleSearch", "filteredUsers", "toLowerCase", "includes", "indexOfLastUser", "indexOfFirstUser", "currentUsers", "slice", "totalPages", "ceil", "paginate", "pageNumber", "pageNumbers", "i", "push", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "role", "onClick", "placeholder", "onChange", "scope", "map", "index", "src", "alt", "onError", "disabled", "xmlns", "viewBox", "fill", "fillRule", "d", "clipRule", "number", "onSubmit", "required", "id", "min", "_c", "$RefreshReg$"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/pages/UserManagement.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport axios from 'axios';\r\nimport { FaUsers, FaUserPlus, FaUserCheck, FaUserClock, FaEdit, FaSearch, FaEye, FaUserSlash, FaBan } from 'react-icons/fa';\r\nimport CustomModal from '../components/CustomModal';\r\n\r\nconst API_BASE_URL = '/backend';\r\n\r\nfunction UserManagement() {\r\n    const navigate = useNavigate();\r\n    const [users, setUsers] = useState([]);\r\n    const [teams, setTeams] = useState([]);\r\n    const [error, setError] = useState('');\r\n    const [success, setSuccess] = useState('');\r\n    const [editingUserId, setEditingUserId] = useState(null);\r\n    const [searchTerm, setSearchTerm] = useState('');\r\n    const [currentPage, setCurrentPage] = useState(1);\r\n    const [usersPerPage] = useState(10);\r\n    const [stats, setStats] = useState({\r\n        totalUsers: 0,\r\n        activeUsers: 0,\r\n        newUsers: 0,\r\n        pendingUsers: 0\r\n    });\r\n    const [editingUser, setEditingUser] = useState({\r\n        username: '',\r\n        full_name: '',\r\n        email: '',\r\n        favorite_team: '',\r\n        balance: 0\r\n    });\r\n    const [showAddUserModal, setShowAddUserModal] = useState(false);\r\n    const [newUser, setNewUser] = useState({\r\n        username: '',\r\n        full_name: '',\r\n        email: '',\r\n        password: '',\r\n        favorite_team: '',\r\n        balance: 0\r\n    });\r\n\r\n    // Modal states\r\n    const [modalState, setModalState] = useState({\r\n        isOpen: false,\r\n        type: 'confirm',\r\n        title: '',\r\n        message: '',\r\n        onConfirm: null,\r\n        confirmText: 'Confirm',\r\n        confirmButtonColor: 'blue'\r\n    });\r\n\r\n    useEffect(() => {\r\n        fetchUsers();\r\n        fetchTeams();\r\n    }, []);\r\n\r\n    const fetchUsers = async () => {\r\n        try {\r\n            const response = await axios.get(`${API_BASE_URL}/handlers/user_management.php`);\r\n            if (response.data.success) {\r\n                const userData = response.data.data || [];\r\n                setUsers(userData);\r\n\r\n                // Calculate stats\r\n                const now = new Date();\r\n                const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);\r\n\r\n                // For demo purposes, we'll simulate some stats\r\n                const totalUsers = userData.length;\r\n                const activeUsers = userData.filter(user => user.last_login && new Date(user.last_login) > oneWeekAgo).length || Math.floor(totalUsers * 0.7);\r\n                const newUsers = userData.filter(user => user.created_at && new Date(user.created_at) > oneWeekAgo).length || Math.floor(totalUsers * 0.2);\r\n                const pendingUsers = userData.filter(user => user.status === 'pending').length || Math.floor(totalUsers * 0.1);\r\n\r\n                setStats({\r\n                    totalUsers,\r\n                    activeUsers,\r\n                    newUsers,\r\n                    pendingUsers\r\n                });\r\n            } else {\r\n                setError(response.data.message || 'Failed to fetch users');\r\n            }\r\n        } catch (err) {\r\n            setError('Failed to fetch users. Please check your network connection and try again.');\r\n            console.error('Error fetching users:', err);\r\n        }\r\n    };\r\n\r\n    const fetchTeams = async () => {\r\n        try {\r\n            const response = await axios.get(`${API_BASE_URL}/handlers/team_management.php`);\r\n            setTeams(response.data.data || []);\r\n        } catch (err) {\r\n            setError('Failed to fetch teams');\r\n        }\r\n    };\r\n\r\n    const getTeamLogo = (teamName) => {\r\n        const team = teams.find(team => team.name === teamName);\r\n        return team ? `${API_BASE_URL}/${team.logo}` : null;\r\n    };\r\n\r\n    const getDefaultAvatar = () => {\r\n        return \"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='40' height='40' viewBox='0 0 40 40'%3E%3Ccircle cx='20' cy='20' r='20' fill='%23e5e7eb'/%3E%3Cpath d='M20 20c3.3 0 6-2.7 6-6s-2.7-6-6-6-6 2.7-6 6 2.7 6 6 6zm0 3c-4 0-12 2-12 6v3h24v-3c0-4-8-6-12-6z' fill='%23374151'/%3E%3C/svg%3E\";\r\n    };\r\n\r\n    const handleEdit = (user) => {\r\n        setEditingUserId(user.user_id);\r\n        setEditingUser(user);\r\n    };\r\n\r\n    const handleUpdate = async (e) => {\r\n        e.preventDefault();\r\n        try {\r\n            const response = await axios.put(`${API_BASE_URL}/handlers/user_management.php?id=${editingUserId}`, editingUser);\r\n            if (response.data.success) {\r\n                setSuccess('User updated successfully!');\r\n                fetchUsers();\r\n                setEditingUserId(null);\r\n                setTimeout(() => setSuccess(''), 3000);\r\n            } else {\r\n                setError(response.data.message || 'Failed to update user');\r\n                setTimeout(() => setError(''), 3000);\r\n            }\r\n        } catch (err) {\r\n            setError('Failed to update user');\r\n        }\r\n    };\r\n\r\n    const handleInputChange = (e) => {\r\n        const { name, value } = e.target;\r\n        setEditingUser(prev => ({ ...prev, [name]: value }));\r\n    };\r\n\r\n    const handleNewUserInputChange = (e) => {\r\n        const { name, value } = e.target;\r\n        setNewUser(prev => ({ ...prev, [name]: value }));\r\n    };\r\n\r\n    const handleAddUser = async (e) => {\r\n        e.preventDefault();\r\n        try {\r\n            const response = await axios.post(`${API_BASE_URL}/handlers/add_user.php`, newUser);\r\n            if (response.data.success) {\r\n                setSuccess('User added successfully!');\r\n                fetchUsers();\r\n                setShowAddUserModal(false);\r\n                setNewUser({\r\n                    username: '',\r\n                    full_name: '',\r\n                    email: '',\r\n                    password: '',\r\n                    favorite_team: '',\r\n                    balance: 0\r\n                });\r\n                // Clear success message after 3 seconds\r\n                setTimeout(() => setSuccess(''), 3000);\r\n            } else {\r\n                setError(response.data.message || 'Failed to add user');\r\n                setTimeout(() => setError(''), 3000);\r\n            }\r\n        } catch (err) {\r\n            setError('Failed to add user');\r\n            setTimeout(() => setError(''), 3000);\r\n        }\r\n    };\r\n\r\n    const handleSuspendUser = (user) => {\r\n        setModalState({\r\n            isOpen: true,\r\n            type: 'confirm',\r\n            title: 'Suspend User',\r\n            message: `Are you sure you want to suspend user \"${user.username}\"? This will prevent them from accessing their account.`,\r\n            confirmText: 'Suspend User',\r\n            confirmButtonColor: 'yellow',\r\n            onConfirm: () => performSuspendUser(user)\r\n        });\r\n    };\r\n\r\n    const performSuspendUser = async (user) => {\r\n        try {\r\n            const response = await axios.post(`${API_BASE_URL}/handlers/suspend_user.php`, {\r\n                user_id: user.user_id,\r\n                action: 'suspend'\r\n            });\r\n            if (response.data.success) {\r\n                setSuccess(`User \"${user.username}\" suspended successfully!`);\r\n                fetchUsers();\r\n                setTimeout(() => setSuccess(''), 3000);\r\n            } else {\r\n                setError(response.data.message || 'Failed to suspend user');\r\n                setTimeout(() => setError(''), 3000);\r\n            }\r\n        } catch (err) {\r\n            setError('Failed to suspend user');\r\n            setTimeout(() => setError(''), 3000);\r\n        }\r\n    };\r\n\r\n    const handleBanUser = async (user) => {\r\n        if (window.confirm(`Are you sure you want to ban user \"${user.username}\"? This action cannot be undone.`)) {\r\n            try {\r\n                const response = await axios.post(`${API_BASE_URL}/handlers/ban_user.php`, {\r\n                    user_id: user.user_id,\r\n                    action: 'ban'\r\n                });\r\n                if (response.data.success) {\r\n                    setSuccess(`User \"${user.username}\" banned successfully!`);\r\n                    fetchUsers();\r\n                    setTimeout(() => setSuccess(''), 3000);\r\n                } else {\r\n                    setError(response.data.message || 'Failed to ban user');\r\n                    setTimeout(() => setError(''), 3000);\r\n                }\r\n            } catch (err) {\r\n                setError('Failed to ban user');\r\n                setTimeout(() => setError(''), 3000);\r\n            }\r\n        }\r\n    };\r\n\r\n    // Search functionality\r\n    const handleSearch = (e) => {\r\n        setSearchTerm(e.target.value);\r\n        setCurrentPage(1); // Reset to first page when searching\r\n    };\r\n\r\n    // Filter users based on search term\r\n    const filteredUsers = users.filter(user =>\r\n        user.username.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n        user.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n        user.email.toLowerCase().includes(searchTerm.toLowerCase())\r\n    );\r\n\r\n    // Pagination\r\n    const indexOfLastUser = currentPage * usersPerPage;\r\n    const indexOfFirstUser = indexOfLastUser - usersPerPage;\r\n    const currentUsers = filteredUsers.slice(indexOfFirstUser, indexOfLastUser);\r\n    const totalPages = Math.ceil(filteredUsers.length / usersPerPage);\r\n\r\n    const paginate = (pageNumber) => setCurrentPage(pageNumber);\r\n\r\n    // Generate page numbers\r\n    const pageNumbers = [];\r\n    for (let i = 1; i <= totalPages; i++) {\r\n        pageNumbers.push(i);\r\n    }\r\n\r\n    return (\r\n        <div className=\"p-6 bg-gray-50 min-h-screen\">\r\n            {/* Page Header */}\r\n            <div className=\"mb-8\">\r\n                <h1 className=\"text-2xl font-bold text-gray-800\">User Management</h1>\r\n                <p className=\"text-gray-600\">Manage all users in the system</p>\r\n            </div>\r\n\r\n            {/* Notification Messages */}\r\n            {error && (\r\n                <div className=\"mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative\" role=\"alert\">\r\n                    <span className=\"block sm:inline\">{error}</span>\r\n                </div>\r\n            )}\r\n            {success && (\r\n                <div className=\"mb-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative\" role=\"alert\">\r\n                    <span className=\"block sm:inline\">{success}</span>\r\n                </div>\r\n            )}\r\n\r\n            {/* Stats Cards */}\r\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\r\n                {/* Total Users */}\r\n                <div className=\"bg-white rounded-lg shadow-sm p-6 flex items-center\">\r\n                    <div className=\"rounded-full bg-blue-100 p-3 mr-4\">\r\n                        <FaUsers className=\"text-blue-500 text-xl\" />\r\n                    </div>\r\n                    <div>\r\n                        <p className=\"text-sm text-gray-500 uppercase tracking-wider\">Total Users</p>\r\n                        <h3 className=\"text-2xl font-bold text-gray-800\">{stats.totalUsers}</h3>\r\n                    </div>\r\n                </div>\r\n\r\n                {/* Active Users */}\r\n                <div className=\"bg-white rounded-lg shadow-sm p-6 flex items-center\">\r\n                    <div className=\"rounded-full bg-green-100 p-3 mr-4\">\r\n                        <FaUserCheck className=\"text-green-500 text-xl\" />\r\n                    </div>\r\n                    <div>\r\n                        <p className=\"text-sm text-gray-500 uppercase tracking-wider\">Active Users</p>\r\n                        <h3 className=\"text-2xl font-bold text-gray-800\">{stats.activeUsers}</h3>\r\n                    </div>\r\n                </div>\r\n\r\n                {/* New Users */}\r\n                <div className=\"bg-white rounded-lg shadow-sm p-6 flex items-center\">\r\n                    <div className=\"rounded-full bg-yellow-100 p-3 mr-4\">\r\n                        <FaUserPlus className=\"text-yellow-500 text-xl\" />\r\n                    </div>\r\n                    <div>\r\n                        <p className=\"text-sm text-gray-500 uppercase tracking-wider\">New Users (7d)</p>\r\n                        <h3 className=\"text-2xl font-bold text-gray-800\">{stats.newUsers}</h3>\r\n                    </div>\r\n                </div>\r\n\r\n                {/* Pending Users */}\r\n                <div className=\"bg-white rounded-lg shadow-sm p-6 flex items-center\">\r\n                    <div className=\"rounded-full bg-purple-100 p-3 mr-4\">\r\n                        <FaUserClock className=\"text-purple-500 text-xl\" />\r\n                    </div>\r\n                    <div>\r\n                        <p className=\"text-sm text-gray-500 uppercase tracking-wider\">Pending Users</p>\r\n                        <h3 className=\"text-2xl font-bold text-gray-800\">{stats.pendingUsers}</h3>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            {/* Search and Filter */}\r\n            <div className=\"bg-white rounded-lg shadow-sm p-6 mb-8\">\r\n                <div className=\"flex flex-col md:flex-row md:items-center md:justify-between mb-4\">\r\n                    <h2 className=\"text-lg font-semibold text-white bg-blue-600 px-4 py-2 rounded mb-4 md:mb-0\">User List</h2>\r\n                    <div className=\"flex flex-col md:flex-row md:items-center space-y-2 md:space-y-0 md:space-x-4\">\r\n                        <button\r\n                            onClick={() => setShowAddUserModal(true)}\r\n                            className=\"flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700\"\r\n                        >\r\n                            <FaUserPlus className=\"mr-2\" />\r\n                            Add User\r\n                        </button>\r\n                        <div className=\"relative w-full md:w-64\">\r\n                            <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n                                <FaSearch className=\"h-4 w-4 text-gray-400\" />\r\n                            </div>\r\n                            <input\r\n                                type=\"text\"\r\n                                className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm\"\r\n                                placeholder=\"Search users...\"\r\n                                value={searchTerm}\r\n                                onChange={handleSearch}\r\n                            />\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n\r\n                {/* Users Table */}\r\n                <div className=\"overflow-x-auto\">\r\n                    <table className=\"min-w-full divide-y divide-gray-200\">\r\n                        <thead className=\"bg-blue-600\">\r\n                            <tr>\r\n                                <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider\">\r\n                                    #\r\n                                </th>\r\n                                <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider\">\r\n                                    Avatar\r\n                                </th>\r\n                                <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider\">\r\n                                    Username\r\n                                </th>\r\n                                <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider\">\r\n                                    Full Name\r\n                                </th>\r\n                                <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider\">\r\n                                    Email\r\n                                </th>\r\n                                <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider\">\r\n                                    Favorite Team\r\n                                </th>\r\n                                <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider\">\r\n                                    Balance\r\n                                </th>\r\n                                <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider\">\r\n                                    Actions\r\n                                </th>\r\n                            </tr>\r\n                        </thead>\r\n                        <tbody className=\"bg-white divide-y divide-gray-200\">\r\n                            {currentUsers.map((user, index) => (\r\n                                <tr key={user.user_id} className=\"hover:bg-gray-50\">\r\n                                    <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\r\n                                        {indexOfFirstUser + index + 1}\r\n                                    </td>\r\n                                    <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                                        <div className=\"flex items-center\">\r\n                                            <img\r\n                                                src={getTeamLogo(user.favorite_team) || getDefaultAvatar()}\r\n                                                alt={user.favorite_team || 'Default Avatar'}\r\n                                                className=\"w-10 h-10 rounded-full object-contain border border-gray-200\"\r\n                                                onError={(e) => {\r\n                                                    e.target.src = getDefaultAvatar();\r\n                                                }}\r\n                                            />\r\n                                        </div>\r\n                                    </td>\r\n                                    <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                                        <div className=\"text-sm font-medium text-gray-900\">{user.username}</div>\r\n                                    </td>\r\n                                    <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                                        <div className=\"text-sm text-gray-500\">{user.full_name}</div>\r\n                                    </td>\r\n                                    <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                                        <div className=\"text-sm text-gray-500\">{user.email}</div>\r\n                                    </td>\r\n                                    <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                                        <div className=\"flex items-center space-x-2\">\r\n                                            {user.favorite_team && getTeamLogo(user.favorite_team) && (\r\n                                                <img\r\n                                                    src={getTeamLogo(user.favorite_team)}\r\n                                                    alt={user.favorite_team}\r\n                                                    className=\"w-5 h-5 rounded-full object-contain\"\r\n                                                />\r\n                                            )}\r\n                                            <div className=\"text-sm text-gray-500\">{user.favorite_team || 'No team selected'}</div>\r\n                                        </div>\r\n                                    </td>\r\n                                    <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                                        <div className=\"text-sm font-medium text-gray-900\">{user.balance} FanCoins</div>\r\n                                    </td>\r\n                                    <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\r\n                                        <div className=\"flex space-x-1\">\r\n                                            <button\r\n                                                onClick={() => navigate(`/admin/users/${user.user_id}`)}\r\n                                                className=\"text-green-600 hover:text-green-900 p-1\"\r\n                                                title=\"View Details\"\r\n                                            >\r\n                                                <FaEye />\r\n                                            </button>\r\n                                            <button\r\n                                                onClick={() => handleEdit(user)}\r\n                                                className=\"text-blue-600 hover:text-blue-900 p-1\"\r\n                                                title=\"Edit User\"\r\n                                            >\r\n                                                <FaEdit />\r\n                                            </button>\r\n                                            <button\r\n                                                onClick={() => handleSuspendUser(user)}\r\n                                                className=\"text-yellow-600 hover:text-yellow-900 p-1\"\r\n                                                title=\"Suspend User\"\r\n                                            >\r\n                                                <FaUserSlash />\r\n                                            </button>\r\n                                            <button\r\n                                                onClick={() => handleBanUser(user)}\r\n                                                className=\"text-red-600 hover:text-red-900 p-1\"\r\n                                                title=\"Ban User\"\r\n                                            >\r\n                                                <FaBan />\r\n                                            </button>\r\n                                        </div>\r\n                                    </td>\r\n                                </tr>\r\n                            ))}\r\n                        </tbody>\r\n                    </table>\r\n                </div>\r\n\r\n                {/* Pagination */}\r\n                {totalPages > 1 && (\r\n                    <div className=\"flex items-center justify-between border-t border-gray-200 px-4 py-3 sm:px-6 mt-4\">\r\n                        <div className=\"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between\">\r\n                            <div>\r\n                                <p className=\"text-sm text-gray-700\">\r\n                                    Showing <span className=\"font-medium\">{indexOfFirstUser + 1}</span> to{' '}\r\n                                    <span className=\"font-medium\">\r\n                                        {indexOfLastUser > filteredUsers.length ? filteredUsers.length : indexOfLastUser}\r\n                                    </span>{' '}\r\n                                    of <span className=\"font-medium\">{filteredUsers.length}</span> results\r\n                                </p>\r\n                            </div>\r\n                            <div>\r\n                                <nav className=\"relative z-0 inline-flex rounded-md shadow-sm -space-x-px\" aria-label=\"Pagination\">\r\n                                    <button\r\n                                        onClick={() => paginate(currentPage > 1 ? currentPage - 1 : 1)}\r\n                                        disabled={currentPage === 1}\r\n                                        className={`relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium ${\r\n                                            currentPage === 1 ? 'text-gray-300 cursor-not-allowed' : 'text-gray-500 hover:bg-gray-50'\r\n                                        }`}\r\n                                    >\r\n                                        <span className=\"sr-only\">Previous</span>\r\n                                        <svg className=\"h-5 w-5\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 20 20\" fill=\"currentColor\" aria-hidden=\"true\">\r\n                                            <path fillRule=\"evenodd\" d=\"M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\r\n                                        </svg>\r\n                                    </button>\r\n\r\n                                    {pageNumbers.map(number => (\r\n                                        <button\r\n                                            key={number}\r\n                                            onClick={() => paginate(number)}\r\n                                            className={`relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium ${\r\n                                                currentPage === number\r\n                                                    ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'\r\n                                                    : 'text-gray-500 hover:bg-gray-50'\r\n                                            }`}\r\n                                        >\r\n                                            {number}\r\n                                        </button>\r\n                                    ))}\r\n\r\n                                    <button\r\n                                        onClick={() => paginate(currentPage < totalPages ? currentPage + 1 : totalPages)}\r\n                                        disabled={currentPage === totalPages}\r\n                                        className={`relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium ${\r\n                                            currentPage === totalPages ? 'text-gray-300 cursor-not-allowed' : 'text-gray-500 hover:bg-gray-50'\r\n                                        }`}\r\n                                    >\r\n                                        <span className=\"sr-only\">Next</span>\r\n                                        <svg className=\"h-5 w-5\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 20 20\" fill=\"currentColor\" aria-hidden=\"true\">\r\n                                            <path fillRule=\"evenodd\" d=\"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z\" clipRule=\"evenodd\" />\r\n                                        </svg>\r\n                                    </button>\r\n                                </nav>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                )}\r\n            </div>\r\n\r\n            {/* Edit User Modal */}\r\n            {editingUserId && (\r\n                <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\r\n                    <div className=\"bg-white rounded-lg shadow-xl w-full max-w-md\">\r\n                        <div className=\"flex justify-between items-center p-4 border-b\">\r\n                            <h3 className=\"text-lg font-semibold text-gray-800\">Edit User</h3>\r\n                            <button\r\n                                className=\"text-gray-500 hover:text-gray-700 text-2xl focus:outline-none\"\r\n                                onClick={() => setEditingUserId(null)}\r\n                            >\r\n                                ×\r\n                            </button>\r\n                        </div>\r\n\r\n                        <form onSubmit={handleUpdate} className=\"p-6\">\r\n                            <div className=\"space-y-4\">\r\n                                <div>\r\n                                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Username</label>\r\n                                    <input\r\n                                        type=\"text\"\r\n                                        name=\"username\"\r\n                                        value={editingUser.username}\r\n                                        onChange={handleInputChange}\r\n                                        className=\"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\r\n                                        required\r\n                                    />\r\n                                </div>\r\n\r\n                                <div>\r\n                                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Full Name</label>\r\n                                    <input\r\n                                        type=\"text\"\r\n                                        name=\"full_name\"\r\n                                        value={editingUser.full_name}\r\n                                        onChange={handleInputChange}\r\n                                        className=\"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\r\n                                        required\r\n                                    />\r\n                                </div>\r\n\r\n                                <div>\r\n                                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Email</label>\r\n                                    <input\r\n                                        type=\"email\"\r\n                                        name=\"email\"\r\n                                        value={editingUser.email}\r\n                                        onChange={handleInputChange}\r\n                                        className=\"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\r\n                                        required\r\n                                    />\r\n                                </div>\r\n\r\n                                <div>\r\n                                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Favorite Team</label>\r\n                                    <select\r\n                                        name=\"favorite_team\"\r\n                                        value={editingUser.favorite_team}\r\n                                        onChange={handleInputChange}\r\n                                        className=\"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\r\n                                        required\r\n                                    >\r\n                                        <option value=\"\">Select Favorite Team</option>\r\n                                        {teams.map(team => (\r\n                                            <option key={team.id} value={team.name}>{team.name}</option>\r\n                                        ))}\r\n                                    </select>\r\n                                </div>\r\n\r\n                                <div>\r\n                                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Balance (FanCoins)</label>\r\n                                    <input\r\n                                        type=\"number\"\r\n                                        name=\"balance\"\r\n                                        value={editingUser.balance}\r\n                                        onChange={handleInputChange}\r\n                                        className=\"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\r\n                                        required\r\n                                    />\r\n                                </div>\r\n                            </div>\r\n\r\n                            <div className=\"mt-6 flex justify-end space-x-3\">\r\n                                <button\r\n                                    type=\"button\"\r\n                                    onClick={() => setEditingUserId(null)}\r\n                                    className=\"px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\r\n                                >\r\n                                    Cancel\r\n                                </button>\r\n                                <button\r\n                                    type=\"submit\"\r\n                                    className=\"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\r\n                                >\r\n                                    Update User\r\n                                </button>\r\n                            </div>\r\n                        </form>\r\n                    </div>\r\n                </div>\r\n            )}\r\n\r\n            {/* Add User Modal */}\r\n            {showAddUserModal && (\r\n                <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\r\n                    <div className=\"bg-white rounded-lg shadow-xl w-full max-w-md\">\r\n                        <div className=\"flex justify-between items-center p-4 border-b\">\r\n                            <h3 className=\"text-lg font-semibold text-gray-800\">Add New User</h3>\r\n                            <button\r\n                                className=\"text-gray-500 hover:text-gray-700 text-2xl focus:outline-none\"\r\n                                onClick={() => setShowAddUserModal(false)}\r\n                            >\r\n                                ×\r\n                            </button>\r\n                        </div>\r\n\r\n                        <form onSubmit={handleAddUser} className=\"p-6\">\r\n                            <div className=\"space-y-4\">\r\n                                <div>\r\n                                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Username</label>\r\n                                    <input\r\n                                        type=\"text\"\r\n                                        name=\"username\"\r\n                                        value={newUser.username}\r\n                                        onChange={handleNewUserInputChange}\r\n                                        className=\"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm\"\r\n                                        required\r\n                                    />\r\n                                </div>\r\n\r\n                                <div>\r\n                                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Full Name</label>\r\n                                    <input\r\n                                        type=\"text\"\r\n                                        name=\"full_name\"\r\n                                        value={newUser.full_name}\r\n                                        onChange={handleNewUserInputChange}\r\n                                        className=\"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm\"\r\n                                        required\r\n                                    />\r\n                                </div>\r\n\r\n                                <div>\r\n                                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Email</label>\r\n                                    <input\r\n                                        type=\"email\"\r\n                                        name=\"email\"\r\n                                        value={newUser.email}\r\n                                        onChange={handleNewUserInputChange}\r\n                                        className=\"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm\"\r\n                                        required\r\n                                    />\r\n                                </div>\r\n\r\n                                <div>\r\n                                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Password</label>\r\n                                    <input\r\n                                        type=\"password\"\r\n                                        name=\"password\"\r\n                                        value={newUser.password}\r\n                                        onChange={handleNewUserInputChange}\r\n                                        className=\"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm\"\r\n                                        required\r\n                                    />\r\n                                </div>\r\n\r\n                                <div>\r\n                                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Favorite Team</label>\r\n                                    <select\r\n                                        name=\"favorite_team\"\r\n                                        value={newUser.favorite_team}\r\n                                        onChange={handleNewUserInputChange}\r\n                                        className=\"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm\"\r\n                                    >\r\n                                        <option value=\"\">Select Favorite Team</option>\r\n                                        {teams.map(team => (\r\n                                            <option key={team.id} value={team.name}>{team.name}</option>\r\n                                        ))}\r\n                                    </select>\r\n                                </div>\r\n\r\n                                <div>\r\n                                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Initial Balance (FanCoins)</label>\r\n                                    <input\r\n                                        type=\"number\"\r\n                                        name=\"balance\"\r\n                                        value={newUser.balance}\r\n                                        onChange={handleNewUserInputChange}\r\n                                        className=\"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm\"\r\n                                        min=\"0\"\r\n                                    />\r\n                                </div>\r\n                            </div>\r\n\r\n                            <div className=\"mt-6 flex justify-end space-x-3\">\r\n                                <button\r\n                                    type=\"button\"\r\n                                    onClick={() => setShowAddUserModal(false)}\r\n                                    className=\"px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500\"\r\n                                >\r\n                                    Cancel\r\n                                </button>\r\n                                <button\r\n                                    type=\"submit\"\r\n                                    className=\"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500\"\r\n                                >\r\n                                    Add User\r\n                                </button>\r\n                            </div>\r\n                        </form>\r\n                    </div>\r\n                </div>\r\n            )}\r\n        </div>\r\n    );\r\n}\r\nexport default UserManagement;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,EAAEC,UAAU,EAAEC,WAAW,EAAEC,WAAW,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,WAAW,EAAEC,KAAK,QAAQ,gBAAgB;AAC3H,OAAOC,WAAW,MAAM,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpD,MAAMC,YAAY,GAAG,UAAU;AAE/B,SAASC,cAAcA,CAAA,EAAG;EAAAC,EAAA;EACtB,MAAMC,QAAQ,GAAGjB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACkB,KAAK,EAAEC,QAAQ,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACsB,KAAK,EAAEC,QAAQ,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACwB,KAAK,EAAEC,QAAQ,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC0B,OAAO,EAAEC,UAAU,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC4B,aAAa,EAAEC,gBAAgB,CAAC,GAAG7B,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAAC8B,UAAU,EAAEC,aAAa,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACgC,WAAW,EAAEC,cAAc,CAAC,GAAGjC,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACkC,YAAY,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EACnC,MAAM,CAACmC,KAAK,EAAEC,QAAQ,CAAC,GAAGpC,QAAQ,CAAC;IAC/BqC,UAAU,EAAE,CAAC;IACbC,WAAW,EAAE,CAAC;IACdC,QAAQ,EAAE,CAAC;IACXC,YAAY,EAAE;EAClB,CAAC,CAAC;EACF,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG1C,QAAQ,CAAC;IAC3C2C,QAAQ,EAAE,EAAE;IACZC,SAAS,EAAE,EAAE;IACbC,KAAK,EAAE,EAAE;IACTC,aAAa,EAAE,EAAE;IACjBC,OAAO,EAAE;EACb,CAAC,CAAC;EACF,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjD,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACkD,OAAO,EAAEC,UAAU,CAAC,GAAGnD,QAAQ,CAAC;IACnC2C,QAAQ,EAAE,EAAE;IACZC,SAAS,EAAE,EAAE;IACbC,KAAK,EAAE,EAAE;IACTO,QAAQ,EAAE,EAAE;IACZN,aAAa,EAAE,EAAE;IACjBC,OAAO,EAAE;EACb,CAAC,CAAC;;EAEF;EACA,MAAM,CAACM,UAAU,EAAEC,aAAa,CAAC,GAAGtD,QAAQ,CAAC;IACzCuD,MAAM,EAAE,KAAK;IACbC,IAAI,EAAE,SAAS;IACfC,KAAK,EAAE,EAAE;IACTC,OAAO,EAAE,EAAE;IACXC,SAAS,EAAE,IAAI;IACfC,WAAW,EAAE,SAAS;IACtBC,kBAAkB,EAAE;EACxB,CAAC,CAAC;EAEF5D,SAAS,CAAC,MAAM;IACZ6D,UAAU,CAAC,CAAC;IACZC,UAAU,CAAC,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMD,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI;MACA,MAAME,QAAQ,GAAG,MAAM7D,KAAK,CAAC8D,GAAG,CAAC,GAAGjD,YAAY,+BAA+B,CAAC;MAChF,IAAIgD,QAAQ,CAACE,IAAI,CAACxC,OAAO,EAAE;QACvB,MAAMyC,QAAQ,GAAGH,QAAQ,CAACE,IAAI,CAACA,IAAI,IAAI,EAAE;QACzC7C,QAAQ,CAAC8C,QAAQ,CAAC;;QAElB;QACA,MAAMC,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;QACtB,MAAMC,UAAU,GAAG,IAAID,IAAI,CAACD,GAAG,CAACG,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;;QAEpE;QACA,MAAMlC,UAAU,GAAG8B,QAAQ,CAACK,MAAM;QAClC,MAAMlC,WAAW,GAAG6B,QAAQ,CAACM,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,UAAU,IAAI,IAAIN,IAAI,CAACK,IAAI,CAACC,UAAU,CAAC,GAAGL,UAAU,CAAC,CAACE,MAAM,IAAII,IAAI,CAACC,KAAK,CAACxC,UAAU,GAAG,GAAG,CAAC;QAC7I,MAAME,QAAQ,GAAG4B,QAAQ,CAACM,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACI,UAAU,IAAI,IAAIT,IAAI,CAACK,IAAI,CAACI,UAAU,CAAC,GAAGR,UAAU,CAAC,CAACE,MAAM,IAAII,IAAI,CAACC,KAAK,CAACxC,UAAU,GAAG,GAAG,CAAC;QAC1I,MAAMG,YAAY,GAAG2B,QAAQ,CAACM,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACK,MAAM,KAAK,SAAS,CAAC,CAACP,MAAM,IAAII,IAAI,CAACC,KAAK,CAACxC,UAAU,GAAG,GAAG,CAAC;QAE9GD,QAAQ,CAAC;UACLC,UAAU;UACVC,WAAW;UACXC,QAAQ;UACRC;QACJ,CAAC,CAAC;MACN,CAAC,MAAM;QACHf,QAAQ,CAACuC,QAAQ,CAACE,IAAI,CAACR,OAAO,IAAI,uBAAuB,CAAC;MAC9D;IACJ,CAAC,CAAC,OAAOsB,GAAG,EAAE;MACVvD,QAAQ,CAAC,4EAA4E,CAAC;MACtFwD,OAAO,CAACzD,KAAK,CAAC,uBAAuB,EAAEwD,GAAG,CAAC;IAC/C;EACJ,CAAC;EAED,MAAMjB,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI;MACA,MAAMC,QAAQ,GAAG,MAAM7D,KAAK,CAAC8D,GAAG,CAAC,GAAGjD,YAAY,+BAA+B,CAAC;MAChFO,QAAQ,CAACyC,QAAQ,CAACE,IAAI,CAACA,IAAI,IAAI,EAAE,CAAC;IACtC,CAAC,CAAC,OAAOc,GAAG,EAAE;MACVvD,QAAQ,CAAC,uBAAuB,CAAC;IACrC;EACJ,CAAC;EAED,MAAMyD,WAAW,GAAIC,QAAQ,IAAK;IAC9B,MAAMC,IAAI,GAAG9D,KAAK,CAAC+D,IAAI,CAACD,IAAI,IAAIA,IAAI,CAACE,IAAI,KAAKH,QAAQ,CAAC;IACvD,OAAOC,IAAI,GAAG,GAAGpE,YAAY,IAAIoE,IAAI,CAACG,IAAI,EAAE,GAAG,IAAI;EACvD,CAAC;EAED,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC3B,OAAO,2SAA2S;EACtT,CAAC;EAED,MAAMC,UAAU,GAAIf,IAAI,IAAK;IACzB7C,gBAAgB,CAAC6C,IAAI,CAACgB,OAAO,CAAC;IAC9BhD,cAAc,CAACgC,IAAI,CAAC;EACxB,CAAC;EAED,MAAMiB,YAAY,GAAG,MAAOC,CAAC,IAAK;IAC9BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAI;MACA,MAAM7B,QAAQ,GAAG,MAAM7D,KAAK,CAAC2F,GAAG,CAAC,GAAG9E,YAAY,oCAAoCY,aAAa,EAAE,EAAEa,WAAW,CAAC;MACjH,IAAIuB,QAAQ,CAACE,IAAI,CAACxC,OAAO,EAAE;QACvBC,UAAU,CAAC,4BAA4B,CAAC;QACxCmC,UAAU,CAAC,CAAC;QACZjC,gBAAgB,CAAC,IAAI,CAAC;QACtBkE,UAAU,CAAC,MAAMpE,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;MAC1C,CAAC,MAAM;QACHF,QAAQ,CAACuC,QAAQ,CAACE,IAAI,CAACR,OAAO,IAAI,uBAAuB,CAAC;QAC1DqC,UAAU,CAAC,MAAMtE,QAAQ,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;MACxC;IACJ,CAAC,CAAC,OAAOuD,GAAG,EAAE;MACVvD,QAAQ,CAAC,uBAAuB,CAAC;IACrC;EACJ,CAAC;EAED,MAAMuE,iBAAiB,GAAIJ,CAAC,IAAK;IAC7B,MAAM;MAAEN,IAAI;MAAEW;IAAM,CAAC,GAAGL,CAAC,CAACM,MAAM;IAChCxD,cAAc,CAACyD,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACb,IAAI,GAAGW;IAAM,CAAC,CAAC,CAAC;EACxD,CAAC;EAED,MAAMG,wBAAwB,GAAIR,CAAC,IAAK;IACpC,MAAM;MAAEN,IAAI;MAAEW;IAAM,CAAC,GAAGL,CAAC,CAACM,MAAM;IAChC/C,UAAU,CAACgD,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACb,IAAI,GAAGW;IAAM,CAAC,CAAC,CAAC;EACpD,CAAC;EAED,MAAMI,aAAa,GAAG,MAAOT,CAAC,IAAK;IAC/BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAI;MACA,MAAM7B,QAAQ,GAAG,MAAM7D,KAAK,CAACmG,IAAI,CAAC,GAAGtF,YAAY,wBAAwB,EAAEkC,OAAO,CAAC;MACnF,IAAIc,QAAQ,CAACE,IAAI,CAACxC,OAAO,EAAE;QACvBC,UAAU,CAAC,0BAA0B,CAAC;QACtCmC,UAAU,CAAC,CAAC;QACZb,mBAAmB,CAAC,KAAK,CAAC;QAC1BE,UAAU,CAAC;UACPR,QAAQ,EAAE,EAAE;UACZC,SAAS,EAAE,EAAE;UACbC,KAAK,EAAE,EAAE;UACTO,QAAQ,EAAE,EAAE;UACZN,aAAa,EAAE,EAAE;UACjBC,OAAO,EAAE;QACb,CAAC,CAAC;QACF;QACAgD,UAAU,CAAC,MAAMpE,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;MAC1C,CAAC,MAAM;QACHF,QAAQ,CAACuC,QAAQ,CAACE,IAAI,CAACR,OAAO,IAAI,oBAAoB,CAAC;QACvDqC,UAAU,CAAC,MAAMtE,QAAQ,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;MACxC;IACJ,CAAC,CAAC,OAAOuD,GAAG,EAAE;MACVvD,QAAQ,CAAC,oBAAoB,CAAC;MAC9BsE,UAAU,CAAC,MAAMtE,QAAQ,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;IACxC;EACJ,CAAC;EAED,MAAM8E,iBAAiB,GAAI7B,IAAI,IAAK;IAChCpB,aAAa,CAAC;MACVC,MAAM,EAAE,IAAI;MACZC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,cAAc;MACrBC,OAAO,EAAE,0CAA0CgB,IAAI,CAAC/B,QAAQ,yDAAyD;MACzHiB,WAAW,EAAE,cAAc;MAC3BC,kBAAkB,EAAE,QAAQ;MAC5BF,SAAS,EAAEA,CAAA,KAAM6C,kBAAkB,CAAC9B,IAAI;IAC5C,CAAC,CAAC;EACN,CAAC;EAED,MAAM8B,kBAAkB,GAAG,MAAO9B,IAAI,IAAK;IACvC,IAAI;MACA,MAAMV,QAAQ,GAAG,MAAM7D,KAAK,CAACmG,IAAI,CAAC,GAAGtF,YAAY,4BAA4B,EAAE;QAC3E0E,OAAO,EAAEhB,IAAI,CAACgB,OAAO;QACrBe,MAAM,EAAE;MACZ,CAAC,CAAC;MACF,IAAIzC,QAAQ,CAACE,IAAI,CAACxC,OAAO,EAAE;QACvBC,UAAU,CAAC,SAAS+C,IAAI,CAAC/B,QAAQ,2BAA2B,CAAC;QAC7DmB,UAAU,CAAC,CAAC;QACZiC,UAAU,CAAC,MAAMpE,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;MAC1C,CAAC,MAAM;QACHF,QAAQ,CAACuC,QAAQ,CAACE,IAAI,CAACR,OAAO,IAAI,wBAAwB,CAAC;QAC3DqC,UAAU,CAAC,MAAMtE,QAAQ,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;MACxC;IACJ,CAAC,CAAC,OAAOuD,GAAG,EAAE;MACVvD,QAAQ,CAAC,wBAAwB,CAAC;MAClCsE,UAAU,CAAC,MAAMtE,QAAQ,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;IACxC;EACJ,CAAC;EAED,MAAMiF,aAAa,GAAG,MAAOhC,IAAI,IAAK;IAClC,IAAIiC,MAAM,CAACC,OAAO,CAAC,sCAAsClC,IAAI,CAAC/B,QAAQ,kCAAkC,CAAC,EAAE;MACvG,IAAI;QACA,MAAMqB,QAAQ,GAAG,MAAM7D,KAAK,CAACmG,IAAI,CAAC,GAAGtF,YAAY,wBAAwB,EAAE;UACvE0E,OAAO,EAAEhB,IAAI,CAACgB,OAAO;UACrBe,MAAM,EAAE;QACZ,CAAC,CAAC;QACF,IAAIzC,QAAQ,CAACE,IAAI,CAACxC,OAAO,EAAE;UACvBC,UAAU,CAAC,SAAS+C,IAAI,CAAC/B,QAAQ,wBAAwB,CAAC;UAC1DmB,UAAU,CAAC,CAAC;UACZiC,UAAU,CAAC,MAAMpE,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;QAC1C,CAAC,MAAM;UACHF,QAAQ,CAACuC,QAAQ,CAACE,IAAI,CAACR,OAAO,IAAI,oBAAoB,CAAC;UACvDqC,UAAU,CAAC,MAAMtE,QAAQ,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;QACxC;MACJ,CAAC,CAAC,OAAOuD,GAAG,EAAE;QACVvD,QAAQ,CAAC,oBAAoB,CAAC;QAC9BsE,UAAU,CAAC,MAAMtE,QAAQ,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;MACxC;IACJ;EACJ,CAAC;;EAED;EACA,MAAMoF,YAAY,GAAIjB,CAAC,IAAK;IACxB7D,aAAa,CAAC6D,CAAC,CAACM,MAAM,CAACD,KAAK,CAAC;IAC7BhE,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;EACvB,CAAC;;EAED;EACA,MAAM6E,aAAa,GAAG1F,KAAK,CAACqD,MAAM,CAACC,IAAI,IACnCA,IAAI,CAAC/B,QAAQ,CAACoE,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAClF,UAAU,CAACiF,WAAW,CAAC,CAAC,CAAC,IAC9DrC,IAAI,CAAC9B,SAAS,CAACmE,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAClF,UAAU,CAACiF,WAAW,CAAC,CAAC,CAAC,IAC/DrC,IAAI,CAAC7B,KAAK,CAACkE,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAClF,UAAU,CAACiF,WAAW,CAAC,CAAC,CAC9D,CAAC;;EAED;EACA,MAAME,eAAe,GAAGjF,WAAW,GAAGE,YAAY;EAClD,MAAMgF,gBAAgB,GAAGD,eAAe,GAAG/E,YAAY;EACvD,MAAMiF,YAAY,GAAGL,aAAa,CAACM,KAAK,CAACF,gBAAgB,EAAED,eAAe,CAAC;EAC3E,MAAMI,UAAU,GAAGzC,IAAI,CAAC0C,IAAI,CAACR,aAAa,CAACtC,MAAM,GAAGtC,YAAY,CAAC;EAEjE,MAAMqF,QAAQ,GAAIC,UAAU,IAAKvF,cAAc,CAACuF,UAAU,CAAC;;EAE3D;EACA,MAAMC,WAAW,GAAG,EAAE;EACtB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIL,UAAU,EAAEK,CAAC,EAAE,EAAE;IAClCD,WAAW,CAACE,IAAI,CAACD,CAAC,CAAC;EACvB;EAEA,oBACI3G,OAAA;IAAK6G,SAAS,EAAC,6BAA6B;IAAAC,QAAA,gBAExC9G,OAAA;MAAK6G,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACjB9G,OAAA;QAAI6G,SAAS,EAAC,kCAAkC;QAAAC,QAAA,EAAC;MAAe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACrElH,OAAA;QAAG6G,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAA8B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9D,CAAC,EAGLzG,KAAK,iBACFT,OAAA;MAAK6G,SAAS,EAAC,+EAA+E;MAACM,IAAI,EAAC,OAAO;MAAAL,QAAA,eACvG9G,OAAA;QAAM6G,SAAS,EAAC,iBAAiB;QAAAC,QAAA,EAAErG;MAAK;QAAAsG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/C,CACR,EACAvG,OAAO,iBACJX,OAAA;MAAK6G,SAAS,EAAC,qFAAqF;MAACM,IAAI,EAAC,OAAO;MAAAL,QAAA,eAC7G9G,OAAA;QAAM6G,SAAS,EAAC,iBAAiB;QAAAC,QAAA,EAAEnG;MAAO;QAAAoG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjD,CACR,eAGDlH,OAAA;MAAK6G,SAAS,EAAC,2DAA2D;MAAAC,QAAA,gBAEtE9G,OAAA;QAAK6G,SAAS,EAAC,qDAAqD;QAAAC,QAAA,gBAChE9G,OAAA;UAAK6G,SAAS,EAAC,mCAAmC;UAAAC,QAAA,eAC9C9G,OAAA,CAACX,OAAO;YAACwH,SAAS,EAAC;UAAuB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC,eACNlH,OAAA;UAAA8G,QAAA,gBACI9G,OAAA;YAAG6G,SAAS,EAAC,gDAAgD;YAAAC,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC7ElH,OAAA;YAAI6G,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAE1F,KAAK,CAACE;UAAU;YAAAyF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGNlH,OAAA;QAAK6G,SAAS,EAAC,qDAAqD;QAAAC,QAAA,gBAChE9G,OAAA;UAAK6G,SAAS,EAAC,oCAAoC;UAAAC,QAAA,eAC/C9G,OAAA,CAACT,WAAW;YAACsH,SAAS,EAAC;UAAwB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD,CAAC,eACNlH,OAAA;UAAA8G,QAAA,gBACI9G,OAAA;YAAG6G,SAAS,EAAC,gDAAgD;YAAAC,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC9ElH,OAAA;YAAI6G,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAE1F,KAAK,CAACG;UAAW;YAAAwF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGNlH,OAAA;QAAK6G,SAAS,EAAC,qDAAqD;QAAAC,QAAA,gBAChE9G,OAAA;UAAK6G,SAAS,EAAC,qCAAqC;UAAAC,QAAA,eAChD9G,OAAA,CAACV,UAAU;YAACuH,SAAS,EAAC;UAAyB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD,CAAC,eACNlH,OAAA;UAAA8G,QAAA,gBACI9G,OAAA;YAAG6G,SAAS,EAAC,gDAAgD;YAAAC,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAChFlH,OAAA;YAAI6G,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAE1F,KAAK,CAACI;UAAQ;YAAAuF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGNlH,OAAA;QAAK6G,SAAS,EAAC,qDAAqD;QAAAC,QAAA,gBAChE9G,OAAA;UAAK6G,SAAS,EAAC,qCAAqC;UAAAC,QAAA,eAChD9G,OAAA,CAACR,WAAW;YAACqH,SAAS,EAAC;UAAyB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD,CAAC,eACNlH,OAAA;UAAA8G,QAAA,gBACI9G,OAAA;YAAG6G,SAAS,EAAC,gDAAgD;YAAAC,QAAA,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC/ElH,OAAA;YAAI6G,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAE1F,KAAK,CAACK;UAAY;YAAAsF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGNlH,OAAA;MAAK6G,SAAS,EAAC,wCAAwC;MAAAC,QAAA,gBACnD9G,OAAA;QAAK6G,SAAS,EAAC,mEAAmE;QAAAC,QAAA,gBAC9E9G,OAAA;UAAI6G,SAAS,EAAC,6EAA6E;UAAAC,QAAA,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1GlH,OAAA;UAAK6G,SAAS,EAAC,+EAA+E;UAAAC,QAAA,gBAC1F9G,OAAA;YACIoH,OAAO,EAAEA,CAAA,KAAMlF,mBAAmB,CAAC,IAAI,CAAE;YACzC2E,SAAS,EAAC,mFAAmF;YAAAC,QAAA,gBAE7F9G,OAAA,CAACV,UAAU;cAACuH,SAAS,EAAC;YAAM;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,YAEnC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTlH,OAAA;YAAK6G,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACpC9G,OAAA;cAAK6G,SAAS,EAAC,sEAAsE;cAAAC,QAAA,eACjF9G,OAAA,CAACN,QAAQ;gBAACmH,SAAS,EAAC;cAAuB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC,eACNlH,OAAA;cACIyC,IAAI,EAAC,MAAM;cACXoE,SAAS,EAAC,qJAAqJ;cAC/JQ,WAAW,EAAC,iBAAiB;cAC7BnC,KAAK,EAAEnE,UAAW;cAClBuG,QAAQ,EAAExB;YAAa;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGNlH,OAAA;QAAK6G,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC5B9G,OAAA;UAAO6G,SAAS,EAAC,qCAAqC;UAAAC,QAAA,gBAClD9G,OAAA;YAAO6G,SAAS,EAAC,aAAa;YAAAC,QAAA,eAC1B9G,OAAA;cAAA8G,QAAA,gBACI9G,OAAA;gBAAIuH,KAAK,EAAC,KAAK;gBAACV,SAAS,EAAC,6EAA6E;gBAAAC,QAAA,EAAC;cAExG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLlH,OAAA;gBAAIuH,KAAK,EAAC,KAAK;gBAACV,SAAS,EAAC,6EAA6E;gBAAAC,QAAA,EAAC;cAExG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLlH,OAAA;gBAAIuH,KAAK,EAAC,KAAK;gBAACV,SAAS,EAAC,6EAA6E;gBAAAC,QAAA,EAAC;cAExG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLlH,OAAA;gBAAIuH,KAAK,EAAC,KAAK;gBAACV,SAAS,EAAC,6EAA6E;gBAAAC,QAAA,EAAC;cAExG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLlH,OAAA;gBAAIuH,KAAK,EAAC,KAAK;gBAACV,SAAS,EAAC,6EAA6E;gBAAAC,QAAA,EAAC;cAExG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLlH,OAAA;gBAAIuH,KAAK,EAAC,KAAK;gBAACV,SAAS,EAAC,6EAA6E;gBAAAC,QAAA,EAAC;cAExG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLlH,OAAA;gBAAIuH,KAAK,EAAC,KAAK;gBAACV,SAAS,EAAC,6EAA6E;gBAAAC,QAAA,EAAC;cAExG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLlH,OAAA;gBAAIuH,KAAK,EAAC,KAAK;gBAACV,SAAS,EAAC,6EAA6E;gBAAAC,QAAA,EAAC;cAExG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACRlH,OAAA;YAAO6G,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAC/CV,YAAY,CAACoB,GAAG,CAAC,CAAC7D,IAAI,EAAE8D,KAAK,kBAC1BzH,OAAA;cAAuB6G,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/C9G,OAAA;gBAAI6G,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,EAC5DX,gBAAgB,GAAGsB,KAAK,GAAG;cAAC;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC,eACLlH,OAAA;gBAAI6G,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,eACvC9G,OAAA;kBAAK6G,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,eAC9B9G,OAAA;oBACI0H,GAAG,EAAEvD,WAAW,CAACR,IAAI,CAAC5B,aAAa,CAAC,IAAI0C,gBAAgB,CAAC,CAAE;oBAC3DkD,GAAG,EAAEhE,IAAI,CAAC5B,aAAa,IAAI,gBAAiB;oBAC5C8E,SAAS,EAAC,8DAA8D;oBACxEe,OAAO,EAAG/C,CAAC,IAAK;sBACZA,CAAC,CAACM,MAAM,CAACuC,GAAG,GAAGjD,gBAAgB,CAAC,CAAC;oBACrC;kBAAE;oBAAAsC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACLlH,OAAA;gBAAI6G,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,eACvC9G,OAAA;kBAAK6G,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAEnD,IAAI,CAAC/B;gBAAQ;kBAAAmF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxE,CAAC,eACLlH,OAAA;gBAAI6G,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,eACvC9G,OAAA;kBAAK6G,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAEnD,IAAI,CAAC9B;gBAAS;kBAAAkF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7D,CAAC,eACLlH,OAAA;gBAAI6G,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,eACvC9G,OAAA;kBAAK6G,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAEnD,IAAI,CAAC7B;gBAAK;kBAAAiF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD,CAAC,eACLlH,OAAA;gBAAI6G,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,eACvC9G,OAAA;kBAAK6G,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,GACvCnD,IAAI,CAAC5B,aAAa,IAAIoC,WAAW,CAACR,IAAI,CAAC5B,aAAa,CAAC,iBAClD/B,OAAA;oBACI0H,GAAG,EAAEvD,WAAW,CAACR,IAAI,CAAC5B,aAAa,CAAE;oBACrC4F,GAAG,EAAEhE,IAAI,CAAC5B,aAAc;oBACxB8E,SAAS,EAAC;kBAAqC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClD,CACJ,eACDlH,OAAA;oBAAK6G,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAEnD,IAAI,CAAC5B,aAAa,IAAI;kBAAkB;oBAAAgF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACLlH,OAAA;gBAAI6G,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,eACvC9G,OAAA;kBAAK6G,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,GAAEnD,IAAI,CAAC3B,OAAO,EAAC,WAAS;gBAAA;kBAAA+E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChF,CAAC,eACLlH,OAAA;gBAAI6G,SAAS,EAAC,iDAAiD;gBAAAC,QAAA,eAC3D9G,OAAA;kBAAK6G,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC3B9G,OAAA;oBACIoH,OAAO,EAAEA,CAAA,KAAMhH,QAAQ,CAAC,gBAAgBuD,IAAI,CAACgB,OAAO,EAAE,CAAE;oBACxDkC,SAAS,EAAC,yCAAyC;oBACnDnE,KAAK,EAAC,cAAc;oBAAAoE,QAAA,eAEpB9G,OAAA,CAACL,KAAK;sBAAAoH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eACTlH,OAAA;oBACIoH,OAAO,EAAEA,CAAA,KAAM1C,UAAU,CAACf,IAAI,CAAE;oBAChCkD,SAAS,EAAC,uCAAuC;oBACjDnE,KAAK,EAAC,WAAW;oBAAAoE,QAAA,eAEjB9G,OAAA,CAACP,MAAM;sBAAAsH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACTlH,OAAA;oBACIoH,OAAO,EAAEA,CAAA,KAAM5B,iBAAiB,CAAC7B,IAAI,CAAE;oBACvCkD,SAAS,EAAC,2CAA2C;oBACrDnE,KAAK,EAAC,cAAc;oBAAAoE,QAAA,eAEpB9G,OAAA,CAACJ,WAAW;sBAAAmH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACX,CAAC,eACTlH,OAAA;oBACIoH,OAAO,EAAEA,CAAA,KAAMzB,aAAa,CAAChC,IAAI,CAAE;oBACnCkD,SAAS,EAAC,qCAAqC;oBAC/CnE,KAAK,EAAC,UAAU;oBAAAoE,QAAA,eAEhB9G,OAAA,CAACH,KAAK;sBAAAkH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA,GAvEAvD,IAAI,CAACgB,OAAO;cAAAoC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAwEjB,CACP;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC,EAGLZ,UAAU,GAAG,CAAC,iBACXtG,OAAA;QAAK6G,SAAS,EAAC,mFAAmF;QAAAC,QAAA,eAC9F9G,OAAA;UAAK6G,SAAS,EAAC,6DAA6D;UAAAC,QAAA,gBACxE9G,OAAA;YAAA8G,QAAA,eACI9G,OAAA;cAAG6G,SAAS,EAAC,uBAAuB;cAAAC,QAAA,GAAC,UACzB,eAAA9G,OAAA;gBAAM6G,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAEX,gBAAgB,GAAG;cAAC;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,OAAG,EAAC,GAAG,eAC1ElH,OAAA;gBAAM6G,SAAS,EAAC,aAAa;gBAAAC,QAAA,EACxBZ,eAAe,GAAGH,aAAa,CAACtC,MAAM,GAAGsC,aAAa,CAACtC,MAAM,GAAGyC;cAAe;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9E,CAAC,EAAC,GAAG,EAAC,KACT,eAAAlH,OAAA;gBAAM6G,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAEf,aAAa,CAACtC;cAAM;gBAAAsD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,YAClE;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNlH,OAAA;YAAA8G,QAAA,eACI9G,OAAA;cAAK6G,SAAS,EAAC,2DAA2D;cAAC,cAAW,YAAY;cAAAC,QAAA,gBAC9F9G,OAAA;gBACIoH,OAAO,EAAEA,CAAA,KAAMZ,QAAQ,CAACvF,WAAW,GAAG,CAAC,GAAGA,WAAW,GAAG,CAAC,GAAG,CAAC,CAAE;gBAC/D4G,QAAQ,EAAE5G,WAAW,KAAK,CAAE;gBAC5B4F,SAAS,EAAE,gHACP5F,WAAW,KAAK,CAAC,GAAG,kCAAkC,GAAG,gCAAgC,EAC1F;gBAAA6F,QAAA,gBAEH9G,OAAA;kBAAM6G,SAAS,EAAC,SAAS;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACzClH,OAAA;kBAAK6G,SAAS,EAAC,SAAS;kBAACiB,KAAK,EAAC,4BAA4B;kBAACC,OAAO,EAAC,WAAW;kBAACC,IAAI,EAAC,cAAc;kBAAC,eAAY,MAAM;kBAAAlB,QAAA,eAClH9G,OAAA;oBAAMiI,QAAQ,EAAC,SAAS;oBAACC,CAAC,EAAC,mHAAmH;oBAACC,QAAQ,EAAC;kBAAS;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,EAERR,WAAW,CAACc,GAAG,CAACY,MAAM,iBACnBpI,OAAA;gBAEIoH,OAAO,EAAEA,CAAA,KAAMZ,QAAQ,CAAC4B,MAAM,CAAE;gBAChCvB,SAAS,EAAE,mGACP5F,WAAW,KAAKmH,MAAM,GAChB,+CAA+C,GAC/C,gCAAgC,EACvC;gBAAAtB,QAAA,EAEFsB;cAAM,GARFA,MAAM;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OASP,CACX,CAAC,eAEFlH,OAAA;gBACIoH,OAAO,EAAEA,CAAA,KAAMZ,QAAQ,CAACvF,WAAW,GAAGqF,UAAU,GAAGrF,WAAW,GAAG,CAAC,GAAGqF,UAAU,CAAE;gBACjFuB,QAAQ,EAAE5G,WAAW,KAAKqF,UAAW;gBACrCO,SAAS,EAAE,gHACP5F,WAAW,KAAKqF,UAAU,GAAG,kCAAkC,GAAG,gCAAgC,EACnG;gBAAAQ,QAAA,gBAEH9G,OAAA;kBAAM6G,SAAS,EAAC,SAAS;kBAAAC,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACrClH,OAAA;kBAAK6G,SAAS,EAAC,SAAS;kBAACiB,KAAK,EAAC,4BAA4B;kBAACC,OAAO,EAAC,WAAW;kBAACC,IAAI,EAAC,cAAc;kBAAC,eAAY,MAAM;kBAAAlB,QAAA,eAClH9G,OAAA;oBAAMiI,QAAQ,EAAC,SAAS;oBAACC,CAAC,EAAC,oHAAoH;oBAACC,QAAQ,EAAC;kBAAS;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CACR;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,EAGLrG,aAAa,iBACVb,OAAA;MAAK6G,SAAS,EAAC,gFAAgF;MAAAC,QAAA,eAC3F9G,OAAA;QAAK6G,SAAS,EAAC,+CAA+C;QAAAC,QAAA,gBAC1D9G,OAAA;UAAK6G,SAAS,EAAC,gDAAgD;UAAAC,QAAA,gBAC3D9G,OAAA;YAAI6G,SAAS,EAAC,qCAAqC;YAAAC,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClElH,OAAA;YACI6G,SAAS,EAAC,+DAA+D;YACzEO,OAAO,EAAEA,CAAA,KAAMtG,gBAAgB,CAAC,IAAI,CAAE;YAAAgG,QAAA,EACzC;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eAENlH,OAAA;UAAMqI,QAAQ,EAAEzD,YAAa;UAACiC,SAAS,EAAC,KAAK;UAAAC,QAAA,gBACzC9G,OAAA;YAAK6G,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACtB9G,OAAA;cAAA8G,QAAA,gBACI9G,OAAA;gBAAO6G,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAChFlH,OAAA;gBACIyC,IAAI,EAAC,MAAM;gBACX8B,IAAI,EAAC,UAAU;gBACfW,KAAK,EAAExD,WAAW,CAACE,QAAS;gBAC5B0F,QAAQ,EAAErC,iBAAkB;gBAC5B4B,SAAS,EAAC,4IAA4I;gBACtJyB,QAAQ;cAAA;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAENlH,OAAA;cAAA8G,QAAA,gBACI9G,OAAA;gBAAO6G,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACjFlH,OAAA;gBACIyC,IAAI,EAAC,MAAM;gBACX8B,IAAI,EAAC,WAAW;gBAChBW,KAAK,EAAExD,WAAW,CAACG,SAAU;gBAC7ByF,QAAQ,EAAErC,iBAAkB;gBAC5B4B,SAAS,EAAC,4IAA4I;gBACtJyB,QAAQ;cAAA;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAENlH,OAAA;cAAA8G,QAAA,gBACI9G,OAAA;gBAAO6G,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC7ElH,OAAA;gBACIyC,IAAI,EAAC,OAAO;gBACZ8B,IAAI,EAAC,OAAO;gBACZW,KAAK,EAAExD,WAAW,CAACI,KAAM;gBACzBwF,QAAQ,EAAErC,iBAAkB;gBAC5B4B,SAAS,EAAC,4IAA4I;gBACtJyB,QAAQ;cAAA;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAENlH,OAAA;cAAA8G,QAAA,gBACI9G,OAAA;gBAAO6G,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACrFlH,OAAA;gBACIuE,IAAI,EAAC,eAAe;gBACpBW,KAAK,EAAExD,WAAW,CAACK,aAAc;gBACjCuF,QAAQ,EAAErC,iBAAkB;gBAC5B4B,SAAS,EAAC,4IAA4I;gBACtJyB,QAAQ;gBAAAxB,QAAA,gBAER9G,OAAA;kBAAQkF,KAAK,EAAC,EAAE;kBAAA4B,QAAA,EAAC;gBAAoB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EAC7C3G,KAAK,CAACiH,GAAG,CAACnD,IAAI,iBACXrE,OAAA;kBAAsBkF,KAAK,EAAEb,IAAI,CAACE,IAAK;kBAAAuC,QAAA,EAAEzC,IAAI,CAACE;gBAAI,GAArCF,IAAI,CAACkE,EAAE;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAuC,CAC9D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,eAENlH,OAAA;cAAA8G,QAAA,gBACI9G,OAAA;gBAAO6G,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAkB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1FlH,OAAA;gBACIyC,IAAI,EAAC,QAAQ;gBACb8B,IAAI,EAAC,SAAS;gBACdW,KAAK,EAAExD,WAAW,CAACM,OAAQ;gBAC3BsF,QAAQ,EAAErC,iBAAkB;gBAC5B4B,SAAS,EAAC,4IAA4I;gBACtJyB,QAAQ;cAAA;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAENlH,OAAA;YAAK6G,SAAS,EAAC,iCAAiC;YAAAC,QAAA,gBAC5C9G,OAAA;cACIyC,IAAI,EAAC,QAAQ;cACb2E,OAAO,EAAEA,CAAA,KAAMtG,gBAAgB,CAAC,IAAI,CAAE;cACtC+F,SAAS,EAAC,2LAA2L;cAAAC,QAAA,EACxM;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTlH,OAAA;cACIyC,IAAI,EAAC,QAAQ;cACboE,SAAS,EAAC,+LAA+L;cAAAC,QAAA,EAC5M;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACR,EAGAjF,gBAAgB,iBACbjC,OAAA;MAAK6G,SAAS,EAAC,gFAAgF;MAAAC,QAAA,eAC3F9G,OAAA;QAAK6G,SAAS,EAAC,+CAA+C;QAAAC,QAAA,gBAC1D9G,OAAA;UAAK6G,SAAS,EAAC,gDAAgD;UAAAC,QAAA,gBAC3D9G,OAAA;YAAI6G,SAAS,EAAC,qCAAqC;YAAAC,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrElH,OAAA;YACI6G,SAAS,EAAC,+DAA+D;YACzEO,OAAO,EAAEA,CAAA,KAAMlF,mBAAmB,CAAC,KAAK,CAAE;YAAA4E,QAAA,EAC7C;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eAENlH,OAAA;UAAMqI,QAAQ,EAAE/C,aAAc;UAACuB,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAC1C9G,OAAA;YAAK6G,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACtB9G,OAAA;cAAA8G,QAAA,gBACI9G,OAAA;gBAAO6G,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAChFlH,OAAA;gBACIyC,IAAI,EAAC,MAAM;gBACX8B,IAAI,EAAC,UAAU;gBACfW,KAAK,EAAE/C,OAAO,CAACP,QAAS;gBACxB0F,QAAQ,EAAEjC,wBAAyB;gBACnCwB,SAAS,EAAC,8IAA8I;gBACxJyB,QAAQ;cAAA;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAENlH,OAAA;cAAA8G,QAAA,gBACI9G,OAAA;gBAAO6G,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACjFlH,OAAA;gBACIyC,IAAI,EAAC,MAAM;gBACX8B,IAAI,EAAC,WAAW;gBAChBW,KAAK,EAAE/C,OAAO,CAACN,SAAU;gBACzByF,QAAQ,EAAEjC,wBAAyB;gBACnCwB,SAAS,EAAC,8IAA8I;gBACxJyB,QAAQ;cAAA;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAENlH,OAAA;cAAA8G,QAAA,gBACI9G,OAAA;gBAAO6G,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC7ElH,OAAA;gBACIyC,IAAI,EAAC,OAAO;gBACZ8B,IAAI,EAAC,OAAO;gBACZW,KAAK,EAAE/C,OAAO,CAACL,KAAM;gBACrBwF,QAAQ,EAAEjC,wBAAyB;gBACnCwB,SAAS,EAAC,8IAA8I;gBACxJyB,QAAQ;cAAA;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAENlH,OAAA;cAAA8G,QAAA,gBACI9G,OAAA;gBAAO6G,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAChFlH,OAAA;gBACIyC,IAAI,EAAC,UAAU;gBACf8B,IAAI,EAAC,UAAU;gBACfW,KAAK,EAAE/C,OAAO,CAACE,QAAS;gBACxBiF,QAAQ,EAAEjC,wBAAyB;gBACnCwB,SAAS,EAAC,8IAA8I;gBACxJyB,QAAQ;cAAA;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAENlH,OAAA;cAAA8G,QAAA,gBACI9G,OAAA;gBAAO6G,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACrFlH,OAAA;gBACIuE,IAAI,EAAC,eAAe;gBACpBW,KAAK,EAAE/C,OAAO,CAACJ,aAAc;gBAC7BuF,QAAQ,EAAEjC,wBAAyB;gBACnCwB,SAAS,EAAC,8IAA8I;gBAAAC,QAAA,gBAExJ9G,OAAA;kBAAQkF,KAAK,EAAC,EAAE;kBAAA4B,QAAA,EAAC;gBAAoB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EAC7C3G,KAAK,CAACiH,GAAG,CAACnD,IAAI,iBACXrE,OAAA;kBAAsBkF,KAAK,EAAEb,IAAI,CAACE,IAAK;kBAAAuC,QAAA,EAAEzC,IAAI,CAACE;gBAAI,GAArCF,IAAI,CAACkE,EAAE;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAuC,CAC9D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,eAENlH,OAAA;cAAA8G,QAAA,gBACI9G,OAAA;gBAAO6G,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAA0B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAClGlH,OAAA;gBACIyC,IAAI,EAAC,QAAQ;gBACb8B,IAAI,EAAC,SAAS;gBACdW,KAAK,EAAE/C,OAAO,CAACH,OAAQ;gBACvBsF,QAAQ,EAAEjC,wBAAyB;gBACnCwB,SAAS,EAAC,8IAA8I;gBACxJ2B,GAAG,EAAC;cAAG;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAENlH,OAAA;YAAK6G,SAAS,EAAC,iCAAiC;YAAAC,QAAA,gBAC5C9G,OAAA;cACIyC,IAAI,EAAC,QAAQ;cACb2E,OAAO,EAAEA,CAAA,KAAMlF,mBAAmB,CAAC,KAAK,CAAE;cAC1C2E,SAAS,EAAC,4LAA4L;cAAAC,QAAA,EACzM;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTlH,OAAA;cACIyC,IAAI,EAAC,QAAQ;cACboE,SAAS,EAAC,kMAAkM;cAAAC,QAAA,EAC/M;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACR;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd;AAAC/G,EAAA,CAjtBQD,cAAc;EAAA,QACFf,WAAW;AAAA;AAAAsJ,EAAA,GADvBvI,cAAc;AAktBvB,eAAeA,cAAc;AAAC,IAAAuI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}