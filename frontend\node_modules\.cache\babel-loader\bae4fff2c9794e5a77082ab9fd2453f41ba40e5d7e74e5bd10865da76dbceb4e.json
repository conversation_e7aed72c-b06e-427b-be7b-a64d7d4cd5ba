{"ast": null, "code": "import React,{useState}from'react';import axios from'axios';import{useNavigate}from'react-router-dom';import{AdminOTPVerification,Admin2FAVerification,Admin2FASetup}from'../components/Admin';import'./AdminLoginPage.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const AdminLoginPage=()=>{const navigate=useNavigate();// Authentication flow state\nconst[authStep,setAuthStep]=useState('login');// 'login', 'otp', '2fa', '2fa_setup'\nconst[adminData,setAdminData]=useState(null);// Login form state\nconst[identifier,setIdentifier]=useState('');const[password,setPassword]=useState('');const[error,setError]=useState('');const[isLoading,setIsLoading]=useState(false);const[rememberMe,setRememberMe]=useState(false);const handleSubmit=async e=>{e.preventDefault();setError('');setIsLoading(true);try{const response=await axios.post('/backend/handlers/admin_login_handler.php',{identifier,password,remember_me:rememberMe});console.log('Admin login request URL:','/backend/handlers/admin_login_handler.php');if(response.data.success){// Store admin data for potential next steps\nsetAdminData({admin_id:response.data.admin_id,username:response.data.username,role:response.data.role});// Check if additional authentication is required\nif(response.data.requires_additional_auth){const nextStep=response.data.next_step;if(nextStep==='otp'){setAuthStep('otp');}else if(nextStep==='2fa'){// Check if 2FA is set up\nsetAuthStep('2fa');}}else{// Complete login - no additional auth required\ncompleteLogin({admin_id:response.data.admin_id,username:response.data.username,role:response.data.role,auth_method:response.data.auth_method||'password_only'});}}else{setError(response.data.message||'Login failed');}}catch(error){console.error('Login error:',error);if(error.response){setError(error.response.data.message||'Invalid credentials');}else if(error.request){setError('Network error. Please check your connection.');}else{setError('An error occurred. Please try again.');}}finally{setIsLoading(false);}};const completeLogin=loginData=>{// Store authentication data\nlocalStorage.setItem('adminId',loginData.admin_id);localStorage.setItem('adminUsername',loginData.username);localStorage.setItem('adminRole',loginData.role);localStorage.setItem('adminAuthMethod',loginData.auth_method);if(loginData.session_token){localStorage.setItem('adminSessionToken',loginData.session_token);}// Navigate to dashboard\nnavigate('/admin/dashboard');};const handleAuthSuccess=authData=>{completeLogin(authData);};const handleBackToLogin=()=>{setAuthStep('login');setAdminData(null);setError('');setPassword('');// Clear password for security\n};// Render different authentication steps\nif(authStep==='otp'&&adminData){return/*#__PURE__*/_jsx(AdminOTPVerification,{adminId:adminData.admin_id,username:adminData.username,onSuccess:handleAuthSuccess,onBack:handleBackToLogin});}if(authStep==='2fa'&&adminData){return/*#__PURE__*/_jsx(Admin2FAVerification,{adminId:adminData.admin_id,username:adminData.username,onSuccess:handleAuthSuccess,onBack:handleBackToLogin});}if(authStep==='2fa_setup'&&adminData){return/*#__PURE__*/_jsx(Admin2FASetup,{adminId:adminData.admin_id,username:adminData.username,onSuccess:handleAuthSuccess,onBack:handleBackToLogin});}// Default login form\nreturn/*#__PURE__*/_jsxs(\"div\",{className:\"admin-login-container\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"login-left-panel\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"login-logo\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"logo-icon\",children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",viewBox:\"0 0 24 24\",fill:\"none\",stroke:\"currentColor\",strokeWidth:\"2\",strokeLinecap:\"round\",strokeLinejoin:\"round\",children:[/*#__PURE__*/_jsx(\"rect\",{x:\"2\",y:\"3\",width:\"20\",height:\"14\",rx:\"2\",ry:\"2\"}),/*#__PURE__*/_jsx(\"line\",{x1:\"8\",y1:\"21\",x2:\"16\",y2:\"21\"}),/*#__PURE__*/_jsx(\"line\",{x1:\"12\",y1:\"17\",x2:\"12\",y2:\"21\"})]})}),/*#__PURE__*/_jsx(\"h1\",{children:\"FanBet247\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"login-form-container\",children:[/*#__PURE__*/_jsx(\"h2\",{children:\"Admin Login\"}),/*#__PURE__*/_jsx(\"p\",{className:\"login-subtitle\",children:\"Enter your credentials to access the admin dashboard\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"security-notice\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"security-icon\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",viewBox:\"0 0 24 24\",fill:\"none\",stroke:\"currentColor\",strokeWidth:\"2\",strokeLinecap:\"round\",strokeLinejoin:\"round\",children:/*#__PURE__*/_jsx(\"path\",{d:\"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z\"})})}),/*#__PURE__*/_jsx(\"span\",{children:\"Enhanced security enabled - Additional verification may be required\"})]}),error&&/*#__PURE__*/_jsx(\"div\",{className:\"error-message\",children:error}),/*#__PURE__*/_jsxs(\"form\",{onSubmit:handleSubmit,children:[/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"identifier\",children:\"Username or Email\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"input-container\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"text\",id:\"identifier\",value:identifier,onChange:e=>setIdentifier(e.target.value),disabled:isLoading,placeholder:\"Enter your username or email\",required:true}),/*#__PURE__*/_jsx(\"div\",{className:\"input-icon\",children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",viewBox:\"0 0 24 24\",fill:\"none\",stroke:\"currentColor\",strokeWidth:\"2\",strokeLinecap:\"round\",strokeLinejoin:\"round\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2\"}),/*#__PURE__*/_jsx(\"circle\",{cx:\"12\",cy:\"7\",r:\"4\"})]})})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"password\",children:\"Password\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"input-container\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"password\",id:\"password\",value:password,onChange:e=>setPassword(e.target.value),disabled:isLoading,placeholder:\"Enter your password\",required:true}),/*#__PURE__*/_jsx(\"div\",{className:\"input-icon\",children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",viewBox:\"0 0 24 24\",fill:\"none\",stroke:\"currentColor\",strokeWidth:\"2\",strokeLinecap:\"round\",strokeLinejoin:\"round\",children:[/*#__PURE__*/_jsx(\"rect\",{x:\"3\",y:\"11\",width:\"18\",height:\"11\",rx:\"2\",ry:\"2\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M7 11V7a5 5 0 0 1 10 0v4\"})]})})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-options\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"remember-me\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",id:\"rememberMe\",checked:rememberMe,onChange:e=>setRememberMe(e.target.checked)}),/*#__PURE__*/_jsx(\"label\",{htmlFor:\"rememberMe\",children:\"Remember me\"})]}),/*#__PURE__*/_jsx(\"button\",{type:\"button\",className:\"forgot-password\",onClick:()=>{// Show temporary message instead of alert\nconst button=document.activeElement;const originalText=button.textContent;button.textContent='Coming soon!';button.style.color='#3b82f6';setTimeout(()=>{button.textContent=originalText;button.style.color='';},3000);},children:\"Forgot password?\"})]}),/*#__PURE__*/_jsx(\"button\",{type:\"submit\",className:\"login-button\",disabled:isLoading,children:isLoading?'Logging in...':'Login'})]})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"login-right-panel\"})]});};export default AdminLoginPage;", "map": {"version": 3, "names": ["React", "useState", "axios", "useNavigate", "AdminOTPVerification", "Admin2FAVerification", "Admin2FASetup", "jsx", "_jsx", "jsxs", "_jsxs", "AdminLoginPage", "navigate", "authStep", "setAuthStep", "adminData", "setAdminData", "identifier", "setIdentifier", "password", "setPassword", "error", "setError", "isLoading", "setIsLoading", "rememberMe", "setRememberMe", "handleSubmit", "e", "preventDefault", "response", "post", "remember_me", "console", "log", "data", "success", "admin_id", "username", "role", "requires_additional_auth", "nextStep", "next_step", "completeLogin", "auth_method", "message", "request", "loginData", "localStorage", "setItem", "session_token", "handleAuthSuccess", "authData", "handleBackToLogin", "adminId", "onSuccess", "onBack", "className", "children", "xmlns", "viewBox", "fill", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "x", "y", "width", "height", "rx", "ry", "x1", "y1", "x2", "y2", "d", "onSubmit", "htmlFor", "type", "id", "value", "onChange", "target", "disabled", "placeholder", "required", "cx", "cy", "r", "checked", "onClick", "button", "document", "activeElement", "originalText", "textContent", "style", "color", "setTimeout"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/pages/AdminLoginPage.js"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport axios from 'axios';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport { AdminOTPVerification, Admin2FAVerification, Admin2FASetup } from '../components/Admin';\r\nimport './AdminLoginPage.css';\r\n\r\n\r\nconst AdminLoginPage = () => {\r\n    const navigate = useNavigate();\r\n\r\n    // Authentication flow state\r\n    const [authStep, setAuthStep] = useState('login'); // 'login', 'otp', '2fa', '2fa_setup'\r\n    const [adminData, setAdminData] = useState(null);\r\n\r\n    // Login form state\r\n    const [identifier, setIdentifier] = useState('');\r\n    const [password, setPassword] = useState('');\r\n    const [error, setError] = useState('');\r\n    const [isLoading, setIsLoading] = useState(false);\r\n    const [rememberMe, setRememberMe] = useState(false);\r\n\r\n    const handleSubmit = async (e) => {\r\n        e.preventDefault();\r\n        setError('');\r\n        setIsLoading(true);\r\n\r\n        try {\r\n            const response = await axios.post('/backend/handlers/admin_login_handler.php', {\r\n                identifier,\r\n                password,\r\n                remember_me: rememberMe\r\n            });\r\n\r\n            console.log('Admin login request URL:', '/backend/handlers/admin_login_handler.php');\r\n\r\n            if (response.data.success) {\r\n                // Store admin data for potential next steps\r\n                setAdminData({\r\n                    admin_id: response.data.admin_id,\r\n                    username: response.data.username,\r\n                    role: response.data.role\r\n                });\r\n\r\n                // Check if additional authentication is required\r\n                if (response.data.requires_additional_auth) {\r\n                    const nextStep = response.data.next_step;\r\n\r\n                    if (nextStep === 'otp') {\r\n                        setAuthStep('otp');\r\n                    } else if (nextStep === '2fa') {\r\n                        // Check if 2FA is set up\r\n                        setAuthStep('2fa');\r\n                    }\r\n                } else {\r\n                    // Complete login - no additional auth required\r\n                    completeLogin({\r\n                        admin_id: response.data.admin_id,\r\n                        username: response.data.username,\r\n                        role: response.data.role,\r\n                        auth_method: response.data.auth_method || 'password_only'\r\n                    });\r\n                }\r\n            } else {\r\n                setError(response.data.message || 'Login failed');\r\n            }\r\n        } catch (error) {\r\n            console.error('Login error:', error);\r\n            if (error.response) {\r\n                setError(error.response.data.message || 'Invalid credentials');\r\n            } else if (error.request) {\r\n                setError('Network error. Please check your connection.');\r\n            } else {\r\n                setError('An error occurred. Please try again.');\r\n            }\r\n        } finally {\r\n            setIsLoading(false);\r\n        }\r\n    };\r\n\r\n    const completeLogin = (loginData) => {\r\n        // Store authentication data\r\n        localStorage.setItem('adminId', loginData.admin_id);\r\n        localStorage.setItem('adminUsername', loginData.username);\r\n        localStorage.setItem('adminRole', loginData.role);\r\n        localStorage.setItem('adminAuthMethod', loginData.auth_method);\r\n\r\n        if (loginData.session_token) {\r\n            localStorage.setItem('adminSessionToken', loginData.session_token);\r\n        }\r\n\r\n        // Navigate to dashboard\r\n        navigate('/admin/dashboard');\r\n    };\r\n\r\n    const handleAuthSuccess = (authData) => {\r\n        completeLogin(authData);\r\n    };\r\n\r\n    const handleBackToLogin = () => {\r\n        setAuthStep('login');\r\n        setAdminData(null);\r\n        setError('');\r\n        setPassword(''); // Clear password for security\r\n    };\r\n\r\n    // Render different authentication steps\r\n    if (authStep === 'otp' && adminData) {\r\n        return (\r\n            <AdminOTPVerification\r\n                adminId={adminData.admin_id}\r\n                username={adminData.username}\r\n                onSuccess={handleAuthSuccess}\r\n                onBack={handleBackToLogin}\r\n            />\r\n        );\r\n    }\r\n\r\n    if (authStep === '2fa' && adminData) {\r\n        return (\r\n            <Admin2FAVerification\r\n                adminId={adminData.admin_id}\r\n                username={adminData.username}\r\n                onSuccess={handleAuthSuccess}\r\n                onBack={handleBackToLogin}\r\n            />\r\n        );\r\n    }\r\n\r\n    if (authStep === '2fa_setup' && adminData) {\r\n        return (\r\n            <Admin2FASetup\r\n                adminId={adminData.admin_id}\r\n                username={adminData.username}\r\n                onSuccess={handleAuthSuccess}\r\n                onBack={handleBackToLogin}\r\n            />\r\n        );\r\n    }\r\n\r\n    // Default login form\r\n    return (\r\n        <div className=\"admin-login-container\">\r\n            <div className=\"login-left-panel\">\r\n                <div className=\"login-logo\">\r\n                    <div className=\"logo-icon\">\r\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\">\r\n                            <rect x=\"2\" y=\"3\" width=\"20\" height=\"14\" rx=\"2\" ry=\"2\"></rect>\r\n                            <line x1=\"8\" y1=\"21\" x2=\"16\" y2=\"21\"></line>\r\n                            <line x1=\"12\" y1=\"17\" x2=\"12\" y2=\"21\"></line>\r\n                        </svg>\r\n                    </div>\r\n                    <h1>FanBet247</h1>\r\n                </div>\r\n\r\n                <div className=\"login-form-container\">\r\n                    <h2>Admin Login</h2>\r\n                    <p className=\"login-subtitle\">Enter your credentials to access the admin dashboard</p>\r\n\r\n                    {/* Security Notice */}\r\n                    <div className=\"security-notice\">\r\n                        <div className=\"security-icon\">\r\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\">\r\n                                <path d=\"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z\"></path>\r\n                            </svg>\r\n                        </div>\r\n                        <span>Enhanced security enabled - Additional verification may be required</span>\r\n                    </div>\r\n\r\n                    {error && <div className=\"error-message\">{error}</div>}\r\n\r\n                    <form onSubmit={handleSubmit}>\r\n                        <div className=\"form-group\">\r\n                            <label htmlFor=\"identifier\">Username or Email</label>\r\n                            <div className=\"input-container\">\r\n                                <input\r\n                                    type=\"text\"\r\n                                    id=\"identifier\"\r\n                                    value={identifier}\r\n                                    onChange={(e) => setIdentifier(e.target.value)}\r\n                                    disabled={isLoading}\r\n                                    placeholder=\"Enter your username or email\"\r\n                                    required\r\n                                />\r\n                                <div className=\"input-icon\">\r\n                                    <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\">\r\n                                        <path d=\"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2\"></path>\r\n                                        <circle cx=\"12\" cy=\"7\" r=\"4\"></circle>\r\n                                    </svg>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n\r\n                        <div className=\"form-group\">\r\n                            <label htmlFor=\"password\">Password</label>\r\n                            <div className=\"input-container\">\r\n                                <input\r\n                                    type=\"password\"\r\n                                    id=\"password\"\r\n                                    value={password}\r\n                                    onChange={(e) => setPassword(e.target.value)}\r\n                                    disabled={isLoading}\r\n                                    placeholder=\"Enter your password\"\r\n                                    required\r\n                                />\r\n                                <div className=\"input-icon\">\r\n                                    <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\">\r\n                                        <rect x=\"3\" y=\"11\" width=\"18\" height=\"11\" rx=\"2\" ry=\"2\"></rect>\r\n                                        <path d=\"M7 11V7a5 5 0 0 1 10 0v4\"></path>\r\n                                    </svg>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n\r\n                        <div className=\"form-options\">\r\n                            <div className=\"remember-me\">\r\n                                <input\r\n                                    type=\"checkbox\"\r\n                                    id=\"rememberMe\"\r\n                                    checked={rememberMe}\r\n                                    onChange={(e) => setRememberMe(e.target.checked)}\r\n                                />\r\n                                <label htmlFor=\"rememberMe\">Remember me</label>\r\n                            </div>\r\n                            <button type=\"button\" className=\"forgot-password\" onClick={() => {\r\n                                // Show temporary message instead of alert\r\n                                const button = document.activeElement;\r\n                                const originalText = button.textContent;\r\n                                button.textContent = 'Coming soon!';\r\n                                button.style.color = '#3b82f6';\r\n                                setTimeout(() => {\r\n                                    button.textContent = originalText;\r\n                                    button.style.color = '';\r\n                                }, 3000);\r\n                            }}>Forgot password?</button>\r\n                        </div>\r\n\r\n                        <button type=\"submit\" className=\"login-button\" disabled={isLoading}>\r\n                            {isLoading ? 'Logging in...' : 'Login'}\r\n                        </button>\r\n                    </form>\r\n                </div>\r\n            </div>\r\n\r\n            <div className=\"login-right-panel\"></div>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default AdminLoginPage;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,MAAO,CAAAC,KAAK,KAAM,OAAO,CACzB,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,OAASC,oBAAoB,CAAEC,oBAAoB,CAAEC,aAAa,KAAQ,qBAAqB,CAC/F,MAAO,sBAAsB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAG9B,KAAM,CAAAC,cAAc,CAAGA,CAAA,GAAM,CACzB,KAAM,CAAAC,QAAQ,CAAGT,WAAW,CAAC,CAAC,CAE9B;AACA,KAAM,CAACU,QAAQ,CAAEC,WAAW,CAAC,CAAGb,QAAQ,CAAC,OAAO,CAAC,CAAE;AACnD,KAAM,CAACc,SAAS,CAAEC,YAAY,CAAC,CAAGf,QAAQ,CAAC,IAAI,CAAC,CAEhD;AACA,KAAM,CAACgB,UAAU,CAAEC,aAAa,CAAC,CAAGjB,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAACkB,QAAQ,CAAEC,WAAW,CAAC,CAAGnB,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAACoB,KAAK,CAAEC,QAAQ,CAAC,CAAGrB,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAACsB,SAAS,CAAEC,YAAY,CAAC,CAAGvB,QAAQ,CAAC,KAAK,CAAC,CACjD,KAAM,CAACwB,UAAU,CAAEC,aAAa,CAAC,CAAGzB,QAAQ,CAAC,KAAK,CAAC,CAEnD,KAAM,CAAA0B,YAAY,CAAG,KAAO,CAAAC,CAAC,EAAK,CAC9BA,CAAC,CAACC,cAAc,CAAC,CAAC,CAClBP,QAAQ,CAAC,EAAE,CAAC,CACZE,YAAY,CAAC,IAAI,CAAC,CAElB,GAAI,CACA,KAAM,CAAAM,QAAQ,CAAG,KAAM,CAAA5B,KAAK,CAAC6B,IAAI,CAAC,2CAA2C,CAAE,CAC3Ed,UAAU,CACVE,QAAQ,CACRa,WAAW,CAAEP,UACjB,CAAC,CAAC,CAEFQ,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAE,2CAA2C,CAAC,CAEpF,GAAIJ,QAAQ,CAACK,IAAI,CAACC,OAAO,CAAE,CACvB;AACApB,YAAY,CAAC,CACTqB,QAAQ,CAAEP,QAAQ,CAACK,IAAI,CAACE,QAAQ,CAChCC,QAAQ,CAAER,QAAQ,CAACK,IAAI,CAACG,QAAQ,CAChCC,IAAI,CAAET,QAAQ,CAACK,IAAI,CAACI,IACxB,CAAC,CAAC,CAEF;AACA,GAAIT,QAAQ,CAACK,IAAI,CAACK,wBAAwB,CAAE,CACxC,KAAM,CAAAC,QAAQ,CAAGX,QAAQ,CAACK,IAAI,CAACO,SAAS,CAExC,GAAID,QAAQ,GAAK,KAAK,CAAE,CACpB3B,WAAW,CAAC,KAAK,CAAC,CACtB,CAAC,IAAM,IAAI2B,QAAQ,GAAK,KAAK,CAAE,CAC3B;AACA3B,WAAW,CAAC,KAAK,CAAC,CACtB,CACJ,CAAC,IAAM,CACH;AACA6B,aAAa,CAAC,CACVN,QAAQ,CAAEP,QAAQ,CAACK,IAAI,CAACE,QAAQ,CAChCC,QAAQ,CAAER,QAAQ,CAACK,IAAI,CAACG,QAAQ,CAChCC,IAAI,CAAET,QAAQ,CAACK,IAAI,CAACI,IAAI,CACxBK,WAAW,CAAEd,QAAQ,CAACK,IAAI,CAACS,WAAW,EAAI,eAC9C,CAAC,CAAC,CACN,CACJ,CAAC,IAAM,CACHtB,QAAQ,CAACQ,QAAQ,CAACK,IAAI,CAACU,OAAO,EAAI,cAAc,CAAC,CACrD,CACJ,CAAE,MAAOxB,KAAK,CAAE,CACZY,OAAO,CAACZ,KAAK,CAAC,cAAc,CAAEA,KAAK,CAAC,CACpC,GAAIA,KAAK,CAACS,QAAQ,CAAE,CAChBR,QAAQ,CAACD,KAAK,CAACS,QAAQ,CAACK,IAAI,CAACU,OAAO,EAAI,qBAAqB,CAAC,CAClE,CAAC,IAAM,IAAIxB,KAAK,CAACyB,OAAO,CAAE,CACtBxB,QAAQ,CAAC,8CAA8C,CAAC,CAC5D,CAAC,IAAM,CACHA,QAAQ,CAAC,sCAAsC,CAAC,CACpD,CACJ,CAAC,OAAS,CACNE,YAAY,CAAC,KAAK,CAAC,CACvB,CACJ,CAAC,CAED,KAAM,CAAAmB,aAAa,CAAII,SAAS,EAAK,CACjC;AACAC,YAAY,CAACC,OAAO,CAAC,SAAS,CAAEF,SAAS,CAACV,QAAQ,CAAC,CACnDW,YAAY,CAACC,OAAO,CAAC,eAAe,CAAEF,SAAS,CAACT,QAAQ,CAAC,CACzDU,YAAY,CAACC,OAAO,CAAC,WAAW,CAAEF,SAAS,CAACR,IAAI,CAAC,CACjDS,YAAY,CAACC,OAAO,CAAC,iBAAiB,CAAEF,SAAS,CAACH,WAAW,CAAC,CAE9D,GAAIG,SAAS,CAACG,aAAa,CAAE,CACzBF,YAAY,CAACC,OAAO,CAAC,mBAAmB,CAAEF,SAAS,CAACG,aAAa,CAAC,CACtE,CAEA;AACAtC,QAAQ,CAAC,kBAAkB,CAAC,CAChC,CAAC,CAED,KAAM,CAAAuC,iBAAiB,CAAIC,QAAQ,EAAK,CACpCT,aAAa,CAACS,QAAQ,CAAC,CAC3B,CAAC,CAED,KAAM,CAAAC,iBAAiB,CAAGA,CAAA,GAAM,CAC5BvC,WAAW,CAAC,OAAO,CAAC,CACpBE,YAAY,CAAC,IAAI,CAAC,CAClBM,QAAQ,CAAC,EAAE,CAAC,CACZF,WAAW,CAAC,EAAE,CAAC,CAAE;AACrB,CAAC,CAED;AACA,GAAIP,QAAQ,GAAK,KAAK,EAAIE,SAAS,CAAE,CACjC,mBACIP,IAAA,CAACJ,oBAAoB,EACjBkD,OAAO,CAAEvC,SAAS,CAACsB,QAAS,CAC5BC,QAAQ,CAAEvB,SAAS,CAACuB,QAAS,CAC7BiB,SAAS,CAAEJ,iBAAkB,CAC7BK,MAAM,CAAEH,iBAAkB,CAC7B,CAAC,CAEV,CAEA,GAAIxC,QAAQ,GAAK,KAAK,EAAIE,SAAS,CAAE,CACjC,mBACIP,IAAA,CAACH,oBAAoB,EACjBiD,OAAO,CAAEvC,SAAS,CAACsB,QAAS,CAC5BC,QAAQ,CAAEvB,SAAS,CAACuB,QAAS,CAC7BiB,SAAS,CAAEJ,iBAAkB,CAC7BK,MAAM,CAAEH,iBAAkB,CAC7B,CAAC,CAEV,CAEA,GAAIxC,QAAQ,GAAK,WAAW,EAAIE,SAAS,CAAE,CACvC,mBACIP,IAAA,CAACF,aAAa,EACVgD,OAAO,CAAEvC,SAAS,CAACsB,QAAS,CAC5BC,QAAQ,CAAEvB,SAAS,CAACuB,QAAS,CAC7BiB,SAAS,CAAEJ,iBAAkB,CAC7BK,MAAM,CAAEH,iBAAkB,CAC7B,CAAC,CAEV,CAEA;AACA,mBACI3C,KAAA,QAAK+C,SAAS,CAAC,uBAAuB,CAAAC,QAAA,eAClChD,KAAA,QAAK+C,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC7BhD,KAAA,QAAK+C,SAAS,CAAC,YAAY,CAAAC,QAAA,eACvBlD,IAAA,QAAKiD,SAAS,CAAC,WAAW,CAAAC,QAAA,cACtBhD,KAAA,QAAKiD,KAAK,CAAC,4BAA4B,CAACC,OAAO,CAAC,WAAW,CAACC,IAAI,CAAC,MAAM,CAACC,MAAM,CAAC,cAAc,CAACC,WAAW,CAAC,GAAG,CAACC,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAAAP,QAAA,eACtJlD,IAAA,SAAM0D,CAAC,CAAC,GAAG,CAACC,CAAC,CAAC,GAAG,CAACC,KAAK,CAAC,IAAI,CAACC,MAAM,CAAC,IAAI,CAACC,EAAE,CAAC,GAAG,CAACC,EAAE,CAAC,GAAG,CAAO,CAAC,cAC9D/D,IAAA,SAAMgE,EAAE,CAAC,GAAG,CAACC,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,IAAI,CAAO,CAAC,cAC5CnE,IAAA,SAAMgE,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,IAAI,CAAO,CAAC,EAC5C,CAAC,CACL,CAAC,cACNnE,IAAA,OAAAkD,QAAA,CAAI,WAAS,CAAI,CAAC,EACjB,CAAC,cAENhD,KAAA,QAAK+C,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACjClD,IAAA,OAAAkD,QAAA,CAAI,aAAW,CAAI,CAAC,cACpBlD,IAAA,MAAGiD,SAAS,CAAC,gBAAgB,CAAAC,QAAA,CAAC,sDAAoD,CAAG,CAAC,cAGtFhD,KAAA,QAAK+C,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC5BlD,IAAA,QAAKiD,SAAS,CAAC,eAAe,CAAAC,QAAA,cAC1BlD,IAAA,QAAKmD,KAAK,CAAC,4BAA4B,CAACC,OAAO,CAAC,WAAW,CAACC,IAAI,CAAC,MAAM,CAACC,MAAM,CAAC,cAAc,CAACC,WAAW,CAAC,GAAG,CAACC,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAAAP,QAAA,cACtJlD,IAAA,SAAMoE,CAAC,CAAC,6CAA6C,CAAO,CAAC,CAC5D,CAAC,CACL,CAAC,cACNpE,IAAA,SAAAkD,QAAA,CAAM,qEAAmE,CAAM,CAAC,EAC/E,CAAC,CAELrC,KAAK,eAAIb,IAAA,QAAKiD,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAErC,KAAK,CAAM,CAAC,cAEtDX,KAAA,SAAMmE,QAAQ,CAAElD,YAAa,CAAA+B,QAAA,eACzBhD,KAAA,QAAK+C,SAAS,CAAC,YAAY,CAAAC,QAAA,eACvBlD,IAAA,UAAOsE,OAAO,CAAC,YAAY,CAAApB,QAAA,CAAC,mBAAiB,CAAO,CAAC,cACrDhD,KAAA,QAAK+C,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC5BlD,IAAA,UACIuE,IAAI,CAAC,MAAM,CACXC,EAAE,CAAC,YAAY,CACfC,KAAK,CAAEhE,UAAW,CAClBiE,QAAQ,CAAGtD,CAAC,EAAKV,aAAa,CAACU,CAAC,CAACuD,MAAM,CAACF,KAAK,CAAE,CAC/CG,QAAQ,CAAE7D,SAAU,CACpB8D,WAAW,CAAC,8BAA8B,CAC1CC,QAAQ,MACX,CAAC,cACF9E,IAAA,QAAKiD,SAAS,CAAC,YAAY,CAAAC,QAAA,cACvBhD,KAAA,QAAKiD,KAAK,CAAC,4BAA4B,CAACC,OAAO,CAAC,WAAW,CAACC,IAAI,CAAC,MAAM,CAACC,MAAM,CAAC,cAAc,CAACC,WAAW,CAAC,GAAG,CAACC,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAAAP,QAAA,eACtJlD,IAAA,SAAMoE,CAAC,CAAC,2CAA2C,CAAO,CAAC,cAC3DpE,IAAA,WAAQ+E,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,GAAG,CAACC,CAAC,CAAC,GAAG,CAAS,CAAC,EACrC,CAAC,CACL,CAAC,EACL,CAAC,EACL,CAAC,cAEN/E,KAAA,QAAK+C,SAAS,CAAC,YAAY,CAAAC,QAAA,eACvBlD,IAAA,UAAOsE,OAAO,CAAC,UAAU,CAAApB,QAAA,CAAC,UAAQ,CAAO,CAAC,cAC1ChD,KAAA,QAAK+C,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC5BlD,IAAA,UACIuE,IAAI,CAAC,UAAU,CACfC,EAAE,CAAC,UAAU,CACbC,KAAK,CAAE9D,QAAS,CAChB+D,QAAQ,CAAGtD,CAAC,EAAKR,WAAW,CAACQ,CAAC,CAACuD,MAAM,CAACF,KAAK,CAAE,CAC7CG,QAAQ,CAAE7D,SAAU,CACpB8D,WAAW,CAAC,qBAAqB,CACjCC,QAAQ,MACX,CAAC,cACF9E,IAAA,QAAKiD,SAAS,CAAC,YAAY,CAAAC,QAAA,cACvBhD,KAAA,QAAKiD,KAAK,CAAC,4BAA4B,CAACC,OAAO,CAAC,WAAW,CAACC,IAAI,CAAC,MAAM,CAACC,MAAM,CAAC,cAAc,CAACC,WAAW,CAAC,GAAG,CAACC,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAAAP,QAAA,eACtJlD,IAAA,SAAM0D,CAAC,CAAC,GAAG,CAACC,CAAC,CAAC,IAAI,CAACC,KAAK,CAAC,IAAI,CAACC,MAAM,CAAC,IAAI,CAACC,EAAE,CAAC,GAAG,CAACC,EAAE,CAAC,GAAG,CAAO,CAAC,cAC/D/D,IAAA,SAAMoE,CAAC,CAAC,0BAA0B,CAAO,CAAC,EACzC,CAAC,CACL,CAAC,EACL,CAAC,EACL,CAAC,cAENlE,KAAA,QAAK+C,SAAS,CAAC,cAAc,CAAAC,QAAA,eACzBhD,KAAA,QAAK+C,SAAS,CAAC,aAAa,CAAAC,QAAA,eACxBlD,IAAA,UACIuE,IAAI,CAAC,UAAU,CACfC,EAAE,CAAC,YAAY,CACfU,OAAO,CAAEjE,UAAW,CACpByD,QAAQ,CAAGtD,CAAC,EAAKF,aAAa,CAACE,CAAC,CAACuD,MAAM,CAACO,OAAO,CAAE,CACpD,CAAC,cACFlF,IAAA,UAAOsE,OAAO,CAAC,YAAY,CAAApB,QAAA,CAAC,aAAW,CAAO,CAAC,EAC9C,CAAC,cACNlD,IAAA,WAAQuE,IAAI,CAAC,QAAQ,CAACtB,SAAS,CAAC,iBAAiB,CAACkC,OAAO,CAAEA,CAAA,GAAM,CAC7D;AACA,KAAM,CAAAC,MAAM,CAAGC,QAAQ,CAACC,aAAa,CACrC,KAAM,CAAAC,YAAY,CAAGH,MAAM,CAACI,WAAW,CACvCJ,MAAM,CAACI,WAAW,CAAG,cAAc,CACnCJ,MAAM,CAACK,KAAK,CAACC,KAAK,CAAG,SAAS,CAC9BC,UAAU,CAAC,IAAM,CACbP,MAAM,CAACI,WAAW,CAAGD,YAAY,CACjCH,MAAM,CAACK,KAAK,CAACC,KAAK,CAAG,EAAE,CAC3B,CAAC,CAAE,IAAI,CAAC,CACZ,CAAE,CAAAxC,QAAA,CAAC,kBAAgB,CAAQ,CAAC,EAC3B,CAAC,cAENlD,IAAA,WAAQuE,IAAI,CAAC,QAAQ,CAACtB,SAAS,CAAC,cAAc,CAAC2B,QAAQ,CAAE7D,SAAU,CAAAmC,QAAA,CAC9DnC,SAAS,CAAG,eAAe,CAAG,OAAO,CAClC,CAAC,EACP,CAAC,EACN,CAAC,EACL,CAAC,cAENf,IAAA,QAAKiD,SAAS,CAAC,mBAAmB,CAAM,CAAC,EACxC,CAAC,CAEd,CAAC,CAED,cAAe,CAAA9C,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}