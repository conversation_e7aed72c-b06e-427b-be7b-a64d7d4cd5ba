{"ast": null, "code": "import React,{useState,useEffect}from'react';import{useNavigate,NavLink}from'react-router-dom';import{FaGamepad,FaUsers,FaCog,FaChartBar,FaMoneyBill,FaTrophy,FaHome,FaUserPlus,FaCreditCard,FaFutbol,FaTasks,FaCoins,FaCalendarAlt,FaUserFriends,FaCashRegister,FaExchangeAlt,FaMedal,FaWrench,FaChartLine,FaShieldAlt,FaSignOutAlt,FaDice,FaBars,FaTimes}from'react-icons/fa';import'./AdminSidebar.css';import{useSiteConfig}from'../contexts/SiteConfigContext';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";function Sidebar(){const navigate=useNavigate();const{config}=useSiteConfig();const[isCollapsed,setIsCollapsed]=useState(false);const[isMobile,setIsMobile]=useState(false);// Check screen size and set mobile state\nuseEffect(()=>{const checkScreenSize=()=>{const screenWidth=window.innerWidth;const isMobileScreen=screenWidth<=1024;// 13 inches and smaller\nsetIsMobile(isMobileScreen);// Auto-collapse on small screens\nif(isMobileScreen){setIsCollapsed(true);}};checkScreenSize();window.addEventListener('resize',checkScreenSize);return()=>window.removeEventListener('resize',checkScreenSize);},[]);const toggleSidebar=()=>{setIsCollapsed(!isCollapsed);};// Define all menu items as a flat list\nconst menuItems=[{link:'/admin/dashboard',text:'Overview',icon:/*#__PURE__*/_jsx(FaHome,{})},// Challenges\n{link:'/admin/challenge-system',text:'Challenge System',icon:/*#__PURE__*/_jsx(FaFutbol,{})},{link:'/admin/challenge-management',text:'Challenge Management',icon:/*#__PURE__*/_jsx(FaTasks,{})},{link:'/admin/credit-challenge',text:'Credit Challenge',icon:/*#__PURE__*/_jsx(FaCoins,{})},{link:'/admin/team-management',text:'Team Management',icon:/*#__PURE__*/_jsx(FaShieldAlt,{})},{link:'/admin/bets',text:'Bet Management',icon:/*#__PURE__*/_jsx(FaDice,{})},// Users\n{link:'/admin/users',text:'User Management',icon:/*#__PURE__*/_jsx(FaUsers,{})},{link:'/admin/add-user',text:'Add User',icon:/*#__PURE__*/_jsx(FaUserPlus,{})},{link:'/admin/credit-user',text:'Credit User',icon:/*#__PURE__*/_jsx(FaCreditCard,{})},// Leagues\n{link:'/admin/league-management',text:'League Management',icon:/*#__PURE__*/_jsx(FaTrophy,{})},{link:'/admin/league-seasons',text:'Season Management',icon:/*#__PURE__*/_jsx(FaCalendarAlt,{})},{link:'/admin/league-users',text:'League Users',icon:/*#__PURE__*/_jsx(FaUserFriends,{})},// Finance\n{link:'/admin/payment-methods',text:'Payment Methods',icon:/*#__PURE__*/_jsx(FaCashRegister,{})},{link:'/admin/transactions',text:'Transactions',icon:/*#__PURE__*/_jsx(FaExchangeAlt,{})},// System\n{link:'/admin/leaderboard',text:'Leaderboard Management',icon:/*#__PURE__*/_jsx(FaMedal,{})},{link:'/admin/settings',text:'System Settings',icon:/*#__PURE__*/_jsx(FaWrench,{})},{link:'/admin/security-settings',text:'Security Settings',icon:/*#__PURE__*/_jsx(FaShieldAlt,{})},{link:'/admin/2fa-settings',text:'2FA Settings',icon:/*#__PURE__*/_jsx(FaShieldAlt,{})},{link:'/admin/reports',text:'Reports and Analytics',icon:/*#__PURE__*/_jsx(FaChartLine,{})}];return/*#__PURE__*/_jsxs(_Fragment,{children:[isMobile&&/*#__PURE__*/_jsx(\"button\",{className:\"mobile-sidebar-toggle\",onClick:toggleSidebar,\"aria-label\":\"Toggle sidebar\",children:isCollapsed?/*#__PURE__*/_jsx(FaBars,{}):/*#__PURE__*/_jsx(FaTimes,{})}),/*#__PURE__*/_jsxs(\"div\",{className:`admin-sidebar ${isCollapsed?'collapsed':''} ${isMobile?'mobile':''}`,children:[/*#__PURE__*/_jsxs(\"div\",{className:\"sidebar-header\",children:[!isMobile&&/*#__PURE__*/_jsx(\"button\",{className:\"desktop-sidebar-toggle\",onClick:toggleSidebar,\"aria-label\":\"Toggle sidebar\",children:isCollapsed?/*#__PURE__*/_jsx(FaBars,{}):/*#__PURE__*/_jsx(FaTimes,{})}),/*#__PURE__*/_jsx(\"div\",{className:\"logo\",children:!isCollapsed&&config.site_logo?/*#__PURE__*/_jsx(\"img\",{src:`/backend/${config.site_logo}`,alt:config.site_name||\"Site Logo\",className:\"logo-icon\",onError:e=>{e.target.style.display='none';e.target.nextSibling.style.display='block';}}):!isCollapsed?/*#__PURE__*/_jsx(\"span\",{className:\"logo-text\",children:config.site_name||\"FanBet247\"}):/*#__PURE__*/_jsx(\"span\",{className:\"logo-collapsed\",children:\"F\"})})]}),/*#__PURE__*/_jsxs(\"nav\",{className:\"admin-sidebar-nav simple-menu\",children:[menuItems.map((item,index)=>/*#__PURE__*/_jsxs(NavLink,{to:item.link,className:_ref=>{let{isActive}=_ref;return`simple-nav-item ${isActive?'active':''}`;},end:true,title:isCollapsed?item.text:'',children:[/*#__PURE__*/_jsx(\"span\",{className:\"simple-nav-item-icon\",children:item.icon}),!isCollapsed&&/*#__PURE__*/_jsx(\"span\",{className:\"simple-nav-item-text\",children:item.text})]},index)),/*#__PURE__*/_jsx(\"div\",{className:\"sidebar-footer\",children:/*#__PURE__*/_jsxs(\"button\",{className:\"logout-button\",onClick:()=>navigate('/admin/login'),style:{backgroundColor:'#dc2626',color:'white'},title:isCollapsed?'Logout':'',children:[/*#__PURE__*/_jsx(\"span\",{className:\"logout-icon\",style:{color:'white'},children:/*#__PURE__*/_jsx(FaSignOutAlt,{})}),!isCollapsed&&/*#__PURE__*/_jsx(\"span\",{className:\"logout-text\",style:{color:'white'},children:\"Logout\"})]})})]})]})]});}export default Sidebar;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "NavLink", "FaGamepad", "FaUsers", "FaCog", "FaChartBar", "FaMoneyBill", "FaTrophy", "FaHome", "FaUserPlus", "FaCreditCard", "FaFutbol", "FaTasks", "FaCoins", "FaCalendarAlt", "FaUserFriends", "FaCashRegister", "FaExchangeAlt", "FaMedal", "FaWrench", "FaChartLine", "FaShieldAlt", "FaSignOutAlt", "FaDice", "FaBars", "FaTimes", "useSiteConfig", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "Sidebar", "navigate", "config", "isCollapsed", "setIsCollapsed", "isMobile", "setIsMobile", "checkScreenSize", "screenWidth", "window", "innerWidth", "isMobileScreen", "addEventListener", "removeEventListener", "toggleSidebar", "menuItems", "link", "text", "icon", "children", "className", "onClick", "site_logo", "src", "alt", "site_name", "onError", "e", "target", "style", "display", "nextS<PERSON>ling", "map", "item", "index", "to", "_ref", "isActive", "end", "title", "backgroundColor", "color"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/components/Sidebar.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate, NavLink } from 'react-router-dom';\nimport {\n    FaGamepad,\n    FaUsers,\n    FaCog,\n    FaChartBar,\n    FaMoneyBill,\n    FaTrophy,\n    FaHome,\n    FaUserPlus,\n    FaCreditCard,\n    FaFutbol,\n    FaTasks,\n    FaCoins,\n    FaCalendarAlt,\n    FaUserFriends,\n    FaCashRegister,\n    FaExchangeAlt,\n    FaMedal,\n    FaWrench,\n    FaChartLine,\n    FaShieldAlt,\n    FaSignOutAlt,\n    FaDice,\n    FaBars,\n    FaTimes\n} from 'react-icons/fa';\nimport './AdminSidebar.css';\nimport { useSiteConfig } from '../contexts/SiteConfigContext';\n\nfunction Sidebar() {\n    const navigate = useNavigate();\n    const { config } = useSiteConfig();\n    const [isCollapsed, setIsCollapsed] = useState(false);\n    const [isMobile, setIsMobile] = useState(false);\n\n    // Check screen size and set mobile state\n    useEffect(() => {\n        const checkScreenSize = () => {\n            const screenWidth = window.innerWidth;\n            const isMobileScreen = screenWidth <= 1024; // 13 inches and smaller\n            setIsMobile(isMobileScreen);\n\n            // Auto-collapse on small screens\n            if (isMobileScreen) {\n                setIsCollapsed(true);\n            }\n        };\n\n        checkScreenSize();\n        window.addEventListener('resize', checkScreenSize);\n        return () => window.removeEventListener('resize', checkScreenSize);\n    }, []);\n\n    const toggleSidebar = () => {\n        setIsCollapsed(!isCollapsed);\n    };\n\n    // Define all menu items as a flat list\n    const menuItems = [\n        { link: '/admin/dashboard', text: 'Overview', icon: <FaHome /> },\n\n        // Challenges\n        { link: '/admin/challenge-system', text: 'Challenge System', icon: <FaFutbol /> },\n        { link: '/admin/challenge-management', text: 'Challenge Management', icon: <FaTasks /> },\n        { link: '/admin/credit-challenge', text: 'Credit Challenge', icon: <FaCoins /> },\n        { link: '/admin/team-management', text: 'Team Management', icon: <FaShieldAlt /> },\n        { link: '/admin/bets', text: 'Bet Management', icon: <FaDice /> },\n\n        // Users\n        { link: '/admin/users', text: 'User Management', icon: <FaUsers /> },\n        { link: '/admin/add-user', text: 'Add User', icon: <FaUserPlus /> },\n        { link: '/admin/credit-user', text: 'Credit User', icon: <FaCreditCard /> },\n\n        // Leagues\n        { link: '/admin/league-management', text: 'League Management', icon: <FaTrophy /> },\n        { link: '/admin/league-seasons', text: 'Season Management', icon: <FaCalendarAlt /> },\n        { link: '/admin/league-users', text: 'League Users', icon: <FaUserFriends /> },\n\n        // Finance\n        { link: '/admin/payment-methods', text: 'Payment Methods', icon: <FaCashRegister /> },\n        { link: '/admin/transactions', text: 'Transactions', icon: <FaExchangeAlt /> },\n\n        // System\n        { link: '/admin/leaderboard', text: 'Leaderboard Management', icon: <FaMedal /> },\n        { link: '/admin/settings', text: 'System Settings', icon: <FaWrench /> },\n        { link: '/admin/security-settings', text: 'Security Settings', icon: <FaShieldAlt /> },\n        { link: '/admin/2fa-settings', text: '2FA Settings', icon: <FaShieldAlt /> },\n        { link: '/admin/reports', text: 'Reports and Analytics', icon: <FaChartLine /> }\n    ];\n\n    return (\n        <>\n            {/* Mobile Toggle Button */}\n            {isMobile && (\n                <button\n                    className=\"mobile-sidebar-toggle\"\n                    onClick={toggleSidebar}\n                    aria-label=\"Toggle sidebar\"\n                >\n                    {isCollapsed ? <FaBars /> : <FaTimes />}\n                </button>\n            )}\n\n            <div className={`admin-sidebar ${isCollapsed ? 'collapsed' : ''} ${isMobile ? 'mobile' : ''}`}>\n                <div className=\"sidebar-header\">\n                    {/* Desktop Toggle Button */}\n                    {!isMobile && (\n                        <button\n                            className=\"desktop-sidebar-toggle\"\n                            onClick={toggleSidebar}\n                            aria-label=\"Toggle sidebar\"\n                        >\n                            {isCollapsed ? <FaBars /> : <FaTimes />}\n                        </button>\n                    )}\n\n                    <div className=\"logo\">\n                        {!isCollapsed && config.site_logo ? (\n                            <img\n                                src={`/backend/${config.site_logo}`}\n                                alt={config.site_name || \"Site Logo\"}\n                                className=\"logo-icon\"\n                                onError={(e) => {\n                                    e.target.style.display = 'none';\n                                    e.target.nextSibling.style.display = 'block';\n                                }}\n                            />\n                        ) : !isCollapsed ? (\n                            <span className=\"logo-text\">{config.site_name || \"FanBet247\"}</span>\n                        ) : (\n                            <span className=\"logo-collapsed\">F</span>\n                        )}\n                    </div>\n                </div>\n\n                <nav className=\"admin-sidebar-nav simple-menu\">\n                    {menuItems.map((item, index) => (\n                        <NavLink\n                            key={index}\n                            to={item.link}\n                            className={({ isActive }) =>\n                                `simple-nav-item ${isActive ? 'active' : ''}`\n                            }\n                            end\n                            title={isCollapsed ? item.text : ''}\n                        >\n                            <span className=\"simple-nav-item-icon\">{item.icon}</span>\n                            {!isCollapsed && <span className=\"simple-nav-item-text\">{item.text}</span>}\n                        </NavLink>\n                    ))}\n\n                    {/* Logout button at bottom of sidebar */}\n                    <div className=\"sidebar-footer\">\n                        <button\n                            className=\"logout-button\"\n                            onClick={() => navigate('/admin/login')}\n                            style={{ backgroundColor: '#dc2626', color: 'white' }}\n                            title={isCollapsed ? 'Logout' : ''}\n                        >\n                            <span className=\"logout-icon\" style={{ color: 'white' }}>\n                                <FaSignOutAlt />\n                            </span>\n                            {!isCollapsed && <span className=\"logout-text\" style={{ color: 'white' }}>Logout</span>}\n                        </button>\n                    </div>\n                </nav>\n            </div>\n        </>\n    );\n}\n\nexport default Sidebar;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,WAAW,CAAEC,OAAO,KAAQ,kBAAkB,CACvD,OACIC,SAAS,CACTC,OAAO,CACPC,KAAK,CACLC,UAAU,CACVC,WAAW,CACXC,QAAQ,CACRC,MAAM,CACNC,UAAU,CACVC,YAAY,CACZC,QAAQ,CACRC,OAAO,CACPC,OAAO,CACPC,aAAa,CACbC,aAAa,CACbC,cAAc,CACdC,aAAa,CACbC,OAAO,CACPC,QAAQ,CACRC,WAAW,CACXC,WAAW,CACXC,YAAY,CACZC,MAAM,CACNC,MAAM,CACNC,OAAO,KACJ,gBAAgB,CACvB,MAAO,oBAAoB,CAC3B,OAASC,aAAa,KAAQ,+BAA+B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAE9D,QAAS,CAAAC,OAAOA,CAAA,CAAG,CACf,KAAM,CAAAC,QAAQ,CAAGlC,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAEmC,MAAO,CAAC,CAAGT,aAAa,CAAC,CAAC,CAClC,KAAM,CAACU,WAAW,CAAEC,cAAc,CAAC,CAAGvC,QAAQ,CAAC,KAAK,CAAC,CACrD,KAAM,CAACwC,QAAQ,CAAEC,WAAW,CAAC,CAAGzC,QAAQ,CAAC,KAAK,CAAC,CAE/C;AACAC,SAAS,CAAC,IAAM,CACZ,KAAM,CAAAyC,eAAe,CAAGA,CAAA,GAAM,CAC1B,KAAM,CAAAC,WAAW,CAAGC,MAAM,CAACC,UAAU,CACrC,KAAM,CAAAC,cAAc,CAAGH,WAAW,EAAI,IAAI,CAAE;AAC5CF,WAAW,CAACK,cAAc,CAAC,CAE3B;AACA,GAAIA,cAAc,CAAE,CAChBP,cAAc,CAAC,IAAI,CAAC,CACxB,CACJ,CAAC,CAEDG,eAAe,CAAC,CAAC,CACjBE,MAAM,CAACG,gBAAgB,CAAC,QAAQ,CAAEL,eAAe,CAAC,CAClD,MAAO,IAAME,MAAM,CAACI,mBAAmB,CAAC,QAAQ,CAAEN,eAAe,CAAC,CACtE,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAO,aAAa,CAAGA,CAAA,GAAM,CACxBV,cAAc,CAAC,CAACD,WAAW,CAAC,CAChC,CAAC,CAED;AACA,KAAM,CAAAY,SAAS,CAAG,CACd,CAAEC,IAAI,CAAE,kBAAkB,CAAEC,IAAI,CAAE,UAAU,CAAEC,IAAI,cAAEvB,IAAA,CAACpB,MAAM,GAAE,CAAE,CAAC,CAEhE;AACA,CAAEyC,IAAI,CAAE,yBAAyB,CAAEC,IAAI,CAAE,kBAAkB,CAAEC,IAAI,cAAEvB,IAAA,CAACjB,QAAQ,GAAE,CAAE,CAAC,CACjF,CAAEsC,IAAI,CAAE,6BAA6B,CAAEC,IAAI,CAAE,sBAAsB,CAAEC,IAAI,cAAEvB,IAAA,CAAChB,OAAO,GAAE,CAAE,CAAC,CACxF,CAAEqC,IAAI,CAAE,yBAAyB,CAAEC,IAAI,CAAE,kBAAkB,CAAEC,IAAI,cAAEvB,IAAA,CAACf,OAAO,GAAE,CAAE,CAAC,CAChF,CAAEoC,IAAI,CAAE,wBAAwB,CAAEC,IAAI,CAAE,iBAAiB,CAAEC,IAAI,cAAEvB,IAAA,CAACP,WAAW,GAAE,CAAE,CAAC,CAClF,CAAE4B,IAAI,CAAE,aAAa,CAAEC,IAAI,CAAE,gBAAgB,CAAEC,IAAI,cAAEvB,IAAA,CAACL,MAAM,GAAE,CAAE,CAAC,CAEjE;AACA,CAAE0B,IAAI,CAAE,cAAc,CAAEC,IAAI,CAAE,iBAAiB,CAAEC,IAAI,cAAEvB,IAAA,CAACzB,OAAO,GAAE,CAAE,CAAC,CACpE,CAAE8C,IAAI,CAAE,iBAAiB,CAAEC,IAAI,CAAE,UAAU,CAAEC,IAAI,cAAEvB,IAAA,CAACnB,UAAU,GAAE,CAAE,CAAC,CACnE,CAAEwC,IAAI,CAAE,oBAAoB,CAAEC,IAAI,CAAE,aAAa,CAAEC,IAAI,cAAEvB,IAAA,CAAClB,YAAY,GAAE,CAAE,CAAC,CAE3E;AACA,CAAEuC,IAAI,CAAE,0BAA0B,CAAEC,IAAI,CAAE,mBAAmB,CAAEC,IAAI,cAAEvB,IAAA,CAACrB,QAAQ,GAAE,CAAE,CAAC,CACnF,CAAE0C,IAAI,CAAE,uBAAuB,CAAEC,IAAI,CAAE,mBAAmB,CAAEC,IAAI,cAAEvB,IAAA,CAACd,aAAa,GAAE,CAAE,CAAC,CACrF,CAAEmC,IAAI,CAAE,qBAAqB,CAAEC,IAAI,CAAE,cAAc,CAAEC,IAAI,cAAEvB,IAAA,CAACb,aAAa,GAAE,CAAE,CAAC,CAE9E;AACA,CAAEkC,IAAI,CAAE,wBAAwB,CAAEC,IAAI,CAAE,iBAAiB,CAAEC,IAAI,cAAEvB,IAAA,CAACZ,cAAc,GAAE,CAAE,CAAC,CACrF,CAAEiC,IAAI,CAAE,qBAAqB,CAAEC,IAAI,CAAE,cAAc,CAAEC,IAAI,cAAEvB,IAAA,CAACX,aAAa,GAAE,CAAE,CAAC,CAE9E;AACA,CAAEgC,IAAI,CAAE,oBAAoB,CAAEC,IAAI,CAAE,wBAAwB,CAAEC,IAAI,cAAEvB,IAAA,CAACV,OAAO,GAAE,CAAE,CAAC,CACjF,CAAE+B,IAAI,CAAE,iBAAiB,CAAEC,IAAI,CAAE,iBAAiB,CAAEC,IAAI,cAAEvB,IAAA,CAACT,QAAQ,GAAE,CAAE,CAAC,CACxE,CAAE8B,IAAI,CAAE,0BAA0B,CAAEC,IAAI,CAAE,mBAAmB,CAAEC,IAAI,cAAEvB,IAAA,CAACP,WAAW,GAAE,CAAE,CAAC,CACtF,CAAE4B,IAAI,CAAE,qBAAqB,CAAEC,IAAI,CAAE,cAAc,CAAEC,IAAI,cAAEvB,IAAA,CAACP,WAAW,GAAE,CAAE,CAAC,CAC5E,CAAE4B,IAAI,CAAE,gBAAgB,CAAEC,IAAI,CAAE,uBAAuB,CAAEC,IAAI,cAAEvB,IAAA,CAACR,WAAW,GAAE,CAAE,CAAC,CACnF,CAED,mBACIU,KAAA,CAAAE,SAAA,EAAAoB,QAAA,EAEKd,QAAQ,eACLV,IAAA,WACIyB,SAAS,CAAC,uBAAuB,CACjCC,OAAO,CAAEP,aAAc,CACvB,aAAW,gBAAgB,CAAAK,QAAA,CAE1BhB,WAAW,cAAGR,IAAA,CAACJ,MAAM,GAAE,CAAC,cAAGI,IAAA,CAACH,OAAO,GAAE,CAAC,CACnC,CACX,cAEDK,KAAA,QAAKuB,SAAS,CAAE,iBAAiBjB,WAAW,CAAG,WAAW,CAAG,EAAE,IAAIE,QAAQ,CAAG,QAAQ,CAAG,EAAE,EAAG,CAAAc,QAAA,eAC1FtB,KAAA,QAAKuB,SAAS,CAAC,gBAAgB,CAAAD,QAAA,EAE1B,CAACd,QAAQ,eACNV,IAAA,WACIyB,SAAS,CAAC,wBAAwB,CAClCC,OAAO,CAAEP,aAAc,CACvB,aAAW,gBAAgB,CAAAK,QAAA,CAE1BhB,WAAW,cAAGR,IAAA,CAACJ,MAAM,GAAE,CAAC,cAAGI,IAAA,CAACH,OAAO,GAAE,CAAC,CACnC,CACX,cAEDG,IAAA,QAAKyB,SAAS,CAAC,MAAM,CAAAD,QAAA,CAChB,CAAChB,WAAW,EAAID,MAAM,CAACoB,SAAS,cAC7B3B,IAAA,QACI4B,GAAG,CAAE,YAAYrB,MAAM,CAACoB,SAAS,EAAG,CACpCE,GAAG,CAAEtB,MAAM,CAACuB,SAAS,EAAI,WAAY,CACrCL,SAAS,CAAC,WAAW,CACrBM,OAAO,CAAGC,CAAC,EAAK,CACZA,CAAC,CAACC,MAAM,CAACC,KAAK,CAACC,OAAO,CAAG,MAAM,CAC/BH,CAAC,CAACC,MAAM,CAACG,WAAW,CAACF,KAAK,CAACC,OAAO,CAAG,OAAO,CAChD,CAAE,CACL,CAAC,CACF,CAAC3B,WAAW,cACZR,IAAA,SAAMyB,SAAS,CAAC,WAAW,CAAAD,QAAA,CAAEjB,MAAM,CAACuB,SAAS,EAAI,WAAW,CAAO,CAAC,cAEpE9B,IAAA,SAAMyB,SAAS,CAAC,gBAAgB,CAAAD,QAAA,CAAC,GAAC,CAAM,CAC3C,CACA,CAAC,EACL,CAAC,cAENtB,KAAA,QAAKuB,SAAS,CAAC,+BAA+B,CAAAD,QAAA,EACzCJ,SAAS,CAACiB,GAAG,CAAC,CAACC,IAAI,CAAEC,KAAK,gBACvBrC,KAAA,CAAC7B,OAAO,EAEJmE,EAAE,CAAEF,IAAI,CAACjB,IAAK,CACdI,SAAS,CAAEgB,IAAA,MAAC,CAAEC,QAAS,CAAC,CAAAD,IAAA,OACpB,mBAAmBC,QAAQ,CAAG,QAAQ,CAAG,EAAE,EAAE,EAChD,CACDC,GAAG,MACHC,KAAK,CAAEpC,WAAW,CAAG8B,IAAI,CAAChB,IAAI,CAAG,EAAG,CAAAE,QAAA,eAEpCxB,IAAA,SAAMyB,SAAS,CAAC,sBAAsB,CAAAD,QAAA,CAAEc,IAAI,CAACf,IAAI,CAAO,CAAC,CACxD,CAACf,WAAW,eAAIR,IAAA,SAAMyB,SAAS,CAAC,sBAAsB,CAAAD,QAAA,CAAEc,IAAI,CAAChB,IAAI,CAAO,CAAC,GATrEiB,KAUA,CACZ,CAAC,cAGFvC,IAAA,QAAKyB,SAAS,CAAC,gBAAgB,CAAAD,QAAA,cAC3BtB,KAAA,WACIuB,SAAS,CAAC,eAAe,CACzBC,OAAO,CAAEA,CAAA,GAAMpB,QAAQ,CAAC,cAAc,CAAE,CACxC4B,KAAK,CAAE,CAAEW,eAAe,CAAE,SAAS,CAAEC,KAAK,CAAE,OAAQ,CAAE,CACtDF,KAAK,CAAEpC,WAAW,CAAG,QAAQ,CAAG,EAAG,CAAAgB,QAAA,eAEnCxB,IAAA,SAAMyB,SAAS,CAAC,aAAa,CAACS,KAAK,CAAE,CAAEY,KAAK,CAAE,OAAQ,CAAE,CAAAtB,QAAA,cACpDxB,IAAA,CAACN,YAAY,GAAE,CAAC,CACd,CAAC,CACN,CAACc,WAAW,eAAIR,IAAA,SAAMyB,SAAS,CAAC,aAAa,CAACS,KAAK,CAAE,CAAEY,KAAK,CAAE,OAAQ,CAAE,CAAAtB,QAAA,CAAC,QAAM,CAAM,CAAC,EACnF,CAAC,CACR,CAAC,EACL,CAAC,EACL,CAAC,EACR,CAAC,CAEX,CAEA,cAAe,CAAAnB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}