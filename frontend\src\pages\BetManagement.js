import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { Fa<PERSON><PERSON><PERSON>, <PERSON>a<PERSON><PERSON><PERSON>, <PERSON>a<PERSON>ye, FaEdit, FaTrash, FaSort, FaDownload } from 'react-icons/fa';
import './BetManagement.css';

const API_BASE_URL = '/backend';

function BetManagement() {
    const [teams, setTeams] = useState([]);
    const [bets, setBets] = useState([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState('');
    const [success, setSuccess] = useState('');

    // Filters and pagination
    const [filters, setFilters] = useState({
        search: '',
        status: '',
        dateFrom: '',
        dateTo: '',
        sortBy: 'created_at',
        order: 'DESC',
        limit: 20
    });

    const [pagination, setPagination] = useState({
        currentPage: 1,
        totalPages: 1,
        totalItems: 0,
        itemsPerPage: 20
    });

    // Modal states
    const [showBetModal, setShowBetModal] = useState(false);
    const [selectedBet, setSelectedBet] = useState(null);

    useEffect(() => {
        fetchTeams();
        fetchAllBets();
    }, [pagination.currentPage, filters]);

    const fetchTeams = async () => {
        try {
            const response = await axios.get(`${API_BASE_URL}/handlers/team_management.php`);
            if (response.data.status === 200) {
                setTeams(response.data.data);
            }
        } catch (err) {
            console.error('Error fetching teams:', err);
        }
    };

    const fetchAllBets = async () => {
        setLoading(true);
        setError('');
        try {
            const params = new URLSearchParams({
                page: pagination.currentPage,
                limit: filters.limit,
                sortBy: filters.sortBy,
                order: filters.order,
                ...(filters.search && { search: filters.search }),
                ...(filters.status && { status: filters.status }),
                ...(filters.dateFrom && { dateFrom: filters.dateFrom }),
                ...(filters.dateTo && { dateTo: filters.dateTo })
            });

            const response = await axios.get(`${API_BASE_URL}/handlers/get_all_bets.php?${params}`);
            if (response.data.success) {
                const processedBets = response.data.bets.map(bet => ({
                    ...bet,
                    user1_pick: getTeamNameFromChoice(bet.bet_choice_user1, bet.team_a, bet.team_b),
                    user2_pick: getTeamNameFromChoice(bet.bet_choice_user2, bet.team_a, bet.team_b)
                }));
                setBets(processedBets);
                setPagination(response.data.pagination || pagination);
            } else {
                setError(response.data.message || 'Failed to fetch bets');
            }
        } catch (err) {
            setError('Failed to fetch bets data');
            console.error('Error fetching bets:', err);
        } finally {
            setLoading(false);
        }
    };

    const getTeamNameFromChoice = (choice, teamA, teamB) => {
        switch (choice) {
            case 'team_a_win':
                return teamA;
            case 'team_b_win':
                return teamB;
            case 'draw':
                return 'Draw';
            default:
                return choice || 'Unknown';
        }
    };

    const handleFilterChange = (key, value) => {
        setFilters(prev => ({ ...prev, [key]: value }));
        setPagination(prev => ({ ...prev, currentPage: 1 }));
    };

    const handleSort = (column) => {
        const newOrder = filters.sortBy === column && filters.order === 'DESC' ? 'ASC' : 'DESC';
        setFilters(prev => ({ ...prev, sortBy: column, order: newOrder }));
    };

    const handlePageChange = (newPage) => {
        setPagination(prev => ({ ...prev, currentPage: newPage }));
    };

    const viewBetDetails = (bet) => {
        setSelectedBet(bet);
        setShowBetModal(true);
    };

    const exportBets = async (format = 'csv') => {
        try {
            const params = new URLSearchParams({
                format,
                ...filters
            });

            const response = await axios.get(`${API_BASE_URL}/handlers/export_bets.php?${params}`, {
                responseType: 'blob'
            });

            const url = window.URL.createObjectURL(new Blob([response.data]));
            const link = document.createElement('a');
            link.href = url;
            link.setAttribute('download', `bets_export_${new Date().toISOString().split('T')[0]}.${format}`);
            document.body.appendChild(link);
            link.click();
            link.remove();
            window.URL.revokeObjectURL(url);
        } catch (err) {
            setError('Failed to export bets');
        }
    };

    const getTeamLogo = (teamName) => {
        const team = teams.find(team => team.name === teamName);
        return team ? `${API_BASE_URL}/${team.logo}` : '';
    };

    const getStatusColor = (status) => {
        switch (status) {
            case 'open':
                return 'bg-yellow-100 text-yellow-800';
            case 'joined':
                return 'bg-blue-100 text-blue-800';
            case 'completed':
                return 'bg-green-100 text-green-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };

    return (
        <div className="admin-container">
            <div className="flex justify-between items-center mb-6">
                <div>
                    <h1 className="text-2xl font-bold text-gray-800 mb-2">Bet Management</h1>
                    <p className="admin-description">
                        Manage all betting activities, view bet details, and monitor betting statistics across the platform.
                    </p>
                </div>
                <button
                    onClick={() => exportBets('csv')}
                    disabled={loading}
                    className="flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                    <FaDownload className="mr-2" />
                    Export CSV
                </button>
            </div>

            {error && (
                <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                    {error}
                </div>
            )}

            {success && (
                <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                    {success}
                </div>
            )}

            {/* Filters */}
            <div className="bg-white rounded-lg shadow-md p-6 mb-6">
                <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Search</label>
                        <div className="relative">
                            <FaSearch className="absolute left-3 top-3 text-gray-400" />
                            <input
                                type="text"
                                placeholder="Search by reference, user..."
                                value={filters.search}
                                onChange={(e) => handleFilterChange('search', e.target.value)}
                                className="pl-10 w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            />
                        </div>
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
                        <select
                            value={filters.status}
                            onChange={(e) => handleFilterChange('status', e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        >
                            <option value="">All Statuses</option>
                            <option value="open">Open</option>
                            <option value="joined">Joined</option>
                            <option value="completed">Completed</option>
                        </select>
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">From Date</label>
                        <input
                            type="date"
                            value={filters.dateFrom}
                            onChange={(e) => handleFilterChange('dateFrom', e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        />
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">To Date</label>
                        <input
                            type="date"
                            value={filters.dateTo}
                            onChange={(e) => handleFilterChange('dateTo', e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        />
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Items per Page</label>
                        <select
                            value={filters.limit}
                            onChange={(e) => handleFilterChange('limit', parseInt(e.target.value))}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        >
                            <option value={10}>10</option>
                            <option value={20}>20</option>
                            <option value={50}>50</option>
                            <option value={100}>100</option>
                        </select>
                    </div>
                </div>
            </div>

            {/* Bets Table */}
            <div className="bg-white rounded-lg shadow-md overflow-hidden">
                <div className="overflow-x-auto">
                    <table className="w-full">
                        <thead className="bg-gray-50">
                            <tr>
                                <th
                                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                                    onClick={() => handleSort('bet_id')}
                                >
                                    <div className="flex items-center">
                                        Reference
                                        <FaSort className="ml-1" />
                                    </div>
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Match
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden lg:table-cell">
                                    User 1
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden lg:table-cell">
                                    User 2
                                </th>
                                <th
                                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 hidden md:table-cell"
                                    onClick={() => handleSort('amount_user1')}
                                >
                                    <div className="flex items-center">
                                        Amounts
                                        <FaSort className="ml-1" />
                                    </div>
                                </th>
                                <th
                                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                                    onClick={() => handleSort('bet_status')}
                                >
                                    <div className="flex items-center">
                                        Status
                                        <FaSort className="ml-1" />
                                    </div>
                                </th>
                                <th
                                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 hidden sm:table-cell"
                                    onClick={() => handleSort('created_at')}
                                >
                                    <div className="flex items-center">
                                        Date
                                        <FaSort className="ml-1" />
                                    </div>
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Actions
                                </th>
                            </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                            {loading ? (
                                <tr>
                                    <td colSpan="8" className="px-6 py-4 text-center">
                                        <div className="flex justify-center items-center">
                                            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                                            <span className="ml-2">Loading...</span>
                                        </div>
                                    </td>
                                </tr>
                            ) : bets.length === 0 ? (
                                <tr>
                                    <td colSpan="8" className="px-6 py-4 text-center text-gray-500">
                                        No bets found
                                    </td>
                                </tr>
                            ) : (
                                bets.map((bet) => (
                                    <tr key={bet.bet_id} className="hover:bg-gray-50">
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <div className="text-sm font-medium text-gray-900">
                                                {bet.unique_code?.toUpperCase() || `${bet.bet_id}DNRBKCC`}
                                            </div>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <div className="flex items-center space-x-2">
                                                <div className="flex items-center space-x-1">
                                                    <img
                                                        src={getTeamLogo(bet.team_a)}
                                                        alt={bet.team_a}
                                                        className="w-6 h-6 rounded-full object-contain"
                                                        onError={(e) => {
                                                            e.target.style.display = 'none';
                                                        }}
                                                    />
                                                    <span className="text-sm text-gray-900">{bet.team_a}</span>
                                                </div>
                                                <span className="text-gray-500 text-sm">vs</span>
                                                <div className="flex items-center space-x-1">
                                                    <img
                                                        src={getTeamLogo(bet.team_b)}
                                                        alt={bet.team_b}
                                                        className="w-6 h-6 rounded-full object-contain"
                                                        onError={(e) => {
                                                            e.target.style.display = 'none';
                                                        }}
                                                    />
                                                    <span className="text-sm text-gray-900">{bet.team_b}</span>
                                                </div>
                                            </div>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap hidden lg:table-cell">
                                            <div>
                                                <div className="text-sm font-medium text-gray-900">{bet.user1_name}</div>
                                                <div className="text-sm text-gray-500">Pick: {bet.user1_pick}</div>
                                            </div>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap hidden lg:table-cell">
                                            <div>
                                                <div className="text-sm font-medium text-gray-900">
                                                    {bet.user2_name || 'Waiting...'}
                                                </div>
                                                {bet.user2_name && (
                                                    <div className="text-sm text-gray-500">Pick: {bet.user2_pick}</div>
                                                )}
                                            </div>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap hidden md:table-cell">
                                            <div>
                                                <div className="text-sm text-gray-900">{bet.amount_user1} FC</div>
                                                {bet.amount_user2 && (
                                                    <div className="text-sm text-gray-500">{bet.amount_user2} FC</div>
                                                )}
                                            </div>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(bet.bet_status)}`}>
                                                {bet.bet_status}
                                            </span>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 hidden sm:table-cell">
                                            {new Date(bet.created_at).toLocaleDateString()}
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <button
                                                onClick={() => viewBetDetails(bet)}
                                                className="text-blue-600 hover:text-blue-900"
                                                title="View Details"
                                            >
                                                <FaEye />
                                            </button>
                                        </td>
                                    </tr>
                                ))
                            )}
                        </tbody>
                    </table>
                </div>
            </div>

            {/* Modern Sports Betting Card Modal */}
            {showBetModal && selectedBet && (
                <div className="fixed inset-0 bg-black bg-opacity-60 overflow-y-auto h-full w-full z-50 flex items-center justify-center p-4">
                    <div className="relative bg-white rounded-2xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
                        {/* Header */}
                        <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6 rounded-t-2xl">
                            <div className="flex justify-between items-center">
                                <div>
                                    <h3 className="text-xl font-bold">Bet Details</h3>
                                    <p className="text-blue-100 text-sm">
                                        Ref: {selectedBet.unique_code?.toUpperCase() || `${selectedBet.bet_id}DNRBKCC`}
                                    </p>
                                </div>
                                <button
                                    onClick={() => setShowBetModal(false)}
                                    className="text-white hover:text-gray-200 text-2xl font-bold w-8 h-8 flex items-center justify-center rounded-full hover:bg-white hover:bg-opacity-20 transition-all"
                                >
                                    ×
                                </button>
                            </div>
                        </div>
                        {/* Match Card */}
                        <div className="p-6">
                            {/* Status Badge */}
                            <div className="flex justify-between items-center mb-6">
                                <span className={`inline-flex px-4 py-2 text-sm font-semibold rounded-full ${getStatusColor(selectedBet.bet_status)}`}>
                                    {selectedBet.bet_status.toUpperCase()}
                                </span>
                                <div className="text-right text-sm text-gray-500">
                                    <p>Created: {new Date(selectedBet.created_at).toLocaleDateString()}</p>
                                    <p>{new Date(selectedBet.created_at).toLocaleTimeString()}</p>
                                </div>
                            </div>

                            {/* Teams VS Section */}
                            <div className="bg-gradient-to-r from-gray-50 to-gray-100 rounded-xl p-6 mb-6">
                                <div className="flex items-center justify-center space-x-8">
                                    {/* Team A */}
                                    <div className="text-center flex-1">
                                        <div className="mb-3">
                                            <img
                                                src={getTeamLogo(selectedBet.team_a)}
                                                alt={selectedBet.team_a}
                                                className="w-16 h-16 mx-auto rounded-full object-contain border-2 border-white shadow-lg"
                                                onError={(e) => {
                                                    e.target.style.display = 'none';
                                                }}
                                            />
                                        </div>
                                        <h4 className="font-bold text-lg text-gray-800">{selectedBet.team_a}</h4>
                                    </div>

                                    {/* VS Divider */}
                                    <div className="text-center">
                                        <div className="bg-blue-600 text-white rounded-full w-12 h-12 flex items-center justify-center font-bold text-sm shadow-lg">
                                            VS
                                        </div>
                                        {selectedBet.match_date && (
                                            <p className="text-xs text-gray-500 mt-2">
                                                {new Date(selectedBet.match_date).toLocaleDateString()}
                                            </p>
                                        )}
                                    </div>

                                    {/* Team B */}
                                    <div className="text-center flex-1">
                                        <div className="mb-3">
                                            <img
                                                src={getTeamLogo(selectedBet.team_b)}
                                                alt={selectedBet.team_b}
                                                className="w-16 h-16 mx-auto rounded-full object-contain border-2 border-white shadow-lg"
                                                onError={(e) => {
                                                    e.target.style.display = 'none';
                                                }}
                                            />
                                        </div>
                                        <h4 className="font-bold text-lg text-gray-800">{selectedBet.team_b}</h4>
                                    </div>
                                </div>
                            </div>

                            {/* Betting Information Cards */}
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                                {/* User 1 Card */}
                                <div className="bg-green-50 border border-green-200 rounded-xl p-4">
                                    <div className="flex items-center justify-between mb-3">
                                        <h4 className="font-semibold text-green-800">Bet Creator</h4>
                                        <span className="bg-green-600 text-white px-2 py-1 rounded-full text-xs font-medium">
                                            USER 1
                                        </span>
                                    </div>
                                    <div className="space-y-2">
                                        <div className="flex justify-between">
                                            <span className="text-sm text-gray-600">Player:</span>
                                            <span className="text-sm font-medium text-gray-900">{selectedBet.user1_name}</span>
                                        </div>
                                        <div className="flex justify-between">
                                            <span className="text-sm text-gray-600">Pick:</span>
                                            <span className="text-sm font-medium text-green-700">{selectedBet.user1_pick}</span>
                                        </div>
                                        <div className="flex justify-between">
                                            <span className="text-sm text-gray-600">Stake:</span>
                                            <span className="text-sm font-bold text-green-800">{selectedBet.amount_user1} FC</span>
                                        </div>
                                        <div className="flex justify-between">
                                            <span className="text-sm text-gray-600">Odds:</span>
                                            <span className="text-sm font-medium text-gray-900">{selectedBet.odds_user1 || '1.8'}</span>
                                        </div>
                                        <div className="flex justify-between border-t border-green-200 pt-2">
                                            <span className="text-sm text-gray-600">Potential Win:</span>
                                            <span className="text-sm font-bold text-green-800">
                                                {selectedBet.potential_return_user1 || Math.round(selectedBet.amount_user1 * 1.8)} FC
                                            </span>
                                        </div>
                                    </div>
                                </div>

                                {/* User 2 Card */}
                                <div className={`${selectedBet.user2_name ? 'bg-blue-50 border-blue-200' : 'bg-gray-50 border-gray-200'} border rounded-xl p-4`}>
                                    <div className="flex items-center justify-between mb-3">
                                        <h4 className={`font-semibold ${selectedBet.user2_name ? 'text-blue-800' : 'text-gray-600'}`}>
                                            Bet Acceptor
                                        </h4>
                                        <span className={`${selectedBet.user2_name ? 'bg-blue-600 text-white' : 'bg-gray-400 text-white'} px-2 py-1 rounded-full text-xs font-medium`}>
                                            USER 2
                                        </span>
                                    </div>
                                    {selectedBet.user2_name ? (
                                        <div className="space-y-2">
                                            <div className="flex justify-between">
                                                <span className="text-sm text-gray-600">Player:</span>
                                                <span className="text-sm font-medium text-gray-900">{selectedBet.user2_name}</span>
                                            </div>
                                            <div className="flex justify-between">
                                                <span className="text-sm text-gray-600">Pick:</span>
                                                <span className="text-sm font-medium text-blue-700">{selectedBet.user2_pick}</span>
                                            </div>
                                            <div className="flex justify-between">
                                                <span className="text-sm text-gray-600">Stake:</span>
                                                <span className="text-sm font-bold text-blue-800">{selectedBet.amount_user2} FC</span>
                                            </div>
                                            <div className="flex justify-between">
                                                <span className="text-sm text-gray-600">Odds:</span>
                                                <span className="text-sm font-medium text-gray-900">{selectedBet.odds_user2 || '1.8'}</span>
                                            </div>
                                            <div className="flex justify-between border-t border-blue-200 pt-2">
                                                <span className="text-sm text-gray-600">Potential Win:</span>
                                                <span className="text-sm font-bold text-blue-800">
                                                    {selectedBet.potential_return_user2 || Math.round(selectedBet.amount_user2 * 1.8)} FC
                                                </span>
                                            </div>
                                        </div>
                                    ) : (
                                        <div className="text-center py-4">
                                            <div className="text-gray-400 mb-2">
                                                <svg className="w-8 h-8 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                                                    <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                                                </svg>
                                            </div>
                                            <p className="text-sm text-gray-500 italic">Waiting for another user to accept this bet...</p>
                                        </div>
                                    )}
                                </div>
                            </div>

                            {/* Match Result & Additional Info */}
                            {(selectedBet.result || selectedBet.match_date) && (
                                <div className="bg-gray-50 rounded-xl p-4">
                                    <h4 className="font-semibold text-gray-800 mb-3">Match Information</h4>
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        {selectedBet.match_date && (
                                            <div className="flex justify-between">
                                                <span className="text-sm text-gray-600">Match Date:</span>
                                                <span className="text-sm font-medium text-gray-900">
                                                    {new Date(selectedBet.match_date).toLocaleDateString()}
                                                </span>
                                            </div>
                                        )}
                                        {selectedBet.result && (
                                            <div className="flex justify-between">
                                                <span className="text-sm text-gray-600">Result:</span>
                                                <span className="text-sm font-medium text-gray-900 capitalize">
                                                    {selectedBet.result.replace('_', ' ')}
                                                </span>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            )}

                            {/* Action Buttons */}
                            <div className="flex justify-end pt-4 border-t border-gray-200">
                                <button
                                    onClick={() => setShowBetModal(false)}
                                    className="px-6 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors font-medium"
                                >
                                    Close
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
}

export default BetManagement;