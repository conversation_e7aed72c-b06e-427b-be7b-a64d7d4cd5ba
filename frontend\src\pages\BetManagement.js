import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { Fa<PERSON><PERSON><PERSON>, <PERSON>a<PERSON><PERSON><PERSON>, <PERSON>a<PERSON>ye, FaEdit, FaTrash, FaSort, FaDownload } from 'react-icons/fa';
import './BetManagement.css';

const API_BASE_URL = '/backend';

function BetManagement() {
    const [teams, setTeams] = useState([]);
    const [bets, setBets] = useState([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState('');
    const [success, setSuccess] = useState('');

    // Filters and pagination
    const [filters, setFilters] = useState({
        search: '',
        status: '',
        dateFrom: '',
        dateTo: '',
        sortBy: 'created_at',
        order: 'DESC',
        limit: 20
    });

    const [pagination, setPagination] = useState({
        currentPage: 1,
        totalPages: 1,
        totalItems: 0,
        itemsPerPage: 20
    });

    // Modal states
    const [showBetModal, setShowBetModal] = useState(false);
    const [selectedBet, setSelectedBet] = useState(null);

    useEffect(() => {
        fetchTeams();
        fetchAllBets();
    }, [pagination.currentPage, filters]);

    const fetchTeams = async () => {
        try {
            const response = await axios.get(`${API_BASE_URL}/handlers/team_management.php`);
            if (response.data.status === 200) {
                setTeams(response.data.data);
            }
        } catch (err) {
            console.error('Error fetching teams:', err);
        }
    };

    const fetchAllBets = async () => {
        setLoading(true);
        setError('');
        try {
            const params = new URLSearchParams({
                page: pagination.currentPage,
                limit: filters.limit,
                sortBy: filters.sortBy,
                order: filters.order,
                ...(filters.search && { search: filters.search }),
                ...(filters.status && { status: filters.status }),
                ...(filters.dateFrom && { dateFrom: filters.dateFrom }),
                ...(filters.dateTo && { dateTo: filters.dateTo })
            });

            const response = await axios.get(`${API_BASE_URL}/handlers/get_all_bets.php?${params}`);
            if (response.data.success) {
                const processedBets = response.data.bets.map(bet => ({
                    ...bet,
                    user1_pick: getTeamNameFromChoice(bet.bet_choice_user1, bet.team_a, bet.team_b),
                    user2_pick: getTeamNameFromChoice(bet.bet_choice_user2, bet.team_a, bet.team_b)
                }));
                setBets(processedBets);
                setPagination(response.data.pagination || pagination);
            } else {
                setError(response.data.message || 'Failed to fetch bets');
            }
        } catch (err) {
            setError('Failed to fetch bets data');
            console.error('Error fetching bets:', err);
        } finally {
            setLoading(false);
        }
    };

    const getTeamNameFromChoice = (choice, teamA, teamB) => {
        switch (choice) {
            case 'team_a_win':
                return teamA;
            case 'team_b_win':
                return teamB;
            case 'draw':
                return 'Draw';
            default:
                return choice || 'Unknown';
        }
    };

    const handleFilterChange = (key, value) => {
        setFilters(prev => ({ ...prev, [key]: value }));
        setPagination(prev => ({ ...prev, currentPage: 1 }));
    };

    const handleSort = (column) => {
        const newOrder = filters.sortBy === column && filters.order === 'DESC' ? 'ASC' : 'DESC';
        setFilters(prev => ({ ...prev, sortBy: column, order: newOrder }));
    };

    const handlePageChange = (newPage) => {
        setPagination(prev => ({ ...prev, currentPage: newPage }));
    };

    const viewBetDetails = (bet) => {
        setSelectedBet(bet);
        setShowBetModal(true);
    };

    const exportBets = async (format = 'csv') => {
        try {
            const params = new URLSearchParams({
                format,
                ...filters
            });

            const response = await axios.get(`${API_BASE_URL}/handlers/export_bets.php?${params}`, {
                responseType: 'blob'
            });

            const url = window.URL.createObjectURL(new Blob([response.data]));
            const link = document.createElement('a');
            link.href = url;
            link.setAttribute('download', `bets_export_${new Date().toISOString().split('T')[0]}.${format}`);
            document.body.appendChild(link);
            link.click();
            link.remove();
            window.URL.revokeObjectURL(url);
        } catch (err) {
            setError('Failed to export bets');
        }
    };

    const getTeamLogo = (teamName) => {
        const team = teams.find(team => team.name === teamName);
        return team ? `${API_BASE_URL}/${team.logo}` : '';
    };

    const getStatusColor = (status) => {
        switch (status) {
            case 'open':
                return 'bg-yellow-100 text-yellow-800';
            case 'joined':
                return 'bg-blue-100 text-blue-800';
            case 'completed':
                return 'bg-green-100 text-green-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };

    return (
        <div className="admin-container">
            <div className="flex justify-between items-center mb-6">
                <div>
                    <h1 className="text-2xl font-bold text-gray-800 mb-2">Bet Management</h1>
                    <p className="admin-description">
                        Manage all betting activities, view bet details, and monitor betting statistics across the platform.
                    </p>
                </div>
                <button
                    onClick={() => exportBets('csv')}
                    disabled={loading}
                    className="flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                    <FaDownload className="mr-2" />
                    Export CSV
                </button>
            </div>

            {error && (
                <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                    {error}
                </div>
            )}

            {success && (
                <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                    {success}
                </div>
            )}

            {/* Filters */}
            <div className="bg-white rounded-lg shadow-md p-6 mb-6">
                <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Search</label>
                        <div className="relative">
                            <FaSearch className="absolute left-3 top-3 text-gray-400" />
                            <input
                                type="text"
                                placeholder="Search by reference, user..."
                                value={filters.search}
                                onChange={(e) => handleFilterChange('search', e.target.value)}
                                className="pl-10 w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            />
                        </div>
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
                        <select
                            value={filters.status}
                            onChange={(e) => handleFilterChange('status', e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        >
                            <option value="">All Statuses</option>
                            <option value="open">Open</option>
                            <option value="joined">Joined</option>
                            <option value="completed">Completed</option>
                        </select>
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">From Date</label>
                        <input
                            type="date"
                            value={filters.dateFrom}
                            onChange={(e) => handleFilterChange('dateFrom', e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        />
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">To Date</label>
                        <input
                            type="date"
                            value={filters.dateTo}
                            onChange={(e) => handleFilterChange('dateTo', e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        />
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Items per Page</label>
                        <select
                            value={filters.limit}
                            onChange={(e) => handleFilterChange('limit', parseInt(e.target.value))}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        >
                            <option value={10}>10</option>
                            <option value={20}>20</option>
                            <option value={50}>50</option>
                            <option value={100}>100</option>
                        </select>
                    </div>
                </div>
            </div>

            {/* Bets Table */}
            <div className="bg-white rounded-lg shadow-md overflow-hidden">
                <div className="overflow-x-auto">
                    <table className="w-full">
                        <thead className="bg-gray-50">
                            <tr>
                                <th
                                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                                    onClick={() => handleSort('bet_id')}
                                >
                                    <div className="flex items-center">
                                        Reference
                                        <FaSort className="ml-1" />
                                    </div>
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Match
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    User 1
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    User 2
                                </th>
                                <th
                                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                                    onClick={() => handleSort('amount_user1')}
                                >
                                    <div className="flex items-center">
                                        Amounts
                                        <FaSort className="ml-1" />
                                    </div>
                                </th>
                                <th
                                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                                    onClick={() => handleSort('bet_status')}
                                >
                                    <div className="flex items-center">
                                        Status
                                        <FaSort className="ml-1" />
                                    </div>
                                </th>
                                <th
                                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                                    onClick={() => handleSort('created_at')}
                                >
                                    <div className="flex items-center">
                                        Date
                                        <FaSort className="ml-1" />
                                    </div>
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Actions
                                </th>
                            </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                            {loading ? (
                                <tr>
                                    <td colSpan="8" className="px-6 py-4 text-center">
                                        <div className="flex justify-center items-center">
                                            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                                            <span className="ml-2">Loading...</span>
                                        </div>
                                    </td>
                                </tr>
                            ) : bets.length === 0 ? (
                                <tr>
                                    <td colSpan="8" className="px-6 py-4 text-center text-gray-500">
                                        No bets found
                                    </td>
                                </tr>
                            ) : (
                                bets.map((bet) => (
                                    <tr key={bet.bet_id} className="hover:bg-gray-50">
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <div className="text-sm font-medium text-gray-900">
                                                {bet.unique_code?.toUpperCase() || `${bet.bet_id}DNRBKCC`}
                                            </div>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <div className="flex items-center space-x-2">
                                                <div className="flex items-center space-x-1">
                                                    <img
                                                        src={getTeamLogo(bet.team_a)}
                                                        alt={bet.team_a}
                                                        className="w-6 h-6 rounded-full object-contain"
                                                        onError={(e) => {
                                                            e.target.style.display = 'none';
                                                        }}
                                                    />
                                                    <span className="text-sm text-gray-900">{bet.team_a}</span>
                                                </div>
                                                <span className="text-gray-500 text-sm">vs</span>
                                                <div className="flex items-center space-x-1">
                                                    <img
                                                        src={getTeamLogo(bet.team_b)}
                                                        alt={bet.team_b}
                                                        className="w-6 h-6 rounded-full object-contain"
                                                        onError={(e) => {
                                                            e.target.style.display = 'none';
                                                        }}
                                                    />
                                                    <span className="text-sm text-gray-900">{bet.team_b}</span>
                                                </div>
                                            </div>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <div>
                                                <div className="text-sm font-medium text-gray-900">{bet.user1_name}</div>
                                                <div className="text-sm text-gray-500">Pick: {bet.user1_pick}</div>
                                            </div>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <div>
                                                <div className="text-sm font-medium text-gray-900">
                                                    {bet.user2_name || 'Waiting...'}
                                                </div>
                                                {bet.user2_name && (
                                                    <div className="text-sm text-gray-500">Pick: {bet.user2_pick}</div>
                                                )}
                                            </div>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <div>
                                                <div className="text-sm text-gray-900">{bet.amount_user1} FC</div>
                                                {bet.amount_user2 && (
                                                    <div className="text-sm text-gray-500">{bet.amount_user2} FC</div>
                                                )}
                                            </div>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(bet.bet_status)}`}>
                                                {bet.bet_status}
                                            </span>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {new Date(bet.created_at).toLocaleDateString()}
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <button
                                                onClick={() => viewBetDetails(bet)}
                                                className="text-blue-600 hover:text-blue-900"
                                                title="View Details"
                                            >
                                                <FaEye />
                                            </button>
                                        </td>
                                    </tr>
                                ))
                            )}
                        </tbody>
                    </table>
                </div>
            </div>

            {/* Bet Details Modal */}
            {showBetModal && selectedBet && (
                <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
                    <div className="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
                        <div className="mt-3">
                            <div className="flex justify-between items-center mb-4">
                                <h3 className="text-lg font-medium text-gray-900">Bet Details</h3>
                                <button
                                    onClick={() => setShowBetModal(false)}
                                    className="text-gray-400 hover:text-gray-600"
                                >
                                    ×
                                </button>
                            </div>
                            <div className="space-y-4">
                                {/* Bet Information */}
                                <div className="grid grid-cols-2 gap-4">
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700">Reference</label>
                                        <p className="mt-1 text-sm text-gray-900">
                                            {selectedBet.unique_code?.toUpperCase() || `${selectedBet.bet_id}DNRBKCC`}
                                        </p>
                                    </div>
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700">Status</label>
                                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(selectedBet.bet_status)}`}>
                                            {selectedBet.bet_status}
                                        </span>
                                    </div>
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700">Match</label>
                                        <div className="mt-1 flex items-center space-x-2">
                                            <div className="flex items-center space-x-1">
                                                <img
                                                    src={getTeamLogo(selectedBet.team_a)}
                                                    alt={selectedBet.team_a}
                                                    className="w-8 h-8 rounded-full object-contain border border-gray-200"
                                                    onError={(e) => {
                                                        e.target.style.display = 'none';
                                                    }}
                                                />
                                                <span className="text-sm text-gray-900 font-medium">{selectedBet.team_a}</span>
                                            </div>
                                            <span className="text-gray-500 text-sm font-bold">VS</span>
                                            <div className="flex items-center space-x-1">
                                                <img
                                                    src={getTeamLogo(selectedBet.team_b)}
                                                    alt={selectedBet.team_b}
                                                    className="w-8 h-8 rounded-full object-contain border border-gray-200"
                                                    onError={(e) => {
                                                        e.target.style.display = 'none';
                                                    }}
                                                />
                                                <span className="text-sm text-gray-900 font-medium">{selectedBet.team_b}</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700">Created</label>
                                        <p className="mt-1 text-sm text-gray-900">
                                            {new Date(selectedBet.created_at).toLocaleString()}
                                        </p>
                                    </div>
                                </div>

                                {/* User 1 Information */}
                                <div className="border-t pt-4">
                                    <h4 className="text-md font-semibold text-gray-800 mb-2">User 1 (Bet Creator)</h4>
                                    <div className="grid grid-cols-2 gap-4">
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700">Username</label>
                                            <p className="mt-1 text-sm text-gray-900">{selectedBet.user1_name}</p>
                                        </div>
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700">Choice</label>
                                            <p className="mt-1 text-sm text-gray-900">{selectedBet.user1_pick}</p>
                                        </div>
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700">Amount</label>
                                            <p className="mt-1 text-sm text-gray-900">{selectedBet.amount_user1} FC</p>
                                        </div>
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700">Odds</label>
                                            <p className="mt-1 text-sm text-gray-900">{selectedBet.odds_user1 || 'N/A'}</p>
                                        </div>
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700">Potential Return</label>
                                            <p className="mt-1 text-sm text-gray-900">{selectedBet.potential_return_user1 || 'N/A'} FC</p>
                                        </div>
                                    </div>
                                </div>

                                {/* User 2 Information */}
                                <div className="border-t pt-4">
                                    <h4 className="text-md font-semibold text-gray-800 mb-2">User 2 (Bet Acceptor)</h4>
                                    {selectedBet.user2_name ? (
                                        <div className="grid grid-cols-2 gap-4">
                                            <div>
                                                <label className="block text-sm font-medium text-gray-700">Username</label>
                                                <p className="mt-1 text-sm text-gray-900">{selectedBet.user2_name}</p>
                                            </div>
                                            <div>
                                                <label className="block text-sm font-medium text-gray-700">Choice</label>
                                                <p className="mt-1 text-sm text-gray-900">{selectedBet.user2_pick}</p>
                                            </div>
                                            <div>
                                                <label className="block text-sm font-medium text-gray-700">Amount</label>
                                                <p className="mt-1 text-sm text-gray-900">{selectedBet.amount_user2} FC</p>
                                            </div>
                                            <div>
                                                <label className="block text-sm font-medium text-gray-700">Odds</label>
                                                <p className="mt-1 text-sm text-gray-900">{selectedBet.odds_user2 || 'N/A'}</p>
                                            </div>
                                            <div>
                                                <label className="block text-sm font-medium text-gray-700">Potential Return</label>
                                                <p className="mt-1 text-sm text-gray-900">{selectedBet.potential_return_user2 || 'N/A'} FC</p>
                                            </div>
                                        </div>
                                    ) : (
                                        <p className="text-sm text-gray-500 italic">Waiting for another user to accept this bet...</p>
                                    )}
                                </div>

                                {/* Additional Details */}
                                {selectedBet.match_date && (
                                    <div className="border-t pt-4">
                                        <h4 className="text-md font-semibold text-gray-800 mb-2">Match Details</h4>
                                        <div className="grid grid-cols-2 gap-4">
                                            <div>
                                                <label className="block text-sm font-medium text-gray-700">Match Date</label>
                                                <p className="mt-1 text-sm text-gray-900">
                                                    {new Date(selectedBet.match_date).toLocaleString()}
                                                </p>
                                            </div>
                                            {selectedBet.result && (
                                                <div>
                                                    <label className="block text-sm font-medium text-gray-700">Result</label>
                                                    <p className="mt-1 text-sm text-gray-900 capitalize">
                                                        {selectedBet.result.replace('_', ' ')}
                                                    </p>
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
}

export default BetManagement;