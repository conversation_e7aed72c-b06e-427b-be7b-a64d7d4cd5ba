{"ast": null, "code": "import React from'react';import{FaExclamationTriangle,FaInfoCircle,FaCheckCircle,FaTimes}from'react-icons/fa';import'./CustomModal.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const CustomModal=_ref=>{let{isOpen,onClose,onConfirm,title,message,type='confirm',// 'alert', 'confirm', 'success', 'error', 'warning'\nconfirmText='Confirm',cancelText='Cancel',showCancel=true,confirmButtonColor='blue',children}=_ref;if(!isOpen)return null;const getIcon=()=>{switch(type){case'success':return/*#__PURE__*/_jsx(FaCheckCircle,{className:\"modal-icon success\"});case'error':return/*#__PURE__*/_jsx(FaExclamationTriangle,{className:\"modal-icon error\"});case'warning':return/*#__PURE__*/_jsx(FaExclamationTriangle,{className:\"modal-icon warning\"});case'alert':case'confirm':default:return/*#__PURE__*/_jsx(FaInfoCircle,{className:\"modal-icon info\"});}};const getConfirmButtonClass=()=>{switch(confirmButtonColor){case'red':return'btn-danger';case'green':return'btn-success';case'yellow':return'btn-warning';case'blue':default:return'btn-primary';}};const handleOverlayClick=e=>{if(e.target===e.currentTarget){onClose();}};const handleConfirm=()=>{if(onConfirm){onConfirm();}onClose();};return/*#__PURE__*/_jsx(\"div\",{className:\"custom-modal-overlay\",onClick:handleOverlayClick,children:/*#__PURE__*/_jsxs(\"div\",{className:\"custom-modal\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"custom-modal-header\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"modal-title-container\",children:[getIcon(),/*#__PURE__*/_jsx(\"h3\",{className:\"modal-title\",children:title})]}),/*#__PURE__*/_jsx(\"button\",{className:\"modal-close-btn\",onClick:onClose,\"aria-label\":\"Close modal\",children:/*#__PURE__*/_jsx(FaTimes,{})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"custom-modal-body\",children:[message&&/*#__PURE__*/_jsx(\"p\",{className:\"modal-message\",children:message}),children]}),/*#__PURE__*/_jsxs(\"div\",{className:\"custom-modal-footer\",children:[showCancel&&/*#__PURE__*/_jsx(\"button\",{className:\"btn btn-secondary\",onClick:onClose,children:cancelText}),(type==='confirm'||onConfirm)&&/*#__PURE__*/_jsx(\"button\",{className:`btn ${getConfirmButtonClass()}`,onClick:handleConfirm,children:confirmText}),type==='alert'&&!onConfirm&&/*#__PURE__*/_jsx(\"button\",{className:\"btn btn-primary\",onClick:onClose,children:\"OK\"})]})]})});};export default CustomModal;", "map": {"version": 3, "names": ["React", "FaExclamationTriangle", "FaInfoCircle", "FaCheckCircle", "FaTimes", "jsx", "_jsx", "jsxs", "_jsxs", "CustomModal", "_ref", "isOpen", "onClose", "onConfirm", "title", "message", "type", "confirmText", "cancelText", "showCancel", "confirmButtonColor", "children", "getIcon", "className", "getConfirmButtonClass", "handleOverlayClick", "e", "target", "currentTarget", "handleConfirm", "onClick"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/components/CustomModal.js"], "sourcesContent": ["import React from 'react';\nimport { FaExclamationTriangle, FaInfoCircle, FaCheckCircle, FaTimes } from 'react-icons/fa';\nimport './CustomModal.css';\n\nconst CustomModal = ({ \n    isOpen, \n    onClose, \n    onConfirm, \n    title, \n    message, \n    type = 'confirm', // 'alert', 'confirm', 'success', 'error', 'warning'\n    confirmText = 'Confirm',\n    cancelText = 'Cancel',\n    showCancel = true,\n    confirmButtonColor = 'blue',\n    children\n}) => {\n    if (!isOpen) return null;\n\n    const getIcon = () => {\n        switch (type) {\n            case 'success':\n                return <FaCheckCircle className=\"modal-icon success\" />;\n            case 'error':\n                return <FaExclamationTriangle className=\"modal-icon error\" />;\n            case 'warning':\n                return <FaExclamationTriangle className=\"modal-icon warning\" />;\n            case 'alert':\n            case 'confirm':\n            default:\n                return <FaInfoCircle className=\"modal-icon info\" />;\n        }\n    };\n\n    const getConfirmButtonClass = () => {\n        switch (confirmButtonColor) {\n            case 'red':\n                return 'btn-danger';\n            case 'green':\n                return 'btn-success';\n            case 'yellow':\n                return 'btn-warning';\n            case 'blue':\n            default:\n                return 'btn-primary';\n        }\n    };\n\n    const handleOverlayClick = (e) => {\n        if (e.target === e.currentTarget) {\n            onClose();\n        }\n    };\n\n    const handleConfirm = () => {\n        if (onConfirm) {\n            onConfirm();\n        }\n        onClose();\n    };\n\n    return (\n        <div className=\"custom-modal-overlay\" onClick={handleOverlayClick}>\n            <div className=\"custom-modal\">\n                <div className=\"custom-modal-header\">\n                    <div className=\"modal-title-container\">\n                        {getIcon()}\n                        <h3 className=\"modal-title\">{title}</h3>\n                    </div>\n                    <button \n                        className=\"modal-close-btn\"\n                        onClick={onClose}\n                        aria-label=\"Close modal\"\n                    >\n                        <FaTimes />\n                    </button>\n                </div>\n\n                <div className=\"custom-modal-body\">\n                    {message && <p className=\"modal-message\">{message}</p>}\n                    {children}\n                </div>\n\n                <div className=\"custom-modal-footer\">\n                    {showCancel && (\n                        <button \n                            className=\"btn btn-secondary\"\n                            onClick={onClose}\n                        >\n                            {cancelText}\n                        </button>\n                    )}\n                    {(type === 'confirm' || onConfirm) && (\n                        <button \n                            className={`btn ${getConfirmButtonClass()}`}\n                            onClick={handleConfirm}\n                        >\n                            {confirmText}\n                        </button>\n                    )}\n                    {type === 'alert' && !onConfirm && (\n                        <button \n                            className=\"btn btn-primary\"\n                            onClick={onClose}\n                        >\n                            OK\n                        </button>\n                    )}\n                </div>\n            </div>\n        </div>\n    );\n};\n\nexport default CustomModal;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,qBAAqB,CAAEC,YAAY,CAAEC,aAAa,CAAEC,OAAO,KAAQ,gBAAgB,CAC5F,MAAO,mBAAmB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE3B,KAAM,CAAAC,WAAW,CAAGC,IAAA,EAYd,IAZe,CACjBC,MAAM,CACNC,OAAO,CACPC,SAAS,CACTC,KAAK,CACLC,OAAO,CACPC,IAAI,CAAG,SAAS,CAAE;AAClBC,WAAW,CAAG,SAAS,CACvBC,UAAU,CAAG,QAAQ,CACrBC,UAAU,CAAG,IAAI,CACjBC,kBAAkB,CAAG,MAAM,CAC3BC,QACJ,CAAC,CAAAX,IAAA,CACG,GAAI,CAACC,MAAM,CAAE,MAAO,KAAI,CAExB,KAAM,CAAAW,OAAO,CAAGA,CAAA,GAAM,CAClB,OAAQN,IAAI,EACR,IAAK,SAAS,CACV,mBAAOV,IAAA,CAACH,aAAa,EAACoB,SAAS,CAAC,oBAAoB,CAAE,CAAC,CAC3D,IAAK,OAAO,CACR,mBAAOjB,IAAA,CAACL,qBAAqB,EAACsB,SAAS,CAAC,kBAAkB,CAAE,CAAC,CACjE,IAAK,SAAS,CACV,mBAAOjB,IAAA,CAACL,qBAAqB,EAACsB,SAAS,CAAC,oBAAoB,CAAE,CAAC,CACnE,IAAK,OAAO,CACZ,IAAK,SAAS,CACd,QACI,mBAAOjB,IAAA,CAACJ,YAAY,EAACqB,SAAS,CAAC,iBAAiB,CAAE,CAAC,CAC3D,CACJ,CAAC,CAED,KAAM,CAAAC,qBAAqB,CAAGA,CAAA,GAAM,CAChC,OAAQJ,kBAAkB,EACtB,IAAK,KAAK,CACN,MAAO,YAAY,CACvB,IAAK,OAAO,CACR,MAAO,aAAa,CACxB,IAAK,QAAQ,CACT,MAAO,aAAa,CACxB,IAAK,MAAM,CACX,QACI,MAAO,aAAa,CAC5B,CACJ,CAAC,CAED,KAAM,CAAAK,kBAAkB,CAAIC,CAAC,EAAK,CAC9B,GAAIA,CAAC,CAACC,MAAM,GAAKD,CAAC,CAACE,aAAa,CAAE,CAC9BhB,OAAO,CAAC,CAAC,CACb,CACJ,CAAC,CAED,KAAM,CAAAiB,aAAa,CAAGA,CAAA,GAAM,CACxB,GAAIhB,SAAS,CAAE,CACXA,SAAS,CAAC,CAAC,CACf,CACAD,OAAO,CAAC,CAAC,CACb,CAAC,CAED,mBACIN,IAAA,QAAKiB,SAAS,CAAC,sBAAsB,CAACO,OAAO,CAAEL,kBAAmB,CAAAJ,QAAA,cAC9Db,KAAA,QAAKe,SAAS,CAAC,cAAc,CAAAF,QAAA,eACzBb,KAAA,QAAKe,SAAS,CAAC,qBAAqB,CAAAF,QAAA,eAChCb,KAAA,QAAKe,SAAS,CAAC,uBAAuB,CAAAF,QAAA,EACjCC,OAAO,CAAC,CAAC,cACVhB,IAAA,OAAIiB,SAAS,CAAC,aAAa,CAAAF,QAAA,CAAEP,KAAK,CAAK,CAAC,EACvC,CAAC,cACNR,IAAA,WACIiB,SAAS,CAAC,iBAAiB,CAC3BO,OAAO,CAAElB,OAAQ,CACjB,aAAW,aAAa,CAAAS,QAAA,cAExBf,IAAA,CAACF,OAAO,GAAE,CAAC,CACP,CAAC,EACR,CAAC,cAENI,KAAA,QAAKe,SAAS,CAAC,mBAAmB,CAAAF,QAAA,EAC7BN,OAAO,eAAIT,IAAA,MAAGiB,SAAS,CAAC,eAAe,CAAAF,QAAA,CAAEN,OAAO,CAAI,CAAC,CACrDM,QAAQ,EACR,CAAC,cAENb,KAAA,QAAKe,SAAS,CAAC,qBAAqB,CAAAF,QAAA,EAC/BF,UAAU,eACPb,IAAA,WACIiB,SAAS,CAAC,mBAAmB,CAC7BO,OAAO,CAAElB,OAAQ,CAAAS,QAAA,CAEhBH,UAAU,CACP,CACX,CACA,CAACF,IAAI,GAAK,SAAS,EAAIH,SAAS,gBAC7BP,IAAA,WACIiB,SAAS,CAAE,OAAOC,qBAAqB,CAAC,CAAC,EAAG,CAC5CM,OAAO,CAAED,aAAc,CAAAR,QAAA,CAEtBJ,WAAW,CACR,CACX,CACAD,IAAI,GAAK,OAAO,EAAI,CAACH,SAAS,eAC3BP,IAAA,WACIiB,SAAS,CAAC,iBAAiB,CAC3BO,OAAO,CAAElB,OAAQ,CAAAS,QAAA,CACpB,IAED,CAAQ,CACX,EACA,CAAC,EACL,CAAC,CACL,CAAC,CAEd,CAAC,CAED,cAAe,CAAAZ,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}