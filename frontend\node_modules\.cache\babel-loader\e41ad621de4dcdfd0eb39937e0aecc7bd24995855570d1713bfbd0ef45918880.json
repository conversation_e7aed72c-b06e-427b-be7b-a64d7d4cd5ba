{"ast": null, "code": "import React,{useState,useEffect}from'react';import axios from'axios';import{<PERSON>a<PERSON><PERSON><PERSON>,<PERSON>a<PERSON><PERSON><PERSON>,<PERSON>a<PERSON>ye,FaEdit,FaTrash,FaSort,FaDownload}from'react-icons/fa';import'./BetManagement.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const API_BASE_URL='/backend';function BetManagement(){var _selectedBet$unique_c;const[teams,setTeams]=useState([]);const[bets,setBets]=useState([]);const[loading,setLoading]=useState(false);const[error,setError]=useState('');const[success,setSuccess]=useState('');// Filters and pagination\nconst[filters,setFilters]=useState({search:'',status:'',dateFrom:'',dateTo:'',sortBy:'created_at',order:'DESC',limit:20});const[pagination,setPagination]=useState({currentPage:1,totalPages:1,totalItems:0,itemsPerPage:20});// Modal states\nconst[showBetModal,setShowBetModal]=useState(false);const[selectedBet,setSelectedBet]=useState(null);useEffect(()=>{fetchTeams();fetchAllBets();},[pagination.currentPage,filters]);const fetchTeams=async()=>{try{const response=await axios.get(`${API_BASE_URL}/handlers/team_management.php`);if(response.data.status===200){setTeams(response.data.data);}}catch(err){console.error('Error fetching teams:',err);}};const fetchAllBets=async()=>{setLoading(true);setError('');try{const params=new URLSearchParams({page:pagination.currentPage,limit:filters.limit,sortBy:filters.sortBy,order:filters.order,...(filters.search&&{search:filters.search}),...(filters.status&&{status:filters.status}),...(filters.dateFrom&&{dateFrom:filters.dateFrom}),...(filters.dateTo&&{dateTo:filters.dateTo})});const response=await axios.get(`${API_BASE_URL}/handlers/get_all_bets.php?${params}`);if(response.data.success){const processedBets=response.data.bets.map(bet=>({...bet,user1_pick:getTeamNameFromChoice(bet.bet_choice_user1,bet.team_a,bet.team_b),user2_pick:getTeamNameFromChoice(bet.bet_choice_user2,bet.team_a,bet.team_b)}));setBets(processedBets);setPagination(response.data.pagination||pagination);}else{setError(response.data.message||'Failed to fetch bets');}}catch(err){setError('Failed to fetch bets data');console.error('Error fetching bets:',err);}finally{setLoading(false);}};const getTeamNameFromChoice=(choice,teamA,teamB)=>{switch(choice){case'team_a_win':return teamA;case'team_b_win':return teamB;case'draw':return'Draw';default:return choice||'Unknown';}};const handleFilterChange=(key,value)=>{setFilters(prev=>({...prev,[key]:value}));setPagination(prev=>({...prev,currentPage:1}));};const handleSort=column=>{const newOrder=filters.sortBy===column&&filters.order==='DESC'?'ASC':'DESC';setFilters(prev=>({...prev,sortBy:column,order:newOrder}));};const handlePageChange=newPage=>{setPagination(prev=>({...prev,currentPage:newPage}));};const viewBetDetails=bet=>{setSelectedBet(bet);setShowBetModal(true);};const exportBets=async function(){let format=arguments.length>0&&arguments[0]!==undefined?arguments[0]:'csv';try{const params=new URLSearchParams({format,...filters});const response=await axios.get(`${API_BASE_URL}/handlers/export_bets.php?${params}`,{responseType:'blob'});const url=window.URL.createObjectURL(new Blob([response.data]));const link=document.createElement('a');link.href=url;link.setAttribute('download',`bets_export_${new Date().toISOString().split('T')[0]}.${format}`);document.body.appendChild(link);link.click();link.remove();window.URL.revokeObjectURL(url);}catch(err){setError('Failed to export bets');}};const getTeamLogo=teamName=>{const team=teams.find(team=>team.name===teamName);return team?`${API_BASE_URL}/${team.logo}`:'';};const getStatusColor=status=>{switch(status){case'open':return'bg-yellow-100 text-yellow-800';case'joined':return'bg-blue-100 text-blue-800';case'completed':return'bg-green-100 text-green-800';default:return'bg-gray-100 text-gray-800';}};return/*#__PURE__*/_jsxs(\"div\",{className:\"admin-container\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between items-center mb-6\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-2xl font-bold text-gray-800 mb-2\",children:\"Bet Management\"}),/*#__PURE__*/_jsx(\"p\",{className:\"admin-description\",children:\"Manage all betting activities, view bet details, and monitor betting statistics across the platform.\"})]}),/*#__PURE__*/_jsxs(\"button\",{onClick:()=>exportBets('csv'),disabled:loading,className:\"flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed\",children:[/*#__PURE__*/_jsx(FaDownload,{className:\"mr-2\"}),\"Export CSV\"]})]}),error&&/*#__PURE__*/_jsx(\"div\",{className:\"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\",children:error}),success&&/*#__PURE__*/_jsx(\"div\",{className:\"bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4\",children:success}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-lg shadow-md p-6 mb-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-5 gap-4\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-2\",children:\"Search\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"relative\",children:[/*#__PURE__*/_jsx(FaSearch,{className:\"absolute left-3 top-3 text-gray-400\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",placeholder:\"Search by reference, user...\",value:filters.search,onChange:e=>handleFilterChange('search',e.target.value),className:\"pl-10 w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-2\",children:\"Status\"}),/*#__PURE__*/_jsxs(\"select\",{value:filters.status,onChange:e=>handleFilterChange('status',e.target.value),className:\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"All Statuses\"}),/*#__PURE__*/_jsx(\"option\",{value:\"open\",children:\"Open\"}),/*#__PURE__*/_jsx(\"option\",{value:\"joined\",children:\"Joined\"}),/*#__PURE__*/_jsx(\"option\",{value:\"completed\",children:\"Completed\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-2\",children:\"From Date\"}),/*#__PURE__*/_jsx(\"input\",{type:\"date\",value:filters.dateFrom,onChange:e=>handleFilterChange('dateFrom',e.target.value),className:\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-2\",children:\"To Date\"}),/*#__PURE__*/_jsx(\"input\",{type:\"date\",value:filters.dateTo,onChange:e=>handleFilterChange('dateTo',e.target.value),className:\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-2\",children:\"Items per Page\"}),/*#__PURE__*/_jsxs(\"select\",{value:filters.limit,onChange:e=>handleFilterChange('limit',parseInt(e.target.value)),className:\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",children:[/*#__PURE__*/_jsx(\"option\",{value:10,children:\"10\"}),/*#__PURE__*/_jsx(\"option\",{value:20,children:\"20\"}),/*#__PURE__*/_jsx(\"option\",{value:50,children:\"50\"}),/*#__PURE__*/_jsx(\"option\",{value:100,children:\"100\"})]})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-lg shadow-md overflow-hidden\",children:/*#__PURE__*/_jsx(\"div\",{className:\"overflow-x-auto\",children:/*#__PURE__*/_jsxs(\"table\",{className:\"w-full\",children:[/*#__PURE__*/_jsx(\"thead\",{className:\"bg-gray-50\",children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100\",onClick:()=>handleSort('bet_id'),children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[\"Reference\",/*#__PURE__*/_jsx(FaSort,{className:\"ml-1\"})]})}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"Match\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"User 1\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"User 2\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100\",onClick:()=>handleSort('amount_user1'),children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[\"Amounts\",/*#__PURE__*/_jsx(FaSort,{className:\"ml-1\"})]})}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100\",onClick:()=>handleSort('bet_status'),children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[\"Status\",/*#__PURE__*/_jsx(FaSort,{className:\"ml-1\"})]})}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100\",onClick:()=>handleSort('created_at'),children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[\"Date\",/*#__PURE__*/_jsx(FaSort,{className:\"ml-1\"})]})}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"Actions\"})]})}),/*#__PURE__*/_jsx(\"tbody\",{className:\"bg-white divide-y divide-gray-200\",children:loading?/*#__PURE__*/_jsx(\"tr\",{children:/*#__PURE__*/_jsx(\"td\",{colSpan:\"8\",className:\"px-6 py-4 text-center\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-center items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"}),/*#__PURE__*/_jsx(\"span\",{className:\"ml-2\",children:\"Loading...\"})]})})}):bets.length===0?/*#__PURE__*/_jsx(\"tr\",{children:/*#__PURE__*/_jsx(\"td\",{colSpan:\"8\",className:\"px-6 py-4 text-center text-gray-500\",children:\"No bets found\"})}):bets.map(bet=>{var _bet$unique_code;return/*#__PURE__*/_jsxs(\"tr\",{className:\"hover:bg-gray-50\",children:[/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap\",children:/*#__PURE__*/_jsx(\"div\",{className:\"text-sm font-medium text-gray-900\",children:((_bet$unique_code=bet.unique_code)===null||_bet$unique_code===void 0?void 0:_bet$unique_code.toUpperCase())||`${bet.bet_id}DNRBKCC`})}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-1\",children:[/*#__PURE__*/_jsx(\"img\",{src:getTeamLogo(bet.team_a),alt:bet.team_a,className:\"w-6 h-6 rounded-full object-contain\",onError:e=>{e.target.style.display='none';}}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-gray-900\",children:bet.team_a})]}),/*#__PURE__*/_jsx(\"span\",{className:\"text-gray-500 text-sm\",children:\"vs\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-1\",children:[/*#__PURE__*/_jsx(\"img\",{src:getTeamLogo(bet.team_b),alt:bet.team_b,className:\"w-6 h-6 rounded-full object-contain\",onError:e=>{e.target.style.display='none';}}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-gray-900\",children:bet.team_b})]})]})}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap\",children:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-sm font-medium text-gray-900\",children:bet.user1_name}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-sm text-gray-500\",children:[\"Pick: \",bet.user1_pick]})]})}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap\",children:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-sm font-medium text-gray-900\",children:bet.user2_name||'Waiting...'}),bet.user2_name&&/*#__PURE__*/_jsxs(\"div\",{className:\"text-sm text-gray-500\",children:[\"Pick: \",bet.user2_pick]})]})}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap\",children:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-sm text-gray-900\",children:[bet.amount_user1,\" FC\"]}),bet.amount_user2&&/*#__PURE__*/_jsxs(\"div\",{className:\"text-sm text-gray-500\",children:[bet.amount_user2,\" FC\"]})]})}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap\",children:/*#__PURE__*/_jsx(\"span\",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(bet.bet_status)}`,children:bet.bet_status})}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",children:new Date(bet.created_at).toLocaleDateString()}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap text-sm font-medium\",children:/*#__PURE__*/_jsx(\"button\",{onClick:()=>viewBetDetails(bet),className:\"text-blue-600 hover:text-blue-900\",title:\"View Details\",children:/*#__PURE__*/_jsx(FaEye,{})})})]},bet.bet_id);})})]})})}),showBetModal&&selectedBet&&/*#__PURE__*/_jsx(\"div\",{className:\"fixed inset-0 bg-black bg-opacity-60 overflow-y-auto h-full w-full z-50 flex items-center justify-center p-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"relative bg-white rounded-2xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-y-auto\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6 rounded-t-2xl\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between items-center\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-xl font-bold\",children:\"Bet Details\"}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-blue-100 text-sm\",children:[\"Ref: \",((_selectedBet$unique_c=selectedBet.unique_code)===null||_selectedBet$unique_c===void 0?void 0:_selectedBet$unique_c.toUpperCase())||`${selectedBet.bet_id}DNRBKCC`]})]}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>setShowBetModal(false),className:\"text-white hover:text-gray-200 text-2xl font-bold w-8 h-8 flex items-center justify-center rounded-full hover:bg-white hover:bg-opacity-20 transition-all\",children:\"\\xD7\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"p-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between items-center mb-6\",children:[/*#__PURE__*/_jsx(\"span\",{className:`inline-flex px-4 py-2 text-sm font-semibold rounded-full ${getStatusColor(selectedBet.bet_status)}`,children:selectedBet.bet_status.toUpperCase()}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-right text-sm text-gray-500\",children:[/*#__PURE__*/_jsxs(\"p\",{children:[\"Created: \",new Date(selectedBet.created_at).toLocaleDateString()]}),/*#__PURE__*/_jsx(\"p\",{children:new Date(selectedBet.created_at).toLocaleTimeString()})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-gradient-to-r from-gray-50 to-gray-100 rounded-xl p-6 mb-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-center space-x-8\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-center flex-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"mb-3\",children:/*#__PURE__*/_jsx(\"img\",{src:getTeamLogo(selectedBet.team_a),alt:selectedBet.team_a,className:\"w-16 h-16 mx-auto rounded-full object-contain border-2 border-white shadow-lg\",onError:e=>{e.target.style.display='none';}})}),/*#__PURE__*/_jsx(\"h4\",{className:\"font-bold text-lg text-gray-800\",children:selectedBet.team_a})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-blue-600 text-white rounded-full w-12 h-12 flex items-center justify-center font-bold text-sm shadow-lg\",children:\"VS\"}),selectedBet.match_date&&/*#__PURE__*/_jsx(\"p\",{className:\"text-xs text-gray-500 mt-2\",children:new Date(selectedBet.match_date).toLocaleDateString()})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-center flex-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"mb-3\",children:/*#__PURE__*/_jsx(\"img\",{src:getTeamLogo(selectedBet.team_b),alt:selectedBet.team_b,className:\"w-16 h-16 mx-auto rounded-full object-contain border-2 border-white shadow-lg\",onError:e=>{e.target.style.display='none';}})}),/*#__PURE__*/_jsx(\"h4\",{className:\"font-bold text-lg text-gray-800\",children:selectedBet.team_b})]})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"bg-green-50 border border-green-200 rounded-xl p-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between mb-3\",children:[/*#__PURE__*/_jsx(\"h4\",{className:\"font-semibold text-green-800\",children:\"Bet Creator\"}),/*#__PURE__*/_jsx(\"span\",{className:\"bg-green-600 text-white px-2 py-1 rounded-full text-xs font-medium\",children:\"USER 1\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-2\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-gray-600\",children:\"Player:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm font-medium text-gray-900\",children:selectedBet.user1_name})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-gray-600\",children:\"Pick:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm font-medium text-green-700\",children:selectedBet.user1_pick})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-gray-600\",children:\"Stake:\"}),/*#__PURE__*/_jsxs(\"span\",{className:\"text-sm font-bold text-green-800\",children:[selectedBet.amount_user1,\" FC\"]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-gray-600\",children:\"Odds:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm font-medium text-gray-900\",children:selectedBet.odds_user1||'1.8'})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between border-t border-green-200 pt-2\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-gray-600\",children:\"Potential Win:\"}),/*#__PURE__*/_jsxs(\"span\",{className:\"text-sm font-bold text-green-800\",children:[selectedBet.potential_return_user1||Math.round(selectedBet.amount_user1*1.8),\" FC\"]})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:`${selectedBet.user2_name?'bg-blue-50 border-blue-200':'bg-gray-50 border-gray-200'} border rounded-xl p-4`,children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between mb-3\",children:[/*#__PURE__*/_jsx(\"h4\",{className:`font-semibold ${selectedBet.user2_name?'text-blue-800':'text-gray-600'}`,children:\"Bet Acceptor\"}),/*#__PURE__*/_jsx(\"span\",{className:`${selectedBet.user2_name?'bg-blue-600 text-white':'bg-gray-400 text-white'} px-2 py-1 rounded-full text-xs font-medium`,children:\"USER 2\"})]}),selectedBet.user2_name?/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-2\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-gray-600\",children:\"Player:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm font-medium text-gray-900\",children:selectedBet.user2_name})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-gray-600\",children:\"Pick:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm font-medium text-blue-700\",children:selectedBet.user2_pick})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-gray-600\",children:\"Stake:\"}),/*#__PURE__*/_jsxs(\"span\",{className:\"text-sm font-bold text-blue-800\",children:[selectedBet.amount_user2,\" FC\"]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-gray-600\",children:\"Odds:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm font-medium text-gray-900\",children:selectedBet.odds_user2||'1.8'})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between border-t border-blue-200 pt-2\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-gray-600\",children:\"Potential Win:\"}),/*#__PURE__*/_jsxs(\"span\",{className:\"text-sm font-bold text-blue-800\",children:[selectedBet.potential_return_user2||Math.round(selectedBet.amount_user2*1.8),\" FC\"]})]})]}):/*#__PURE__*/_jsxs(\"div\",{className:\"text-center py-4\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-gray-400 mb-2\",children:/*#__PURE__*/_jsx(\"svg\",{className:\"w-8 h-8 mx-auto\",fill:\"currentColor\",viewBox:\"0 0 20 20\",children:/*#__PURE__*/_jsx(\"path\",{fillRule:\"evenodd\",d:\"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z\",clipRule:\"evenodd\"})})}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-500 italic\",children:\"Waiting for another user to accept this bet...\"})]})]})]}),(selectedBet.result||selectedBet.match_date)&&/*#__PURE__*/_jsxs(\"div\",{className:\"bg-gray-50 rounded-xl p-4\",children:[/*#__PURE__*/_jsx(\"h4\",{className:\"font-semibold text-gray-800 mb-3\",children:\"Match Information\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-2 gap-4\",children:[selectedBet.match_date&&/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-gray-600\",children:\"Match Date:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm font-medium text-gray-900\",children:new Date(selectedBet.match_date).toLocaleDateString()})]}),selectedBet.result&&/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-gray-600\",children:\"Result:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm font-medium text-gray-900 capitalize\",children:selectedBet.result.replace('_',' ')})]})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"flex justify-end pt-4 border-t border-gray-200\",children:/*#__PURE__*/_jsx(\"button\",{onClick:()=>setShowBetModal(false),className:\"px-6 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors font-medium\",children:\"Close\"})})]})]})})]});}export default BetManagement;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "FaSearch", "FaFilter", "FaEye", "FaEdit", "FaTrash", "FaSort", "FaDownload", "jsx", "_jsx", "jsxs", "_jsxs", "API_BASE_URL", "BetManagement", "_selectedBet$unique_c", "teams", "setTeams", "bets", "setBets", "loading", "setLoading", "error", "setError", "success", "setSuccess", "filters", "setFilters", "search", "status", "dateFrom", "dateTo", "sortBy", "order", "limit", "pagination", "setPagination", "currentPage", "totalPages", "totalItems", "itemsPerPage", "showBetModal", "setShowBetModal", "selectedBet", "setSelectedBet", "fetchTeams", "fetchAllBets", "response", "get", "data", "err", "console", "params", "URLSearchParams", "page", "processedBets", "map", "bet", "user1_pick", "getTeamNameFromChoice", "bet_choice_user1", "team_a", "team_b", "user2_pick", "bet_choice_user2", "message", "choice", "teamA", "teamB", "handleFilterChange", "key", "value", "prev", "handleSort", "column", "newOrder", "handlePageChange", "newPage", "viewBetDetails", "exportBets", "format", "arguments", "length", "undefined", "responseType", "url", "window", "URL", "createObjectURL", "Blob", "link", "document", "createElement", "href", "setAttribute", "Date", "toISOString", "split", "body", "append<PERSON><PERSON><PERSON>", "click", "remove", "revokeObjectURL", "getTeamLogo", "teamName", "team", "find", "name", "logo", "getStatusColor", "className", "children", "onClick", "disabled", "type", "placeholder", "onChange", "e", "target", "parseInt", "colSpan", "_bet$unique_code", "unique_code", "toUpperCase", "bet_id", "src", "alt", "onError", "style", "display", "user1_name", "user2_name", "amount_user1", "amount_user2", "bet_status", "created_at", "toLocaleDateString", "title", "toLocaleTimeString", "match_date", "odds_user1", "potential_return_user1", "Math", "round", "odds_user2", "potential_return_user2", "fill", "viewBox", "fillRule", "d", "clipRule", "result", "replace"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/pages/BetManagement.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { Fa<PERSON><PERSON><PERSON>, <PERSON>a<PERSON><PERSON><PERSON>, <PERSON>a<PERSON>ye, FaEdit, FaTrash, FaSort, FaDownload } from 'react-icons/fa';\nimport './BetManagement.css';\n\nconst API_BASE_URL = '/backend';\n\nfunction BetManagement() {\n    const [teams, setTeams] = useState([]);\n    const [bets, setBets] = useState([]);\n    const [loading, setLoading] = useState(false);\n    const [error, setError] = useState('');\n    const [success, setSuccess] = useState('');\n\n    // Filters and pagination\n    const [filters, setFilters] = useState({\n        search: '',\n        status: '',\n        dateFrom: '',\n        dateTo: '',\n        sortBy: 'created_at',\n        order: 'DESC',\n        limit: 20\n    });\n\n    const [pagination, setPagination] = useState({\n        currentPage: 1,\n        totalPages: 1,\n        totalItems: 0,\n        itemsPerPage: 20\n    });\n\n    // Modal states\n    const [showBetModal, setShowBetModal] = useState(false);\n    const [selectedBet, setSelectedBet] = useState(null);\n\n    useEffect(() => {\n        fetchTeams();\n        fetchAllBets();\n    }, [pagination.currentPage, filters]);\n\n    const fetchTeams = async () => {\n        try {\n            const response = await axios.get(`${API_BASE_URL}/handlers/team_management.php`);\n            if (response.data.status === 200) {\n                setTeams(response.data.data);\n            }\n        } catch (err) {\n            console.error('Error fetching teams:', err);\n        }\n    };\n\n    const fetchAllBets = async () => {\n        setLoading(true);\n        setError('');\n        try {\n            const params = new URLSearchParams({\n                page: pagination.currentPage,\n                limit: filters.limit,\n                sortBy: filters.sortBy,\n                order: filters.order,\n                ...(filters.search && { search: filters.search }),\n                ...(filters.status && { status: filters.status }),\n                ...(filters.dateFrom && { dateFrom: filters.dateFrom }),\n                ...(filters.dateTo && { dateTo: filters.dateTo })\n            });\n\n            const response = await axios.get(`${API_BASE_URL}/handlers/get_all_bets.php?${params}`);\n            if (response.data.success) {\n                const processedBets = response.data.bets.map(bet => ({\n                    ...bet,\n                    user1_pick: getTeamNameFromChoice(bet.bet_choice_user1, bet.team_a, bet.team_b),\n                    user2_pick: getTeamNameFromChoice(bet.bet_choice_user2, bet.team_a, bet.team_b)\n                }));\n                setBets(processedBets);\n                setPagination(response.data.pagination || pagination);\n            } else {\n                setError(response.data.message || 'Failed to fetch bets');\n            }\n        } catch (err) {\n            setError('Failed to fetch bets data');\n            console.error('Error fetching bets:', err);\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const getTeamNameFromChoice = (choice, teamA, teamB) => {\n        switch (choice) {\n            case 'team_a_win':\n                return teamA;\n            case 'team_b_win':\n                return teamB;\n            case 'draw':\n                return 'Draw';\n            default:\n                return choice || 'Unknown';\n        }\n    };\n\n    const handleFilterChange = (key, value) => {\n        setFilters(prev => ({ ...prev, [key]: value }));\n        setPagination(prev => ({ ...prev, currentPage: 1 }));\n    };\n\n    const handleSort = (column) => {\n        const newOrder = filters.sortBy === column && filters.order === 'DESC' ? 'ASC' : 'DESC';\n        setFilters(prev => ({ ...prev, sortBy: column, order: newOrder }));\n    };\n\n    const handlePageChange = (newPage) => {\n        setPagination(prev => ({ ...prev, currentPage: newPage }));\n    };\n\n    const viewBetDetails = (bet) => {\n        setSelectedBet(bet);\n        setShowBetModal(true);\n    };\n\n    const exportBets = async (format = 'csv') => {\n        try {\n            const params = new URLSearchParams({\n                format,\n                ...filters\n            });\n\n            const response = await axios.get(`${API_BASE_URL}/handlers/export_bets.php?${params}`, {\n                responseType: 'blob'\n            });\n\n            const url = window.URL.createObjectURL(new Blob([response.data]));\n            const link = document.createElement('a');\n            link.href = url;\n            link.setAttribute('download', `bets_export_${new Date().toISOString().split('T')[0]}.${format}`);\n            document.body.appendChild(link);\n            link.click();\n            link.remove();\n            window.URL.revokeObjectURL(url);\n        } catch (err) {\n            setError('Failed to export bets');\n        }\n    };\n\n    const getTeamLogo = (teamName) => {\n        const team = teams.find(team => team.name === teamName);\n        return team ? `${API_BASE_URL}/${team.logo}` : '';\n    };\n\n    const getStatusColor = (status) => {\n        switch (status) {\n            case 'open':\n                return 'bg-yellow-100 text-yellow-800';\n            case 'joined':\n                return 'bg-blue-100 text-blue-800';\n            case 'completed':\n                return 'bg-green-100 text-green-800';\n            default:\n                return 'bg-gray-100 text-gray-800';\n        }\n    };\n\n    return (\n        <div className=\"admin-container\">\n            <div className=\"flex justify-between items-center mb-6\">\n                <div>\n                    <h1 className=\"text-2xl font-bold text-gray-800 mb-2\">Bet Management</h1>\n                    <p className=\"admin-description\">\n                        Manage all betting activities, view bet details, and monitor betting statistics across the platform.\n                    </p>\n                </div>\n                <button\n                    onClick={() => exportBets('csv')}\n                    disabled={loading}\n                    className=\"flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed\"\n                >\n                    <FaDownload className=\"mr-2\" />\n                    Export CSV\n                </button>\n            </div>\n\n            {error && (\n                <div className=\"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\">\n                    {error}\n                </div>\n            )}\n\n            {success && (\n                <div className=\"bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4\">\n                    {success}\n                </div>\n            )}\n\n            {/* Filters */}\n            <div className=\"bg-white rounded-lg shadow-md p-6 mb-6\">\n                <div className=\"grid grid-cols-1 md:grid-cols-5 gap-4\">\n                    <div>\n                        <label className=\"block text-sm font-medium text-gray-700 mb-2\">Search</label>\n                        <div className=\"relative\">\n                            <FaSearch className=\"absolute left-3 top-3 text-gray-400\" />\n                            <input\n                                type=\"text\"\n                                placeholder=\"Search by reference, user...\"\n                                value={filters.search}\n                                onChange={(e) => handleFilterChange('search', e.target.value)}\n                                className=\"pl-10 w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                            />\n                        </div>\n                    </div>\n\n                    <div>\n                        <label className=\"block text-sm font-medium text-gray-700 mb-2\">Status</label>\n                        <select\n                            value={filters.status}\n                            onChange={(e) => handleFilterChange('status', e.target.value)}\n                            className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                        >\n                            <option value=\"\">All Statuses</option>\n                            <option value=\"open\">Open</option>\n                            <option value=\"joined\">Joined</option>\n                            <option value=\"completed\">Completed</option>\n                        </select>\n                    </div>\n\n                    <div>\n                        <label className=\"block text-sm font-medium text-gray-700 mb-2\">From Date</label>\n                        <input\n                            type=\"date\"\n                            value={filters.dateFrom}\n                            onChange={(e) => handleFilterChange('dateFrom', e.target.value)}\n                            className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                        />\n                    </div>\n\n                    <div>\n                        <label className=\"block text-sm font-medium text-gray-700 mb-2\">To Date</label>\n                        <input\n                            type=\"date\"\n                            value={filters.dateTo}\n                            onChange={(e) => handleFilterChange('dateTo', e.target.value)}\n                            className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                        />\n                    </div>\n\n                    <div>\n                        <label className=\"block text-sm font-medium text-gray-700 mb-2\">Items per Page</label>\n                        <select\n                            value={filters.limit}\n                            onChange={(e) => handleFilterChange('limit', parseInt(e.target.value))}\n                            className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                        >\n                            <option value={10}>10</option>\n                            <option value={20}>20</option>\n                            <option value={50}>50</option>\n                            <option value={100}>100</option>\n                        </select>\n                    </div>\n                </div>\n            </div>\n\n            {/* Bets Table */}\n            <div className=\"bg-white rounded-lg shadow-md overflow-hidden\">\n                <div className=\"overflow-x-auto\">\n                    <table className=\"w-full\">\n                        <thead className=\"bg-gray-50\">\n                            <tr>\n                                <th\n                                    className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100\"\n                                    onClick={() => handleSort('bet_id')}\n                                >\n                                    <div className=\"flex items-center\">\n                                        Reference\n                                        <FaSort className=\"ml-1\" />\n                                    </div>\n                                </th>\n                                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                                    Match\n                                </th>\n                                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                                    User 1\n                                </th>\n                                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                                    User 2\n                                </th>\n                                <th\n                                    className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100\"\n                                    onClick={() => handleSort('amount_user1')}\n                                >\n                                    <div className=\"flex items-center\">\n                                        Amounts\n                                        <FaSort className=\"ml-1\" />\n                                    </div>\n                                </th>\n                                <th\n                                    className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100\"\n                                    onClick={() => handleSort('bet_status')}\n                                >\n                                    <div className=\"flex items-center\">\n                                        Status\n                                        <FaSort className=\"ml-1\" />\n                                    </div>\n                                </th>\n                                <th\n                                    className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100\"\n                                    onClick={() => handleSort('created_at')}\n                                >\n                                    <div className=\"flex items-center\">\n                                        Date\n                                        <FaSort className=\"ml-1\" />\n                                    </div>\n                                </th>\n                                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                                    Actions\n                                </th>\n                            </tr>\n                        </thead>\n                        <tbody className=\"bg-white divide-y divide-gray-200\">\n                            {loading ? (\n                                <tr>\n                                    <td colSpan=\"8\" className=\"px-6 py-4 text-center\">\n                                        <div className=\"flex justify-center items-center\">\n                                            <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n                                            <span className=\"ml-2\">Loading...</span>\n                                        </div>\n                                    </td>\n                                </tr>\n                            ) : bets.length === 0 ? (\n                                <tr>\n                                    <td colSpan=\"8\" className=\"px-6 py-4 text-center text-gray-500\">\n                                        No bets found\n                                    </td>\n                                </tr>\n                            ) : (\n                                bets.map((bet) => (\n                                    <tr key={bet.bet_id} className=\"hover:bg-gray-50\">\n                                        <td className=\"px-6 py-4 whitespace-nowrap\">\n                                            <div className=\"text-sm font-medium text-gray-900\">\n                                                {bet.unique_code?.toUpperCase() || `${bet.bet_id}DNRBKCC`}\n                                            </div>\n                                        </td>\n                                        <td className=\"px-6 py-4 whitespace-nowrap\">\n                                            <div className=\"flex items-center space-x-2\">\n                                                <div className=\"flex items-center space-x-1\">\n                                                    <img\n                                                        src={getTeamLogo(bet.team_a)}\n                                                        alt={bet.team_a}\n                                                        className=\"w-6 h-6 rounded-full object-contain\"\n                                                        onError={(e) => {\n                                                            e.target.style.display = 'none';\n                                                        }}\n                                                    />\n                                                    <span className=\"text-sm text-gray-900\">{bet.team_a}</span>\n                                                </div>\n                                                <span className=\"text-gray-500 text-sm\">vs</span>\n                                                <div className=\"flex items-center space-x-1\">\n                                                    <img\n                                                        src={getTeamLogo(bet.team_b)}\n                                                        alt={bet.team_b}\n                                                        className=\"w-6 h-6 rounded-full object-contain\"\n                                                        onError={(e) => {\n                                                            e.target.style.display = 'none';\n                                                        }}\n                                                    />\n                                                    <span className=\"text-sm text-gray-900\">{bet.team_b}</span>\n                                                </div>\n                                            </div>\n                                        </td>\n                                        <td className=\"px-6 py-4 whitespace-nowrap\">\n                                            <div>\n                                                <div className=\"text-sm font-medium text-gray-900\">{bet.user1_name}</div>\n                                                <div className=\"text-sm text-gray-500\">Pick: {bet.user1_pick}</div>\n                                            </div>\n                                        </td>\n                                        <td className=\"px-6 py-4 whitespace-nowrap\">\n                                            <div>\n                                                <div className=\"text-sm font-medium text-gray-900\">\n                                                    {bet.user2_name || 'Waiting...'}\n                                                </div>\n                                                {bet.user2_name && (\n                                                    <div className=\"text-sm text-gray-500\">Pick: {bet.user2_pick}</div>\n                                                )}\n                                            </div>\n                                        </td>\n                                        <td className=\"px-6 py-4 whitespace-nowrap\">\n                                            <div>\n                                                <div className=\"text-sm text-gray-900\">{bet.amount_user1} FC</div>\n                                                {bet.amount_user2 && (\n                                                    <div className=\"text-sm text-gray-500\">{bet.amount_user2} FC</div>\n                                                )}\n                                            </div>\n                                        </td>\n                                        <td className=\"px-6 py-4 whitespace-nowrap\">\n                                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(bet.bet_status)}`}>\n                                                {bet.bet_status}\n                                            </span>\n                                        </td>\n                                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                                            {new Date(bet.created_at).toLocaleDateString()}\n                                        </td>\n                                        <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                                            <button\n                                                onClick={() => viewBetDetails(bet)}\n                                                className=\"text-blue-600 hover:text-blue-900\"\n                                                title=\"View Details\"\n                                            >\n                                                <FaEye />\n                                            </button>\n                                        </td>\n                                    </tr>\n                                ))\n                            )}\n                        </tbody>\n                    </table>\n                </div>\n            </div>\n\n            {/* Modern Sports Betting Card Modal */}\n            {showBetModal && selectedBet && (\n                <div className=\"fixed inset-0 bg-black bg-opacity-60 overflow-y-auto h-full w-full z-50 flex items-center justify-center p-4\">\n                    <div className=\"relative bg-white rounded-2xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-y-auto\">\n                        {/* Header */}\n                        <div className=\"bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6 rounded-t-2xl\">\n                            <div className=\"flex justify-between items-center\">\n                                <div>\n                                    <h3 className=\"text-xl font-bold\">Bet Details</h3>\n                                    <p className=\"text-blue-100 text-sm\">\n                                        Ref: {selectedBet.unique_code?.toUpperCase() || `${selectedBet.bet_id}DNRBKCC`}\n                                    </p>\n                                </div>\n                                <button\n                                    onClick={() => setShowBetModal(false)}\n                                    className=\"text-white hover:text-gray-200 text-2xl font-bold w-8 h-8 flex items-center justify-center rounded-full hover:bg-white hover:bg-opacity-20 transition-all\"\n                                >\n                                    ×\n                                </button>\n                            </div>\n                        </div>\n                        {/* Match Card */}\n                        <div className=\"p-6\">\n                            {/* Status Badge */}\n                            <div className=\"flex justify-between items-center mb-6\">\n                                <span className={`inline-flex px-4 py-2 text-sm font-semibold rounded-full ${getStatusColor(selectedBet.bet_status)}`}>\n                                    {selectedBet.bet_status.toUpperCase()}\n                                </span>\n                                <div className=\"text-right text-sm text-gray-500\">\n                                    <p>Created: {new Date(selectedBet.created_at).toLocaleDateString()}</p>\n                                    <p>{new Date(selectedBet.created_at).toLocaleTimeString()}</p>\n                                </div>\n                            </div>\n\n                            {/* Teams VS Section */}\n                            <div className=\"bg-gradient-to-r from-gray-50 to-gray-100 rounded-xl p-6 mb-6\">\n                                <div className=\"flex items-center justify-center space-x-8\">\n                                    {/* Team A */}\n                                    <div className=\"text-center flex-1\">\n                                        <div className=\"mb-3\">\n                                            <img\n                                                src={getTeamLogo(selectedBet.team_a)}\n                                                alt={selectedBet.team_a}\n                                                className=\"w-16 h-16 mx-auto rounded-full object-contain border-2 border-white shadow-lg\"\n                                                onError={(e) => {\n                                                    e.target.style.display = 'none';\n                                                }}\n                                            />\n                                        </div>\n                                        <h4 className=\"font-bold text-lg text-gray-800\">{selectedBet.team_a}</h4>\n                                    </div>\n\n                                    {/* VS Divider */}\n                                    <div className=\"text-center\">\n                                        <div className=\"bg-blue-600 text-white rounded-full w-12 h-12 flex items-center justify-center font-bold text-sm shadow-lg\">\n                                            VS\n                                        </div>\n                                        {selectedBet.match_date && (\n                                            <p className=\"text-xs text-gray-500 mt-2\">\n                                                {new Date(selectedBet.match_date).toLocaleDateString()}\n                                            </p>\n                                        )}\n                                    </div>\n\n                                    {/* Team B */}\n                                    <div className=\"text-center flex-1\">\n                                        <div className=\"mb-3\">\n                                            <img\n                                                src={getTeamLogo(selectedBet.team_b)}\n                                                alt={selectedBet.team_b}\n                                                className=\"w-16 h-16 mx-auto rounded-full object-contain border-2 border-white shadow-lg\"\n                                                onError={(e) => {\n                                                    e.target.style.display = 'none';\n                                                }}\n                                            />\n                                        </div>\n                                        <h4 className=\"font-bold text-lg text-gray-800\">{selectedBet.team_b}</h4>\n                                    </div>\n                                </div>\n                            </div>\n\n                            {/* Betting Information Cards */}\n                            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6\">\n                                {/* User 1 Card */}\n                                <div className=\"bg-green-50 border border-green-200 rounded-xl p-4\">\n                                    <div className=\"flex items-center justify-between mb-3\">\n                                        <h4 className=\"font-semibold text-green-800\">Bet Creator</h4>\n                                        <span className=\"bg-green-600 text-white px-2 py-1 rounded-full text-xs font-medium\">\n                                            USER 1\n                                        </span>\n                                    </div>\n                                    <div className=\"space-y-2\">\n                                        <div className=\"flex justify-between\">\n                                            <span className=\"text-sm text-gray-600\">Player:</span>\n                                            <span className=\"text-sm font-medium text-gray-900\">{selectedBet.user1_name}</span>\n                                        </div>\n                                        <div className=\"flex justify-between\">\n                                            <span className=\"text-sm text-gray-600\">Pick:</span>\n                                            <span className=\"text-sm font-medium text-green-700\">{selectedBet.user1_pick}</span>\n                                        </div>\n                                        <div className=\"flex justify-between\">\n                                            <span className=\"text-sm text-gray-600\">Stake:</span>\n                                            <span className=\"text-sm font-bold text-green-800\">{selectedBet.amount_user1} FC</span>\n                                        </div>\n                                        <div className=\"flex justify-between\">\n                                            <span className=\"text-sm text-gray-600\">Odds:</span>\n                                            <span className=\"text-sm font-medium text-gray-900\">{selectedBet.odds_user1 || '1.8'}</span>\n                                        </div>\n                                        <div className=\"flex justify-between border-t border-green-200 pt-2\">\n                                            <span className=\"text-sm text-gray-600\">Potential Win:</span>\n                                            <span className=\"text-sm font-bold text-green-800\">\n                                                {selectedBet.potential_return_user1 || Math.round(selectedBet.amount_user1 * 1.8)} FC\n                                            </span>\n                                        </div>\n                                    </div>\n                                </div>\n\n                                {/* User 2 Card */}\n                                <div className={`${selectedBet.user2_name ? 'bg-blue-50 border-blue-200' : 'bg-gray-50 border-gray-200'} border rounded-xl p-4`}>\n                                    <div className=\"flex items-center justify-between mb-3\">\n                                        <h4 className={`font-semibold ${selectedBet.user2_name ? 'text-blue-800' : 'text-gray-600'}`}>\n                                            Bet Acceptor\n                                        </h4>\n                                        <span className={`${selectedBet.user2_name ? 'bg-blue-600 text-white' : 'bg-gray-400 text-white'} px-2 py-1 rounded-full text-xs font-medium`}>\n                                            USER 2\n                                        </span>\n                                    </div>\n                                    {selectedBet.user2_name ? (\n                                        <div className=\"space-y-2\">\n                                            <div className=\"flex justify-between\">\n                                                <span className=\"text-sm text-gray-600\">Player:</span>\n                                                <span className=\"text-sm font-medium text-gray-900\">{selectedBet.user2_name}</span>\n                                            </div>\n                                            <div className=\"flex justify-between\">\n                                                <span className=\"text-sm text-gray-600\">Pick:</span>\n                                                <span className=\"text-sm font-medium text-blue-700\">{selectedBet.user2_pick}</span>\n                                            </div>\n                                            <div className=\"flex justify-between\">\n                                                <span className=\"text-sm text-gray-600\">Stake:</span>\n                                                <span className=\"text-sm font-bold text-blue-800\">{selectedBet.amount_user2} FC</span>\n                                            </div>\n                                            <div className=\"flex justify-between\">\n                                                <span className=\"text-sm text-gray-600\">Odds:</span>\n                                                <span className=\"text-sm font-medium text-gray-900\">{selectedBet.odds_user2 || '1.8'}</span>\n                                            </div>\n                                            <div className=\"flex justify-between border-t border-blue-200 pt-2\">\n                                                <span className=\"text-sm text-gray-600\">Potential Win:</span>\n                                                <span className=\"text-sm font-bold text-blue-800\">\n                                                    {selectedBet.potential_return_user2 || Math.round(selectedBet.amount_user2 * 1.8)} FC\n                                                </span>\n                                            </div>\n                                        </div>\n                                    ) : (\n                                        <div className=\"text-center py-4\">\n                                            <div className=\"text-gray-400 mb-2\">\n                                                <svg className=\"w-8 h-8 mx-auto\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                                                    <path fillRule=\"evenodd\" d=\"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z\" clipRule=\"evenodd\" />\n                                                </svg>\n                                            </div>\n                                            <p className=\"text-sm text-gray-500 italic\">Waiting for another user to accept this bet...</p>\n                                        </div>\n                                    )}\n                                </div>\n                            </div>\n\n                            {/* Match Result & Additional Info */}\n                            {(selectedBet.result || selectedBet.match_date) && (\n                                <div className=\"bg-gray-50 rounded-xl p-4\">\n                                    <h4 className=\"font-semibold text-gray-800 mb-3\">Match Information</h4>\n                                    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                                        {selectedBet.match_date && (\n                                            <div className=\"flex justify-between\">\n                                                <span className=\"text-sm text-gray-600\">Match Date:</span>\n                                                <span className=\"text-sm font-medium text-gray-900\">\n                                                    {new Date(selectedBet.match_date).toLocaleDateString()}\n                                                </span>\n                                            </div>\n                                        )}\n                                        {selectedBet.result && (\n                                            <div className=\"flex justify-between\">\n                                                <span className=\"text-sm text-gray-600\">Result:</span>\n                                                <span className=\"text-sm font-medium text-gray-900 capitalize\">\n                                                    {selectedBet.result.replace('_', ' ')}\n                                                </span>\n                                            </div>\n                                        )}\n                                    </div>\n                                </div>\n                            )}\n\n                            {/* Action Buttons */}\n                            <div className=\"flex justify-end pt-4 border-t border-gray-200\">\n                                <button\n                                    onClick={() => setShowBetModal(false)}\n                                    className=\"px-6 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors font-medium\"\n                                >\n                                    Close\n                                </button>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n            )}\n        </div>\n    );\n}\n\nexport default BetManagement;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,MAAO,CAAAC,KAAK,KAAM,OAAO,CACzB,OAASC,QAAQ,CAAEC,QAAQ,CAAEC,KAAK,CAAEC,MAAM,CAAEC,OAAO,CAAEC,MAAM,CAAEC,UAAU,KAAQ,gBAAgB,CAC/F,MAAO,qBAAqB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE7B,KAAM,CAAAC,YAAY,CAAG,UAAU,CAE/B,QAAS,CAAAC,aAAaA,CAAA,CAAG,KAAAC,qBAAA,CACrB,KAAM,CAACC,KAAK,CAAEC,QAAQ,CAAC,CAAGlB,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAACmB,IAAI,CAAEC,OAAO,CAAC,CAAGpB,QAAQ,CAAC,EAAE,CAAC,CACpC,KAAM,CAACqB,OAAO,CAAEC,UAAU,CAAC,CAAGtB,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAACuB,KAAK,CAAEC,QAAQ,CAAC,CAAGxB,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAACyB,OAAO,CAAEC,UAAU,CAAC,CAAG1B,QAAQ,CAAC,EAAE,CAAC,CAE1C;AACA,KAAM,CAAC2B,OAAO,CAAEC,UAAU,CAAC,CAAG5B,QAAQ,CAAC,CACnC6B,MAAM,CAAE,EAAE,CACVC,MAAM,CAAE,EAAE,CACVC,QAAQ,CAAE,EAAE,CACZC,MAAM,CAAE,EAAE,CACVC,MAAM,CAAE,YAAY,CACpBC,KAAK,CAAE,MAAM,CACbC,KAAK,CAAE,EACX,CAAC,CAAC,CAEF,KAAM,CAACC,UAAU,CAAEC,aAAa,CAAC,CAAGrC,QAAQ,CAAC,CACzCsC,WAAW,CAAE,CAAC,CACdC,UAAU,CAAE,CAAC,CACbC,UAAU,CAAE,CAAC,CACbC,YAAY,CAAE,EAClB,CAAC,CAAC,CAEF;AACA,KAAM,CAACC,YAAY,CAAEC,eAAe,CAAC,CAAG3C,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAAC4C,WAAW,CAAEC,cAAc,CAAC,CAAG7C,QAAQ,CAAC,IAAI,CAAC,CAEpDC,SAAS,CAAC,IAAM,CACZ6C,UAAU,CAAC,CAAC,CACZC,YAAY,CAAC,CAAC,CAClB,CAAC,CAAE,CAACX,UAAU,CAACE,WAAW,CAAEX,OAAO,CAAC,CAAC,CAErC,KAAM,CAAAmB,UAAU,CAAG,KAAAA,CAAA,GAAY,CAC3B,GAAI,CACA,KAAM,CAAAE,QAAQ,CAAG,KAAM,CAAA9C,KAAK,CAAC+C,GAAG,CAAC,GAAGnC,YAAY,+BAA+B,CAAC,CAChF,GAAIkC,QAAQ,CAACE,IAAI,CAACpB,MAAM,GAAK,GAAG,CAAE,CAC9BZ,QAAQ,CAAC8B,QAAQ,CAACE,IAAI,CAACA,IAAI,CAAC,CAChC,CACJ,CAAE,MAAOC,GAAG,CAAE,CACVC,OAAO,CAAC7B,KAAK,CAAC,uBAAuB,CAAE4B,GAAG,CAAC,CAC/C,CACJ,CAAC,CAED,KAAM,CAAAJ,YAAY,CAAG,KAAAA,CAAA,GAAY,CAC7BzB,UAAU,CAAC,IAAI,CAAC,CAChBE,QAAQ,CAAC,EAAE,CAAC,CACZ,GAAI,CACA,KAAM,CAAA6B,MAAM,CAAG,GAAI,CAAAC,eAAe,CAAC,CAC/BC,IAAI,CAAEnB,UAAU,CAACE,WAAW,CAC5BH,KAAK,CAAER,OAAO,CAACQ,KAAK,CACpBF,MAAM,CAAEN,OAAO,CAACM,MAAM,CACtBC,KAAK,CAAEP,OAAO,CAACO,KAAK,CACpB,IAAIP,OAAO,CAACE,MAAM,EAAI,CAAEA,MAAM,CAAEF,OAAO,CAACE,MAAO,CAAC,CAAC,CACjD,IAAIF,OAAO,CAACG,MAAM,EAAI,CAAEA,MAAM,CAAEH,OAAO,CAACG,MAAO,CAAC,CAAC,CACjD,IAAIH,OAAO,CAACI,QAAQ,EAAI,CAAEA,QAAQ,CAAEJ,OAAO,CAACI,QAAS,CAAC,CAAC,CACvD,IAAIJ,OAAO,CAACK,MAAM,EAAI,CAAEA,MAAM,CAAEL,OAAO,CAACK,MAAO,CAAC,CACpD,CAAC,CAAC,CAEF,KAAM,CAAAgB,QAAQ,CAAG,KAAM,CAAA9C,KAAK,CAAC+C,GAAG,CAAC,GAAGnC,YAAY,8BAA8BuC,MAAM,EAAE,CAAC,CACvF,GAAIL,QAAQ,CAACE,IAAI,CAACzB,OAAO,CAAE,CACvB,KAAM,CAAA+B,aAAa,CAAGR,QAAQ,CAACE,IAAI,CAAC/B,IAAI,CAACsC,GAAG,CAACC,GAAG,GAAK,CACjD,GAAGA,GAAG,CACNC,UAAU,CAAEC,qBAAqB,CAACF,GAAG,CAACG,gBAAgB,CAAEH,GAAG,CAACI,MAAM,CAAEJ,GAAG,CAACK,MAAM,CAAC,CAC/EC,UAAU,CAAEJ,qBAAqB,CAACF,GAAG,CAACO,gBAAgB,CAAEP,GAAG,CAACI,MAAM,CAAEJ,GAAG,CAACK,MAAM,CAClF,CAAC,CAAC,CAAC,CACH3C,OAAO,CAACoC,aAAa,CAAC,CACtBnB,aAAa,CAACW,QAAQ,CAACE,IAAI,CAACd,UAAU,EAAIA,UAAU,CAAC,CACzD,CAAC,IAAM,CACHZ,QAAQ,CAACwB,QAAQ,CAACE,IAAI,CAACgB,OAAO,EAAI,sBAAsB,CAAC,CAC7D,CACJ,CAAE,MAAOf,GAAG,CAAE,CACV3B,QAAQ,CAAC,2BAA2B,CAAC,CACrC4B,OAAO,CAAC7B,KAAK,CAAC,sBAAsB,CAAE4B,GAAG,CAAC,CAC9C,CAAC,OAAS,CACN7B,UAAU,CAAC,KAAK,CAAC,CACrB,CACJ,CAAC,CAED,KAAM,CAAAsC,qBAAqB,CAAGA,CAACO,MAAM,CAAEC,KAAK,CAAEC,KAAK,GAAK,CACpD,OAAQF,MAAM,EACV,IAAK,YAAY,CACb,MAAO,CAAAC,KAAK,CAChB,IAAK,YAAY,CACb,MAAO,CAAAC,KAAK,CAChB,IAAK,MAAM,CACP,MAAO,MAAM,CACjB,QACI,MAAO,CAAAF,MAAM,EAAI,SAAS,CAClC,CACJ,CAAC,CAED,KAAM,CAAAG,kBAAkB,CAAGA,CAACC,GAAG,CAAEC,KAAK,GAAK,CACvC5C,UAAU,CAAC6C,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAE,CAACF,GAAG,EAAGC,KAAM,CAAC,CAAC,CAAC,CAC/CnC,aAAa,CAACoC,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAEnC,WAAW,CAAE,CAAE,CAAC,CAAC,CAAC,CACxD,CAAC,CAED,KAAM,CAAAoC,UAAU,CAAIC,MAAM,EAAK,CAC3B,KAAM,CAAAC,QAAQ,CAAGjD,OAAO,CAACM,MAAM,GAAK0C,MAAM,EAAIhD,OAAO,CAACO,KAAK,GAAK,MAAM,CAAG,KAAK,CAAG,MAAM,CACvFN,UAAU,CAAC6C,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAExC,MAAM,CAAE0C,MAAM,CAAEzC,KAAK,CAAE0C,QAAS,CAAC,CAAC,CAAC,CACtE,CAAC,CAED,KAAM,CAAAC,gBAAgB,CAAIC,OAAO,EAAK,CAClCzC,aAAa,CAACoC,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAEnC,WAAW,CAAEwC,OAAQ,CAAC,CAAC,CAAC,CAC9D,CAAC,CAED,KAAM,CAAAC,cAAc,CAAIrB,GAAG,EAAK,CAC5Bb,cAAc,CAACa,GAAG,CAAC,CACnBf,eAAe,CAAC,IAAI,CAAC,CACzB,CAAC,CAED,KAAM,CAAAqC,UAAU,CAAG,cAAAA,CAAA,CAA0B,IAAnB,CAAAC,MAAM,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,KAAK,CACpC,GAAI,CACA,KAAM,CAAA7B,MAAM,CAAG,GAAI,CAAAC,eAAe,CAAC,CAC/B2B,MAAM,CACN,GAAGtD,OACP,CAAC,CAAC,CAEF,KAAM,CAAAqB,QAAQ,CAAG,KAAM,CAAA9C,KAAK,CAAC+C,GAAG,CAAC,GAAGnC,YAAY,6BAA6BuC,MAAM,EAAE,CAAE,CACnFgC,YAAY,CAAE,MAClB,CAAC,CAAC,CAEF,KAAM,CAAAC,GAAG,CAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAAC,GAAI,CAAAC,IAAI,CAAC,CAAC1C,QAAQ,CAACE,IAAI,CAAC,CAAC,CAAC,CACjE,KAAM,CAAAyC,IAAI,CAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC,CACxCF,IAAI,CAACG,IAAI,CAAGR,GAAG,CACfK,IAAI,CAACI,YAAY,CAAC,UAAU,CAAE,eAAe,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAIjB,MAAM,EAAE,CAAC,CAChGW,QAAQ,CAACO,IAAI,CAACC,WAAW,CAACT,IAAI,CAAC,CAC/BA,IAAI,CAACU,KAAK,CAAC,CAAC,CACZV,IAAI,CAACW,MAAM,CAAC,CAAC,CACbf,MAAM,CAACC,GAAG,CAACe,eAAe,CAACjB,GAAG,CAAC,CACnC,CAAE,MAAOnC,GAAG,CAAE,CACV3B,QAAQ,CAAC,uBAAuB,CAAC,CACrC,CACJ,CAAC,CAED,KAAM,CAAAgF,WAAW,CAAIC,QAAQ,EAAK,CAC9B,KAAM,CAAAC,IAAI,CAAGzF,KAAK,CAAC0F,IAAI,CAACD,IAAI,EAAIA,IAAI,CAACE,IAAI,GAAKH,QAAQ,CAAC,CACvD,MAAO,CAAAC,IAAI,CAAG,GAAG5F,YAAY,IAAI4F,IAAI,CAACG,IAAI,EAAE,CAAG,EAAE,CACrD,CAAC,CAED,KAAM,CAAAC,cAAc,CAAIhF,MAAM,EAAK,CAC/B,OAAQA,MAAM,EACV,IAAK,MAAM,CACP,MAAO,+BAA+B,CAC1C,IAAK,QAAQ,CACT,MAAO,2BAA2B,CACtC,IAAK,WAAW,CACZ,MAAO,6BAA6B,CACxC,QACI,MAAO,2BAA2B,CAC1C,CACJ,CAAC,CAED,mBACIjB,KAAA,QAAKkG,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC5BnG,KAAA,QAAKkG,SAAS,CAAC,wCAAwC,CAAAC,QAAA,eACnDnG,KAAA,QAAAmG,QAAA,eACIrG,IAAA,OAAIoG,SAAS,CAAC,uCAAuC,CAAAC,QAAA,CAAC,gBAAc,CAAI,CAAC,cACzErG,IAAA,MAAGoG,SAAS,CAAC,mBAAmB,CAAAC,QAAA,CAAC,sGAEjC,CAAG,CAAC,EACH,CAAC,cACNnG,KAAA,WACIoG,OAAO,CAAEA,CAAA,GAAMjC,UAAU,CAAC,KAAK,CAAE,CACjCkC,QAAQ,CAAE7F,OAAQ,CAClB0F,SAAS,CAAC,mIAAmI,CAAAC,QAAA,eAE7IrG,IAAA,CAACF,UAAU,EAACsG,SAAS,CAAC,MAAM,CAAE,CAAC,aAEnC,EAAQ,CAAC,EACR,CAAC,CAELxF,KAAK,eACFZ,IAAA,QAAKoG,SAAS,CAAC,sEAAsE,CAAAC,QAAA,CAChFzF,KAAK,CACL,CACR,CAEAE,OAAO,eACJd,IAAA,QAAKoG,SAAS,CAAC,4EAA4E,CAAAC,QAAA,CACtFvF,OAAO,CACP,CACR,cAGDd,IAAA,QAAKoG,SAAS,CAAC,wCAAwC,CAAAC,QAAA,cACnDnG,KAAA,QAAKkG,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eAClDnG,KAAA,QAAAmG,QAAA,eACIrG,IAAA,UAAOoG,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,QAAM,CAAO,CAAC,cAC9EnG,KAAA,QAAKkG,SAAS,CAAC,UAAU,CAAAC,QAAA,eACrBrG,IAAA,CAACR,QAAQ,EAAC4G,SAAS,CAAC,qCAAqC,CAAE,CAAC,cAC5DpG,IAAA,UACIwG,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,8BAA8B,CAC1C5C,KAAK,CAAE7C,OAAO,CAACE,MAAO,CACtBwF,QAAQ,CAAGC,CAAC,EAAKhD,kBAAkB,CAAC,QAAQ,CAAEgD,CAAC,CAACC,MAAM,CAAC/C,KAAK,CAAE,CAC9DuC,SAAS,CAAC,oHAAoH,CACjI,CAAC,EACD,CAAC,EACL,CAAC,cAENlG,KAAA,QAAAmG,QAAA,eACIrG,IAAA,UAAOoG,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,QAAM,CAAO,CAAC,cAC9EnG,KAAA,WACI2D,KAAK,CAAE7C,OAAO,CAACG,MAAO,CACtBuF,QAAQ,CAAGC,CAAC,EAAKhD,kBAAkB,CAAC,QAAQ,CAAEgD,CAAC,CAACC,MAAM,CAAC/C,KAAK,CAAE,CAC9DuC,SAAS,CAAC,8GAA8G,CAAAC,QAAA,eAExHrG,IAAA,WAAQ6D,KAAK,CAAC,EAAE,CAAAwC,QAAA,CAAC,cAAY,CAAQ,CAAC,cACtCrG,IAAA,WAAQ6D,KAAK,CAAC,MAAM,CAAAwC,QAAA,CAAC,MAAI,CAAQ,CAAC,cAClCrG,IAAA,WAAQ6D,KAAK,CAAC,QAAQ,CAAAwC,QAAA,CAAC,QAAM,CAAQ,CAAC,cACtCrG,IAAA,WAAQ6D,KAAK,CAAC,WAAW,CAAAwC,QAAA,CAAC,WAAS,CAAQ,CAAC,EACxC,CAAC,EACR,CAAC,cAENnG,KAAA,QAAAmG,QAAA,eACIrG,IAAA,UAAOoG,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,WAAS,CAAO,CAAC,cACjFrG,IAAA,UACIwG,IAAI,CAAC,MAAM,CACX3C,KAAK,CAAE7C,OAAO,CAACI,QAAS,CACxBsF,QAAQ,CAAGC,CAAC,EAAKhD,kBAAkB,CAAC,UAAU,CAAEgD,CAAC,CAACC,MAAM,CAAC/C,KAAK,CAAE,CAChEuC,SAAS,CAAC,8GAA8G,CAC3H,CAAC,EACD,CAAC,cAENlG,KAAA,QAAAmG,QAAA,eACIrG,IAAA,UAAOoG,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,SAAO,CAAO,CAAC,cAC/ErG,IAAA,UACIwG,IAAI,CAAC,MAAM,CACX3C,KAAK,CAAE7C,OAAO,CAACK,MAAO,CACtBqF,QAAQ,CAAGC,CAAC,EAAKhD,kBAAkB,CAAC,QAAQ,CAAEgD,CAAC,CAACC,MAAM,CAAC/C,KAAK,CAAE,CAC9DuC,SAAS,CAAC,8GAA8G,CAC3H,CAAC,EACD,CAAC,cAENlG,KAAA,QAAAmG,QAAA,eACIrG,IAAA,UAAOoG,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,gBAAc,CAAO,CAAC,cACtFnG,KAAA,WACI2D,KAAK,CAAE7C,OAAO,CAACQ,KAAM,CACrBkF,QAAQ,CAAGC,CAAC,EAAKhD,kBAAkB,CAAC,OAAO,CAAEkD,QAAQ,CAACF,CAAC,CAACC,MAAM,CAAC/C,KAAK,CAAC,CAAE,CACvEuC,SAAS,CAAC,8GAA8G,CAAAC,QAAA,eAExHrG,IAAA,WAAQ6D,KAAK,CAAE,EAAG,CAAAwC,QAAA,CAAC,IAAE,CAAQ,CAAC,cAC9BrG,IAAA,WAAQ6D,KAAK,CAAE,EAAG,CAAAwC,QAAA,CAAC,IAAE,CAAQ,CAAC,cAC9BrG,IAAA,WAAQ6D,KAAK,CAAE,EAAG,CAAAwC,QAAA,CAAC,IAAE,CAAQ,CAAC,cAC9BrG,IAAA,WAAQ6D,KAAK,CAAE,GAAI,CAAAwC,QAAA,CAAC,KAAG,CAAQ,CAAC,EAC5B,CAAC,EACR,CAAC,EACL,CAAC,CACL,CAAC,cAGNrG,IAAA,QAAKoG,SAAS,CAAC,+CAA+C,CAAAC,QAAA,cAC1DrG,IAAA,QAAKoG,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC5BnG,KAAA,UAAOkG,SAAS,CAAC,QAAQ,CAAAC,QAAA,eACrBrG,IAAA,UAAOoG,SAAS,CAAC,YAAY,CAAAC,QAAA,cACzBnG,KAAA,OAAAmG,QAAA,eACIrG,IAAA,OACIoG,SAAS,CAAC,iHAAiH,CAC3HE,OAAO,CAAEA,CAAA,GAAMvC,UAAU,CAAC,QAAQ,CAAE,CAAAsC,QAAA,cAEpCnG,KAAA,QAAKkG,SAAS,CAAC,mBAAmB,CAAAC,QAAA,EAAC,WAE/B,cAAArG,IAAA,CAACH,MAAM,EAACuG,SAAS,CAAC,MAAM,CAAE,CAAC,EAC1B,CAAC,CACN,CAAC,cACLpG,IAAA,OAAIoG,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAAC,OAE/F,CAAI,CAAC,cACLrG,IAAA,OAAIoG,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAAC,QAE/F,CAAI,CAAC,cACLrG,IAAA,OAAIoG,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAAC,QAE/F,CAAI,CAAC,cACLrG,IAAA,OACIoG,SAAS,CAAC,iHAAiH,CAC3HE,OAAO,CAAEA,CAAA,GAAMvC,UAAU,CAAC,cAAc,CAAE,CAAAsC,QAAA,cAE1CnG,KAAA,QAAKkG,SAAS,CAAC,mBAAmB,CAAAC,QAAA,EAAC,SAE/B,cAAArG,IAAA,CAACH,MAAM,EAACuG,SAAS,CAAC,MAAM,CAAE,CAAC,EAC1B,CAAC,CACN,CAAC,cACLpG,IAAA,OACIoG,SAAS,CAAC,iHAAiH,CAC3HE,OAAO,CAAEA,CAAA,GAAMvC,UAAU,CAAC,YAAY,CAAE,CAAAsC,QAAA,cAExCnG,KAAA,QAAKkG,SAAS,CAAC,mBAAmB,CAAAC,QAAA,EAAC,QAE/B,cAAArG,IAAA,CAACH,MAAM,EAACuG,SAAS,CAAC,MAAM,CAAE,CAAC,EAC1B,CAAC,CACN,CAAC,cACLpG,IAAA,OACIoG,SAAS,CAAC,iHAAiH,CAC3HE,OAAO,CAAEA,CAAA,GAAMvC,UAAU,CAAC,YAAY,CAAE,CAAAsC,QAAA,cAExCnG,KAAA,QAAKkG,SAAS,CAAC,mBAAmB,CAAAC,QAAA,EAAC,MAE/B,cAAArG,IAAA,CAACH,MAAM,EAACuG,SAAS,CAAC,MAAM,CAAE,CAAC,EAC1B,CAAC,CACN,CAAC,cACLpG,IAAA,OAAIoG,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAAC,SAE/F,CAAI,CAAC,EACL,CAAC,CACF,CAAC,cACRrG,IAAA,UAAOoG,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAC/C3F,OAAO,cACJV,IAAA,OAAAqG,QAAA,cACIrG,IAAA,OAAI8G,OAAO,CAAC,GAAG,CAACV,SAAS,CAAC,uBAAuB,CAAAC,QAAA,cAC7CnG,KAAA,QAAKkG,SAAS,CAAC,kCAAkC,CAAAC,QAAA,eAC7CrG,IAAA,QAAKoG,SAAS,CAAC,8DAA8D,CAAM,CAAC,cACpFpG,IAAA,SAAMoG,SAAS,CAAC,MAAM,CAAAC,QAAA,CAAC,YAAU,CAAM,CAAC,EACvC,CAAC,CACN,CAAC,CACL,CAAC,CACL7F,IAAI,CAACgE,MAAM,GAAK,CAAC,cACjBxE,IAAA,OAAAqG,QAAA,cACIrG,IAAA,OAAI8G,OAAO,CAAC,GAAG,CAACV,SAAS,CAAC,qCAAqC,CAAAC,QAAA,CAAC,eAEhE,CAAI,CAAC,CACL,CAAC,CAEL7F,IAAI,CAACsC,GAAG,CAAEC,GAAG,OAAAgE,gBAAA,oBACT7G,KAAA,OAAqBkG,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC7CrG,IAAA,OAAIoG,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cACvCrG,IAAA,QAAKoG,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAC7C,EAAAU,gBAAA,CAAAhE,GAAG,CAACiE,WAAW,UAAAD,gBAAA,iBAAfA,gBAAA,CAAiBE,WAAW,CAAC,CAAC,GAAI,GAAGlE,GAAG,CAACmE,MAAM,SAAS,CACxD,CAAC,CACN,CAAC,cACLlH,IAAA,OAAIoG,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cACvCnG,KAAA,QAAKkG,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eACxCnG,KAAA,QAAKkG,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eACxCrG,IAAA,QACImH,GAAG,CAAEtB,WAAW,CAAC9C,GAAG,CAACI,MAAM,CAAE,CAC7BiE,GAAG,CAAErE,GAAG,CAACI,MAAO,CAChBiD,SAAS,CAAC,qCAAqC,CAC/CiB,OAAO,CAAGV,CAAC,EAAK,CACZA,CAAC,CAACC,MAAM,CAACU,KAAK,CAACC,OAAO,CAAG,MAAM,CACnC,CAAE,CACL,CAAC,cACFvH,IAAA,SAAMoG,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAEtD,GAAG,CAACI,MAAM,CAAO,CAAC,EAC1D,CAAC,cACNnD,IAAA,SAAMoG,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,IAAE,CAAM,CAAC,cACjDnG,KAAA,QAAKkG,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eACxCrG,IAAA,QACImH,GAAG,CAAEtB,WAAW,CAAC9C,GAAG,CAACK,MAAM,CAAE,CAC7BgE,GAAG,CAAErE,GAAG,CAACK,MAAO,CAChBgD,SAAS,CAAC,qCAAqC,CAC/CiB,OAAO,CAAGV,CAAC,EAAK,CACZA,CAAC,CAACC,MAAM,CAACU,KAAK,CAACC,OAAO,CAAG,MAAM,CACnC,CAAE,CACL,CAAC,cACFvH,IAAA,SAAMoG,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAEtD,GAAG,CAACK,MAAM,CAAO,CAAC,EAC1D,CAAC,EACL,CAAC,CACN,CAAC,cACLpD,IAAA,OAAIoG,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cACvCnG,KAAA,QAAAmG,QAAA,eACIrG,IAAA,QAAKoG,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAEtD,GAAG,CAACyE,UAAU,CAAM,CAAC,cACzEtH,KAAA,QAAKkG,SAAS,CAAC,uBAAuB,CAAAC,QAAA,EAAC,QAAM,CAACtD,GAAG,CAACC,UAAU,EAAM,CAAC,EAClE,CAAC,CACN,CAAC,cACLhD,IAAA,OAAIoG,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cACvCnG,KAAA,QAAAmG,QAAA,eACIrG,IAAA,QAAKoG,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAC7CtD,GAAG,CAAC0E,UAAU,EAAI,YAAY,CAC9B,CAAC,CACL1E,GAAG,CAAC0E,UAAU,eACXvH,KAAA,QAAKkG,SAAS,CAAC,uBAAuB,CAAAC,QAAA,EAAC,QAAM,CAACtD,GAAG,CAACM,UAAU,EAAM,CACrE,EACA,CAAC,CACN,CAAC,cACLrD,IAAA,OAAIoG,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cACvCnG,KAAA,QAAAmG,QAAA,eACInG,KAAA,QAAKkG,SAAS,CAAC,uBAAuB,CAAAC,QAAA,EAAEtD,GAAG,CAAC2E,YAAY,CAAC,KAAG,EAAK,CAAC,CACjE3E,GAAG,CAAC4E,YAAY,eACbzH,KAAA,QAAKkG,SAAS,CAAC,uBAAuB,CAAAC,QAAA,EAAEtD,GAAG,CAAC4E,YAAY,CAAC,KAAG,EAAK,CACpE,EACA,CAAC,CACN,CAAC,cACL3H,IAAA,OAAIoG,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cACvCrG,IAAA,SAAMoG,SAAS,CAAE,4DAA4DD,cAAc,CAACpD,GAAG,CAAC6E,UAAU,CAAC,EAAG,CAAAvB,QAAA,CACzGtD,GAAG,CAAC6E,UAAU,CACb,CAAC,CACP,CAAC,cACL5H,IAAA,OAAIoG,SAAS,CAAC,mDAAmD,CAAAC,QAAA,CAC5D,GAAI,CAAAhB,IAAI,CAACtC,GAAG,CAAC8E,UAAU,CAAC,CAACC,kBAAkB,CAAC,CAAC,CAC9C,CAAC,cACL9H,IAAA,OAAIoG,SAAS,CAAC,iDAAiD,CAAAC,QAAA,cAC3DrG,IAAA,WACIsG,OAAO,CAAEA,CAAA,GAAMlC,cAAc,CAACrB,GAAG,CAAE,CACnCqD,SAAS,CAAC,mCAAmC,CAC7C2B,KAAK,CAAC,cAAc,CAAA1B,QAAA,cAEpBrG,IAAA,CAACN,KAAK,GAAE,CAAC,CACL,CAAC,CACT,CAAC,GAzEAqD,GAAG,CAACmE,MA0ET,CAAC,EACR,CACJ,CACE,CAAC,EACL,CAAC,CACP,CAAC,CACL,CAAC,CAGLnF,YAAY,EAAIE,WAAW,eACxBjC,IAAA,QAAKoG,SAAS,CAAC,8GAA8G,CAAAC,QAAA,cACzHnG,KAAA,QAAKkG,SAAS,CAAC,wFAAwF,CAAAC,QAAA,eAEnGrG,IAAA,QAAKoG,SAAS,CAAC,2EAA2E,CAAAC,QAAA,cACtFnG,KAAA,QAAKkG,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAC9CnG,KAAA,QAAAmG,QAAA,eACIrG,IAAA,OAAIoG,SAAS,CAAC,mBAAmB,CAAAC,QAAA,CAAC,aAAW,CAAI,CAAC,cAClDnG,KAAA,MAAGkG,SAAS,CAAC,uBAAuB,CAAAC,QAAA,EAAC,OAC5B,CAAC,EAAAhG,qBAAA,CAAA4B,WAAW,CAAC+E,WAAW,UAAA3G,qBAAA,iBAAvBA,qBAAA,CAAyB4G,WAAW,CAAC,CAAC,GAAI,GAAGhF,WAAW,CAACiF,MAAM,SAAS,EAC/E,CAAC,EACH,CAAC,cACNlH,IAAA,WACIsG,OAAO,CAAEA,CAAA,GAAMtE,eAAe,CAAC,KAAK,CAAE,CACtCoE,SAAS,CAAC,2JAA2J,CAAAC,QAAA,CACxK,MAED,CAAQ,CAAC,EACR,CAAC,CACL,CAAC,cAENnG,KAAA,QAAKkG,SAAS,CAAC,KAAK,CAAAC,QAAA,eAEhBnG,KAAA,QAAKkG,SAAS,CAAC,wCAAwC,CAAAC,QAAA,eACnDrG,IAAA,SAAMoG,SAAS,CAAE,4DAA4DD,cAAc,CAAClE,WAAW,CAAC2F,UAAU,CAAC,EAAG,CAAAvB,QAAA,CACjHpE,WAAW,CAAC2F,UAAU,CAACX,WAAW,CAAC,CAAC,CACnC,CAAC,cACP/G,KAAA,QAAKkG,SAAS,CAAC,kCAAkC,CAAAC,QAAA,eAC7CnG,KAAA,MAAAmG,QAAA,EAAG,WAAS,CAAC,GAAI,CAAAhB,IAAI,CAACpD,WAAW,CAAC4F,UAAU,CAAC,CAACC,kBAAkB,CAAC,CAAC,EAAI,CAAC,cACvE9H,IAAA,MAAAqG,QAAA,CAAI,GAAI,CAAAhB,IAAI,CAACpD,WAAW,CAAC4F,UAAU,CAAC,CAACG,kBAAkB,CAAC,CAAC,CAAI,CAAC,EAC7D,CAAC,EACL,CAAC,cAGNhI,IAAA,QAAKoG,SAAS,CAAC,+DAA+D,CAAAC,QAAA,cAC1EnG,KAAA,QAAKkG,SAAS,CAAC,4CAA4C,CAAAC,QAAA,eAEvDnG,KAAA,QAAKkG,SAAS,CAAC,oBAAoB,CAAAC,QAAA,eAC/BrG,IAAA,QAAKoG,SAAS,CAAC,MAAM,CAAAC,QAAA,cACjBrG,IAAA,QACImH,GAAG,CAAEtB,WAAW,CAAC5D,WAAW,CAACkB,MAAM,CAAE,CACrCiE,GAAG,CAAEnF,WAAW,CAACkB,MAAO,CACxBiD,SAAS,CAAC,+EAA+E,CACzFiB,OAAO,CAAGV,CAAC,EAAK,CACZA,CAAC,CAACC,MAAM,CAACU,KAAK,CAACC,OAAO,CAAG,MAAM,CACnC,CAAE,CACL,CAAC,CACD,CAAC,cACNvH,IAAA,OAAIoG,SAAS,CAAC,iCAAiC,CAAAC,QAAA,CAAEpE,WAAW,CAACkB,MAAM,CAAK,CAAC,EACxE,CAAC,cAGNjD,KAAA,QAAKkG,SAAS,CAAC,aAAa,CAAAC,QAAA,eACxBrG,IAAA,QAAKoG,SAAS,CAAC,4GAA4G,CAAAC,QAAA,CAAC,IAE5H,CAAK,CAAC,CACLpE,WAAW,CAACgG,UAAU,eACnBjI,IAAA,MAAGoG,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CACpC,GAAI,CAAAhB,IAAI,CAACpD,WAAW,CAACgG,UAAU,CAAC,CAACH,kBAAkB,CAAC,CAAC,CACvD,CACN,EACA,CAAC,cAGN5H,KAAA,QAAKkG,SAAS,CAAC,oBAAoB,CAAAC,QAAA,eAC/BrG,IAAA,QAAKoG,SAAS,CAAC,MAAM,CAAAC,QAAA,cACjBrG,IAAA,QACImH,GAAG,CAAEtB,WAAW,CAAC5D,WAAW,CAACmB,MAAM,CAAE,CACrCgE,GAAG,CAAEnF,WAAW,CAACmB,MAAO,CACxBgD,SAAS,CAAC,+EAA+E,CACzFiB,OAAO,CAAGV,CAAC,EAAK,CACZA,CAAC,CAACC,MAAM,CAACU,KAAK,CAACC,OAAO,CAAG,MAAM,CACnC,CAAE,CACL,CAAC,CACD,CAAC,cACNvH,IAAA,OAAIoG,SAAS,CAAC,iCAAiC,CAAAC,QAAA,CAAEpE,WAAW,CAACmB,MAAM,CAAK,CAAC,EACxE,CAAC,EACL,CAAC,CACL,CAAC,cAGNlD,KAAA,QAAKkG,SAAS,CAAC,4CAA4C,CAAAC,QAAA,eAEvDnG,KAAA,QAAKkG,SAAS,CAAC,oDAAoD,CAAAC,QAAA,eAC/DnG,KAAA,QAAKkG,SAAS,CAAC,wCAAwC,CAAAC,QAAA,eACnDrG,IAAA,OAAIoG,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,aAAW,CAAI,CAAC,cAC7DrG,IAAA,SAAMoG,SAAS,CAAC,oEAAoE,CAAAC,QAAA,CAAC,QAErF,CAAM,CAAC,EACN,CAAC,cACNnG,KAAA,QAAKkG,SAAS,CAAC,WAAW,CAAAC,QAAA,eACtBnG,KAAA,QAAKkG,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACjCrG,IAAA,SAAMoG,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,SAAO,CAAM,CAAC,cACtDrG,IAAA,SAAMoG,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAEpE,WAAW,CAACuF,UAAU,CAAO,CAAC,EAClF,CAAC,cACNtH,KAAA,QAAKkG,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACjCrG,IAAA,SAAMoG,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,OAAK,CAAM,CAAC,cACpDrG,IAAA,SAAMoG,SAAS,CAAC,oCAAoC,CAAAC,QAAA,CAAEpE,WAAW,CAACe,UAAU,CAAO,CAAC,EACnF,CAAC,cACN9C,KAAA,QAAKkG,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACjCrG,IAAA,SAAMoG,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,QAAM,CAAM,CAAC,cACrDnG,KAAA,SAAMkG,SAAS,CAAC,kCAAkC,CAAAC,QAAA,EAAEpE,WAAW,CAACyF,YAAY,CAAC,KAAG,EAAM,CAAC,EACtF,CAAC,cACNxH,KAAA,QAAKkG,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACjCrG,IAAA,SAAMoG,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,OAAK,CAAM,CAAC,cACpDrG,IAAA,SAAMoG,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAEpE,WAAW,CAACiG,UAAU,EAAI,KAAK,CAAO,CAAC,EAC3F,CAAC,cACNhI,KAAA,QAAKkG,SAAS,CAAC,qDAAqD,CAAAC,QAAA,eAChErG,IAAA,SAAMoG,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,gBAAc,CAAM,CAAC,cAC7DnG,KAAA,SAAMkG,SAAS,CAAC,kCAAkC,CAAAC,QAAA,EAC7CpE,WAAW,CAACkG,sBAAsB,EAAIC,IAAI,CAACC,KAAK,CAACpG,WAAW,CAACyF,YAAY,CAAG,GAAG,CAAC,CAAC,KACtF,EAAM,CAAC,EACN,CAAC,EACL,CAAC,EACL,CAAC,cAGNxH,KAAA,QAAKkG,SAAS,CAAE,GAAGnE,WAAW,CAACwF,UAAU,CAAG,4BAA4B,CAAG,4BAA4B,wBAAyB,CAAApB,QAAA,eAC5HnG,KAAA,QAAKkG,SAAS,CAAC,wCAAwC,CAAAC,QAAA,eACnDrG,IAAA,OAAIoG,SAAS,CAAE,iBAAiBnE,WAAW,CAACwF,UAAU,CAAG,eAAe,CAAG,eAAe,EAAG,CAAApB,QAAA,CAAC,cAE9F,CAAI,CAAC,cACLrG,IAAA,SAAMoG,SAAS,CAAE,GAAGnE,WAAW,CAACwF,UAAU,CAAG,wBAAwB,CAAG,wBAAwB,6CAA8C,CAAApB,QAAA,CAAC,QAE/I,CAAM,CAAC,EACN,CAAC,CACLpE,WAAW,CAACwF,UAAU,cACnBvH,KAAA,QAAKkG,SAAS,CAAC,WAAW,CAAAC,QAAA,eACtBnG,KAAA,QAAKkG,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACjCrG,IAAA,SAAMoG,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,SAAO,CAAM,CAAC,cACtDrG,IAAA,SAAMoG,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAEpE,WAAW,CAACwF,UAAU,CAAO,CAAC,EAClF,CAAC,cACNvH,KAAA,QAAKkG,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACjCrG,IAAA,SAAMoG,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,OAAK,CAAM,CAAC,cACpDrG,IAAA,SAAMoG,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAEpE,WAAW,CAACoB,UAAU,CAAO,CAAC,EAClF,CAAC,cACNnD,KAAA,QAAKkG,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACjCrG,IAAA,SAAMoG,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,QAAM,CAAM,CAAC,cACrDnG,KAAA,SAAMkG,SAAS,CAAC,iCAAiC,CAAAC,QAAA,EAAEpE,WAAW,CAAC0F,YAAY,CAAC,KAAG,EAAM,CAAC,EACrF,CAAC,cACNzH,KAAA,QAAKkG,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACjCrG,IAAA,SAAMoG,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,OAAK,CAAM,CAAC,cACpDrG,IAAA,SAAMoG,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAEpE,WAAW,CAACqG,UAAU,EAAI,KAAK,CAAO,CAAC,EAC3F,CAAC,cACNpI,KAAA,QAAKkG,SAAS,CAAC,oDAAoD,CAAAC,QAAA,eAC/DrG,IAAA,SAAMoG,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,gBAAc,CAAM,CAAC,cAC7DnG,KAAA,SAAMkG,SAAS,CAAC,iCAAiC,CAAAC,QAAA,EAC5CpE,WAAW,CAACsG,sBAAsB,EAAIH,IAAI,CAACC,KAAK,CAACpG,WAAW,CAAC0F,YAAY,CAAG,GAAG,CAAC,CAAC,KACtF,EAAM,CAAC,EACN,CAAC,EACL,CAAC,cAENzH,KAAA,QAAKkG,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC7BrG,IAAA,QAAKoG,SAAS,CAAC,oBAAoB,CAAAC,QAAA,cAC/BrG,IAAA,QAAKoG,SAAS,CAAC,iBAAiB,CAACoC,IAAI,CAAC,cAAc,CAACC,OAAO,CAAC,WAAW,CAAApC,QAAA,cACpErG,IAAA,SAAM0I,QAAQ,CAAC,SAAS,CAACC,CAAC,CAAC,qDAAqD,CAACC,QAAQ,CAAC,SAAS,CAAE,CAAC,CACrG,CAAC,CACL,CAAC,cACN5I,IAAA,MAAGoG,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,gDAA8C,CAAG,CAAC,EAC7F,CACR,EACA,CAAC,EACL,CAAC,CAGL,CAACpE,WAAW,CAAC4G,MAAM,EAAI5G,WAAW,CAACgG,UAAU,gBAC1C/H,KAAA,QAAKkG,SAAS,CAAC,2BAA2B,CAAAC,QAAA,eACtCrG,IAAA,OAAIoG,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,mBAAiB,CAAI,CAAC,cACvEnG,KAAA,QAAKkG,SAAS,CAAC,uCAAuC,CAAAC,QAAA,EACjDpE,WAAW,CAACgG,UAAU,eACnB/H,KAAA,QAAKkG,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACjCrG,IAAA,SAAMoG,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,aAAW,CAAM,CAAC,cAC1DrG,IAAA,SAAMoG,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAC9C,GAAI,CAAAhB,IAAI,CAACpD,WAAW,CAACgG,UAAU,CAAC,CAACH,kBAAkB,CAAC,CAAC,CACpD,CAAC,EACN,CACR,CACA7F,WAAW,CAAC4G,MAAM,eACf3I,KAAA,QAAKkG,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACjCrG,IAAA,SAAMoG,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,SAAO,CAAM,CAAC,cACtDrG,IAAA,SAAMoG,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CACzDpE,WAAW,CAAC4G,MAAM,CAACC,OAAO,CAAC,GAAG,CAAE,GAAG,CAAC,CACnC,CAAC,EACN,CACR,EACA,CAAC,EACL,CACR,cAGD9I,IAAA,QAAKoG,SAAS,CAAC,gDAAgD,CAAAC,QAAA,cAC3DrG,IAAA,WACIsG,OAAO,CAAEA,CAAA,GAAMtE,eAAe,CAAC,KAAK,CAAE,CACtCoE,SAAS,CAAC,6FAA6F,CAAAC,QAAA,CAC1G,OAED,CAAQ,CAAC,CACR,CAAC,EACL,CAAC,EACL,CAAC,CACL,CACR,EACA,CAAC,CAEd,CAEA,cAAe,CAAAjG,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}