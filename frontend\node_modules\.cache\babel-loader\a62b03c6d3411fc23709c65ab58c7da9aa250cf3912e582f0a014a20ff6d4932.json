{"ast": null, "code": "var _jsxFileName = \"C:\\\\MAMP\\\\htdocs\\\\FanBet247\\\\frontend\\\\src\\\\components\\\\CustomModal.js\";\nimport React from 'react';\nimport { FaExclamationTriangle, FaInfoCircle, FaCheckCircle, FaTimes } from 'react-icons/fa';\nimport './CustomModal.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CustomModal = ({\n  isOpen,\n  onClose,\n  onConfirm,\n  title,\n  message,\n  type = 'confirm',\n  // 'alert', 'confirm', 'success', 'error', 'warning'\n  confirmText = 'Confirm',\n  cancelText = 'Cancel',\n  showCancel = true,\n  confirmButtonColor = 'blue',\n  children\n}) => {\n  if (!isOpen) return null;\n  const getIcon = () => {\n    switch (type) {\n      case 'success':\n        return /*#__PURE__*/_jsxDEV(FaCheckCircle, {\n          className: \"modal-icon success\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 24\n        }, this);\n      case 'error':\n        return /*#__PURE__*/_jsxDEV(FaExclamationTriangle, {\n          className: \"modal-icon error\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 24\n        }, this);\n      case 'warning':\n        return /*#__PURE__*/_jsxDEV(FaExclamationTriangle, {\n          className: \"modal-icon warning\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 24\n        }, this);\n      case 'alert':\n      case 'confirm':\n      default:\n        return /*#__PURE__*/_jsxDEV(FaInfoCircle, {\n          className: \"modal-icon info\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 24\n        }, this);\n    }\n  };\n  const getConfirmButtonClass = () => {\n    switch (confirmButtonColor) {\n      case 'red':\n        return 'btn-danger';\n      case 'green':\n        return 'btn-success';\n      case 'yellow':\n        return 'btn-warning';\n      case 'blue':\n      default:\n        return 'btn-primary';\n    }\n  };\n  const handleOverlayClick = e => {\n    if (e.target === e.currentTarget) {\n      onClose();\n    }\n  };\n  const handleConfirm = () => {\n    if (onConfirm) {\n      onConfirm();\n    }\n    onClose();\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"custom-modal-overlay\",\n    onClick: handleOverlayClick,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"custom-modal\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"custom-modal-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-title-container\",\n          children: [getIcon(), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"modal-title\",\n            children: title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 68,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"modal-close-btn\",\n          onClick: onClose,\n          \"aria-label\": \"Close modal\",\n          children: /*#__PURE__*/_jsxDEV(FaTimes, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"custom-modal-body\",\n        children: [message && /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"modal-message\",\n          children: message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 33\n        }, this), children]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"custom-modal-footer\",\n        children: [showCancel && /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-secondary\",\n          onClick: onClose,\n          children: cancelText\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 25\n        }, this), (type === 'confirm' || onConfirm) && /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `btn ${getConfirmButtonClass()}`,\n          onClick: handleConfirm,\n          children: confirmText\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 25\n        }, this), type === 'alert' && !onConfirm && /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary\",\n          onClick: onClose,\n          children: \"OK\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 64,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 63,\n    columnNumber: 9\n  }, this);\n};\n_c = CustomModal;\nexport default CustomModal;\nvar _c;\n$RefreshReg$(_c, \"CustomModal\");", "map": {"version": 3, "names": ["React", "FaExclamationTriangle", "FaInfoCircle", "FaCheckCircle", "FaTimes", "jsxDEV", "_jsxDEV", "CustomModal", "isOpen", "onClose", "onConfirm", "title", "message", "type", "confirmText", "cancelText", "showCancel", "confirmButtonColor", "children", "getIcon", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getConfirmButtonClass", "handleOverlayClick", "e", "target", "currentTarget", "handleConfirm", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/components/CustomModal.js"], "sourcesContent": ["import React from 'react';\nimport { FaExclamationTriangle, FaInfoCircle, FaCheckCircle, FaTimes } from 'react-icons/fa';\nimport './CustomModal.css';\n\nconst CustomModal = ({ \n    isOpen, \n    onClose, \n    onConfirm, \n    title, \n    message, \n    type = 'confirm', // 'alert', 'confirm', 'success', 'error', 'warning'\n    confirmText = 'Confirm',\n    cancelText = 'Cancel',\n    showCancel = true,\n    confirmButtonColor = 'blue',\n    children\n}) => {\n    if (!isOpen) return null;\n\n    const getIcon = () => {\n        switch (type) {\n            case 'success':\n                return <FaCheckCircle className=\"modal-icon success\" />;\n            case 'error':\n                return <FaExclamationTriangle className=\"modal-icon error\" />;\n            case 'warning':\n                return <FaExclamationTriangle className=\"modal-icon warning\" />;\n            case 'alert':\n            case 'confirm':\n            default:\n                return <FaInfoCircle className=\"modal-icon info\" />;\n        }\n    };\n\n    const getConfirmButtonClass = () => {\n        switch (confirmButtonColor) {\n            case 'red':\n                return 'btn-danger';\n            case 'green':\n                return 'btn-success';\n            case 'yellow':\n                return 'btn-warning';\n            case 'blue':\n            default:\n                return 'btn-primary';\n        }\n    };\n\n    const handleOverlayClick = (e) => {\n        if (e.target === e.currentTarget) {\n            onClose();\n        }\n    };\n\n    const handleConfirm = () => {\n        if (onConfirm) {\n            onConfirm();\n        }\n        onClose();\n    };\n\n    return (\n        <div className=\"custom-modal-overlay\" onClick={handleOverlayClick}>\n            <div className=\"custom-modal\">\n                <div className=\"custom-modal-header\">\n                    <div className=\"modal-title-container\">\n                        {getIcon()}\n                        <h3 className=\"modal-title\">{title}</h3>\n                    </div>\n                    <button \n                        className=\"modal-close-btn\"\n                        onClick={onClose}\n                        aria-label=\"Close modal\"\n                    >\n                        <FaTimes />\n                    </button>\n                </div>\n\n                <div className=\"custom-modal-body\">\n                    {message && <p className=\"modal-message\">{message}</p>}\n                    {children}\n                </div>\n\n                <div className=\"custom-modal-footer\">\n                    {showCancel && (\n                        <button \n                            className=\"btn btn-secondary\"\n                            onClick={onClose}\n                        >\n                            {cancelText}\n                        </button>\n                    )}\n                    {(type === 'confirm' || onConfirm) && (\n                        <button \n                            className={`btn ${getConfirmButtonClass()}`}\n                            onClick={handleConfirm}\n                        >\n                            {confirmText}\n                        </button>\n                    )}\n                    {type === 'alert' && !onConfirm && (\n                        <button \n                            className=\"btn btn-primary\"\n                            onClick={onClose}\n                        >\n                            OK\n                        </button>\n                    )}\n                </div>\n            </div>\n        </div>\n    );\n};\n\nexport default CustomModal;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,qBAAqB,EAAEC,YAAY,EAAEC,aAAa,EAAEC,OAAO,QAAQ,gBAAgB;AAC5F,OAAO,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,MAAMC,WAAW,GAAGA,CAAC;EACjBC,MAAM;EACNC,OAAO;EACPC,SAAS;EACTC,KAAK;EACLC,OAAO;EACPC,IAAI,GAAG,SAAS;EAAE;EAClBC,WAAW,GAAG,SAAS;EACvBC,UAAU,GAAG,QAAQ;EACrBC,UAAU,GAAG,IAAI;EACjBC,kBAAkB,GAAG,MAAM;EAC3BC;AACJ,CAAC,KAAK;EACF,IAAI,CAACV,MAAM,EAAE,OAAO,IAAI;EAExB,MAAMW,OAAO,GAAGA,CAAA,KAAM;IAClB,QAAQN,IAAI;MACR,KAAK,SAAS;QACV,oBAAOP,OAAA,CAACH,aAAa;UAACiB,SAAS,EAAC;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC3D,KAAK,OAAO;QACR,oBAAOlB,OAAA,CAACL,qBAAqB;UAACmB,SAAS,EAAC;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACjE,KAAK,SAAS;QACV,oBAAOlB,OAAA,CAACL,qBAAqB;UAACmB,SAAS,EAAC;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACnE,KAAK,OAAO;MACZ,KAAK,SAAS;MACd;QACI,oBAAOlB,OAAA,CAACJ,YAAY;UAACkB,SAAS,EAAC;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IAC3D;EACJ,CAAC;EAED,MAAMC,qBAAqB,GAAGA,CAAA,KAAM;IAChC,QAAQR,kBAAkB;MACtB,KAAK,KAAK;QACN,OAAO,YAAY;MACvB,KAAK,OAAO;QACR,OAAO,aAAa;MACxB,KAAK,QAAQ;QACT,OAAO,aAAa;MACxB,KAAK,MAAM;MACX;QACI,OAAO,aAAa;IAC5B;EACJ,CAAC;EAED,MAAMS,kBAAkB,GAAIC,CAAC,IAAK;IAC9B,IAAIA,CAAC,CAACC,MAAM,KAAKD,CAAC,CAACE,aAAa,EAAE;MAC9BpB,OAAO,CAAC,CAAC;IACb;EACJ,CAAC;EAED,MAAMqB,aAAa,GAAGA,CAAA,KAAM;IACxB,IAAIpB,SAAS,EAAE;MACXA,SAAS,CAAC,CAAC;IACf;IACAD,OAAO,CAAC,CAAC;EACb,CAAC;EAED,oBACIH,OAAA;IAAKc,SAAS,EAAC,sBAAsB;IAACW,OAAO,EAAEL,kBAAmB;IAAAR,QAAA,eAC9DZ,OAAA;MAAKc,SAAS,EAAC,cAAc;MAAAF,QAAA,gBACzBZ,OAAA;QAAKc,SAAS,EAAC,qBAAqB;QAAAF,QAAA,gBAChCZ,OAAA;UAAKc,SAAS,EAAC,uBAAuB;UAAAF,QAAA,GACjCC,OAAO,CAAC,CAAC,eACVb,OAAA;YAAIc,SAAS,EAAC,aAAa;YAAAF,QAAA,EAAEP;UAAK;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC,eACNlB,OAAA;UACIc,SAAS,EAAC,iBAAiB;UAC3BW,OAAO,EAAEtB,OAAQ;UACjB,cAAW,aAAa;UAAAS,QAAA,eAExBZ,OAAA,CAACF,OAAO;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,eAENlB,OAAA;QAAKc,SAAS,EAAC,mBAAmB;QAAAF,QAAA,GAC7BN,OAAO,iBAAIN,OAAA;UAAGc,SAAS,EAAC,eAAe;UAAAF,QAAA,EAAEN;QAAO;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EACrDN,QAAQ;MAAA;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,eAENlB,OAAA;QAAKc,SAAS,EAAC,qBAAqB;QAAAF,QAAA,GAC/BF,UAAU,iBACPV,OAAA;UACIc,SAAS,EAAC,mBAAmB;UAC7BW,OAAO,EAAEtB,OAAQ;UAAAS,QAAA,EAEhBH;QAAU;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CACX,EACA,CAACX,IAAI,KAAK,SAAS,IAAIH,SAAS,kBAC7BJ,OAAA;UACIc,SAAS,EAAE,OAAOK,qBAAqB,CAAC,CAAC,EAAG;UAC5CM,OAAO,EAAED,aAAc;UAAAZ,QAAA,EAEtBJ;QAAW;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CACX,EACAX,IAAI,KAAK,OAAO,IAAI,CAACH,SAAS,iBAC3BJ,OAAA;UACIc,SAAS,EAAC,iBAAiB;UAC3BW,OAAO,EAAEtB,OAAQ;UAAAS,QAAA,EACpB;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACX;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAACQ,EAAA,GA5GIzB,WAAW;AA8GjB,eAAeA,WAAW;AAAC,IAAAyB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}