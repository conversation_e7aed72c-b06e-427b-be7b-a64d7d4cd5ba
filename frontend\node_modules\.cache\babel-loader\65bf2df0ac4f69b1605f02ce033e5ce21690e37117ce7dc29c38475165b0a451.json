{"ast": null, "code": "var _jsxFileName = \"C:\\\\MAMP\\\\htdocs\\\\FanBet247\\\\frontend\\\\src\\\\pages\\\\AdminLoginPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport axios from 'axios';\nimport { useNavigate } from 'react-router-dom';\nimport { AdminOTPVerification, Admin2FAVerification, Admin2FASetup } from '../components/Admin';\nimport './AdminLoginPage.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AdminLoginPage = () => {\n  _s();\n  const navigate = useNavigate();\n\n  // Authentication flow state\n  const [authStep, setAuthStep] = useState('login'); // 'login', 'otp', '2fa', '2fa_setup'\n  const [adminData, setAdminData] = useState(null);\n\n  // Login form state\n  const [identifier, setIdentifier] = useState('');\n  const [password, setPassword] = useState('');\n  const [error, setError] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [rememberMe, setRememberMe] = useState(false);\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setError('');\n    setIsLoading(true);\n    try {\n      const response = await axios.post('/backend/handlers/admin_login_handler.php', {\n        identifier,\n        password,\n        remember_me: rememberMe\n      });\n      console.log('Admin login request URL:', '/backend/handlers/admin_login_handler.php');\n      if (response.data.success) {\n        // Store admin data for potential next steps\n        setAdminData({\n          admin_id: response.data.admin_id,\n          username: response.data.username,\n          role: response.data.role\n        });\n\n        // Check if additional authentication is required\n        if (response.data.requires_additional_auth) {\n          const nextStep = response.data.next_step;\n          if (nextStep === 'otp') {\n            setAuthStep('otp');\n          } else if (nextStep === '2fa') {\n            // Check if 2FA is set up\n            setAuthStep('2fa');\n          }\n        } else {\n          // Complete login - no additional auth required\n          completeLogin({\n            admin_id: response.data.admin_id,\n            username: response.data.username,\n            role: response.data.role,\n            auth_method: response.data.auth_method || 'password_only'\n          });\n        }\n      } else {\n        setError(response.data.message || 'Login failed');\n      }\n    } catch (error) {\n      console.error('Login error:', error);\n      if (error.response) {\n        setError(error.response.data.message || 'Invalid credentials');\n      } else if (error.request) {\n        setError('Network error. Please check your connection.');\n      } else {\n        setError('An error occurred. Please try again.');\n      }\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const completeLogin = loginData => {\n    // Store authentication data\n    localStorage.setItem('adminId', loginData.admin_id);\n    localStorage.setItem('adminUsername', loginData.username);\n    localStorage.setItem('adminRole', loginData.role);\n    localStorage.setItem('adminAuthMethod', loginData.auth_method);\n    if (loginData.session_token) {\n      localStorage.setItem('adminSessionToken', loginData.session_token);\n    }\n\n    // Navigate to dashboard\n    navigate('/admin/dashboard');\n  };\n  const handleAuthSuccess = authData => {\n    completeLogin(authData);\n  };\n  const handleBackToLogin = () => {\n    setAuthStep('login');\n    setAdminData(null);\n    setError('');\n    setPassword(''); // Clear password for security\n  };\n\n  // Render different authentication steps\n  if (authStep === 'otp' && adminData) {\n    return /*#__PURE__*/_jsxDEV(AdminOTPVerification, {\n      adminId: adminData.admin_id,\n      username: adminData.username,\n      onSuccess: handleAuthSuccess,\n      onBack: handleBackToLogin\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 109,\n      columnNumber: 13\n    }, this);\n  }\n  if (authStep === '2fa' && adminData) {\n    return /*#__PURE__*/_jsxDEV(Admin2FAVerification, {\n      adminId: adminData.admin_id,\n      username: adminData.username,\n      onSuccess: handleAuthSuccess,\n      onBack: handleBackToLogin\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 120,\n      columnNumber: 13\n    }, this);\n  }\n  if (authStep === '2fa_setup' && adminData) {\n    return /*#__PURE__*/_jsxDEV(Admin2FASetup, {\n      adminId: adminData.admin_id,\n      username: adminData.username,\n      onSuccess: handleAuthSuccess,\n      onBack: handleBackToLogin\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 131,\n      columnNumber: 13\n    }, this);\n  }\n\n  // Default login form\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"admin-login-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"login-left-panel\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"login-logo\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"logo-icon\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            strokeWidth: \"2\",\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            children: [/*#__PURE__*/_jsxDEV(\"rect\", {\n              x: \"2\",\n              y: \"3\",\n              width: \"20\",\n              height: \"14\",\n              rx: \"2\",\n              ry: \"2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 147,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n              x1: \"8\",\n              y1: \"21\",\n              x2: \"16\",\n              y2: \"21\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n              x1: \"12\",\n              y1: \"17\",\n              x2: \"12\",\n              y2: \"21\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"FanBet247\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 144,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"login-form-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Admin Login\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"login-subtitle\",\n          children: \"Enter your credentials to access the admin dashboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"security-notice\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"security-icon\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              viewBox: \"0 0 24 24\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              strokeWidth: \"2\",\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 163,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Enhanced security enabled - Additional verification may be required\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 21\n        }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"error-message\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 31\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"identifier\",\n              children: \"Username or Email\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"input-container\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                id: \"identifier\",\n                value: identifier,\n                onChange: e => setIdentifier(e.target.value),\n                disabled: isLoading,\n                placeholder: \"Enter your username or email\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 175,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"input-icon\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  viewBox: \"0 0 24 24\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\",\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 186,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                    cx: \"12\",\n                    cy: \"7\",\n                    r: \"4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 187,\n                    columnNumber: 41\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 185,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"password\",\n              children: \"Password\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"input-container\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"password\",\n                id: \"password\",\n                value: password,\n                onChange: e => setPassword(e.target.value),\n                disabled: isLoading,\n                placeholder: \"Enter your password\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 196,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"input-icon\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  viewBox: \"0 0 24 24\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\",\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  children: [/*#__PURE__*/_jsxDEV(\"rect\", {\n                    x: \"3\",\n                    y: \"11\",\n                    width: \"18\",\n                    height: \"11\",\n                    rx: \"2\",\n                    ry: \"2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 207,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M7 11V7a5 5 0 0 1 10 0v4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 208,\n                    columnNumber: 41\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 206,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-options\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"remember-me\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                id: \"rememberMe\",\n                checked: rememberMe,\n                onChange: e => setRememberMe(e.target.checked)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 216,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"rememberMe\",\n                children: \"Remember me\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 222,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              className: \"forgot-password\",\n              onClick: () => alert('Password reset functionality coming soon!'),\n              children: \"Forgot password?\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            className: \"login-button\",\n            disabled: isLoading,\n            children: isLoading ? 'Logging in...' : 'Login'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 155,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 143,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"login-right-panel\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 234,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 142,\n    columnNumber: 9\n  }, this);\n};\n_s(AdminLoginPage, \"+i+vG8ZZN2IMWgLnqQjlFqQNSUo=\", false, function () {\n  return [useNavigate];\n});\n_c = AdminLoginPage;\nexport default AdminLoginPage;\nvar _c;\n$RefreshReg$(_c, \"AdminLoginPage\");", "map": {"version": 3, "names": ["React", "useState", "axios", "useNavigate", "AdminOTPVerification", "Admin2FAVerification", "Admin2FASetup", "jsxDEV", "_jsxDEV", "AdminLoginPage", "_s", "navigate", "authStep", "setAuthStep", "adminData", "setAdminData", "identifier", "setIdentifier", "password", "setPassword", "error", "setError", "isLoading", "setIsLoading", "rememberMe", "setRememberMe", "handleSubmit", "e", "preventDefault", "response", "post", "remember_me", "console", "log", "data", "success", "admin_id", "username", "role", "requires_additional_auth", "nextStep", "next_step", "completeLogin", "auth_method", "message", "request", "loginData", "localStorage", "setItem", "session_token", "handleAuthSuccess", "authData", "handleBackToLogin", "adminId", "onSuccess", "onBack", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "children", "xmlns", "viewBox", "fill", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "x", "y", "width", "height", "rx", "ry", "x1", "y1", "x2", "y2", "d", "onSubmit", "htmlFor", "type", "id", "value", "onChange", "target", "disabled", "placeholder", "required", "cx", "cy", "r", "checked", "onClick", "alert", "_c", "$RefreshReg$"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/pages/AdminLoginPage.js"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport axios from 'axios';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport { AdminOTPVerification, Admin2FAVerification, Admin2FASetup } from '../components/Admin';\r\nimport './AdminLoginPage.css';\r\n\r\n\r\nconst AdminLoginPage = () => {\r\n    const navigate = useNavigate();\r\n\r\n    // Authentication flow state\r\n    const [authStep, setAuthStep] = useState('login'); // 'login', 'otp', '2fa', '2fa_setup'\r\n    const [adminData, setAdminData] = useState(null);\r\n\r\n    // Login form state\r\n    const [identifier, setIdentifier] = useState('');\r\n    const [password, setPassword] = useState('');\r\n    const [error, setError] = useState('');\r\n    const [isLoading, setIsLoading] = useState(false);\r\n    const [rememberMe, setRememberMe] = useState(false);\r\n\r\n    const handleSubmit = async (e) => {\r\n        e.preventDefault();\r\n        setError('');\r\n        setIsLoading(true);\r\n\r\n        try {\r\n            const response = await axios.post('/backend/handlers/admin_login_handler.php', {\r\n                identifier,\r\n                password,\r\n                remember_me: rememberMe\r\n            });\r\n\r\n            console.log('Admin login request URL:', '/backend/handlers/admin_login_handler.php');\r\n\r\n            if (response.data.success) {\r\n                // Store admin data for potential next steps\r\n                setAdminData({\r\n                    admin_id: response.data.admin_id,\r\n                    username: response.data.username,\r\n                    role: response.data.role\r\n                });\r\n\r\n                // Check if additional authentication is required\r\n                if (response.data.requires_additional_auth) {\r\n                    const nextStep = response.data.next_step;\r\n\r\n                    if (nextStep === 'otp') {\r\n                        setAuthStep('otp');\r\n                    } else if (nextStep === '2fa') {\r\n                        // Check if 2FA is set up\r\n                        setAuthStep('2fa');\r\n                    }\r\n                } else {\r\n                    // Complete login - no additional auth required\r\n                    completeLogin({\r\n                        admin_id: response.data.admin_id,\r\n                        username: response.data.username,\r\n                        role: response.data.role,\r\n                        auth_method: response.data.auth_method || 'password_only'\r\n                    });\r\n                }\r\n            } else {\r\n                setError(response.data.message || 'Login failed');\r\n            }\r\n        } catch (error) {\r\n            console.error('Login error:', error);\r\n            if (error.response) {\r\n                setError(error.response.data.message || 'Invalid credentials');\r\n            } else if (error.request) {\r\n                setError('Network error. Please check your connection.');\r\n            } else {\r\n                setError('An error occurred. Please try again.');\r\n            }\r\n        } finally {\r\n            setIsLoading(false);\r\n        }\r\n    };\r\n\r\n    const completeLogin = (loginData) => {\r\n        // Store authentication data\r\n        localStorage.setItem('adminId', loginData.admin_id);\r\n        localStorage.setItem('adminUsername', loginData.username);\r\n        localStorage.setItem('adminRole', loginData.role);\r\n        localStorage.setItem('adminAuthMethod', loginData.auth_method);\r\n\r\n        if (loginData.session_token) {\r\n            localStorage.setItem('adminSessionToken', loginData.session_token);\r\n        }\r\n\r\n        // Navigate to dashboard\r\n        navigate('/admin/dashboard');\r\n    };\r\n\r\n    const handleAuthSuccess = (authData) => {\r\n        completeLogin(authData);\r\n    };\r\n\r\n    const handleBackToLogin = () => {\r\n        setAuthStep('login');\r\n        setAdminData(null);\r\n        setError('');\r\n        setPassword(''); // Clear password for security\r\n    };\r\n\r\n    // Render different authentication steps\r\n    if (authStep === 'otp' && adminData) {\r\n        return (\r\n            <AdminOTPVerification\r\n                adminId={adminData.admin_id}\r\n                username={adminData.username}\r\n                onSuccess={handleAuthSuccess}\r\n                onBack={handleBackToLogin}\r\n            />\r\n        );\r\n    }\r\n\r\n    if (authStep === '2fa' && adminData) {\r\n        return (\r\n            <Admin2FAVerification\r\n                adminId={adminData.admin_id}\r\n                username={adminData.username}\r\n                onSuccess={handleAuthSuccess}\r\n                onBack={handleBackToLogin}\r\n            />\r\n        );\r\n    }\r\n\r\n    if (authStep === '2fa_setup' && adminData) {\r\n        return (\r\n            <Admin2FASetup\r\n                adminId={adminData.admin_id}\r\n                username={adminData.username}\r\n                onSuccess={handleAuthSuccess}\r\n                onBack={handleBackToLogin}\r\n            />\r\n        );\r\n    }\r\n\r\n    // Default login form\r\n    return (\r\n        <div className=\"admin-login-container\">\r\n            <div className=\"login-left-panel\">\r\n                <div className=\"login-logo\">\r\n                    <div className=\"logo-icon\">\r\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\">\r\n                            <rect x=\"2\" y=\"3\" width=\"20\" height=\"14\" rx=\"2\" ry=\"2\"></rect>\r\n                            <line x1=\"8\" y1=\"21\" x2=\"16\" y2=\"21\"></line>\r\n                            <line x1=\"12\" y1=\"17\" x2=\"12\" y2=\"21\"></line>\r\n                        </svg>\r\n                    </div>\r\n                    <h1>FanBet247</h1>\r\n                </div>\r\n\r\n                <div className=\"login-form-container\">\r\n                    <h2>Admin Login</h2>\r\n                    <p className=\"login-subtitle\">Enter your credentials to access the admin dashboard</p>\r\n\r\n                    {/* Security Notice */}\r\n                    <div className=\"security-notice\">\r\n                        <div className=\"security-icon\">\r\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\">\r\n                                <path d=\"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z\"></path>\r\n                            </svg>\r\n                        </div>\r\n                        <span>Enhanced security enabled - Additional verification may be required</span>\r\n                    </div>\r\n\r\n                    {error && <div className=\"error-message\">{error}</div>}\r\n\r\n                    <form onSubmit={handleSubmit}>\r\n                        <div className=\"form-group\">\r\n                            <label htmlFor=\"identifier\">Username or Email</label>\r\n                            <div className=\"input-container\">\r\n                                <input\r\n                                    type=\"text\"\r\n                                    id=\"identifier\"\r\n                                    value={identifier}\r\n                                    onChange={(e) => setIdentifier(e.target.value)}\r\n                                    disabled={isLoading}\r\n                                    placeholder=\"Enter your username or email\"\r\n                                    required\r\n                                />\r\n                                <div className=\"input-icon\">\r\n                                    <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\">\r\n                                        <path d=\"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2\"></path>\r\n                                        <circle cx=\"12\" cy=\"7\" r=\"4\"></circle>\r\n                                    </svg>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n\r\n                        <div className=\"form-group\">\r\n                            <label htmlFor=\"password\">Password</label>\r\n                            <div className=\"input-container\">\r\n                                <input\r\n                                    type=\"password\"\r\n                                    id=\"password\"\r\n                                    value={password}\r\n                                    onChange={(e) => setPassword(e.target.value)}\r\n                                    disabled={isLoading}\r\n                                    placeholder=\"Enter your password\"\r\n                                    required\r\n                                />\r\n                                <div className=\"input-icon\">\r\n                                    <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\">\r\n                                        <rect x=\"3\" y=\"11\" width=\"18\" height=\"11\" rx=\"2\" ry=\"2\"></rect>\r\n                                        <path d=\"M7 11V7a5 5 0 0 1 10 0v4\"></path>\r\n                                    </svg>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n\r\n                        <div className=\"form-options\">\r\n                            <div className=\"remember-me\">\r\n                                <input\r\n                                    type=\"checkbox\"\r\n                                    id=\"rememberMe\"\r\n                                    checked={rememberMe}\r\n                                    onChange={(e) => setRememberMe(e.target.checked)}\r\n                                />\r\n                                <label htmlFor=\"rememberMe\">Remember me</label>\r\n                            </div>\r\n                            <button type=\"button\" className=\"forgot-password\" onClick={() => alert('Password reset functionality coming soon!')}>Forgot password?</button>\r\n                        </div>\r\n\r\n                        <button type=\"submit\" className=\"login-button\" disabled={isLoading}>\r\n                            {isLoading ? 'Logging in...' : 'Login'}\r\n                        </button>\r\n                    </form>\r\n                </div>\r\n            </div>\r\n\r\n            <div className=\"login-right-panel\"></div>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default AdminLoginPage;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,oBAAoB,EAAEC,oBAAoB,EAAEC,aAAa,QAAQ,qBAAqB;AAC/F,OAAO,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAG9B,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAMC,QAAQ,GAAGR,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAM,CAACS,QAAQ,EAAEC,WAAW,CAAC,GAAGZ,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;EACnD,MAAM,CAACa,SAAS,EAAEC,YAAY,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;;EAEhD;EACA,MAAM,CAACe,UAAU,EAAEC,aAAa,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACiB,QAAQ,EAAEC,WAAW,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACmB,KAAK,EAAEC,QAAQ,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACqB,SAAS,EAAEC,YAAY,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACuB,UAAU,EAAEC,aAAa,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EAEnD,MAAMyB,YAAY,GAAG,MAAOC,CAAC,IAAK;IAC9BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBP,QAAQ,CAAC,EAAE,CAAC;IACZE,YAAY,CAAC,IAAI,CAAC;IAElB,IAAI;MACA,MAAMM,QAAQ,GAAG,MAAM3B,KAAK,CAAC4B,IAAI,CAAC,2CAA2C,EAAE;QAC3Ed,UAAU;QACVE,QAAQ;QACRa,WAAW,EAAEP;MACjB,CAAC,CAAC;MAEFQ,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE,2CAA2C,CAAC;MAEpF,IAAIJ,QAAQ,CAACK,IAAI,CAACC,OAAO,EAAE;QACvB;QACApB,YAAY,CAAC;UACTqB,QAAQ,EAAEP,QAAQ,CAACK,IAAI,CAACE,QAAQ;UAChCC,QAAQ,EAAER,QAAQ,CAACK,IAAI,CAACG,QAAQ;UAChCC,IAAI,EAAET,QAAQ,CAACK,IAAI,CAACI;QACxB,CAAC,CAAC;;QAEF;QACA,IAAIT,QAAQ,CAACK,IAAI,CAACK,wBAAwB,EAAE;UACxC,MAAMC,QAAQ,GAAGX,QAAQ,CAACK,IAAI,CAACO,SAAS;UAExC,IAAID,QAAQ,KAAK,KAAK,EAAE;YACpB3B,WAAW,CAAC,KAAK,CAAC;UACtB,CAAC,MAAM,IAAI2B,QAAQ,KAAK,KAAK,EAAE;YAC3B;YACA3B,WAAW,CAAC,KAAK,CAAC;UACtB;QACJ,CAAC,MAAM;UACH;UACA6B,aAAa,CAAC;YACVN,QAAQ,EAAEP,QAAQ,CAACK,IAAI,CAACE,QAAQ;YAChCC,QAAQ,EAAER,QAAQ,CAACK,IAAI,CAACG,QAAQ;YAChCC,IAAI,EAAET,QAAQ,CAACK,IAAI,CAACI,IAAI;YACxBK,WAAW,EAAEd,QAAQ,CAACK,IAAI,CAACS,WAAW,IAAI;UAC9C,CAAC,CAAC;QACN;MACJ,CAAC,MAAM;QACHtB,QAAQ,CAACQ,QAAQ,CAACK,IAAI,CAACU,OAAO,IAAI,cAAc,CAAC;MACrD;IACJ,CAAC,CAAC,OAAOxB,KAAK,EAAE;MACZY,OAAO,CAACZ,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;MACpC,IAAIA,KAAK,CAACS,QAAQ,EAAE;QAChBR,QAAQ,CAACD,KAAK,CAACS,QAAQ,CAACK,IAAI,CAACU,OAAO,IAAI,qBAAqB,CAAC;MAClE,CAAC,MAAM,IAAIxB,KAAK,CAACyB,OAAO,EAAE;QACtBxB,QAAQ,CAAC,8CAA8C,CAAC;MAC5D,CAAC,MAAM;QACHA,QAAQ,CAAC,sCAAsC,CAAC;MACpD;IACJ,CAAC,SAAS;MACNE,YAAY,CAAC,KAAK,CAAC;IACvB;EACJ,CAAC;EAED,MAAMmB,aAAa,GAAII,SAAS,IAAK;IACjC;IACAC,YAAY,CAACC,OAAO,CAAC,SAAS,EAAEF,SAAS,CAACV,QAAQ,CAAC;IACnDW,YAAY,CAACC,OAAO,CAAC,eAAe,EAAEF,SAAS,CAACT,QAAQ,CAAC;IACzDU,YAAY,CAACC,OAAO,CAAC,WAAW,EAAEF,SAAS,CAACR,IAAI,CAAC;IACjDS,YAAY,CAACC,OAAO,CAAC,iBAAiB,EAAEF,SAAS,CAACH,WAAW,CAAC;IAE9D,IAAIG,SAAS,CAACG,aAAa,EAAE;MACzBF,YAAY,CAACC,OAAO,CAAC,mBAAmB,EAAEF,SAAS,CAACG,aAAa,CAAC;IACtE;;IAEA;IACAtC,QAAQ,CAAC,kBAAkB,CAAC;EAChC,CAAC;EAED,MAAMuC,iBAAiB,GAAIC,QAAQ,IAAK;IACpCT,aAAa,CAACS,QAAQ,CAAC;EAC3B,CAAC;EAED,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;IAC5BvC,WAAW,CAAC,OAAO,CAAC;IACpBE,YAAY,CAAC,IAAI,CAAC;IAClBM,QAAQ,CAAC,EAAE,CAAC;IACZF,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC;EACrB,CAAC;;EAED;EACA,IAAIP,QAAQ,KAAK,KAAK,IAAIE,SAAS,EAAE;IACjC,oBACIN,OAAA,CAACJ,oBAAoB;MACjBiD,OAAO,EAAEvC,SAAS,CAACsB,QAAS;MAC5BC,QAAQ,EAAEvB,SAAS,CAACuB,QAAS;MAC7BiB,SAAS,EAAEJ,iBAAkB;MAC7BK,MAAM,EAAEH;IAAkB;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7B,CAAC;EAEV;EAEA,IAAI/C,QAAQ,KAAK,KAAK,IAAIE,SAAS,EAAE;IACjC,oBACIN,OAAA,CAACH,oBAAoB;MACjBgD,OAAO,EAAEvC,SAAS,CAACsB,QAAS;MAC5BC,QAAQ,EAAEvB,SAAS,CAACuB,QAAS;MAC7BiB,SAAS,EAAEJ,iBAAkB;MAC7BK,MAAM,EAAEH;IAAkB;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7B,CAAC;EAEV;EAEA,IAAI/C,QAAQ,KAAK,WAAW,IAAIE,SAAS,EAAE;IACvC,oBACIN,OAAA,CAACF,aAAa;MACV+C,OAAO,EAAEvC,SAAS,CAACsB,QAAS;MAC5BC,QAAQ,EAAEvB,SAAS,CAACuB,QAAS;MAC7BiB,SAAS,EAAEJ,iBAAkB;MAC7BK,MAAM,EAAEH;IAAkB;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7B,CAAC;EAEV;;EAEA;EACA,oBACInD,OAAA;IAAKoD,SAAS,EAAC,uBAAuB;IAAAC,QAAA,gBAClCrD,OAAA;MAAKoD,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC7BrD,OAAA;QAAKoD,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACvBrD,OAAA;UAAKoD,SAAS,EAAC,WAAW;UAAAC,QAAA,eACtBrD,OAAA;YAAKsD,KAAK,EAAC,4BAA4B;YAACC,OAAO,EAAC,WAAW;YAACC,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,WAAW,EAAC,GAAG;YAACC,aAAa,EAAC,OAAO;YAACC,cAAc,EAAC,OAAO;YAAAP,QAAA,gBACtJrD,OAAA;cAAM6D,CAAC,EAAC,GAAG;cAACC,CAAC,EAAC,GAAG;cAACC,KAAK,EAAC,IAAI;cAACC,MAAM,EAAC,IAAI;cAACC,EAAE,EAAC,GAAG;cAACC,EAAE,EAAC;YAAG;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC9DnD,OAAA;cAAMmE,EAAE,EAAC,GAAG;cAACC,EAAE,EAAC,IAAI;cAACC,EAAE,EAAC,IAAI;cAACC,EAAE,EAAC;YAAI;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC5CnD,OAAA;cAAMmE,EAAE,EAAC,IAAI;cAACC,EAAE,EAAC,IAAI;cAACC,EAAE,EAAC,IAAI;cAACC,EAAE,EAAC;YAAI;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACNnD,OAAA;UAAAqD,QAAA,EAAI;QAAS;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC,eAENnD,OAAA;QAAKoD,SAAS,EAAC,sBAAsB;QAAAC,QAAA,gBACjCrD,OAAA;UAAAqD,QAAA,EAAI;QAAW;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACpBnD,OAAA;UAAGoD,SAAS,EAAC,gBAAgB;UAAAC,QAAA,EAAC;QAAoD;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAGtFnD,OAAA;UAAKoD,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC5BrD,OAAA;YAAKoD,SAAS,EAAC,eAAe;YAAAC,QAAA,eAC1BrD,OAAA;cAAKsD,KAAK,EAAC,4BAA4B;cAACC,OAAO,EAAC,WAAW;cAACC,IAAI,EAAC,MAAM;cAACC,MAAM,EAAC,cAAc;cAACC,WAAW,EAAC,GAAG;cAACC,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAAAP,QAAA,eACtJrD,OAAA;gBAAMuE,CAAC,EAAC;cAA6C;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACNnD,OAAA;YAAAqD,QAAA,EAAM;UAAmE;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/E,CAAC,EAELvC,KAAK,iBAAIZ,OAAA;UAAKoD,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAEzC;QAAK;UAAAoC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAEtDnD,OAAA;UAAMwE,QAAQ,EAAEtD,YAAa;UAAAmC,QAAA,gBACzBrD,OAAA;YAAKoD,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACvBrD,OAAA;cAAOyE,OAAO,EAAC,YAAY;cAAApB,QAAA,EAAC;YAAiB;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACrDnD,OAAA;cAAKoD,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC5BrD,OAAA;gBACI0E,IAAI,EAAC,MAAM;gBACXC,EAAE,EAAC,YAAY;gBACfC,KAAK,EAAEpE,UAAW;gBAClBqE,QAAQ,EAAG1D,CAAC,IAAKV,aAAa,CAACU,CAAC,CAAC2D,MAAM,CAACF,KAAK,CAAE;gBAC/CG,QAAQ,EAAEjE,SAAU;gBACpBkE,WAAW,EAAC,8BAA8B;gBAC1CC,QAAQ;cAAA;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC,eACFnD,OAAA;gBAAKoD,SAAS,EAAC,YAAY;gBAAAC,QAAA,eACvBrD,OAAA;kBAAKsD,KAAK,EAAC,4BAA4B;kBAACC,OAAO,EAAC,WAAW;kBAACC,IAAI,EAAC,MAAM;kBAACC,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC,GAAG;kBAACC,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAAAP,QAAA,gBACtJrD,OAAA;oBAAMuE,CAAC,EAAC;kBAA2C;oBAAAvB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC3DnD,OAAA;oBAAQkF,EAAE,EAAC,IAAI;oBAACC,EAAE,EAAC,GAAG;oBAACC,CAAC,EAAC;kBAAG;oBAAApC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAENnD,OAAA;YAAKoD,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACvBrD,OAAA;cAAOyE,OAAO,EAAC,UAAU;cAAApB,QAAA,EAAC;YAAQ;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC1CnD,OAAA;cAAKoD,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC5BrD,OAAA;gBACI0E,IAAI,EAAC,UAAU;gBACfC,EAAE,EAAC,UAAU;gBACbC,KAAK,EAAElE,QAAS;gBAChBmE,QAAQ,EAAG1D,CAAC,IAAKR,WAAW,CAACQ,CAAC,CAAC2D,MAAM,CAACF,KAAK,CAAE;gBAC7CG,QAAQ,EAAEjE,SAAU;gBACpBkE,WAAW,EAAC,qBAAqB;gBACjCC,QAAQ;cAAA;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC,eACFnD,OAAA;gBAAKoD,SAAS,EAAC,YAAY;gBAAAC,QAAA,eACvBrD,OAAA;kBAAKsD,KAAK,EAAC,4BAA4B;kBAACC,OAAO,EAAC,WAAW;kBAACC,IAAI,EAAC,MAAM;kBAACC,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC,GAAG;kBAACC,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAAAP,QAAA,gBACtJrD,OAAA;oBAAM6D,CAAC,EAAC,GAAG;oBAACC,CAAC,EAAC,IAAI;oBAACC,KAAK,EAAC,IAAI;oBAACC,MAAM,EAAC,IAAI;oBAACC,EAAE,EAAC,GAAG;oBAACC,EAAE,EAAC;kBAAG;oBAAAlB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC/DnD,OAAA;oBAAMuE,CAAC,EAAC;kBAA0B;oBAAAvB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAENnD,OAAA;YAAKoD,SAAS,EAAC,cAAc;YAAAC,QAAA,gBACzBrD,OAAA;cAAKoD,SAAS,EAAC,aAAa;cAAAC,QAAA,gBACxBrD,OAAA;gBACI0E,IAAI,EAAC,UAAU;gBACfC,EAAE,EAAC,YAAY;gBACfU,OAAO,EAAErE,UAAW;gBACpB6D,QAAQ,EAAG1D,CAAC,IAAKF,aAAa,CAACE,CAAC,CAAC2D,MAAM,CAACO,OAAO;cAAE;gBAAArC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC,eACFnD,OAAA;gBAAOyE,OAAO,EAAC,YAAY;gBAAApB,QAAA,EAAC;cAAW;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC,eACNnD,OAAA;cAAQ0E,IAAI,EAAC,QAAQ;cAACtB,SAAS,EAAC,iBAAiB;cAACkC,OAAO,EAAEA,CAAA,KAAMC,KAAK,CAAC,2CAA2C,CAAE;cAAAlC,QAAA,EAAC;YAAgB;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7I,CAAC,eAENnD,OAAA;YAAQ0E,IAAI,EAAC,QAAQ;YAACtB,SAAS,EAAC,cAAc;YAAC2B,QAAQ,EAAEjE,SAAU;YAAAuC,QAAA,EAC9DvC,SAAS,GAAG,eAAe,GAAG;UAAO;YAAAkC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAENnD,OAAA;MAAKoD,SAAS,EAAC;IAAmB;MAAAJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACxC,CAAC;AAEd,CAAC;AAACjD,EAAA,CArOID,cAAc;EAAA,QACCN,WAAW;AAAA;AAAA6F,EAAA,GAD1BvF,cAAc;AAuOpB,eAAeA,cAAc;AAAC,IAAAuF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}