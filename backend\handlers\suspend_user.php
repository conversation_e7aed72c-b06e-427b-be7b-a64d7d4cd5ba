<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../config/database.php';

try {
    $database = new Database();
    $db = $database->getConnection();

    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $input = json_decode(file_get_contents('php://input'), true);
        
        $user_id = $input['user_id'] ?? null;
        $action = $input['action'] ?? 'suspend';
        
        if (!$user_id) {
            echo json_encode([
                'success' => false,
                'message' => 'User ID is required'
            ]);
            exit;
        }
        
        // Check if user exists
        $check_query = "SELECT user_id, username FROM users WHERE user_id = :user_id";
        $stmt = $db->prepare($check_query);
        $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
        $stmt->execute();
        
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$user) {
            echo json_encode([
                'success' => false,
                'message' => 'User not found'
            ]);
            exit;
        }
        
        // Check if status column exists, if not add it
        $check_column = "SHOW COLUMNS FROM users LIKE 'status'";
        $column_exists = $db->query($check_column)->rowCount() > 0;
        
        if (!$column_exists) {
            $add_column = "ALTER TABLE users ADD COLUMN status ENUM('active', 'suspended', 'banned') DEFAULT 'active'";
            $db->exec($add_column);
        }
        
        // Update user status
        $status = ($action === 'suspend') ? 'suspended' : 'active';
        $update_query = "UPDATE users SET status = :status WHERE user_id = :user_id";
        
        $stmt = $db->prepare($update_query);
        $stmt->bindParam(':status', $status);
        $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
        
        if ($stmt->execute()) {
            echo json_encode([
                'success' => true,
                'message' => "User {$user['username']} has been {$status} successfully"
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'message' => 'Failed to update user status'
            ]);
        }
        
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Method not allowed'
        ]);
    }

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Database error: ' . $e->getMessage()
    ]);
}
?>
