{"ast": null, "code": "import React,{useState,useEffect}from'react';import axios from'axios';import{FaSave,FaTrash,FaExchangeAlt,FaClock,FaCalendarAlt,FaFutbol,FaInfoCircle,FaCheckCircle,FaExclamationTriangle,FaHome,FaPlane}from'react-icons/fa';import'./ChallengeSystem.css';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const API_BASE_URL='/backend';function ChallengeSystem(){var _teams$find,_teams$find2,_teams$find3,_teams$find4;const[challenges,setChallenges]=useState([]);const[teams,setTeams]=useState([]);const[newChallenge,setNewChallenge]=useState({team1:'',team2:'',odds1:1.8,odds2:1.8,goalAdvantage1:0,goalAdvantage2:0,startTime:'',endTime:'',matchTime:'',matchType:'full_time',oddsDraw:0.8,oddsLost:0.2});const[error,setError]=useState('');const[success,setSuccess]=useState('');useEffect(()=>{fetchTeams();},[]);const fetchTeams=async()=>{try{const response=await axios.get(`${API_BASE_URL}/handlers/team_management.php`);setTeams(response.data.data);}catch(err){console.error(\"Error fetching teams:\",err);}};const handleInputChange=e=>{const{name,value}=e.target;setNewChallenge(prev=>({...prev,[name]:value}));};const handleSubmit=async e=>{e.preventDefault();setError('');setSuccess('');if(!newChallenge.team1||!newChallenge.team2||!newChallenge.odds1||!newChallenge.odds2){setError('Team names and odds are required.');return;}try{const formData=new FormData();const team1Data=teams.find(t=>t.name===newChallenge.team1);const team2Data=teams.find(t=>t.name===newChallenge.team2);formData.append('team1',newChallenge.team1);formData.append('team2',newChallenge.team2);formData.append('odds1',newChallenge.odds1);formData.append('odds2',newChallenge.odds2);formData.append('goalAdvantage1',newChallenge.goalAdvantage1);formData.append('goalAdvantage2',newChallenge.goalAdvantage2);formData.append('startTime',newChallenge.startTime);formData.append('endTime',newChallenge.endTime);formData.append('matchTime',newChallenge.matchTime);formData.append('matchType',newChallenge.matchType);formData.append('oddsDraw',newChallenge.oddsDraw);formData.append('oddsLost',newChallenge.oddsLost);formData.append('logo1',team1Data?`${API_BASE_URL}/${team1Data.logo}`:'');formData.append('logo2',team2Data?`${API_BASE_URL}/${team2Data.logo}`:'');const response=await axios.post(`${API_BASE_URL}/handlers/create_challenge.php`,formData,{headers:{'Content-Type':'multipart/form-data'}});if(response.data.success){setSuccess('Challenge created successfully!');setNewChallenge({team1:'',team2:'',odds1:1.8,odds2:1.8,goalAdvantage1:0,goalAdvantage2:0,startTime:'',endTime:'',matchTime:'',matchType:'full_time',oddsDraw:0.8,oddsLost:0.2});setTimeout(()=>{setSuccess('');},3000);}else{setError(response.data.message||'Failed to create challenge');setTimeout(()=>{setError('');},3000);}}catch(err){setError('Failed to create challenge');}};const handleDiscard=()=>{setNewChallenge({team1:'',team2:'',odds1:1.8,odds2:1.8,goalAdvantage1:0,goalAdvantage2:0,startTime:'',endTime:'',matchTime:'',matchType:'full_time',oddsDraw:0.8,oddsLost:0.2});setError('');setSuccess('');};return/*#__PURE__*/_jsxs(\"div\",{className:\"challenge-system\",children:[/*#__PURE__*/_jsx(\"h1\",{children:\"Create a New Challenge\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"header-actions\",children:[/*#__PURE__*/_jsxs(\"button\",{onClick:handleSubmit,style:{backgroundColor:'#166534',color:'white'},children:[/*#__PURE__*/_jsx(FaSave,{}),/*#__PURE__*/_jsx(\"span\",{children:\"Save Challenge\"})]}),/*#__PURE__*/_jsxs(\"button\",{onClick:handleDiscard,style:{backgroundColor:'#dc2626',color:'white',border:'none'},children:[/*#__PURE__*/_jsx(FaTrash,{}),/*#__PURE__*/_jsx(\"span\",{children:\"Discard Changes\"})]})]}),error&&/*#__PURE__*/_jsxs(\"div\",{className:\"error-message\",children:[/*#__PURE__*/_jsx(FaExclamationTriangle,{}),error]}),success&&/*#__PURE__*/_jsxs(\"div\",{className:\"success-message\",children:[/*#__PURE__*/_jsx(FaCheckCircle,{}),success]}),/*#__PURE__*/_jsxs(\"form\",{onSubmit:handleSubmit,className:\"challenge-form\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"match-settings\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"match-type-section\",children:[/*#__PURE__*/_jsxs(\"h3\",{children:[/*#__PURE__*/_jsx(FaFutbol,{}),\" Match Settings\"]}),/*#__PURE__*/_jsxs(\"div\",{className:\"match-settings-grid\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"matchType\",className:\"required-field\",children:\"Match Type:\"}),/*#__PURE__*/_jsxs(\"select\",{id:\"matchType\",name:\"matchType\",value:newChallenge.matchType,onChange:handleInputChange,required:true,children:[/*#__PURE__*/_jsx(\"option\",{value:\"full_time\",children:\"Full Time\"}),/*#__PURE__*/_jsx(\"option\",{value:\"half_time\",children:\"Half Time\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsxs(\"label\",{htmlFor:\"oddsDraw\",children:[\"Draw Odds:\",/*#__PURE__*/_jsx(\"span\",{className:\"info-tooltip\",title:\"Multiplier for draw results\",children:/*#__PURE__*/_jsx(FaInfoCircle,{size:12,style:{marginLeft:'5px',color:'#6b7280'}})})]}),/*#__PURE__*/_jsx(\"input\",{type:\"number\",id:\"oddsDraw\",name:\"oddsDraw\",value:newChallenge.oddsDraw,onChange:handleInputChange,step:\"0.1\",min:\"0\"}),/*#__PURE__*/_jsx(\"p\",{className:\"odds-explanation\",children:\"Default: 0.8\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsxs(\"label\",{htmlFor:\"oddsLost\",children:[\"Lost Odds:\",/*#__PURE__*/_jsx(\"span\",{className:\"info-tooltip\",title:\"Multiplier for lost results\",children:/*#__PURE__*/_jsx(FaInfoCircle,{size:12,style:{marginLeft:'5px',color:'#6b7280'}})})]}),/*#__PURE__*/_jsx(\"input\",{type:\"number\",id:\"oddsLost\",name:\"oddsLost\",value:newChallenge.oddsLost,onChange:handleInputChange,step:\"0.1\",min:\"0\"}),/*#__PURE__*/_jsx(\"p\",{className:\"odds-explanation\",children:\"Default: 0.2\"})]})]})]})}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',flexDirection:'row',justifyContent:'space-between',gap:'20px',width:'100%',position:'relative',alignItems:'flex-start'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{flex:'1',backgroundColor:'white',borderRadius:'6px',padding:'1.25rem',boxShadow:'0 1px 3px rgba(0, 0, 0, 0.05)',border:'1px solid #e5e7eb',display:'flex',flexDirection:'column',gap:'0.75rem',width:'48%',maxWidth:'48%'},className:\"team-section team1\",children:[/*#__PURE__*/_jsxs(\"h3\",{children:[/*#__PURE__*/_jsx(FaHome,{}),\" HOME TEAM\"]}),/*#__PURE__*/_jsx(\"label\",{htmlFor:\"team1\",className:\"required-field\",children:\"Select Team 1:\"}),/*#__PURE__*/_jsxs(\"select\",{id:\"team1\",name:\"team1\",value:newChallenge.team1,onChange:handleInputChange,required:true,children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select a team\"}),teams.map(team=>/*#__PURE__*/_jsx(\"option\",{value:team.name,children:team.name},team.id))]}),newChallenge.team1?/*#__PURE__*/_jsx(\"div\",{className:\"logo-container\",children:/*#__PURE__*/_jsx(\"img\",{src:`${API_BASE_URL}/${(_teams$find=teams.find(t=>t.name===newChallenge.team1))===null||_teams$find===void 0?void 0:_teams$find.logo}`,alt:\"Team 1 Logo\",className:\"logo-preview\"})}):/*#__PURE__*/_jsx(\"div\",{className:\"logo-container empty-logo\",children:/*#__PURE__*/_jsx(FaHome,{size:40,style:{color:'#d1d5db'}})}),/*#__PURE__*/_jsxs(\"label\",{htmlFor:\"odds1\",className:\"required-field\",children:[\"Odds for Team 1:\",/*#__PURE__*/_jsx(\"span\",{className:\"info-tooltip\",title:\"Multiplier for team 1 win\",children:/*#__PURE__*/_jsx(FaInfoCircle,{size:12,style:{marginLeft:'5px',color:'#6b7280'}})})]}),/*#__PURE__*/_jsx(\"input\",{type:\"number\",id:\"odds1\",name:\"odds1\",value:newChallenge.odds1,onChange:handleInputChange,required:true,step:\"0.01\",min:\"1\"}),/*#__PURE__*/_jsxs(\"p\",{className:\"odds-explanation\",children:[\"User's bet x \",newChallenge.odds1,\" = Potential winnings\"]}),/*#__PURE__*/_jsx(\"label\",{htmlFor:\"goalAdvantage1\",children:\"Goal Advantage:\"}),/*#__PURE__*/_jsx(\"input\",{type:\"number\",id:\"goalAdvantage1\",name:\"goalAdvantage1\",value:newChallenge.goalAdvantage1,onChange:handleInputChange})]}),/*#__PURE__*/_jsx(\"div\",{style:{display:'flex',alignItems:'center',justifyContent:'center',width:'40px',height:'40px',backgroundColor:'#166534',borderRadius:'50%',color:'white',fontSize:'1rem',boxShadow:'0 2px 4px rgba(0, 0, 0, 0.1)',zIndex:'10',alignSelf:'center',margin:'0 10px'},className:\"vs-divider\",children:/*#__PURE__*/_jsx(FaExchangeAlt,{})}),/*#__PURE__*/_jsxs(\"div\",{style:{flex:'1',backgroundColor:'white',borderRadius:'6px',padding:'1.25rem',boxShadow:'0 1px 3px rgba(0, 0, 0, 0.05)',border:'1px solid #e5e7eb',display:'flex',flexDirection:'column',gap:'0.75rem',width:'48%',maxWidth:'48%'},className:\"team-section team2\",children:[/*#__PURE__*/_jsxs(\"h3\",{children:[/*#__PURE__*/_jsx(FaPlane,{}),\" AWAY TEAM\"]}),/*#__PURE__*/_jsx(\"label\",{htmlFor:\"team2\",className:\"required-field\",children:\"Select Team 2:\"}),/*#__PURE__*/_jsxs(\"select\",{id:\"team2\",name:\"team2\",value:newChallenge.team2,onChange:handleInputChange,required:true,children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select a team\"}),teams.map(team=>/*#__PURE__*/_jsx(\"option\",{value:team.name,children:team.name},team.id))]}),newChallenge.team2?/*#__PURE__*/_jsx(\"div\",{className:\"logo-container\",children:/*#__PURE__*/_jsx(\"img\",{src:`${API_BASE_URL}/${(_teams$find2=teams.find(t=>t.name===newChallenge.team2))===null||_teams$find2===void 0?void 0:_teams$find2.logo}`,alt:\"Team 2 Logo\",className:\"logo-preview\"})}):/*#__PURE__*/_jsx(\"div\",{className:\"logo-container empty-logo\",children:/*#__PURE__*/_jsx(FaPlane,{size:40,style:{color:'#d1d5db'}})}),/*#__PURE__*/_jsxs(\"label\",{htmlFor:\"odds2\",className:\"required-field\",children:[\"Odds for Team 2:\",/*#__PURE__*/_jsx(\"span\",{className:\"info-tooltip\",title:\"Multiplier for team 2 win\",children:/*#__PURE__*/_jsx(FaInfoCircle,{size:12,style:{marginLeft:'5px',color:'#6b7280'}})})]}),/*#__PURE__*/_jsx(\"input\",{type:\"number\",id:\"odds2\",name:\"odds2\",value:newChallenge.odds2,onChange:handleInputChange,required:true,step:\"0.01\",min:\"1\"}),/*#__PURE__*/_jsxs(\"p\",{className:\"odds-explanation\",children:[\"User's bet x \",newChallenge.odds2,\" = Potential winnings\"]}),/*#__PURE__*/_jsx(\"label\",{htmlFor:\"goalAdvantage2\",children:\"Goal Advantage:\"}),/*#__PURE__*/_jsx(\"input\",{type:\"number\",id:\"goalAdvantage2\",name:\"goalAdvantage2\",value:newChallenge.goalAdvantage2,onChange:handleInputChange})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"time-section\",children:[/*#__PURE__*/_jsxs(\"h3\",{children:[/*#__PURE__*/_jsx(FaClock,{}),\" Time Settings\"]}),/*#__PURE__*/_jsxs(\"div\",{className:\"time-groups-container\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"time-group\",children:[/*#__PURE__*/_jsxs(\"label\",{htmlFor:\"startTime\",className:\"required-field\",children:[/*#__PURE__*/_jsx(FaCalendarAlt,{className:\"time-icon\"}),\" Challenge Start Time:\"]}),/*#__PURE__*/_jsx(\"input\",{type:\"datetime-local\",id:\"startTime\",name:\"startTime\",value:newChallenge.startTime,onChange:handleInputChange,required:true})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"time-group\",children:[/*#__PURE__*/_jsxs(\"label\",{htmlFor:\"endTime\",className:\"required-field\",children:[/*#__PURE__*/_jsx(FaCalendarAlt,{className:\"time-icon\"}),\" Challenge End Time:\"]}),/*#__PURE__*/_jsx(\"input\",{type:\"datetime-local\",id:\"endTime\",name:\"endTime\",value:newChallenge.endTime,onChange:handleInputChange,required:true})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"time-group\",children:[/*#__PURE__*/_jsxs(\"label\",{htmlFor:\"matchTime\",className:\"required-field\",children:[/*#__PURE__*/_jsx(FaCalendarAlt,{className:\"time-icon\"}),\" Actual Match Time:\"]}),/*#__PURE__*/_jsx(\"input\",{type:\"datetime-local\",id:\"matchTime\",name:\"matchTime\",value:newChallenge.matchTime,onChange:handleInputChange,required:true})]})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"challenge-preview\",children:[/*#__PURE__*/_jsxs(\"h3\",{children:[/*#__PURE__*/_jsx(FaFutbol,{}),\" Challenge Preview\"]}),!newChallenge.team1||!newChallenge.team2?/*#__PURE__*/_jsxs(\"div\",{className:\"empty-preview\",children:[/*#__PURE__*/_jsx(FaInfoCircle,{}),/*#__PURE__*/_jsx(\"p\",{children:\"Select teams to see the challenge preview\"})]}):/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',flexDirection:'row',justifyContent:'space-between',gap:'20px',width:'100%',position:'relative',alignItems:'center',marginBottom:'1.5rem'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{flex:'1',display:'flex',flexDirection:'column',alignItems:'center',textAlign:'center',width:'48%',maxWidth:'48%'},className:\"preview-team\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"logo-container\",children:/*#__PURE__*/_jsx(\"img\",{src:`${API_BASE_URL}/${(_teams$find3=teams.find(t=>t.name===newChallenge.team1))===null||_teams$find3===void 0?void 0:_teams$find3.logo}`,alt:\"Team 1 Logo\",className:\"logo-preview\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"preview-team-name\",children:newChallenge.team1}),/*#__PURE__*/_jsxs(\"div\",{className:\"preview-odds\",children:[\"Odds: \",newChallenge.odds1]}),newChallenge.goalAdvantage1>0&&/*#__PURE__*/_jsxs(\"div\",{className:\"preview-advantage\",children:[\"+\",newChallenge.goalAdvantage1,\" Goal Advantage\"]})]}),/*#__PURE__*/_jsx(\"div\",{style:{display:'flex',alignItems:'center',justifyContent:'center',width:'40px',height:'40px',backgroundColor:'#166534',borderRadius:'50%',color:'white',fontSize:'1.25rem',fontWeight:'700',boxShadow:'0 2px 4px rgba(0, 0, 0, 0.1)',margin:'0 10px'},className:\"preview-vs\",children:\"VS\"}),/*#__PURE__*/_jsxs(\"div\",{style:{flex:'1',display:'flex',flexDirection:'column',alignItems:'center',textAlign:'center',width:'48%',maxWidth:'48%'},className:\"preview-team\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"logo-container\",children:/*#__PURE__*/_jsx(\"img\",{src:`${API_BASE_URL}/${(_teams$find4=teams.find(t=>t.name===newChallenge.team2))===null||_teams$find4===void 0?void 0:_teams$find4.logo}`,alt:\"Team 2 Logo\",className:\"logo-preview\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"preview-team-name\",children:newChallenge.team2}),/*#__PURE__*/_jsxs(\"div\",{className:\"preview-odds\",children:[\"Odds: \",newChallenge.odds2]}),newChallenge.goalAdvantage2>0&&/*#__PURE__*/_jsxs(\"div\",{className:\"preview-advantage\",children:[\"+\",newChallenge.goalAdvantage2,\" Goal Advantage\"]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"preview-time-details\",children:[/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"span\",{children:\"Start Time\"}),/*#__PURE__*/_jsx(FaCalendarAlt,{className:\"time-detail-icon\"}),newChallenge.startTime?new Date(newChallenge.startTime).toLocaleString():'Not set']}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"span\",{children:\"End Time\"}),/*#__PURE__*/_jsx(FaCalendarAlt,{className:\"time-detail-icon\"}),newChallenge.endTime?new Date(newChallenge.endTime).toLocaleString():'Not set']}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"span\",{children:\"Match Time\"}),/*#__PURE__*/_jsx(FaCalendarAlt,{className:\"time-detail-icon\"}),newChallenge.matchTime?new Date(newChallenge.matchTime).toLocaleString():'Not set']}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"span\",{children:\"Match Type\"}),/*#__PURE__*/_jsx(FaFutbol,{className:\"time-detail-icon\"}),newChallenge.matchType.replace('_',' ').toUpperCase()]})]})]})]})]});}export default ChallengeSystem;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "FaSave", "FaTrash", "FaExchangeAlt", "FaClock", "FaCalendarAlt", "FaFutbol", "FaInfoCircle", "FaCheckCircle", "FaExclamationTriangle", "FaHome", "FaPlane", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "API_BASE_URL", "ChallengeSystem", "_teams$find", "_teams$find2", "_teams$find3", "_teams$find4", "challenges", "setChallenges", "teams", "setTeams", "newChallenge", "setNewChallenge", "team1", "team2", "odds1", "odds2", "goalAdvantage1", "goalAdvantage2", "startTime", "endTime", "matchTime", "matchType", "oddsDraw", "oddsLost", "error", "setError", "success", "setSuccess", "fetchTeams", "response", "get", "data", "err", "console", "handleInputChange", "e", "name", "value", "target", "prev", "handleSubmit", "preventDefault", "formData", "FormData", "team1Data", "find", "t", "team2Data", "append", "logo", "post", "headers", "setTimeout", "message", "handleDiscard", "className", "children", "onClick", "style", "backgroundColor", "color", "border", "onSubmit", "htmlFor", "id", "onChange", "required", "title", "size", "marginLeft", "type", "step", "min", "display", "flexDirection", "justifyContent", "gap", "width", "position", "alignItems", "flex", "borderRadius", "padding", "boxShadow", "max<PERSON><PERSON><PERSON>", "map", "team", "src", "alt", "height", "fontSize", "zIndex", "alignSelf", "margin", "marginBottom", "textAlign", "fontWeight", "Date", "toLocaleString", "replace", "toUpperCase"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/pages/ChallengeSystem.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport {\n  FaSave,\n  FaTrash,\n  FaExchangeAlt,\n  FaClock,\n  FaCalendarAlt,\n  FaFutbol,\n  FaInfoCircle,\n  FaCheckCircle,\n  FaExclamationTriangle,\n  FaHome,\n  FaPlane\n} from 'react-icons/fa';\nimport './ChallengeSystem.css';\nconst API_BASE_URL = '/backend';\n\nfunction ChallengeSystem() {\n  const [challenges, setChallenges] = useState([]);\n  const [teams, setTeams] = useState([]);\n  const [newChallenge, setNewChallenge] = useState({\n    team1: '',\n    team2: '',\n    odds1: 1.8,\n    odds2: 1.8,\n    goalAdvantage1: 0,\n    goalAdvantage2: 0,\n    startTime: '',\n    endTime: '',\n    matchTime: '',\n    matchType: 'full_time',\n    oddsDraw: 0.8,\n    oddsLost: 0.2,\n  });\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n\n  useEffect(() => {\n    fetchTeams();\n  }, []);\n\n  const fetchTeams = async () => {\n    try {\n      const response = await axios.get(`${API_BASE_URL}/handlers/team_management.php`);\n      setTeams(response.data.data);\n    } catch (err) {\n      console.error(\"Error fetching teams:\", err);\n    }\n  };\n\n  const handleInputChange = (e) => {\n    const { name, value } = e.target;\n    setNewChallenge(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setError('');\n    setSuccess('');\n\n    if (!newChallenge.team1 || !newChallenge.team2 || !newChallenge.odds1 || !newChallenge.odds2) {\n      setError('Team names and odds are required.');\n      return;\n    }\n\n    try {\n      const formData = new FormData();\n      const team1Data = teams.find(t => t.name === newChallenge.team1);\n      const team2Data = teams.find(t => t.name === newChallenge.team2);\n\n      formData.append('team1', newChallenge.team1);\n      formData.append('team2', newChallenge.team2);\n      formData.append('odds1', newChallenge.odds1);\n      formData.append('odds2', newChallenge.odds2);\n      formData.append('goalAdvantage1', newChallenge.goalAdvantage1);\n      formData.append('goalAdvantage2', newChallenge.goalAdvantage2);\n      formData.append('startTime', newChallenge.startTime);\n      formData.append('endTime', newChallenge.endTime);\n      formData.append('matchTime', newChallenge.matchTime);\n      formData.append('matchType', newChallenge.matchType);\n      formData.append('oddsDraw', newChallenge.oddsDraw);\n      formData.append('oddsLost', newChallenge.oddsLost);\n      formData.append('logo1', team1Data ? `${API_BASE_URL}/${team1Data.logo}` : '');\n      formData.append('logo2', team2Data ? `${API_BASE_URL}/${team2Data.logo}` : '');\n\n      const response = await axios.post(`${API_BASE_URL}/handlers/create_challenge.php`, formData, {\n        headers: { 'Content-Type': 'multipart/form-data' }\n      });\n\n      if (response.data.success) {\n        setSuccess('Challenge created successfully!');\n        setNewChallenge({\n          team1: '',\n          team2: '',\n          odds1: 1.8,\n          odds2: 1.8,\n          goalAdvantage1: 0,\n          goalAdvantage2: 0,\n          startTime: '',\n          endTime: '',\n          matchTime: '',\n          matchType: 'full_time',\n          oddsDraw: 0.8,\n          oddsLost: 0.2,\n        });\n        setTimeout(() => {\n          setSuccess('');\n        }, 3000);\n      } else {\n        setError(response.data.message || 'Failed to create challenge');\n        setTimeout(() => {\n          setError('');\n        }, 3000);\n      }\n    } catch (err) {\n      setError('Failed to create challenge');\n    }\n  };\n\n  const handleDiscard = () => {\n    setNewChallenge({\n      team1: '', team2: '', odds1: 1.8, odds2: 1.8,\n      goalAdvantage1: 0, goalAdvantage2: 0,\n      startTime: '', endTime: '', matchTime: '',\n      matchType: 'full_time',\n      oddsDraw: 0.8,\n      oddsLost: 0.2,\n    });\n    setError('');\n    setSuccess('');\n  };\n\n  return (\n    <div className=\"challenge-system\">\n      <h1>Create a New Challenge</h1>\n      <div className=\"header-actions\">\n        <button onClick={handleSubmit} style={{ backgroundColor: '#166534', color: 'white' }}>\n          <FaSave />\n          <span>Save Challenge</span>\n        </button>\n        <button onClick={handleDiscard} style={{ backgroundColor: '#dc2626', color: 'white', border: 'none' }}>\n          <FaTrash />\n          <span>Discard Changes</span>\n        </button>\n      </div>\n      {error && (\n        <div className=\"error-message\">\n          <FaExclamationTriangle />\n          {error}\n        </div>\n      )}\n      {success && (\n        <div className=\"success-message\">\n          <FaCheckCircle />\n          {success}\n        </div>\n      )}\n      <form onSubmit={handleSubmit} className=\"challenge-form\">\n        <div className=\"match-settings\">\n          <div className=\"match-type-section\">\n            <h3><FaFutbol /> Match Settings</h3>\n            <div className=\"match-settings-grid\">\n              <div className=\"form-group\">\n                <label htmlFor=\"matchType\" className=\"required-field\">Match Type:</label>\n                <select\n                  id=\"matchType\"\n                  name=\"matchType\"\n                  value={newChallenge.matchType}\n                  onChange={handleInputChange}\n                  required\n                >\n                  <option value=\"full_time\">Full Time</option>\n                  <option value=\"half_time\">Half Time</option>\n                </select>\n              </div>\n              <div className=\"form-group\">\n                <label htmlFor=\"oddsDraw\">\n                  Draw Odds:\n                  <span className=\"info-tooltip\" title=\"Multiplier for draw results\">\n                    <FaInfoCircle size={12} style={{ marginLeft: '5px', color: '#6b7280' }} />\n                  </span>\n                </label>\n                <input\n                  type=\"number\"\n                  id=\"oddsDraw\"\n                  name=\"oddsDraw\"\n                  value={newChallenge.oddsDraw}\n                  onChange={handleInputChange}\n                  step=\"0.1\"\n                  min=\"0\"\n                />\n                <p className=\"odds-explanation\">Default: 0.8</p>\n              </div>\n              <div className=\"form-group\">\n                <label htmlFor=\"oddsLost\">\n                  Lost Odds:\n                  <span className=\"info-tooltip\" title=\"Multiplier for lost results\">\n                    <FaInfoCircle size={12} style={{ marginLeft: '5px', color: '#6b7280' }} />\n                  </span>\n                </label>\n                <input\n                  type=\"number\"\n                  id=\"oddsLost\"\n                  name=\"oddsLost\"\n                  value={newChallenge.oddsLost}\n                  onChange={handleInputChange}\n                  step=\"0.1\"\n                  min=\"0\"\n                />\n                <p className=\"odds-explanation\">Default: 0.2</p>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div style={{\n            display: 'flex',\n            flexDirection: 'row',\n            justifyContent: 'space-between',\n            gap: '20px',\n            width: '100%',\n            position: 'relative',\n            alignItems: 'flex-start'\n          }}>\n          <div style={{\n            flex: '1',\n            backgroundColor: 'white',\n            borderRadius: '6px',\n            padding: '1.25rem',\n            boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)',\n            border: '1px solid #e5e7eb',\n            display: 'flex',\n            flexDirection: 'column',\n            gap: '0.75rem',\n            width: '48%',\n            maxWidth: '48%'\n          }} className=\"team-section team1\">\n            <h3><FaHome /> HOME TEAM</h3>\n            <label htmlFor=\"team1\" className=\"required-field\">Select Team 1:</label>\n            <select id=\"team1\" name=\"team1\" value={newChallenge.team1} onChange={handleInputChange} required>\n              <option value=\"\">Select a team</option>\n              {teams.map((team) => (\n                <option key={team.id} value={team.name}>\n                  {team.name}\n                </option>\n              ))}\n            </select>\n\n            {newChallenge.team1 ? (\n              <div className=\"logo-container\">\n                <img\n                  src={`${API_BASE_URL}/${teams.find(t => t.name === newChallenge.team1)?.logo}`}\n                  alt=\"Team 1 Logo\"\n                  className=\"logo-preview\"\n                />\n              </div>\n            ) : (\n              <div className=\"logo-container empty-logo\">\n                <FaHome size={40} style={{ color: '#d1d5db' }} />\n              </div>\n            )}\n\n            <label htmlFor=\"odds1\" className=\"required-field\">\n              Odds for Team 1:\n              <span className=\"info-tooltip\" title=\"Multiplier for team 1 win\">\n                <FaInfoCircle size={12} style={{ marginLeft: '5px', color: '#6b7280' }} />\n              </span>\n            </label>\n            <input\n              type=\"number\"\n              id=\"odds1\"\n              name=\"odds1\"\n              value={newChallenge.odds1}\n              onChange={handleInputChange}\n              required\n              step=\"0.01\"\n              min=\"1\"\n            />\n            <p className=\"odds-explanation\">\n              User's bet x {newChallenge.odds1} = Potential winnings\n            </p>\n\n            <label htmlFor=\"goalAdvantage1\">Goal Advantage:</label>\n            <input\n              type=\"number\"\n              id=\"goalAdvantage1\"\n              name=\"goalAdvantage1\"\n              value={newChallenge.goalAdvantage1}\n              onChange={handleInputChange}\n            />\n          </div>\n\n          <div style={{\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            width: '40px',\n            height: '40px',\n            backgroundColor: '#166534',\n            borderRadius: '50%',\n            color: 'white',\n            fontSize: '1rem',\n            boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',\n            zIndex: '10',\n            alignSelf: 'center',\n            margin: '0 10px'\n          }} className=\"vs-divider\">\n            <FaExchangeAlt />\n          </div>\n\n          <div style={{\n            flex: '1',\n            backgroundColor: 'white',\n            borderRadius: '6px',\n            padding: '1.25rem',\n            boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)',\n            border: '1px solid #e5e7eb',\n            display: 'flex',\n            flexDirection: 'column',\n            gap: '0.75rem',\n            width: '48%',\n            maxWidth: '48%'\n          }} className=\"team-section team2\">\n            <h3><FaPlane /> AWAY TEAM</h3>\n            <label htmlFor=\"team2\" className=\"required-field\">Select Team 2:</label>\n            <select id=\"team2\" name=\"team2\" value={newChallenge.team2} onChange={handleInputChange} required>\n              <option value=\"\">Select a team</option>\n              {teams.map((team) => (\n                <option key={team.id} value={team.name}>\n                  {team.name}\n                </option>\n              ))}\n            </select>\n\n            {newChallenge.team2 ? (\n              <div className=\"logo-container\">\n                <img\n                  src={`${API_BASE_URL}/${teams.find(t => t.name === newChallenge.team2)?.logo}`}\n                  alt=\"Team 2 Logo\"\n                  className=\"logo-preview\"\n                />\n              </div>\n            ) : (\n              <div className=\"logo-container empty-logo\">\n                <FaPlane size={40} style={{ color: '#d1d5db' }} />\n              </div>\n            )}\n\n            <label htmlFor=\"odds2\" className=\"required-field\">\n              Odds for Team 2:\n              <span className=\"info-tooltip\" title=\"Multiplier for team 2 win\">\n                <FaInfoCircle size={12} style={{ marginLeft: '5px', color: '#6b7280' }} />\n              </span>\n            </label>\n            <input\n              type=\"number\"\n              id=\"odds2\"\n              name=\"odds2\"\n              value={newChallenge.odds2}\n              onChange={handleInputChange}\n              required\n              step=\"0.01\"\n              min=\"1\"\n            />\n            <p className=\"odds-explanation\">\n              User's bet x {newChallenge.odds2} = Potential winnings\n            </p>\n\n            <label htmlFor=\"goalAdvantage2\">Goal Advantage:</label>\n            <input\n              type=\"number\"\n              id=\"goalAdvantage2\"\n              name=\"goalAdvantage2\"\n              value={newChallenge.goalAdvantage2}\n              onChange={handleInputChange}\n            />\n          </div>\n        </div>\n\n        <div className=\"time-section\">\n          <h3><FaClock /> Time Settings</h3>\n\n          <div className=\"time-groups-container\">\n            <div className=\"time-group\">\n              <label htmlFor=\"startTime\" className=\"required-field\">\n                <FaCalendarAlt className=\"time-icon\" /> Challenge Start Time:\n              </label>\n              <input\n                type=\"datetime-local\"\n                id=\"startTime\"\n                name=\"startTime\"\n                value={newChallenge.startTime}\n                onChange={handleInputChange}\n                required\n              />\n            </div>\n\n            <div className=\"time-group\">\n              <label htmlFor=\"endTime\" className=\"required-field\">\n                <FaCalendarAlt className=\"time-icon\" /> Challenge End Time:\n              </label>\n              <input\n                type=\"datetime-local\"\n                id=\"endTime\"\n                name=\"endTime\"\n                value={newChallenge.endTime}\n                onChange={handleInputChange}\n                required\n              />\n            </div>\n\n            <div className=\"time-group\">\n              <label htmlFor=\"matchTime\" className=\"required-field\">\n                <FaCalendarAlt className=\"time-icon\" /> Actual Match Time:\n              </label>\n              <input\n                type=\"datetime-local\"\n                id=\"matchTime\"\n                name=\"matchTime\"\n                value={newChallenge.matchTime}\n                onChange={handleInputChange}\n                required\n              />\n            </div>\n          </div>\n        </div>\n      </form>\n\n      <div className=\"challenge-preview\">\n        <h3><FaFutbol /> Challenge Preview</h3>\n\n        {(!newChallenge.team1 || !newChallenge.team2) ? (\n          <div className=\"empty-preview\">\n            <FaInfoCircle />\n            <p>Select teams to see the challenge preview</p>\n          </div>\n        ) : (\n          <>\n            <div style={{\n              display: 'flex',\n              flexDirection: 'row',\n              justifyContent: 'space-between',\n              gap: '20px',\n              width: '100%',\n              position: 'relative',\n              alignItems: 'center',\n              marginBottom: '1.5rem'\n            }}>\n              <div style={{\n                flex: '1',\n                display: 'flex',\n                flexDirection: 'column',\n                alignItems: 'center',\n                textAlign: 'center',\n                width: '48%',\n                maxWidth: '48%'\n              }} className=\"preview-team\">\n                <div className=\"logo-container\">\n                  <img\n                    src={`${API_BASE_URL}/${teams.find(t => t.name === newChallenge.team1)?.logo}`}\n                    alt=\"Team 1 Logo\"\n                    className=\"logo-preview\"\n                  />\n                </div>\n                <div className=\"preview-team-name\">{newChallenge.team1}</div>\n                <div className=\"preview-odds\">Odds: {newChallenge.odds1}</div>\n                {newChallenge.goalAdvantage1 > 0 && (\n                  <div className=\"preview-advantage\">+{newChallenge.goalAdvantage1} Goal Advantage</div>\n                )}\n              </div>\n\n              <div style={{\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                width: '40px',\n                height: '40px',\n                backgroundColor: '#166534',\n                borderRadius: '50%',\n                color: 'white',\n                fontSize: '1.25rem',\n                fontWeight: '700',\n                boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',\n                margin: '0 10px'\n              }} className=\"preview-vs\">VS</div>\n\n              <div style={{\n                flex: '1',\n                display: 'flex',\n                flexDirection: 'column',\n                alignItems: 'center',\n                textAlign: 'center',\n                width: '48%',\n                maxWidth: '48%'\n              }} className=\"preview-team\">\n                <div className=\"logo-container\">\n                  <img\n                    src={`${API_BASE_URL}/${teams.find(t => t.name === newChallenge.team2)?.logo}`}\n                    alt=\"Team 2 Logo\"\n                    className=\"logo-preview\"\n                  />\n                </div>\n                <div className=\"preview-team-name\">{newChallenge.team2}</div>\n                <div className=\"preview-odds\">Odds: {newChallenge.odds2}</div>\n                {newChallenge.goalAdvantage2 > 0 && (\n                  <div className=\"preview-advantage\">+{newChallenge.goalAdvantage2} Goal Advantage</div>\n                )}\n              </div>\n            </div>\n\n            <div className=\"preview-time-details\">\n              <p>\n                <span>Start Time</span>\n                <FaCalendarAlt className=\"time-detail-icon\" />\n                {newChallenge.startTime ? new Date(newChallenge.startTime).toLocaleString() : 'Not set'}\n              </p>\n              <p>\n                <span>End Time</span>\n                <FaCalendarAlt className=\"time-detail-icon\" />\n                {newChallenge.endTime ? new Date(newChallenge.endTime).toLocaleString() : 'Not set'}\n              </p>\n              <p>\n                <span>Match Time</span>\n                <FaCalendarAlt className=\"time-detail-icon\" />\n                {newChallenge.matchTime ? new Date(newChallenge.matchTime).toLocaleString() : 'Not set'}\n              </p>\n              <p>\n                <span>Match Type</span>\n                <FaFutbol className=\"time-detail-icon\" />\n                {newChallenge.matchType.replace('_', ' ').toUpperCase()}\n              </p>\n            </div>\n          </>\n        )}\n      </div>\n    </div>\n  );\n}\n\nexport default ChallengeSystem;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,MAAO,CAAAC,KAAK,KAAM,OAAO,CACzB,OACEC,MAAM,CACNC,OAAO,CACPC,aAAa,CACbC,OAAO,CACPC,aAAa,CACbC,QAAQ,CACRC,YAAY,CACZC,aAAa,CACbC,qBAAqB,CACrBC,MAAM,CACNC,OAAO,KACF,gBAAgB,CACvB,MAAO,uBAAuB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAC/B,KAAM,CAAAC,YAAY,CAAG,UAAU,CAE/B,QAAS,CAAAC,eAAeA,CAAA,CAAG,KAAAC,WAAA,CAAAC,YAAA,CAAAC,YAAA,CAAAC,YAAA,CACzB,KAAM,CAACC,UAAU,CAAEC,aAAa,CAAC,CAAG3B,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAAC4B,KAAK,CAAEC,QAAQ,CAAC,CAAG7B,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAAC8B,YAAY,CAAEC,eAAe,CAAC,CAAG/B,QAAQ,CAAC,CAC/CgC,KAAK,CAAE,EAAE,CACTC,KAAK,CAAE,EAAE,CACTC,KAAK,CAAE,GAAG,CACVC,KAAK,CAAE,GAAG,CACVC,cAAc,CAAE,CAAC,CACjBC,cAAc,CAAE,CAAC,CACjBC,SAAS,CAAE,EAAE,CACbC,OAAO,CAAE,EAAE,CACXC,SAAS,CAAE,EAAE,CACbC,SAAS,CAAE,WAAW,CACtBC,QAAQ,CAAE,GAAG,CACbC,QAAQ,CAAE,GACZ,CAAC,CAAC,CACF,KAAM,CAACC,KAAK,CAAEC,QAAQ,CAAC,CAAG7C,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAAC8C,OAAO,CAAEC,UAAU,CAAC,CAAG/C,QAAQ,CAAC,EAAE,CAAC,CAE1CC,SAAS,CAAC,IAAM,CACd+C,UAAU,CAAC,CAAC,CACd,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAA,UAAU,CAAG,KAAAA,CAAA,GAAY,CAC7B,GAAI,CACF,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAA/C,KAAK,CAACgD,GAAG,CAAC,GAAG9B,YAAY,+BAA+B,CAAC,CAChFS,QAAQ,CAACoB,QAAQ,CAACE,IAAI,CAACA,IAAI,CAAC,CAC9B,CAAE,MAAOC,GAAG,CAAE,CACZC,OAAO,CAACT,KAAK,CAAC,uBAAuB,CAAEQ,GAAG,CAAC,CAC7C,CACF,CAAC,CAED,KAAM,CAAAE,iBAAiB,CAAIC,CAAC,EAAK,CAC/B,KAAM,CAAEC,IAAI,CAAEC,KAAM,CAAC,CAAGF,CAAC,CAACG,MAAM,CAChC3B,eAAe,CAAC4B,IAAI,GAAK,CACvB,GAAGA,IAAI,CACP,CAACH,IAAI,EAAGC,KACV,CAAC,CAAC,CAAC,CACL,CAAC,CAED,KAAM,CAAAG,YAAY,CAAG,KAAO,CAAAL,CAAC,EAAK,CAChCA,CAAC,CAACM,cAAc,CAAC,CAAC,CAClBhB,QAAQ,CAAC,EAAE,CAAC,CACZE,UAAU,CAAC,EAAE,CAAC,CAEd,GAAI,CAACjB,YAAY,CAACE,KAAK,EAAI,CAACF,YAAY,CAACG,KAAK,EAAI,CAACH,YAAY,CAACI,KAAK,EAAI,CAACJ,YAAY,CAACK,KAAK,CAAE,CAC5FU,QAAQ,CAAC,mCAAmC,CAAC,CAC7C,OACF,CAEA,GAAI,CACF,KAAM,CAAAiB,QAAQ,CAAG,GAAI,CAAAC,QAAQ,CAAC,CAAC,CAC/B,KAAM,CAAAC,SAAS,CAAGpC,KAAK,CAACqC,IAAI,CAACC,CAAC,EAAIA,CAAC,CAACV,IAAI,GAAK1B,YAAY,CAACE,KAAK,CAAC,CAChE,KAAM,CAAAmC,SAAS,CAAGvC,KAAK,CAACqC,IAAI,CAACC,CAAC,EAAIA,CAAC,CAACV,IAAI,GAAK1B,YAAY,CAACG,KAAK,CAAC,CAEhE6B,QAAQ,CAACM,MAAM,CAAC,OAAO,CAAEtC,YAAY,CAACE,KAAK,CAAC,CAC5C8B,QAAQ,CAACM,MAAM,CAAC,OAAO,CAAEtC,YAAY,CAACG,KAAK,CAAC,CAC5C6B,QAAQ,CAACM,MAAM,CAAC,OAAO,CAAEtC,YAAY,CAACI,KAAK,CAAC,CAC5C4B,QAAQ,CAACM,MAAM,CAAC,OAAO,CAAEtC,YAAY,CAACK,KAAK,CAAC,CAC5C2B,QAAQ,CAACM,MAAM,CAAC,gBAAgB,CAAEtC,YAAY,CAACM,cAAc,CAAC,CAC9D0B,QAAQ,CAACM,MAAM,CAAC,gBAAgB,CAAEtC,YAAY,CAACO,cAAc,CAAC,CAC9DyB,QAAQ,CAACM,MAAM,CAAC,WAAW,CAAEtC,YAAY,CAACQ,SAAS,CAAC,CACpDwB,QAAQ,CAACM,MAAM,CAAC,SAAS,CAAEtC,YAAY,CAACS,OAAO,CAAC,CAChDuB,QAAQ,CAACM,MAAM,CAAC,WAAW,CAAEtC,YAAY,CAACU,SAAS,CAAC,CACpDsB,QAAQ,CAACM,MAAM,CAAC,WAAW,CAAEtC,YAAY,CAACW,SAAS,CAAC,CACpDqB,QAAQ,CAACM,MAAM,CAAC,UAAU,CAAEtC,YAAY,CAACY,QAAQ,CAAC,CAClDoB,QAAQ,CAACM,MAAM,CAAC,UAAU,CAAEtC,YAAY,CAACa,QAAQ,CAAC,CAClDmB,QAAQ,CAACM,MAAM,CAAC,OAAO,CAAEJ,SAAS,CAAG,GAAG5C,YAAY,IAAI4C,SAAS,CAACK,IAAI,EAAE,CAAG,EAAE,CAAC,CAC9EP,QAAQ,CAACM,MAAM,CAAC,OAAO,CAAED,SAAS,CAAG,GAAG/C,YAAY,IAAI+C,SAAS,CAACE,IAAI,EAAE,CAAG,EAAE,CAAC,CAE9E,KAAM,CAAApB,QAAQ,CAAG,KAAM,CAAA/C,KAAK,CAACoE,IAAI,CAAC,GAAGlD,YAAY,gCAAgC,CAAE0C,QAAQ,CAAE,CAC3FS,OAAO,CAAE,CAAE,cAAc,CAAE,qBAAsB,CACnD,CAAC,CAAC,CAEF,GAAItB,QAAQ,CAACE,IAAI,CAACL,OAAO,CAAE,CACzBC,UAAU,CAAC,iCAAiC,CAAC,CAC7ChB,eAAe,CAAC,CACdC,KAAK,CAAE,EAAE,CACTC,KAAK,CAAE,EAAE,CACTC,KAAK,CAAE,GAAG,CACVC,KAAK,CAAE,GAAG,CACVC,cAAc,CAAE,CAAC,CACjBC,cAAc,CAAE,CAAC,CACjBC,SAAS,CAAE,EAAE,CACbC,OAAO,CAAE,EAAE,CACXC,SAAS,CAAE,EAAE,CACbC,SAAS,CAAE,WAAW,CACtBC,QAAQ,CAAE,GAAG,CACbC,QAAQ,CAAE,GACZ,CAAC,CAAC,CACF6B,UAAU,CAAC,IAAM,CACfzB,UAAU,CAAC,EAAE,CAAC,CAChB,CAAC,CAAE,IAAI,CAAC,CACV,CAAC,IAAM,CACLF,QAAQ,CAACI,QAAQ,CAACE,IAAI,CAACsB,OAAO,EAAI,4BAA4B,CAAC,CAC/DD,UAAU,CAAC,IAAM,CACf3B,QAAQ,CAAC,EAAE,CAAC,CACd,CAAC,CAAE,IAAI,CAAC,CACV,CACF,CAAE,MAAOO,GAAG,CAAE,CACZP,QAAQ,CAAC,4BAA4B,CAAC,CACxC,CACF,CAAC,CAED,KAAM,CAAA6B,aAAa,CAAGA,CAAA,GAAM,CAC1B3C,eAAe,CAAC,CACdC,KAAK,CAAE,EAAE,CAAEC,KAAK,CAAE,EAAE,CAAEC,KAAK,CAAE,GAAG,CAAEC,KAAK,CAAE,GAAG,CAC5CC,cAAc,CAAE,CAAC,CAAEC,cAAc,CAAE,CAAC,CACpCC,SAAS,CAAE,EAAE,CAAEC,OAAO,CAAE,EAAE,CAAEC,SAAS,CAAE,EAAE,CACzCC,SAAS,CAAE,WAAW,CACtBC,QAAQ,CAAE,GAAG,CACbC,QAAQ,CAAE,GACZ,CAAC,CAAC,CACFE,QAAQ,CAAC,EAAE,CAAC,CACZE,UAAU,CAAC,EAAE,CAAC,CAChB,CAAC,CAED,mBACE9B,KAAA,QAAK0D,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/B7D,IAAA,OAAA6D,QAAA,CAAI,wBAAsB,CAAI,CAAC,cAC/B3D,KAAA,QAAK0D,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7B3D,KAAA,WAAQ4D,OAAO,CAAEjB,YAAa,CAACkB,KAAK,CAAE,CAAEC,eAAe,CAAE,SAAS,CAAEC,KAAK,CAAE,OAAQ,CAAE,CAAAJ,QAAA,eACnF7D,IAAA,CAACZ,MAAM,GAAE,CAAC,cACVY,IAAA,SAAA6D,QAAA,CAAM,gBAAc,CAAM,CAAC,EACrB,CAAC,cACT3D,KAAA,WAAQ4D,OAAO,CAAEH,aAAc,CAACI,KAAK,CAAE,CAAEC,eAAe,CAAE,SAAS,CAAEC,KAAK,CAAE,OAAO,CAAEC,MAAM,CAAE,MAAO,CAAE,CAAAL,QAAA,eACpG7D,IAAA,CAACX,OAAO,GAAE,CAAC,cACXW,IAAA,SAAA6D,QAAA,CAAM,iBAAe,CAAM,CAAC,EACtB,CAAC,EACN,CAAC,CACLhC,KAAK,eACJ3B,KAAA,QAAK0D,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5B7D,IAAA,CAACJ,qBAAqB,GAAE,CAAC,CACxBiC,KAAK,EACH,CACN,CACAE,OAAO,eACN7B,KAAA,QAAK0D,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9B7D,IAAA,CAACL,aAAa,GAAE,CAAC,CAChBoC,OAAO,EACL,CACN,cACD7B,KAAA,SAAMiE,QAAQ,CAAEtB,YAAa,CAACe,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eACtD7D,IAAA,QAAK4D,SAAS,CAAC,gBAAgB,CAAAC,QAAA,cAC7B3D,KAAA,QAAK0D,SAAS,CAAC,oBAAoB,CAAAC,QAAA,eACjC3D,KAAA,OAAA2D,QAAA,eAAI7D,IAAA,CAACP,QAAQ,GAAE,CAAC,kBAAe,EAAI,CAAC,cACpCS,KAAA,QAAK0D,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eAClC3D,KAAA,QAAK0D,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzB7D,IAAA,UAAOoE,OAAO,CAAC,WAAW,CAACR,SAAS,CAAC,gBAAgB,CAAAC,QAAA,CAAC,aAAW,CAAO,CAAC,cACzE3D,KAAA,WACEmE,EAAE,CAAC,WAAW,CACd5B,IAAI,CAAC,WAAW,CAChBC,KAAK,CAAE3B,YAAY,CAACW,SAAU,CAC9B4C,QAAQ,CAAE/B,iBAAkB,CAC5BgC,QAAQ,MAAAV,QAAA,eAER7D,IAAA,WAAQ0C,KAAK,CAAC,WAAW,CAAAmB,QAAA,CAAC,WAAS,CAAQ,CAAC,cAC5C7D,IAAA,WAAQ0C,KAAK,CAAC,WAAW,CAAAmB,QAAA,CAAC,WAAS,CAAQ,CAAC,EACtC,CAAC,EACN,CAAC,cACN3D,KAAA,QAAK0D,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzB3D,KAAA,UAAOkE,OAAO,CAAC,UAAU,CAAAP,QAAA,EAAC,YAExB,cAAA7D,IAAA,SAAM4D,SAAS,CAAC,cAAc,CAACY,KAAK,CAAC,6BAA6B,CAAAX,QAAA,cAChE7D,IAAA,CAACN,YAAY,EAAC+E,IAAI,CAAE,EAAG,CAACV,KAAK,CAAE,CAAEW,UAAU,CAAE,KAAK,CAAET,KAAK,CAAE,SAAU,CAAE,CAAE,CAAC,CACtE,CAAC,EACF,CAAC,cACRjE,IAAA,UACE2E,IAAI,CAAC,QAAQ,CACbN,EAAE,CAAC,UAAU,CACb5B,IAAI,CAAC,UAAU,CACfC,KAAK,CAAE3B,YAAY,CAACY,QAAS,CAC7B2C,QAAQ,CAAE/B,iBAAkB,CAC5BqC,IAAI,CAAC,KAAK,CACVC,GAAG,CAAC,GAAG,CACR,CAAC,cACF7E,IAAA,MAAG4D,SAAS,CAAC,kBAAkB,CAAAC,QAAA,CAAC,cAAY,CAAG,CAAC,EAC7C,CAAC,cACN3D,KAAA,QAAK0D,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzB3D,KAAA,UAAOkE,OAAO,CAAC,UAAU,CAAAP,QAAA,EAAC,YAExB,cAAA7D,IAAA,SAAM4D,SAAS,CAAC,cAAc,CAACY,KAAK,CAAC,6BAA6B,CAAAX,QAAA,cAChE7D,IAAA,CAACN,YAAY,EAAC+E,IAAI,CAAE,EAAG,CAACV,KAAK,CAAE,CAAEW,UAAU,CAAE,KAAK,CAAET,KAAK,CAAE,SAAU,CAAE,CAAE,CAAC,CACtE,CAAC,EACF,CAAC,cACRjE,IAAA,UACE2E,IAAI,CAAC,QAAQ,CACbN,EAAE,CAAC,UAAU,CACb5B,IAAI,CAAC,UAAU,CACfC,KAAK,CAAE3B,YAAY,CAACa,QAAS,CAC7B0C,QAAQ,CAAE/B,iBAAkB,CAC5BqC,IAAI,CAAC,KAAK,CACVC,GAAG,CAAC,GAAG,CACR,CAAC,cACF7E,IAAA,MAAG4D,SAAS,CAAC,kBAAkB,CAAAC,QAAA,CAAC,cAAY,CAAG,CAAC,EAC7C,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,cAEN3D,KAAA,QAAK6D,KAAK,CAAE,CACRe,OAAO,CAAE,MAAM,CACfC,aAAa,CAAE,KAAK,CACpBC,cAAc,CAAE,eAAe,CAC/BC,GAAG,CAAE,MAAM,CACXC,KAAK,CAAE,MAAM,CACbC,QAAQ,CAAE,UAAU,CACpBC,UAAU,CAAE,YACd,CAAE,CAAAvB,QAAA,eACF3D,KAAA,QAAK6D,KAAK,CAAE,CACVsB,IAAI,CAAE,GAAG,CACTrB,eAAe,CAAE,OAAO,CACxBsB,YAAY,CAAE,KAAK,CACnBC,OAAO,CAAE,SAAS,CAClBC,SAAS,CAAE,+BAA+B,CAC1CtB,MAAM,CAAE,mBAAmB,CAC3BY,OAAO,CAAE,MAAM,CACfC,aAAa,CAAE,QAAQ,CACvBE,GAAG,CAAE,SAAS,CACdC,KAAK,CAAE,KAAK,CACZO,QAAQ,CAAE,KACZ,CAAE,CAAC7B,SAAS,CAAC,oBAAoB,CAAAC,QAAA,eAC/B3D,KAAA,OAAA2D,QAAA,eAAI7D,IAAA,CAACH,MAAM,GAAE,CAAC,aAAU,EAAI,CAAC,cAC7BG,IAAA,UAAOoE,OAAO,CAAC,OAAO,CAACR,SAAS,CAAC,gBAAgB,CAAAC,QAAA,CAAC,gBAAc,CAAO,CAAC,cACxE3D,KAAA,WAAQmE,EAAE,CAAC,OAAO,CAAC5B,IAAI,CAAC,OAAO,CAACC,KAAK,CAAE3B,YAAY,CAACE,KAAM,CAACqD,QAAQ,CAAE/B,iBAAkB,CAACgC,QAAQ,MAAAV,QAAA,eAC9F7D,IAAA,WAAQ0C,KAAK,CAAC,EAAE,CAAAmB,QAAA,CAAC,eAAa,CAAQ,CAAC,CACtChD,KAAK,CAAC6E,GAAG,CAAEC,IAAI,eACd3F,IAAA,WAAsB0C,KAAK,CAAEiD,IAAI,CAAClD,IAAK,CAAAoB,QAAA,CACpC8B,IAAI,CAAClD,IAAI,EADCkD,IAAI,CAACtB,EAEV,CACT,CAAC,EACI,CAAC,CAERtD,YAAY,CAACE,KAAK,cACjBjB,IAAA,QAAK4D,SAAS,CAAC,gBAAgB,CAAAC,QAAA,cAC7B7D,IAAA,QACE4F,GAAG,CAAE,GAAGvF,YAAY,KAAAE,WAAA,CAAIM,KAAK,CAACqC,IAAI,CAACC,CAAC,EAAIA,CAAC,CAACV,IAAI,GAAK1B,YAAY,CAACE,KAAK,CAAC,UAAAV,WAAA,iBAA9CA,WAAA,CAAgD+C,IAAI,EAAG,CAC/EuC,GAAG,CAAC,aAAa,CACjBjC,SAAS,CAAC,cAAc,CACzB,CAAC,CACC,CAAC,cAEN5D,IAAA,QAAK4D,SAAS,CAAC,2BAA2B,CAAAC,QAAA,cACxC7D,IAAA,CAACH,MAAM,EAAC4E,IAAI,CAAE,EAAG,CAACV,KAAK,CAAE,CAAEE,KAAK,CAAE,SAAU,CAAE,CAAE,CAAC,CAC9C,CACN,cAED/D,KAAA,UAAOkE,OAAO,CAAC,OAAO,CAACR,SAAS,CAAC,gBAAgB,CAAAC,QAAA,EAAC,kBAEhD,cAAA7D,IAAA,SAAM4D,SAAS,CAAC,cAAc,CAACY,KAAK,CAAC,2BAA2B,CAAAX,QAAA,cAC9D7D,IAAA,CAACN,YAAY,EAAC+E,IAAI,CAAE,EAAG,CAACV,KAAK,CAAE,CAAEW,UAAU,CAAE,KAAK,CAAET,KAAK,CAAE,SAAU,CAAE,CAAE,CAAC,CACtE,CAAC,EACF,CAAC,cACRjE,IAAA,UACE2E,IAAI,CAAC,QAAQ,CACbN,EAAE,CAAC,OAAO,CACV5B,IAAI,CAAC,OAAO,CACZC,KAAK,CAAE3B,YAAY,CAACI,KAAM,CAC1BmD,QAAQ,CAAE/B,iBAAkB,CAC5BgC,QAAQ,MACRK,IAAI,CAAC,MAAM,CACXC,GAAG,CAAC,GAAG,CACR,CAAC,cACF3E,KAAA,MAAG0D,SAAS,CAAC,kBAAkB,CAAAC,QAAA,EAAC,eACjB,CAAC9C,YAAY,CAACI,KAAK,CAAC,uBACnC,EAAG,CAAC,cAEJnB,IAAA,UAAOoE,OAAO,CAAC,gBAAgB,CAAAP,QAAA,CAAC,iBAAe,CAAO,CAAC,cACvD7D,IAAA,UACE2E,IAAI,CAAC,QAAQ,CACbN,EAAE,CAAC,gBAAgB,CACnB5B,IAAI,CAAC,gBAAgB,CACrBC,KAAK,CAAE3B,YAAY,CAACM,cAAe,CACnCiD,QAAQ,CAAE/B,iBAAkB,CAC7B,CAAC,EACC,CAAC,cAENvC,IAAA,QAAK+D,KAAK,CAAE,CACVe,OAAO,CAAE,MAAM,CACfM,UAAU,CAAE,QAAQ,CACpBJ,cAAc,CAAE,QAAQ,CACxBE,KAAK,CAAE,MAAM,CACbY,MAAM,CAAE,MAAM,CACd9B,eAAe,CAAE,SAAS,CAC1BsB,YAAY,CAAE,KAAK,CACnBrB,KAAK,CAAE,OAAO,CACd8B,QAAQ,CAAE,MAAM,CAChBP,SAAS,CAAE,8BAA8B,CACzCQ,MAAM,CAAE,IAAI,CACZC,SAAS,CAAE,QAAQ,CACnBC,MAAM,CAAE,QACV,CAAE,CAACtC,SAAS,CAAC,YAAY,CAAAC,QAAA,cACvB7D,IAAA,CAACV,aAAa,GAAE,CAAC,CACd,CAAC,cAENY,KAAA,QAAK6D,KAAK,CAAE,CACVsB,IAAI,CAAE,GAAG,CACTrB,eAAe,CAAE,OAAO,CACxBsB,YAAY,CAAE,KAAK,CACnBC,OAAO,CAAE,SAAS,CAClBC,SAAS,CAAE,+BAA+B,CAC1CtB,MAAM,CAAE,mBAAmB,CAC3BY,OAAO,CAAE,MAAM,CACfC,aAAa,CAAE,QAAQ,CACvBE,GAAG,CAAE,SAAS,CACdC,KAAK,CAAE,KAAK,CACZO,QAAQ,CAAE,KACZ,CAAE,CAAC7B,SAAS,CAAC,oBAAoB,CAAAC,QAAA,eAC/B3D,KAAA,OAAA2D,QAAA,eAAI7D,IAAA,CAACF,OAAO,GAAE,CAAC,aAAU,EAAI,CAAC,cAC9BE,IAAA,UAAOoE,OAAO,CAAC,OAAO,CAACR,SAAS,CAAC,gBAAgB,CAAAC,QAAA,CAAC,gBAAc,CAAO,CAAC,cACxE3D,KAAA,WAAQmE,EAAE,CAAC,OAAO,CAAC5B,IAAI,CAAC,OAAO,CAACC,KAAK,CAAE3B,YAAY,CAACG,KAAM,CAACoD,QAAQ,CAAE/B,iBAAkB,CAACgC,QAAQ,MAAAV,QAAA,eAC9F7D,IAAA,WAAQ0C,KAAK,CAAC,EAAE,CAAAmB,QAAA,CAAC,eAAa,CAAQ,CAAC,CACtChD,KAAK,CAAC6E,GAAG,CAAEC,IAAI,eACd3F,IAAA,WAAsB0C,KAAK,CAAEiD,IAAI,CAAClD,IAAK,CAAAoB,QAAA,CACpC8B,IAAI,CAAClD,IAAI,EADCkD,IAAI,CAACtB,EAEV,CACT,CAAC,EACI,CAAC,CAERtD,YAAY,CAACG,KAAK,cACjBlB,IAAA,QAAK4D,SAAS,CAAC,gBAAgB,CAAAC,QAAA,cAC7B7D,IAAA,QACE4F,GAAG,CAAE,GAAGvF,YAAY,KAAAG,YAAA,CAAIK,KAAK,CAACqC,IAAI,CAACC,CAAC,EAAIA,CAAC,CAACV,IAAI,GAAK1B,YAAY,CAACG,KAAK,CAAC,UAAAV,YAAA,iBAA9CA,YAAA,CAAgD8C,IAAI,EAAG,CAC/EuC,GAAG,CAAC,aAAa,CACjBjC,SAAS,CAAC,cAAc,CACzB,CAAC,CACC,CAAC,cAEN5D,IAAA,QAAK4D,SAAS,CAAC,2BAA2B,CAAAC,QAAA,cACxC7D,IAAA,CAACF,OAAO,EAAC2E,IAAI,CAAE,EAAG,CAACV,KAAK,CAAE,CAAEE,KAAK,CAAE,SAAU,CAAE,CAAE,CAAC,CAC/C,CACN,cAED/D,KAAA,UAAOkE,OAAO,CAAC,OAAO,CAACR,SAAS,CAAC,gBAAgB,CAAAC,QAAA,EAAC,kBAEhD,cAAA7D,IAAA,SAAM4D,SAAS,CAAC,cAAc,CAACY,KAAK,CAAC,2BAA2B,CAAAX,QAAA,cAC9D7D,IAAA,CAACN,YAAY,EAAC+E,IAAI,CAAE,EAAG,CAACV,KAAK,CAAE,CAAEW,UAAU,CAAE,KAAK,CAAET,KAAK,CAAE,SAAU,CAAE,CAAE,CAAC,CACtE,CAAC,EACF,CAAC,cACRjE,IAAA,UACE2E,IAAI,CAAC,QAAQ,CACbN,EAAE,CAAC,OAAO,CACV5B,IAAI,CAAC,OAAO,CACZC,KAAK,CAAE3B,YAAY,CAACK,KAAM,CAC1BkD,QAAQ,CAAE/B,iBAAkB,CAC5BgC,QAAQ,MACRK,IAAI,CAAC,MAAM,CACXC,GAAG,CAAC,GAAG,CACR,CAAC,cACF3E,KAAA,MAAG0D,SAAS,CAAC,kBAAkB,CAAAC,QAAA,EAAC,eACjB,CAAC9C,YAAY,CAACK,KAAK,CAAC,uBACnC,EAAG,CAAC,cAEJpB,IAAA,UAAOoE,OAAO,CAAC,gBAAgB,CAAAP,QAAA,CAAC,iBAAe,CAAO,CAAC,cACvD7D,IAAA,UACE2E,IAAI,CAAC,QAAQ,CACbN,EAAE,CAAC,gBAAgB,CACnB5B,IAAI,CAAC,gBAAgB,CACrBC,KAAK,CAAE3B,YAAY,CAACO,cAAe,CACnCgD,QAAQ,CAAE/B,iBAAkB,CAC7B,CAAC,EACC,CAAC,EACH,CAAC,cAENrC,KAAA,QAAK0D,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3B3D,KAAA,OAAA2D,QAAA,eAAI7D,IAAA,CAACT,OAAO,GAAE,CAAC,iBAAc,EAAI,CAAC,cAElCW,KAAA,QAAK0D,SAAS,CAAC,uBAAuB,CAAAC,QAAA,eACpC3D,KAAA,QAAK0D,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzB3D,KAAA,UAAOkE,OAAO,CAAC,WAAW,CAACR,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eACnD7D,IAAA,CAACR,aAAa,EAACoE,SAAS,CAAC,WAAW,CAAE,CAAC,yBACzC,EAAO,CAAC,cACR5D,IAAA,UACE2E,IAAI,CAAC,gBAAgB,CACrBN,EAAE,CAAC,WAAW,CACd5B,IAAI,CAAC,WAAW,CAChBC,KAAK,CAAE3B,YAAY,CAACQ,SAAU,CAC9B+C,QAAQ,CAAE/B,iBAAkB,CAC5BgC,QAAQ,MACT,CAAC,EACC,CAAC,cAENrE,KAAA,QAAK0D,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzB3D,KAAA,UAAOkE,OAAO,CAAC,SAAS,CAACR,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eACjD7D,IAAA,CAACR,aAAa,EAACoE,SAAS,CAAC,WAAW,CAAE,CAAC,uBACzC,EAAO,CAAC,cACR5D,IAAA,UACE2E,IAAI,CAAC,gBAAgB,CACrBN,EAAE,CAAC,SAAS,CACZ5B,IAAI,CAAC,SAAS,CACdC,KAAK,CAAE3B,YAAY,CAACS,OAAQ,CAC5B8C,QAAQ,CAAE/B,iBAAkB,CAC5BgC,QAAQ,MACT,CAAC,EACC,CAAC,cAENrE,KAAA,QAAK0D,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzB3D,KAAA,UAAOkE,OAAO,CAAC,WAAW,CAACR,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eACnD7D,IAAA,CAACR,aAAa,EAACoE,SAAS,CAAC,WAAW,CAAE,CAAC,sBACzC,EAAO,CAAC,cACR5D,IAAA,UACE2E,IAAI,CAAC,gBAAgB,CACrBN,EAAE,CAAC,WAAW,CACd5B,IAAI,CAAC,WAAW,CAChBC,KAAK,CAAE3B,YAAY,CAACU,SAAU,CAC9B6C,QAAQ,CAAE/B,iBAAkB,CAC5BgC,QAAQ,MACT,CAAC,EACC,CAAC,EACH,CAAC,EACH,CAAC,EACF,CAAC,cAEPrE,KAAA,QAAK0D,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChC3D,KAAA,OAAA2D,QAAA,eAAI7D,IAAA,CAACP,QAAQ,GAAE,CAAC,qBAAkB,EAAI,CAAC,CAErC,CAACsB,YAAY,CAACE,KAAK,EAAI,CAACF,YAAY,CAACG,KAAK,cAC1ChB,KAAA,QAAK0D,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5B7D,IAAA,CAACN,YAAY,GAAE,CAAC,cAChBM,IAAA,MAAA6D,QAAA,CAAG,2CAAyC,CAAG,CAAC,EAC7C,CAAC,cAEN3D,KAAA,CAAAE,SAAA,EAAAyD,QAAA,eACE3D,KAAA,QAAK6D,KAAK,CAAE,CACVe,OAAO,CAAE,MAAM,CACfC,aAAa,CAAE,KAAK,CACpBC,cAAc,CAAE,eAAe,CAC/BC,GAAG,CAAE,MAAM,CACXC,KAAK,CAAE,MAAM,CACbC,QAAQ,CAAE,UAAU,CACpBC,UAAU,CAAE,QAAQ,CACpBe,YAAY,CAAE,QAChB,CAAE,CAAAtC,QAAA,eACA3D,KAAA,QAAK6D,KAAK,CAAE,CACVsB,IAAI,CAAE,GAAG,CACTP,OAAO,CAAE,MAAM,CACfC,aAAa,CAAE,QAAQ,CACvBK,UAAU,CAAE,QAAQ,CACpBgB,SAAS,CAAE,QAAQ,CACnBlB,KAAK,CAAE,KAAK,CACZO,QAAQ,CAAE,KACZ,CAAE,CAAC7B,SAAS,CAAC,cAAc,CAAAC,QAAA,eACzB7D,IAAA,QAAK4D,SAAS,CAAC,gBAAgB,CAAAC,QAAA,cAC7B7D,IAAA,QACE4F,GAAG,CAAE,GAAGvF,YAAY,KAAAI,YAAA,CAAII,KAAK,CAACqC,IAAI,CAACC,CAAC,EAAIA,CAAC,CAACV,IAAI,GAAK1B,YAAY,CAACE,KAAK,CAAC,UAAAR,YAAA,iBAA9CA,YAAA,CAAgD6C,IAAI,EAAG,CAC/EuC,GAAG,CAAC,aAAa,CACjBjC,SAAS,CAAC,cAAc,CACzB,CAAC,CACC,CAAC,cACN5D,IAAA,QAAK4D,SAAS,CAAC,mBAAmB,CAAAC,QAAA,CAAE9C,YAAY,CAACE,KAAK,CAAM,CAAC,cAC7Df,KAAA,QAAK0D,SAAS,CAAC,cAAc,CAAAC,QAAA,EAAC,QAAM,CAAC9C,YAAY,CAACI,KAAK,EAAM,CAAC,CAC7DJ,YAAY,CAACM,cAAc,CAAG,CAAC,eAC9BnB,KAAA,QAAK0D,SAAS,CAAC,mBAAmB,CAAAC,QAAA,EAAC,GAAC,CAAC9C,YAAY,CAACM,cAAc,CAAC,iBAAe,EAAK,CACtF,EACE,CAAC,cAENrB,IAAA,QAAK+D,KAAK,CAAE,CACVe,OAAO,CAAE,MAAM,CACfM,UAAU,CAAE,QAAQ,CACpBJ,cAAc,CAAE,QAAQ,CACxBE,KAAK,CAAE,MAAM,CACbY,MAAM,CAAE,MAAM,CACd9B,eAAe,CAAE,SAAS,CAC1BsB,YAAY,CAAE,KAAK,CACnBrB,KAAK,CAAE,OAAO,CACd8B,QAAQ,CAAE,SAAS,CACnBM,UAAU,CAAE,KAAK,CACjBb,SAAS,CAAE,8BAA8B,CACzCU,MAAM,CAAE,QACV,CAAE,CAACtC,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,IAAE,CAAK,CAAC,cAElC3D,KAAA,QAAK6D,KAAK,CAAE,CACVsB,IAAI,CAAE,GAAG,CACTP,OAAO,CAAE,MAAM,CACfC,aAAa,CAAE,QAAQ,CACvBK,UAAU,CAAE,QAAQ,CACpBgB,SAAS,CAAE,QAAQ,CACnBlB,KAAK,CAAE,KAAK,CACZO,QAAQ,CAAE,KACZ,CAAE,CAAC7B,SAAS,CAAC,cAAc,CAAAC,QAAA,eACzB7D,IAAA,QAAK4D,SAAS,CAAC,gBAAgB,CAAAC,QAAA,cAC7B7D,IAAA,QACE4F,GAAG,CAAE,GAAGvF,YAAY,KAAAK,YAAA,CAAIG,KAAK,CAACqC,IAAI,CAACC,CAAC,EAAIA,CAAC,CAACV,IAAI,GAAK1B,YAAY,CAACG,KAAK,CAAC,UAAAR,YAAA,iBAA9CA,YAAA,CAAgD4C,IAAI,EAAG,CAC/EuC,GAAG,CAAC,aAAa,CACjBjC,SAAS,CAAC,cAAc,CACzB,CAAC,CACC,CAAC,cACN5D,IAAA,QAAK4D,SAAS,CAAC,mBAAmB,CAAAC,QAAA,CAAE9C,YAAY,CAACG,KAAK,CAAM,CAAC,cAC7DhB,KAAA,QAAK0D,SAAS,CAAC,cAAc,CAAAC,QAAA,EAAC,QAAM,CAAC9C,YAAY,CAACK,KAAK,EAAM,CAAC,CAC7DL,YAAY,CAACO,cAAc,CAAG,CAAC,eAC9BpB,KAAA,QAAK0D,SAAS,CAAC,mBAAmB,CAAAC,QAAA,EAAC,GAAC,CAAC9C,YAAY,CAACO,cAAc,CAAC,iBAAe,EAAK,CACtF,EACE,CAAC,EACH,CAAC,cAENpB,KAAA,QAAK0D,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACnC3D,KAAA,MAAA2D,QAAA,eACE7D,IAAA,SAAA6D,QAAA,CAAM,YAAU,CAAM,CAAC,cACvB7D,IAAA,CAACR,aAAa,EAACoE,SAAS,CAAC,kBAAkB,CAAE,CAAC,CAC7C7C,YAAY,CAACQ,SAAS,CAAG,GAAI,CAAA+E,IAAI,CAACvF,YAAY,CAACQ,SAAS,CAAC,CAACgF,cAAc,CAAC,CAAC,CAAG,SAAS,EACtF,CAAC,cACJrG,KAAA,MAAA2D,QAAA,eACE7D,IAAA,SAAA6D,QAAA,CAAM,UAAQ,CAAM,CAAC,cACrB7D,IAAA,CAACR,aAAa,EAACoE,SAAS,CAAC,kBAAkB,CAAE,CAAC,CAC7C7C,YAAY,CAACS,OAAO,CAAG,GAAI,CAAA8E,IAAI,CAACvF,YAAY,CAACS,OAAO,CAAC,CAAC+E,cAAc,CAAC,CAAC,CAAG,SAAS,EAClF,CAAC,cACJrG,KAAA,MAAA2D,QAAA,eACE7D,IAAA,SAAA6D,QAAA,CAAM,YAAU,CAAM,CAAC,cACvB7D,IAAA,CAACR,aAAa,EAACoE,SAAS,CAAC,kBAAkB,CAAE,CAAC,CAC7C7C,YAAY,CAACU,SAAS,CAAG,GAAI,CAAA6E,IAAI,CAACvF,YAAY,CAACU,SAAS,CAAC,CAAC8E,cAAc,CAAC,CAAC,CAAG,SAAS,EACtF,CAAC,cACJrG,KAAA,MAAA2D,QAAA,eACE7D,IAAA,SAAA6D,QAAA,CAAM,YAAU,CAAM,CAAC,cACvB7D,IAAA,CAACP,QAAQ,EAACmE,SAAS,CAAC,kBAAkB,CAAE,CAAC,CACxC7C,YAAY,CAACW,SAAS,CAAC8E,OAAO,CAAC,GAAG,CAAE,GAAG,CAAC,CAACC,WAAW,CAAC,CAAC,EACtD,CAAC,EACD,CAAC,EACN,CACH,EACE,CAAC,EACH,CAAC,CAEV,CAEA,cAAe,CAAAnG,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}