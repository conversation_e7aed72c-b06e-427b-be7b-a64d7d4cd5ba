{"ast": null, "code": "var _jsxFileName = \"C:\\\\MAMP\\\\htdocs\\\\FanBet247\\\\frontend\\\\src\\\\pages\\\\AdminLoginPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport axios from 'axios';\nimport { useNavigate } from 'react-router-dom';\nimport { AdminOTPVerification, Admin2FAVerification, Admin2FASetup } from '../components/Admin';\nimport './AdminLoginPage.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AdminLoginPage = () => {\n  _s();\n  const navigate = useNavigate();\n\n  // Authentication flow state\n  const [authStep, setAuthStep] = useState('login'); // 'login', 'otp', '2fa', '2fa_setup'\n  const [adminData, setAdminData] = useState(null);\n\n  // Login form state\n  const [identifier, setIdentifier] = useState('');\n  const [password, setPassword] = useState('');\n  const [error, setError] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [rememberMe, setRememberMe] = useState(false);\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setError('');\n    setIsLoading(true);\n    try {\n      const response = await axios.post('/backend/handlers/admin_login_handler.php', {\n        identifier,\n        password,\n        remember_me: rememberMe\n      });\n      console.log('Admin login request URL:', '/backend/handlers/admin_login_handler.php');\n      if (response.data.success) {\n        // Store admin data for potential next steps\n        setAdminData({\n          admin_id: response.data.admin_id,\n          username: response.data.username,\n          role: response.data.role\n        });\n\n        // Check if additional authentication is required\n        if (response.data.requires_additional_auth) {\n          const nextStep = response.data.next_step;\n          if (nextStep === 'otp') {\n            setAuthStep('otp');\n          } else if (nextStep === '2fa') {\n            // Check if 2FA is set up\n            setAuthStep('2fa');\n          }\n        } else {\n          // Complete login - no additional auth required\n          completeLogin({\n            admin_id: response.data.admin_id,\n            username: response.data.username,\n            role: response.data.role,\n            auth_method: response.data.auth_method || 'password_only'\n          });\n        }\n      } else {\n        setError(response.data.message || 'Login failed');\n      }\n    } catch (error) {\n      console.error('Login error:', error);\n      if (error.response) {\n        setError(error.response.data.message || 'Invalid credentials');\n      } else if (error.request) {\n        setError('Network error. Please check your connection.');\n      } else {\n        setError('An error occurred. Please try again.');\n      }\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const completeLogin = loginData => {\n    // Store authentication data\n    localStorage.setItem('adminId', loginData.admin_id);\n    localStorage.setItem('adminUsername', loginData.username);\n    localStorage.setItem('adminRole', loginData.role);\n    localStorage.setItem('adminAuthMethod', loginData.auth_method);\n    if (loginData.session_token) {\n      localStorage.setItem('adminSessionToken', loginData.session_token);\n    }\n\n    // Navigate to dashboard\n    navigate('/admin/dashboard');\n  };\n  const handleAuthSuccess = authData => {\n    completeLogin(authData);\n  };\n  const handleBackToLogin = () => {\n    setAuthStep('login');\n    setAdminData(null);\n    setError('');\n    setPassword(''); // Clear password for security\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"admin-login-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"login-left-panel\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"login-logo\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"logo-icon\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            strokeWidth: \"2\",\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            children: [/*#__PURE__*/_jsxDEV(\"rect\", {\n              x: \"2\",\n              y: \"3\",\n              width: \"20\",\n              height: \"14\",\n              rx: \"2\",\n              ry: \"2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 112,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n              x1: \"8\",\n              y1: \"21\",\n              x2: \"16\",\n              y2: \"21\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 113,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n              x1: \"12\",\n              y1: \"17\",\n              x2: \"12\",\n              y2: \"21\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 114,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"FanBet247\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"login-form-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Admin Login\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"login-subtitle\",\n          children: \"Enter your credentials to access the admin dashboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 21\n        }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"error-message\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 31\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"identifier\",\n              children: \"Username or Email\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"input-container\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                id: \"identifier\",\n                value: identifier,\n                onChange: e => setIdentifier(e.target.value),\n                disabled: isLoading,\n                placeholder: \"Enter your username or email\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 130,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"input-icon\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  viewBox: \"0 0 24 24\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\",\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 141,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                    cx: \"12\",\n                    cy: \"7\",\n                    r: \"4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 142,\n                    columnNumber: 41\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 140,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 139,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 129,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 127,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"password\",\n              children: \"Password\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"input-container\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"password\",\n                id: \"password\",\n                value: password,\n                onChange: e => setPassword(e.target.value),\n                disabled: isLoading,\n                placeholder: \"Enter your password\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 151,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"input-icon\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  viewBox: \"0 0 24 24\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\",\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  children: [/*#__PURE__*/_jsxDEV(\"rect\", {\n                    x: \"3\",\n                    y: \"11\",\n                    width: \"18\",\n                    height: \"11\",\n                    rx: \"2\",\n                    ry: \"2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 162,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M7 11V7a5 5 0 0 1 10 0v4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 163,\n                    columnNumber: 41\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 161,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 160,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-options\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"remember-me\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                id: \"rememberMe\",\n                checked: rememberMe,\n                onChange: e => setRememberMe(e.target.checked)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 171,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"rememberMe\",\n                children: \"Remember me\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 177,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              className: \"forgot-password\",\n              onClick: () => alert('Password reset functionality coming soon!'),\n              children: \"Forgot password?\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            className: \"login-button\",\n            disabled: isLoading,\n            children: isLoading ? 'Logging in...' : 'Login'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 108,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"login-right-panel\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 189,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 107,\n    columnNumber: 9\n  }, this);\n};\n_s(AdminLoginPage, \"+i+vG8ZZN2IMWgLnqQjlFqQNSUo=\", false, function () {\n  return [useNavigate];\n});\n_c = AdminLoginPage;\nexport default AdminLoginPage;\nvar _c;\n$RefreshReg$(_c, \"AdminLoginPage\");", "map": {"version": 3, "names": ["React", "useState", "axios", "useNavigate", "AdminOTPVerification", "Admin2FAVerification", "Admin2FASetup", "jsxDEV", "_jsxDEV", "AdminLoginPage", "_s", "navigate", "authStep", "setAuthStep", "adminData", "setAdminData", "identifier", "setIdentifier", "password", "setPassword", "error", "setError", "isLoading", "setIsLoading", "rememberMe", "setRememberMe", "handleSubmit", "e", "preventDefault", "response", "post", "remember_me", "console", "log", "data", "success", "admin_id", "username", "role", "requires_additional_auth", "nextStep", "next_step", "completeLogin", "auth_method", "message", "request", "loginData", "localStorage", "setItem", "session_token", "handleAuthSuccess", "authData", "handleBackToLogin", "className", "children", "xmlns", "viewBox", "fill", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "x", "y", "width", "height", "rx", "ry", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "x1", "y1", "x2", "y2", "onSubmit", "htmlFor", "type", "id", "value", "onChange", "target", "disabled", "placeholder", "required", "d", "cx", "cy", "r", "checked", "onClick", "alert", "_c", "$RefreshReg$"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/pages/AdminLoginPage.js"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport axios from 'axios';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport { AdminOTPVerification, Admin2FAVerification, Admin2FASetup } from '../components/Admin';\r\nimport './AdminLoginPage.css';\r\n\r\n\r\nconst AdminLoginPage = () => {\r\n    const navigate = useNavigate();\r\n\r\n    // Authentication flow state\r\n    const [authStep, setAuthStep] = useState('login'); // 'login', 'otp', '2fa', '2fa_setup'\r\n    const [adminData, setAdminData] = useState(null);\r\n\r\n    // Login form state\r\n    const [identifier, setIdentifier] = useState('');\r\n    const [password, setPassword] = useState('');\r\n    const [error, setError] = useState('');\r\n    const [isLoading, setIsLoading] = useState(false);\r\n    const [rememberMe, setRememberMe] = useState(false);\r\n\r\n    const handleSubmit = async (e) => {\r\n        e.preventDefault();\r\n        setError('');\r\n        setIsLoading(true);\r\n\r\n        try {\r\n            const response = await axios.post('/backend/handlers/admin_login_handler.php', {\r\n                identifier,\r\n                password,\r\n                remember_me: rememberMe\r\n            });\r\n\r\n            console.log('Admin login request URL:', '/backend/handlers/admin_login_handler.php');\r\n\r\n            if (response.data.success) {\r\n                // Store admin data for potential next steps\r\n                setAdminData({\r\n                    admin_id: response.data.admin_id,\r\n                    username: response.data.username,\r\n                    role: response.data.role\r\n                });\r\n\r\n                // Check if additional authentication is required\r\n                if (response.data.requires_additional_auth) {\r\n                    const nextStep = response.data.next_step;\r\n\r\n                    if (nextStep === 'otp') {\r\n                        setAuthStep('otp');\r\n                    } else if (nextStep === '2fa') {\r\n                        // Check if 2FA is set up\r\n                        setAuthStep('2fa');\r\n                    }\r\n                } else {\r\n                    // Complete login - no additional auth required\r\n                    completeLogin({\r\n                        admin_id: response.data.admin_id,\r\n                        username: response.data.username,\r\n                        role: response.data.role,\r\n                        auth_method: response.data.auth_method || 'password_only'\r\n                    });\r\n                }\r\n            } else {\r\n                setError(response.data.message || 'Login failed');\r\n            }\r\n        } catch (error) {\r\n            console.error('Login error:', error);\r\n            if (error.response) {\r\n                setError(error.response.data.message || 'Invalid credentials');\r\n            } else if (error.request) {\r\n                setError('Network error. Please check your connection.');\r\n            } else {\r\n                setError('An error occurred. Please try again.');\r\n            }\r\n        } finally {\r\n            setIsLoading(false);\r\n        }\r\n    };\r\n\r\n    const completeLogin = (loginData) => {\r\n        // Store authentication data\r\n        localStorage.setItem('adminId', loginData.admin_id);\r\n        localStorage.setItem('adminUsername', loginData.username);\r\n        localStorage.setItem('adminRole', loginData.role);\r\n        localStorage.setItem('adminAuthMethod', loginData.auth_method);\r\n\r\n        if (loginData.session_token) {\r\n            localStorage.setItem('adminSessionToken', loginData.session_token);\r\n        }\r\n\r\n        // Navigate to dashboard\r\n        navigate('/admin/dashboard');\r\n    };\r\n\r\n    const handleAuthSuccess = (authData) => {\r\n        completeLogin(authData);\r\n    };\r\n\r\n    const handleBackToLogin = () => {\r\n        setAuthStep('login');\r\n        setAdminData(null);\r\n        setError('');\r\n        setPassword(''); // Clear password for security\r\n    };\r\n\r\n    return (\r\n        <div className=\"admin-login-container\">\r\n            <div className=\"login-left-panel\">\r\n                <div className=\"login-logo\">\r\n                    <div className=\"logo-icon\">\r\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\">\r\n                            <rect x=\"2\" y=\"3\" width=\"20\" height=\"14\" rx=\"2\" ry=\"2\"></rect>\r\n                            <line x1=\"8\" y1=\"21\" x2=\"16\" y2=\"21\"></line>\r\n                            <line x1=\"12\" y1=\"17\" x2=\"12\" y2=\"21\"></line>\r\n                        </svg>\r\n                    </div>\r\n                    <h1>FanBet247</h1>\r\n                </div>\r\n\r\n                <div className=\"login-form-container\">\r\n                    <h2>Admin Login</h2>\r\n                    <p className=\"login-subtitle\">Enter your credentials to access the admin dashboard</p>\r\n\r\n                    {error && <div className=\"error-message\">{error}</div>}\r\n\r\n                    <form onSubmit={handleSubmit}>\r\n                        <div className=\"form-group\">\r\n                            <label htmlFor=\"identifier\">Username or Email</label>\r\n                            <div className=\"input-container\">\r\n                                <input\r\n                                    type=\"text\"\r\n                                    id=\"identifier\"\r\n                                    value={identifier}\r\n                                    onChange={(e) => setIdentifier(e.target.value)}\r\n                                    disabled={isLoading}\r\n                                    placeholder=\"Enter your username or email\"\r\n                                    required\r\n                                />\r\n                                <div className=\"input-icon\">\r\n                                    <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\">\r\n                                        <path d=\"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2\"></path>\r\n                                        <circle cx=\"12\" cy=\"7\" r=\"4\"></circle>\r\n                                    </svg>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n\r\n                        <div className=\"form-group\">\r\n                            <label htmlFor=\"password\">Password</label>\r\n                            <div className=\"input-container\">\r\n                                <input\r\n                                    type=\"password\"\r\n                                    id=\"password\"\r\n                                    value={password}\r\n                                    onChange={(e) => setPassword(e.target.value)}\r\n                                    disabled={isLoading}\r\n                                    placeholder=\"Enter your password\"\r\n                                    required\r\n                                />\r\n                                <div className=\"input-icon\">\r\n                                    <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\">\r\n                                        <rect x=\"3\" y=\"11\" width=\"18\" height=\"11\" rx=\"2\" ry=\"2\"></rect>\r\n                                        <path d=\"M7 11V7a5 5 0 0 1 10 0v4\"></path>\r\n                                    </svg>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n\r\n                        <div className=\"form-options\">\r\n                            <div className=\"remember-me\">\r\n                                <input\r\n                                    type=\"checkbox\"\r\n                                    id=\"rememberMe\"\r\n                                    checked={rememberMe}\r\n                                    onChange={(e) => setRememberMe(e.target.checked)}\r\n                                />\r\n                                <label htmlFor=\"rememberMe\">Remember me</label>\r\n                            </div>\r\n                            <button type=\"button\" className=\"forgot-password\" onClick={() => alert('Password reset functionality coming soon!')}>Forgot password?</button>\r\n                        </div>\r\n\r\n                        <button type=\"submit\" className=\"login-button\" disabled={isLoading}>\r\n                            {isLoading ? 'Logging in...' : 'Login'}\r\n                        </button>\r\n                    </form>\r\n                </div>\r\n            </div>\r\n\r\n            <div className=\"login-right-panel\"></div>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default AdminLoginPage;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,oBAAoB,EAAEC,oBAAoB,EAAEC,aAAa,QAAQ,qBAAqB;AAC/F,OAAO,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAG9B,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAMC,QAAQ,GAAGR,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAM,CAACS,QAAQ,EAAEC,WAAW,CAAC,GAAGZ,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;EACnD,MAAM,CAACa,SAAS,EAAEC,YAAY,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;;EAEhD;EACA,MAAM,CAACe,UAAU,EAAEC,aAAa,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACiB,QAAQ,EAAEC,WAAW,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACmB,KAAK,EAAEC,QAAQ,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACqB,SAAS,EAAEC,YAAY,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACuB,UAAU,EAAEC,aAAa,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EAEnD,MAAMyB,YAAY,GAAG,MAAOC,CAAC,IAAK;IAC9BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBP,QAAQ,CAAC,EAAE,CAAC;IACZE,YAAY,CAAC,IAAI,CAAC;IAElB,IAAI;MACA,MAAMM,QAAQ,GAAG,MAAM3B,KAAK,CAAC4B,IAAI,CAAC,2CAA2C,EAAE;QAC3Ed,UAAU;QACVE,QAAQ;QACRa,WAAW,EAAEP;MACjB,CAAC,CAAC;MAEFQ,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE,2CAA2C,CAAC;MAEpF,IAAIJ,QAAQ,CAACK,IAAI,CAACC,OAAO,EAAE;QACvB;QACApB,YAAY,CAAC;UACTqB,QAAQ,EAAEP,QAAQ,CAACK,IAAI,CAACE,QAAQ;UAChCC,QAAQ,EAAER,QAAQ,CAACK,IAAI,CAACG,QAAQ;UAChCC,IAAI,EAAET,QAAQ,CAACK,IAAI,CAACI;QACxB,CAAC,CAAC;;QAEF;QACA,IAAIT,QAAQ,CAACK,IAAI,CAACK,wBAAwB,EAAE;UACxC,MAAMC,QAAQ,GAAGX,QAAQ,CAACK,IAAI,CAACO,SAAS;UAExC,IAAID,QAAQ,KAAK,KAAK,EAAE;YACpB3B,WAAW,CAAC,KAAK,CAAC;UACtB,CAAC,MAAM,IAAI2B,QAAQ,KAAK,KAAK,EAAE;YAC3B;YACA3B,WAAW,CAAC,KAAK,CAAC;UACtB;QACJ,CAAC,MAAM;UACH;UACA6B,aAAa,CAAC;YACVN,QAAQ,EAAEP,QAAQ,CAACK,IAAI,CAACE,QAAQ;YAChCC,QAAQ,EAAER,QAAQ,CAACK,IAAI,CAACG,QAAQ;YAChCC,IAAI,EAAET,QAAQ,CAACK,IAAI,CAACI,IAAI;YACxBK,WAAW,EAAEd,QAAQ,CAACK,IAAI,CAACS,WAAW,IAAI;UAC9C,CAAC,CAAC;QACN;MACJ,CAAC,MAAM;QACHtB,QAAQ,CAACQ,QAAQ,CAACK,IAAI,CAACU,OAAO,IAAI,cAAc,CAAC;MACrD;IACJ,CAAC,CAAC,OAAOxB,KAAK,EAAE;MACZY,OAAO,CAACZ,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;MACpC,IAAIA,KAAK,CAACS,QAAQ,EAAE;QAChBR,QAAQ,CAACD,KAAK,CAACS,QAAQ,CAACK,IAAI,CAACU,OAAO,IAAI,qBAAqB,CAAC;MAClE,CAAC,MAAM,IAAIxB,KAAK,CAACyB,OAAO,EAAE;QACtBxB,QAAQ,CAAC,8CAA8C,CAAC;MAC5D,CAAC,MAAM;QACHA,QAAQ,CAAC,sCAAsC,CAAC;MACpD;IACJ,CAAC,SAAS;MACNE,YAAY,CAAC,KAAK,CAAC;IACvB;EACJ,CAAC;EAED,MAAMmB,aAAa,GAAII,SAAS,IAAK;IACjC;IACAC,YAAY,CAACC,OAAO,CAAC,SAAS,EAAEF,SAAS,CAACV,QAAQ,CAAC;IACnDW,YAAY,CAACC,OAAO,CAAC,eAAe,EAAEF,SAAS,CAACT,QAAQ,CAAC;IACzDU,YAAY,CAACC,OAAO,CAAC,WAAW,EAAEF,SAAS,CAACR,IAAI,CAAC;IACjDS,YAAY,CAACC,OAAO,CAAC,iBAAiB,EAAEF,SAAS,CAACH,WAAW,CAAC;IAE9D,IAAIG,SAAS,CAACG,aAAa,EAAE;MACzBF,YAAY,CAACC,OAAO,CAAC,mBAAmB,EAAEF,SAAS,CAACG,aAAa,CAAC;IACtE;;IAEA;IACAtC,QAAQ,CAAC,kBAAkB,CAAC;EAChC,CAAC;EAED,MAAMuC,iBAAiB,GAAIC,QAAQ,IAAK;IACpCT,aAAa,CAACS,QAAQ,CAAC;EAC3B,CAAC;EAED,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;IAC5BvC,WAAW,CAAC,OAAO,CAAC;IACpBE,YAAY,CAAC,IAAI,CAAC;IAClBM,QAAQ,CAAC,EAAE,CAAC;IACZF,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC;EACrB,CAAC;EAED,oBACIX,OAAA;IAAK6C,SAAS,EAAC,uBAAuB;IAAAC,QAAA,gBAClC9C,OAAA;MAAK6C,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC7B9C,OAAA;QAAK6C,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACvB9C,OAAA;UAAK6C,SAAS,EAAC,WAAW;UAAAC,QAAA,eACtB9C,OAAA;YAAK+C,KAAK,EAAC,4BAA4B;YAACC,OAAO,EAAC,WAAW;YAACC,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,WAAW,EAAC,GAAG;YAACC,aAAa,EAAC,OAAO;YAACC,cAAc,EAAC,OAAO;YAAAP,QAAA,gBACtJ9C,OAAA;cAAMsD,CAAC,EAAC,GAAG;cAACC,CAAC,EAAC,GAAG;cAACC,KAAK,EAAC,IAAI;cAACC,MAAM,EAAC,IAAI;cAACC,EAAE,EAAC,GAAG;cAACC,EAAE,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC9D/D,OAAA;cAAMgE,EAAE,EAAC,GAAG;cAACC,EAAE,EAAC,IAAI;cAACC,EAAE,EAAC,IAAI;cAACC,EAAE,EAAC;YAAI;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC5C/D,OAAA;cAAMgE,EAAE,EAAC,IAAI;cAACC,EAAE,EAAC,IAAI;cAACC,EAAE,EAAC,IAAI;cAACC,EAAE,EAAC;YAAI;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACN/D,OAAA;UAAA8C,QAAA,EAAI;QAAS;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC,eAEN/D,OAAA;QAAK6C,SAAS,EAAC,sBAAsB;QAAAC,QAAA,gBACjC9C,OAAA;UAAA8C,QAAA,EAAI;QAAW;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACpB/D,OAAA;UAAG6C,SAAS,EAAC,gBAAgB;UAAAC,QAAA,EAAC;QAAoD;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,EAErFnD,KAAK,iBAAIZ,OAAA;UAAK6C,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAElC;QAAK;UAAAgD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAEtD/D,OAAA;UAAMoE,QAAQ,EAAElD,YAAa;UAAA4B,QAAA,gBACzB9C,OAAA;YAAK6C,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACvB9C,OAAA;cAAOqE,OAAO,EAAC,YAAY;cAAAvB,QAAA,EAAC;YAAiB;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACrD/D,OAAA;cAAK6C,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC5B9C,OAAA;gBACIsE,IAAI,EAAC,MAAM;gBACXC,EAAE,EAAC,YAAY;gBACfC,KAAK,EAAEhE,UAAW;gBAClBiE,QAAQ,EAAGtD,CAAC,IAAKV,aAAa,CAACU,CAAC,CAACuD,MAAM,CAACF,KAAK,CAAE;gBAC/CG,QAAQ,EAAE7D,SAAU;gBACpB8D,WAAW,EAAC,8BAA8B;gBAC1CC,QAAQ;cAAA;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC,eACF/D,OAAA;gBAAK6C,SAAS,EAAC,YAAY;gBAAAC,QAAA,eACvB9C,OAAA;kBAAK+C,KAAK,EAAC,4BAA4B;kBAACC,OAAO,EAAC,WAAW;kBAACC,IAAI,EAAC,MAAM;kBAACC,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC,GAAG;kBAACC,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAAAP,QAAA,gBACtJ9C,OAAA;oBAAM8E,CAAC,EAAC;kBAA2C;oBAAAlB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC3D/D,OAAA;oBAAQ+E,EAAE,EAAC,IAAI;oBAACC,EAAE,EAAC,GAAG;oBAACC,CAAC,EAAC;kBAAG;oBAAArB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAEN/D,OAAA;YAAK6C,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACvB9C,OAAA;cAAOqE,OAAO,EAAC,UAAU;cAAAvB,QAAA,EAAC;YAAQ;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC1C/D,OAAA;cAAK6C,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC5B9C,OAAA;gBACIsE,IAAI,EAAC,UAAU;gBACfC,EAAE,EAAC,UAAU;gBACbC,KAAK,EAAE9D,QAAS;gBAChB+D,QAAQ,EAAGtD,CAAC,IAAKR,WAAW,CAACQ,CAAC,CAACuD,MAAM,CAACF,KAAK,CAAE;gBAC7CG,QAAQ,EAAE7D,SAAU;gBACpB8D,WAAW,EAAC,qBAAqB;gBACjCC,QAAQ;cAAA;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC,eACF/D,OAAA;gBAAK6C,SAAS,EAAC,YAAY;gBAAAC,QAAA,eACvB9C,OAAA;kBAAK+C,KAAK,EAAC,4BAA4B;kBAACC,OAAO,EAAC,WAAW;kBAACC,IAAI,EAAC,MAAM;kBAACC,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC,GAAG;kBAACC,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAAAP,QAAA,gBACtJ9C,OAAA;oBAAMsD,CAAC,EAAC,GAAG;oBAACC,CAAC,EAAC,IAAI;oBAACC,KAAK,EAAC,IAAI;oBAACC,MAAM,EAAC,IAAI;oBAACC,EAAE,EAAC,GAAG;oBAACC,EAAE,EAAC;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC/D/D,OAAA;oBAAM8E,CAAC,EAAC;kBAA0B;oBAAAlB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAEN/D,OAAA;YAAK6C,SAAS,EAAC,cAAc;YAAAC,QAAA,gBACzB9C,OAAA;cAAK6C,SAAS,EAAC,aAAa;cAAAC,QAAA,gBACxB9C,OAAA;gBACIsE,IAAI,EAAC,UAAU;gBACfC,EAAE,EAAC,YAAY;gBACfW,OAAO,EAAElE,UAAW;gBACpByD,QAAQ,EAAGtD,CAAC,IAAKF,aAAa,CAACE,CAAC,CAACuD,MAAM,CAACQ,OAAO;cAAE;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC,eACF/D,OAAA;gBAAOqE,OAAO,EAAC,YAAY;gBAAAvB,QAAA,EAAC;cAAW;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC,eACN/D,OAAA;cAAQsE,IAAI,EAAC,QAAQ;cAACzB,SAAS,EAAC,iBAAiB;cAACsC,OAAO,EAAEA,CAAA,KAAMC,KAAK,CAAC,2CAA2C,CAAE;cAAAtC,QAAA,EAAC;YAAgB;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7I,CAAC,eAEN/D,OAAA;YAAQsE,IAAI,EAAC,QAAQ;YAACzB,SAAS,EAAC,cAAc;YAAC8B,QAAQ,EAAE7D,SAAU;YAAAgC,QAAA,EAC9DhC,SAAS,GAAG,eAAe,GAAG;UAAO;YAAA8C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAEN/D,OAAA;MAAK6C,SAAS,EAAC;IAAmB;MAAAe,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACxC,CAAC;AAEd,CAAC;AAAC7D,EAAA,CAxLID,cAAc;EAAA,QACCN,WAAW;AAAA;AAAA0F,EAAA,GAD1BpF,cAAc;AA0LpB,eAAeA,cAAc;AAAC,IAAAoF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}