{"ast": null, "code": "import React,{useState}from'react';import axios from'axios';import{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON>,FaEx<PERSON><PERSON>riangle,FaArrowLeft,FaQuestionCircle}from'react-icons/fa';import'../../pages/AdminLoginPage.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const API_BASE_URL='/backend';const Admin2FAVerification=_ref=>{let{adminId,username,onSuccess,onBack}=_ref;const[verificationCode,setVerificationCode]=useState('');const[backupCode,setBackupCode]=useState('');const[useBackupCode,setUseBackupCode]=useState(false);const[loading,setLoading]=useState(false);const[error,setError]=useState('');const[success,setSuccess]=useState('');const verify2FA=async()=>{const code=useBackupCode?backupCode:verificationCode;if(!code||(useBackupCode?code.length!==8:code.length!==6)){setError(useBackupCode?'Please enter a valid 8-character backup code':'Please enter a valid 6-digit code');return;}try{setLoading(true);setError('');const response=await axios.post(`${API_BASE_URL}/handlers/admin_verify_2fa.php`,{admin_id:adminId,code:code,is_backup_code:useBackupCode});if(response.data.success){setSuccess('2FA verification successful!');// Show warning if backup codes are running low\nif(response.data.backup_code_warning){setTimeout(()=>{alert(response.data.backup_code_warning);},1000);}setTimeout(()=>{onSuccess({admin_id:response.data.admin_id,username:response.data.username,role:response.data.role,session_token:response.data.session_token,auth_method:response.data.auth_method,used_backup_code:response.data.used_backup_code,remaining_backup_codes:response.data.remaining_backup_codes});},1000);}else{setError(response.data.message||'Invalid verification code');// Clear the input on error\nif(useBackupCode){setBackupCode('');}else{setVerificationCode('');}}}catch(err){setError('Failed to verify 2FA code. Please try again.');console.error('2FA verification error:',err);if(useBackupCode){setBackupCode('');}else{setVerificationCode('');}}finally{setLoading(false);}};const handleKeyPress=e=>{if(e.key==='Enter'){verify2FA();}};const handleSubmit=e=>{e.preventDefault();verify2FA();};const toggleBackupCode=()=>{setUseBackupCode(!useBackupCode);setVerificationCode('');setBackupCode('');setError('');};return/*#__PURE__*/_jsxs(\"div\",{className:\"admin-login-container\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"login-left-panel\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"login-logo\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"logo-icon\",children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",viewBox:\"0 0 24 24\",fill:\"none\",stroke:\"currentColor\",strokeWidth:\"2\",strokeLinecap:\"round\",strokeLinejoin:\"round\",children:[/*#__PURE__*/_jsx(\"rect\",{x:\"2\",y:\"3\",width:\"20\",height:\"14\",rx:\"2\",ry:\"2\"}),/*#__PURE__*/_jsx(\"line\",{x1:\"8\",y1:\"21\",x2:\"16\",y2:\"21\"}),/*#__PURE__*/_jsx(\"line\",{x1:\"12\",y1:\"17\",x2:\"12\",y2:\"21\"})]})}),/*#__PURE__*/_jsx(\"h1\",{children:\"FanBet247\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"login-form-container\",children:[/*#__PURE__*/_jsx(\"h2\",{children:\"Two-Factor Authentication\"}),/*#__PURE__*/_jsx(\"p\",{className:\"login-subtitle\",children:useBackupCode?'Enter one of your backup codes to continue':'Enter the 6-digit code from your Google Authenticator app'}),/*#__PURE__*/_jsxs(\"div\",{className:\"security-notice\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"security-icon\",children:/*#__PURE__*/_jsx(FaShieldAlt,{})}),/*#__PURE__*/_jsxs(\"span\",{children:[\"Logged in as: \",/*#__PURE__*/_jsx(\"strong\",{children:username})]})]}),error&&/*#__PURE__*/_jsx(\"div\",{className:\"error-message\",children:error}),success&&/*#__PURE__*/_jsx(\"div\",{style:{padding:'0.75rem',backgroundColor:'rgba(34, 197, 94, 0.1)',color:'#16a34a',borderRadius:'0.5rem',marginBottom:'1.5rem',textAlign:'center',fontSize:'0.875rem',border:'1px solid rgba(34, 197, 94, 0.2)'},children:success}),/*#__PURE__*/_jsxs(\"form\",{onSubmit:handleSubmit,children:[/*#__PURE__*/_jsx(\"div\",{className:\"form-group\",children:useBackupCode?/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"backupCode\",children:\"Backup Code\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"input-container\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"text\",id:\"backupCode\",maxLength:\"8\",value:backupCode,onChange:e=>setBackupCode(e.target.value.toUpperCase().replace(/[^A-Z0-9]/g,'')),onKeyPress:handleKeyPress,placeholder:\"XXXXXXXX\",disabled:loading,style:{textAlign:'center',fontSize:'1.125rem',fontFamily:'monospace',letterSpacing:'0.1em'}}),/*#__PURE__*/_jsx(\"div\",{className:\"input-icon\",children:/*#__PURE__*/_jsx(FaKey,{})})]}),/*#__PURE__*/_jsx(\"p\",{style:{fontSize:'0.75rem',color:'#6b7280',marginTop:'0.25rem'},children:\"Enter one of your 8-character backup codes\"})]}):/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"verificationCode\",children:\"Authenticator Code\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"input-container\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"text\",id:\"verificationCode\",maxLength:\"6\",value:verificationCode,onChange:e=>setVerificationCode(e.target.value.replace(/\\D/g,'')),onKeyPress:handleKeyPress,placeholder:\"000000\",disabled:loading,style:{textAlign:'center',fontSize:'1.25rem',fontFamily:'monospace',letterSpacing:'0.2em'}}),/*#__PURE__*/_jsx(\"div\",{className:\"input-icon\",children:/*#__PURE__*/_jsx(FaShieldAlt,{})})]}),/*#__PURE__*/_jsx(\"p\",{style:{fontSize:'0.75rem',color:'#6b7280',marginTop:'0.25rem'},children:\"Enter the 6-digit code from Google Authenticator\"})]})}),/*#__PURE__*/_jsx(\"div\",{style:{textAlign:'center',marginBottom:'1.5rem'},children:/*#__PURE__*/_jsxs(\"button\",{type:\"button\",onClick:toggleBackupCode,disabled:loading,style:{display:'inline-flex',alignItems:'center',gap:'0.5rem',color:'#2C5F2D',fontSize:'0.875rem',textDecoration:'none',transition:'color 0.3s ease',background:'none',border:'none',cursor:'pointer',padding:'0.5rem',borderRadius:'0.375rem',whiteSpace:'nowrap'},onMouseOver:e=>e.target.style.color='#224924',onMouseOut:e=>e.target.style.color='#2C5F2D',children:[/*#__PURE__*/_jsx(FaQuestionCircle,{}),useBackupCode?'Use Authenticator App':'Use Backup Code']})}),/*#__PURE__*/_jsxs(\"button\",{type:\"submit\",className:\"login-button\",disabled:loading||(useBackupCode?backupCode.length!==8:verificationCode.length!==6),style:{display:'flex',alignItems:'center',justifyContent:'center',gap:'0.5rem'},children:[loading&&/*#__PURE__*/_jsx(FaSpinner,{className:\"animate-spin\"}),loading?'Verifying...':'Verify Code']})]}),/*#__PURE__*/_jsxs(\"button\",{type:\"button\",onClick:onBack,disabled:loading,style:{width:'100%',display:'flex',alignItems:'center',justifyContent:'center',gap:'0.5rem',backgroundColor:'#dc2626',color:'white',fontSize:'1rem',fontWeight:'500',border:'none',borderRadius:'0.5rem',padding:'0.75rem 1rem',cursor:'pointer',transition:'all 0.3s ease',whiteSpace:'nowrap',textTransform:'uppercase',letterSpacing:'0.5px',marginTop:'1rem'},onMouseOver:e=>{e.target.style.backgroundColor='#b91c1c';},onMouseOut:e=>{e.target.style.backgroundColor='#dc2626';},children:[/*#__PURE__*/_jsx(FaArrowLeft,{}),\"Back to Login\"]}),/*#__PURE__*/_jsx(\"div\",{style:{padding:'0.75rem 1rem',backgroundColor:'rgba(59, 130, 246, 0.1)',border:'1px solid rgba(59, 130, 246, 0.2)',borderRadius:'0.5rem',marginTop:'1.5rem'},children:/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'flex-start',gap:'0.75rem'},children:[/*#__PURE__*/_jsx(FaQuestionCircle,{style:{color:'#3b82f6',marginTop:'0.125rem',flexShrink:0}}),/*#__PURE__*/_jsxs(\"div\",{style:{fontSize:'0.875rem',color:'#1e40af'},children:[/*#__PURE__*/_jsx(\"p\",{style:{fontWeight:'500',marginBottom:'0.25rem'},children:\"Need help?\"}),useBackupCode?/*#__PURE__*/_jsx(\"p\",{style:{margin:0},children:\"Backup codes are 8-character codes you saved when setting up 2FA. Each code can only be used once.\"}):/*#__PURE__*/_jsx(\"p\",{style:{margin:0},children:\"Open your Google Authenticator app and find the 6-digit code for FanBet247 Admin. The code changes every 30 seconds.\"})]})]})})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"login-right-panel\"})]});};export default Admin2FAVerification;", "map": {"version": 3, "names": ["React", "useState", "axios", "FaShieldAlt", "FaKey", "FaSpinner", "FaExclamationTriangle", "FaArrowLeft", "FaQuestionCircle", "jsx", "_jsx", "jsxs", "_jsxs", "API_BASE_URL", "Admin2FAVerification", "_ref", "adminId", "username", "onSuccess", "onBack", "verificationCode", "setVerificationCode", "backupCode", "setBackupCode", "useBackupCode", "setUseBackupCode", "loading", "setLoading", "error", "setError", "success", "setSuccess", "verify2FA", "code", "length", "response", "post", "admin_id", "is_backup_code", "data", "backup_code_warning", "setTimeout", "alert", "role", "session_token", "auth_method", "used_backup_code", "remaining_backup_codes", "message", "err", "console", "handleKeyPress", "e", "key", "handleSubmit", "preventDefault", "toggleBackupCode", "className", "children", "xmlns", "viewBox", "fill", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "x", "y", "width", "height", "rx", "ry", "x1", "y1", "x2", "y2", "style", "padding", "backgroundColor", "color", "borderRadius", "marginBottom", "textAlign", "fontSize", "border", "onSubmit", "htmlFor", "type", "id", "max<PERSON><PERSON><PERSON>", "value", "onChange", "target", "toUpperCase", "replace", "onKeyPress", "placeholder", "disabled", "fontFamily", "letterSpacing", "marginTop", "onClick", "display", "alignItems", "gap", "textDecoration", "transition", "background", "cursor", "whiteSpace", "onMouseOver", "onMouseOut", "justifyContent", "fontWeight", "textTransform", "flexShrink", "margin"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/components/Admin/Admin2FAVerification.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport axios from 'axios';\nimport { Fa<PERSON><PERSON><PERSON><PERSON>lt, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, FaExclamation<PERSON>riangle, FaArrowLeft, FaQuestionCircle } from 'react-icons/fa';\nimport '../../pages/AdminLoginPage.css';\n\nconst API_BASE_URL = '/backend';\n\nconst Admin2FAVerification = ({ adminId, username, onSuccess, onBack }) => {\n    const [verificationCode, setVerificationCode] = useState('');\n    const [backupCode, setBackupCode] = useState('');\n    const [useBackupCode, setUseBackupCode] = useState(false);\n    const [loading, setLoading] = useState(false);\n    const [error, setError] = useState('');\n    const [success, setSuccess] = useState('');\n\n    const verify2FA = async () => {\n        const code = useBackupCode ? backupCode : verificationCode;\n        \n        if (!code || (useBackupCode ? code.length !== 8 : code.length !== 6)) {\n            setError(useBackupCode ? 'Please enter a valid 8-character backup code' : 'Please enter a valid 6-digit code');\n            return;\n        }\n\n        try {\n            setLoading(true);\n            setError('');\n\n            const response = await axios.post(`${API_BASE_URL}/handlers/admin_verify_2fa.php`, {\n                admin_id: adminId,\n                code: code,\n                is_backup_code: useBackupCode\n            });\n\n            if (response.data.success) {\n                setSuccess('2FA verification successful!');\n                \n                // Show warning if backup codes are running low\n                if (response.data.backup_code_warning) {\n                    setTimeout(() => {\n                        alert(response.data.backup_code_warning);\n                    }, 1000);\n                }\n                \n                setTimeout(() => {\n                    onSuccess({\n                        admin_id: response.data.admin_id,\n                        username: response.data.username,\n                        role: response.data.role,\n                        session_token: response.data.session_token,\n                        auth_method: response.data.auth_method,\n                        used_backup_code: response.data.used_backup_code,\n                        remaining_backup_codes: response.data.remaining_backup_codes\n                    });\n                }, 1000);\n            } else {\n                setError(response.data.message || 'Invalid verification code');\n                // Clear the input on error\n                if (useBackupCode) {\n                    setBackupCode('');\n                } else {\n                    setVerificationCode('');\n                }\n            }\n        } catch (err) {\n            setError('Failed to verify 2FA code. Please try again.');\n            console.error('2FA verification error:', err);\n            if (useBackupCode) {\n                setBackupCode('');\n            } else {\n                setVerificationCode('');\n            }\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const handleKeyPress = (e) => {\n        if (e.key === 'Enter') {\n            verify2FA();\n        }\n    };\n\n    const handleSubmit = (e) => {\n        e.preventDefault();\n        verify2FA();\n    };\n\n    const toggleBackupCode = () => {\n        setUseBackupCode(!useBackupCode);\n        setVerificationCode('');\n        setBackupCode('');\n        setError('');\n    };\n\n    return (\n        <div className=\"admin-login-container\">\n            <div className=\"login-left-panel\">\n                <div className=\"login-logo\">\n                    <div className=\"logo-icon\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\">\n                            <rect x=\"2\" y=\"3\" width=\"20\" height=\"14\" rx=\"2\" ry=\"2\"></rect>\n                            <line x1=\"8\" y1=\"21\" x2=\"16\" y2=\"21\"></line>\n                            <line x1=\"12\" y1=\"17\" x2=\"12\" y2=\"21\"></line>\n                        </svg>\n                    </div>\n                    <h1>FanBet247</h1>\n                </div>\n\n                <div className=\"login-form-container\">\n                    <h2>Two-Factor Authentication</h2>\n                    <p className=\"login-subtitle\">\n                        {useBackupCode\n                            ? 'Enter one of your backup codes to continue'\n                            : 'Enter the 6-digit code from your Google Authenticator app'\n                        }\n                    </p>\n\n                    {/* User Info */}\n                    <div className=\"security-notice\">\n                        <div className=\"security-icon\">\n                            <FaShieldAlt />\n                        </div>\n                        <span>Logged in as: <strong>{username}</strong></span>\n                    </div>\n\n                    {/* Error Message */}\n                    {error && <div className=\"error-message\">{error}</div>}\n\n                    {/* Success Message */}\n                    {success && (\n                        <div style={{\n                            padding: '0.75rem',\n                            backgroundColor: 'rgba(34, 197, 94, 0.1)',\n                            color: '#16a34a',\n                            borderRadius: '0.5rem',\n                            marginBottom: '1.5rem',\n                            textAlign: 'center',\n                            fontSize: '0.875rem',\n                            border: '1px solid rgba(34, 197, 94, 0.2)'\n                        }}>\n                            {success}\n                        </div>\n                    )}\n\n                    <form onSubmit={handleSubmit}>\n                        {/* Verification Input */}\n                        <div className=\"form-group\">\n                            {useBackupCode ? (\n                                <div>\n                                    <label htmlFor=\"backupCode\">Backup Code</label>\n                                    <div className=\"input-container\">\n                                        <input\n                                            type=\"text\"\n                                            id=\"backupCode\"\n                                            maxLength=\"8\"\n                                            value={backupCode}\n                                            onChange={(e) => setBackupCode(e.target.value.toUpperCase().replace(/[^A-Z0-9]/g, ''))}\n                                            onKeyPress={handleKeyPress}\n                                            placeholder=\"XXXXXXXX\"\n                                            disabled={loading}\n                                            style={{\n                                                textAlign: 'center',\n                                                fontSize: '1.125rem',\n                                                fontFamily: 'monospace',\n                                                letterSpacing: '0.1em'\n                                            }}\n                                        />\n                                        <div className=\"input-icon\">\n                                            <FaKey />\n                                        </div>\n                                    </div>\n                                    <p style={{ fontSize: '0.75rem', color: '#6b7280', marginTop: '0.25rem' }}>\n                                        Enter one of your 8-character backup codes\n                                    </p>\n                                </div>\n                            ) : (\n                                <div>\n                                    <label htmlFor=\"verificationCode\">Authenticator Code</label>\n                                    <div className=\"input-container\">\n                                        <input\n                                            type=\"text\"\n                                            id=\"verificationCode\"\n                                            maxLength=\"6\"\n                                            value={verificationCode}\n                                            onChange={(e) => setVerificationCode(e.target.value.replace(/\\D/g, ''))}\n                                            onKeyPress={handleKeyPress}\n                                            placeholder=\"000000\"\n                                            disabled={loading}\n                                            style={{\n                                                textAlign: 'center',\n                                                fontSize: '1.25rem',\n                                                fontFamily: 'monospace',\n                                                letterSpacing: '0.2em'\n                                            }}\n                                        />\n                                        <div className=\"input-icon\">\n                                            <FaShieldAlt />\n                                        </div>\n                                    </div>\n                                    <p style={{ fontSize: '0.75rem', color: '#6b7280', marginTop: '0.25rem' }}>\n                                        Enter the 6-digit code from Google Authenticator\n                                    </p>\n                                </div>\n                            )}\n                        </div>\n\n                        {/* Toggle Backup Code Link */}\n                        <div style={{\n                            textAlign: 'center',\n                            marginBottom: '1.5rem'\n                        }}>\n                            <button\n                                type=\"button\"\n                                onClick={toggleBackupCode}\n                                disabled={loading}\n                                style={{\n                                    display: 'inline-flex',\n                                    alignItems: 'center',\n                                    gap: '0.5rem',\n                                    color: '#2C5F2D',\n                                    fontSize: '0.875rem',\n                                    textDecoration: 'none',\n                                    transition: 'color 0.3s ease',\n                                    background: 'none',\n                                    border: 'none',\n                                    cursor: 'pointer',\n                                    padding: '0.5rem',\n                                    borderRadius: '0.375rem',\n                                    whiteSpace: 'nowrap'\n                                }}\n                                onMouseOver={(e) => e.target.style.color = '#224924'}\n                                onMouseOut={(e) => e.target.style.color = '#2C5F2D'}\n                            >\n                                <FaQuestionCircle />\n                                {useBackupCode ? 'Use Authenticator App' : 'Use Backup Code'}\n                            </button>\n                        </div>\n\n                        {/* Verify Button */}\n                        <button\n                            type=\"submit\"\n                            className=\"login-button\"\n                            disabled={loading || (useBackupCode ? backupCode.length !== 8 : verificationCode.length !== 6)}\n                            style={{\n                                display: 'flex',\n                                alignItems: 'center',\n                                justifyContent: 'center',\n                                gap: '0.5rem'\n                            }}\n                        >\n                            {loading && <FaSpinner className=\"animate-spin\" />}\n                            {loading ? 'Verifying...' : 'Verify Code'}\n                        </button>\n                    </form>\n\n                    {/* Back to Login Button */}\n                    <button\n                        type=\"button\"\n                        onClick={onBack}\n                        disabled={loading}\n                        style={{\n                            width: '100%',\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'center',\n                            gap: '0.5rem',\n                            backgroundColor: '#dc2626',\n                            color: 'white',\n                            fontSize: '1rem',\n                            fontWeight: '500',\n                            border: 'none',\n                            borderRadius: '0.5rem',\n                            padding: '0.75rem 1rem',\n                            cursor: 'pointer',\n                            transition: 'all 0.3s ease',\n                            whiteSpace: 'nowrap',\n                            textTransform: 'uppercase',\n                            letterSpacing: '0.5px',\n                            marginTop: '1rem'\n                        }}\n                        onMouseOver={(e) => {\n                            e.target.style.backgroundColor = '#b91c1c';\n                        }}\n                        onMouseOut={(e) => {\n                            e.target.style.backgroundColor = '#dc2626';\n                        }}\n                    >\n                        <FaArrowLeft />\n                        Back to Login\n                    </button>\n\n                    {/* Help Text */}\n                    <div style={{\n                        padding: '0.75rem 1rem',\n                        backgroundColor: 'rgba(59, 130, 246, 0.1)',\n                        border: '1px solid rgba(59, 130, 246, 0.2)',\n                        borderRadius: '0.5rem',\n                        marginTop: '1.5rem'\n                    }}>\n                        <div style={{ display: 'flex', alignItems: 'flex-start', gap: '0.75rem' }}>\n                            <FaQuestionCircle style={{ color: '#3b82f6', marginTop: '0.125rem', flexShrink: 0 }} />\n                            <div style={{ fontSize: '0.875rem', color: '#1e40af' }}>\n                                <p style={{ fontWeight: '500', marginBottom: '0.25rem' }}>Need help?</p>\n                                {useBackupCode ? (\n                                    <p style={{ margin: 0 }}>\n                                        Backup codes are 8-character codes you saved when setting up 2FA.\n                                        Each code can only be used once.\n                                    </p>\n                                ) : (\n                                    <p style={{ margin: 0 }}>\n                                        Open your Google Authenticator app and find the 6-digit code for FanBet247 Admin.\n                                        The code changes every 30 seconds.\n                                    </p>\n                                )}\n                            </div>\n                        </div>\n                    </div>\n                </div>\n            </div>\n\n            <div className=\"login-right-panel\"></div>\n        </div>\n    );\n};\n\nexport default Admin2FAVerification;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,MAAO,CAAAC,KAAK,KAAM,OAAO,CACzB,OAASC,WAAW,CAAEC,KAAK,CAAEC,SAAS,CAAEC,qBAAqB,CAAEC,WAAW,CAAEC,gBAAgB,KAAQ,gBAAgB,CACpH,MAAO,gCAAgC,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAExC,KAAM,CAAAC,YAAY,CAAG,UAAU,CAE/B,KAAM,CAAAC,oBAAoB,CAAGC,IAAA,EAA8C,IAA7C,CAAEC,OAAO,CAAEC,QAAQ,CAAEC,SAAS,CAAEC,MAAO,CAAC,CAAAJ,IAAA,CAClE,KAAM,CAACK,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGpB,QAAQ,CAAC,EAAE,CAAC,CAC5D,KAAM,CAACqB,UAAU,CAAEC,aAAa,CAAC,CAAGtB,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAACuB,aAAa,CAAEC,gBAAgB,CAAC,CAAGxB,QAAQ,CAAC,KAAK,CAAC,CACzD,KAAM,CAACyB,OAAO,CAAEC,UAAU,CAAC,CAAG1B,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAAC2B,KAAK,CAAEC,QAAQ,CAAC,CAAG5B,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAAC6B,OAAO,CAAEC,UAAU,CAAC,CAAG9B,QAAQ,CAAC,EAAE,CAAC,CAE1C,KAAM,CAAA+B,SAAS,CAAG,KAAAA,CAAA,GAAY,CAC1B,KAAM,CAAAC,IAAI,CAAGT,aAAa,CAAGF,UAAU,CAAGF,gBAAgB,CAE1D,GAAI,CAACa,IAAI,GAAKT,aAAa,CAAGS,IAAI,CAACC,MAAM,GAAK,CAAC,CAAGD,IAAI,CAACC,MAAM,GAAK,CAAC,CAAC,CAAE,CAClEL,QAAQ,CAACL,aAAa,CAAG,8CAA8C,CAAG,mCAAmC,CAAC,CAC9G,OACJ,CAEA,GAAI,CACAG,UAAU,CAAC,IAAI,CAAC,CAChBE,QAAQ,CAAC,EAAE,CAAC,CAEZ,KAAM,CAAAM,QAAQ,CAAG,KAAM,CAAAjC,KAAK,CAACkC,IAAI,CAAC,GAAGvB,YAAY,gCAAgC,CAAE,CAC/EwB,QAAQ,CAAErB,OAAO,CACjBiB,IAAI,CAAEA,IAAI,CACVK,cAAc,CAAEd,aACpB,CAAC,CAAC,CAEF,GAAIW,QAAQ,CAACI,IAAI,CAACT,OAAO,CAAE,CACvBC,UAAU,CAAC,8BAA8B,CAAC,CAE1C;AACA,GAAII,QAAQ,CAACI,IAAI,CAACC,mBAAmB,CAAE,CACnCC,UAAU,CAAC,IAAM,CACbC,KAAK,CAACP,QAAQ,CAACI,IAAI,CAACC,mBAAmB,CAAC,CAC5C,CAAC,CAAE,IAAI,CAAC,CACZ,CAEAC,UAAU,CAAC,IAAM,CACbvB,SAAS,CAAC,CACNmB,QAAQ,CAAEF,QAAQ,CAACI,IAAI,CAACF,QAAQ,CAChCpB,QAAQ,CAAEkB,QAAQ,CAACI,IAAI,CAACtB,QAAQ,CAChC0B,IAAI,CAAER,QAAQ,CAACI,IAAI,CAACI,IAAI,CACxBC,aAAa,CAAET,QAAQ,CAACI,IAAI,CAACK,aAAa,CAC1CC,WAAW,CAAEV,QAAQ,CAACI,IAAI,CAACM,WAAW,CACtCC,gBAAgB,CAAEX,QAAQ,CAACI,IAAI,CAACO,gBAAgB,CAChDC,sBAAsB,CAAEZ,QAAQ,CAACI,IAAI,CAACQ,sBAC1C,CAAC,CAAC,CACN,CAAC,CAAE,IAAI,CAAC,CACZ,CAAC,IAAM,CACHlB,QAAQ,CAACM,QAAQ,CAACI,IAAI,CAACS,OAAO,EAAI,2BAA2B,CAAC,CAC9D;AACA,GAAIxB,aAAa,CAAE,CACfD,aAAa,CAAC,EAAE,CAAC,CACrB,CAAC,IAAM,CACHF,mBAAmB,CAAC,EAAE,CAAC,CAC3B,CACJ,CACJ,CAAE,MAAO4B,GAAG,CAAE,CACVpB,QAAQ,CAAC,8CAA8C,CAAC,CACxDqB,OAAO,CAACtB,KAAK,CAAC,yBAAyB,CAAEqB,GAAG,CAAC,CAC7C,GAAIzB,aAAa,CAAE,CACfD,aAAa,CAAC,EAAE,CAAC,CACrB,CAAC,IAAM,CACHF,mBAAmB,CAAC,EAAE,CAAC,CAC3B,CACJ,CAAC,OAAS,CACNM,UAAU,CAAC,KAAK,CAAC,CACrB,CACJ,CAAC,CAED,KAAM,CAAAwB,cAAc,CAAIC,CAAC,EAAK,CAC1B,GAAIA,CAAC,CAACC,GAAG,GAAK,OAAO,CAAE,CACnBrB,SAAS,CAAC,CAAC,CACf,CACJ,CAAC,CAED,KAAM,CAAAsB,YAAY,CAAIF,CAAC,EAAK,CACxBA,CAAC,CAACG,cAAc,CAAC,CAAC,CAClBvB,SAAS,CAAC,CAAC,CACf,CAAC,CAED,KAAM,CAAAwB,gBAAgB,CAAGA,CAAA,GAAM,CAC3B/B,gBAAgB,CAAC,CAACD,aAAa,CAAC,CAChCH,mBAAmB,CAAC,EAAE,CAAC,CACvBE,aAAa,CAAC,EAAE,CAAC,CACjBM,QAAQ,CAAC,EAAE,CAAC,CAChB,CAAC,CAED,mBACIjB,KAAA,QAAK6C,SAAS,CAAC,uBAAuB,CAAAC,QAAA,eAClC9C,KAAA,QAAK6C,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC7B9C,KAAA,QAAK6C,SAAS,CAAC,YAAY,CAAAC,QAAA,eACvBhD,IAAA,QAAK+C,SAAS,CAAC,WAAW,CAAAC,QAAA,cACtB9C,KAAA,QAAK+C,KAAK,CAAC,4BAA4B,CAACC,OAAO,CAAC,WAAW,CAACC,IAAI,CAAC,MAAM,CAACC,MAAM,CAAC,cAAc,CAACC,WAAW,CAAC,GAAG,CAACC,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAAAP,QAAA,eACtJhD,IAAA,SAAMwD,CAAC,CAAC,GAAG,CAACC,CAAC,CAAC,GAAG,CAACC,KAAK,CAAC,IAAI,CAACC,MAAM,CAAC,IAAI,CAACC,EAAE,CAAC,GAAG,CAACC,EAAE,CAAC,GAAG,CAAO,CAAC,cAC9D7D,IAAA,SAAM8D,EAAE,CAAC,GAAG,CAACC,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,IAAI,CAAO,CAAC,cAC5CjE,IAAA,SAAM8D,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,IAAI,CAAO,CAAC,EAC5C,CAAC,CACL,CAAC,cACNjE,IAAA,OAAAgD,QAAA,CAAI,WAAS,CAAI,CAAC,EACjB,CAAC,cAEN9C,KAAA,QAAK6C,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACjChD,IAAA,OAAAgD,QAAA,CAAI,2BAAyB,CAAI,CAAC,cAClChD,IAAA,MAAG+C,SAAS,CAAC,gBAAgB,CAAAC,QAAA,CACxBlC,aAAa,CACR,4CAA4C,CAC5C,2DAA2D,CAElE,CAAC,cAGJZ,KAAA,QAAK6C,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC5BhD,IAAA,QAAK+C,SAAS,CAAC,eAAe,CAAAC,QAAA,cAC1BhD,IAAA,CAACP,WAAW,GAAE,CAAC,CACd,CAAC,cACNS,KAAA,SAAA8C,QAAA,EAAM,gBAAc,cAAAhD,IAAA,WAAAgD,QAAA,CAASzC,QAAQ,CAAS,CAAC,EAAM,CAAC,EACrD,CAAC,CAGLW,KAAK,eAAIlB,IAAA,QAAK+C,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAE9B,KAAK,CAAM,CAAC,CAGrDE,OAAO,eACJpB,IAAA,QAAKkE,KAAK,CAAE,CACRC,OAAO,CAAE,SAAS,CAClBC,eAAe,CAAE,wBAAwB,CACzCC,KAAK,CAAE,SAAS,CAChBC,YAAY,CAAE,QAAQ,CACtBC,YAAY,CAAE,QAAQ,CACtBC,SAAS,CAAE,QAAQ,CACnBC,QAAQ,CAAE,UAAU,CACpBC,MAAM,CAAE,kCACZ,CAAE,CAAA1B,QAAA,CACG5B,OAAO,CACP,CACR,cAEDlB,KAAA,SAAMyE,QAAQ,CAAE/B,YAAa,CAAAI,QAAA,eAEzBhD,IAAA,QAAK+C,SAAS,CAAC,YAAY,CAAAC,QAAA,CACtBlC,aAAa,cACVZ,KAAA,QAAA8C,QAAA,eACIhD,IAAA,UAAO4E,OAAO,CAAC,YAAY,CAAA5B,QAAA,CAAC,aAAW,CAAO,CAAC,cAC/C9C,KAAA,QAAK6C,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC5BhD,IAAA,UACI6E,IAAI,CAAC,MAAM,CACXC,EAAE,CAAC,YAAY,CACfC,SAAS,CAAC,GAAG,CACbC,KAAK,CAAEpE,UAAW,CAClBqE,QAAQ,CAAGvC,CAAC,EAAK7B,aAAa,CAAC6B,CAAC,CAACwC,MAAM,CAACF,KAAK,CAACG,WAAW,CAAC,CAAC,CAACC,OAAO,CAAC,YAAY,CAAE,EAAE,CAAC,CAAE,CACvFC,UAAU,CAAE5C,cAAe,CAC3B6C,WAAW,CAAC,UAAU,CACtBC,QAAQ,CAAEvE,OAAQ,CAClBkD,KAAK,CAAE,CACHM,SAAS,CAAE,QAAQ,CACnBC,QAAQ,CAAE,UAAU,CACpBe,UAAU,CAAE,WAAW,CACvBC,aAAa,CAAE,OACnB,CAAE,CACL,CAAC,cACFzF,IAAA,QAAK+C,SAAS,CAAC,YAAY,CAAAC,QAAA,cACvBhD,IAAA,CAACN,KAAK,GAAE,CAAC,CACR,CAAC,EACL,CAAC,cACNM,IAAA,MAAGkE,KAAK,CAAE,CAAEO,QAAQ,CAAE,SAAS,CAAEJ,KAAK,CAAE,SAAS,CAAEqB,SAAS,CAAE,SAAU,CAAE,CAAA1C,QAAA,CAAC,4CAE3E,CAAG,CAAC,EACH,CAAC,cAEN9C,KAAA,QAAA8C,QAAA,eACIhD,IAAA,UAAO4E,OAAO,CAAC,kBAAkB,CAAA5B,QAAA,CAAC,oBAAkB,CAAO,CAAC,cAC5D9C,KAAA,QAAK6C,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC5BhD,IAAA,UACI6E,IAAI,CAAC,MAAM,CACXC,EAAE,CAAC,kBAAkB,CACrBC,SAAS,CAAC,GAAG,CACbC,KAAK,CAAEtE,gBAAiB,CACxBuE,QAAQ,CAAGvC,CAAC,EAAK/B,mBAAmB,CAAC+B,CAAC,CAACwC,MAAM,CAACF,KAAK,CAACI,OAAO,CAAC,KAAK,CAAE,EAAE,CAAC,CAAE,CACxEC,UAAU,CAAE5C,cAAe,CAC3B6C,WAAW,CAAC,QAAQ,CACpBC,QAAQ,CAAEvE,OAAQ,CAClBkD,KAAK,CAAE,CACHM,SAAS,CAAE,QAAQ,CACnBC,QAAQ,CAAE,SAAS,CACnBe,UAAU,CAAE,WAAW,CACvBC,aAAa,CAAE,OACnB,CAAE,CACL,CAAC,cACFzF,IAAA,QAAK+C,SAAS,CAAC,YAAY,CAAAC,QAAA,cACvBhD,IAAA,CAACP,WAAW,GAAE,CAAC,CACd,CAAC,EACL,CAAC,cACNO,IAAA,MAAGkE,KAAK,CAAE,CAAEO,QAAQ,CAAE,SAAS,CAAEJ,KAAK,CAAE,SAAS,CAAEqB,SAAS,CAAE,SAAU,CAAE,CAAA1C,QAAA,CAAC,kDAE3E,CAAG,CAAC,EACH,CACR,CACA,CAAC,cAGNhD,IAAA,QAAKkE,KAAK,CAAE,CACRM,SAAS,CAAE,QAAQ,CACnBD,YAAY,CAAE,QAClB,CAAE,CAAAvB,QAAA,cACE9C,KAAA,WACI2E,IAAI,CAAC,QAAQ,CACbc,OAAO,CAAE7C,gBAAiB,CAC1ByC,QAAQ,CAAEvE,OAAQ,CAClBkD,KAAK,CAAE,CACH0B,OAAO,CAAE,aAAa,CACtBC,UAAU,CAAE,QAAQ,CACpBC,GAAG,CAAE,QAAQ,CACbzB,KAAK,CAAE,SAAS,CAChBI,QAAQ,CAAE,UAAU,CACpBsB,cAAc,CAAE,MAAM,CACtBC,UAAU,CAAE,iBAAiB,CAC7BC,UAAU,CAAE,MAAM,CAClBvB,MAAM,CAAE,MAAM,CACdwB,MAAM,CAAE,SAAS,CACjB/B,OAAO,CAAE,QAAQ,CACjBG,YAAY,CAAE,UAAU,CACxB6B,UAAU,CAAE,QAChB,CAAE,CACFC,WAAW,CAAG1D,CAAC,EAAKA,CAAC,CAACwC,MAAM,CAAChB,KAAK,CAACG,KAAK,CAAG,SAAU,CACrDgC,UAAU,CAAG3D,CAAC,EAAKA,CAAC,CAACwC,MAAM,CAAChB,KAAK,CAACG,KAAK,CAAG,SAAU,CAAArB,QAAA,eAEpDhD,IAAA,CAACF,gBAAgB,GAAE,CAAC,CACnBgB,aAAa,CAAG,uBAAuB,CAAG,iBAAiB,EACxD,CAAC,CACR,CAAC,cAGNZ,KAAA,WACI2E,IAAI,CAAC,QAAQ,CACb9B,SAAS,CAAC,cAAc,CACxBwC,QAAQ,CAAEvE,OAAO,GAAKF,aAAa,CAAGF,UAAU,CAACY,MAAM,GAAK,CAAC,CAAGd,gBAAgB,CAACc,MAAM,GAAK,CAAC,CAAE,CAC/F0C,KAAK,CAAE,CACH0B,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBS,cAAc,CAAE,QAAQ,CACxBR,GAAG,CAAE,QACT,CAAE,CAAA9C,QAAA,EAEDhC,OAAO,eAAIhB,IAAA,CAACL,SAAS,EAACoD,SAAS,CAAC,cAAc,CAAE,CAAC,CACjD/B,OAAO,CAAG,cAAc,CAAG,aAAa,EACrC,CAAC,EACP,CAAC,cAGPd,KAAA,WACI2E,IAAI,CAAC,QAAQ,CACbc,OAAO,CAAElF,MAAO,CAChB8E,QAAQ,CAAEvE,OAAQ,CAClBkD,KAAK,CAAE,CACHR,KAAK,CAAE,MAAM,CACbkC,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBS,cAAc,CAAE,QAAQ,CACxBR,GAAG,CAAE,QAAQ,CACb1B,eAAe,CAAE,SAAS,CAC1BC,KAAK,CAAE,OAAO,CACdI,QAAQ,CAAE,MAAM,CAChB8B,UAAU,CAAE,KAAK,CACjB7B,MAAM,CAAE,MAAM,CACdJ,YAAY,CAAE,QAAQ,CACtBH,OAAO,CAAE,cAAc,CACvB+B,MAAM,CAAE,SAAS,CACjBF,UAAU,CAAE,eAAe,CAC3BG,UAAU,CAAE,QAAQ,CACpBK,aAAa,CAAE,WAAW,CAC1Bf,aAAa,CAAE,OAAO,CACtBC,SAAS,CAAE,MACf,CAAE,CACFU,WAAW,CAAG1D,CAAC,EAAK,CAChBA,CAAC,CAACwC,MAAM,CAAChB,KAAK,CAACE,eAAe,CAAG,SAAS,CAC9C,CAAE,CACFiC,UAAU,CAAG3D,CAAC,EAAK,CACfA,CAAC,CAACwC,MAAM,CAAChB,KAAK,CAACE,eAAe,CAAG,SAAS,CAC9C,CAAE,CAAApB,QAAA,eAEFhD,IAAA,CAACH,WAAW,GAAE,CAAC,gBAEnB,EAAQ,CAAC,cAGTG,IAAA,QAAKkE,KAAK,CAAE,CACRC,OAAO,CAAE,cAAc,CACvBC,eAAe,CAAE,yBAAyB,CAC1CM,MAAM,CAAE,mCAAmC,CAC3CJ,YAAY,CAAE,QAAQ,CACtBoB,SAAS,CAAE,QACf,CAAE,CAAA1C,QAAA,cACE9C,KAAA,QAAKgE,KAAK,CAAE,CAAE0B,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,YAAY,CAAEC,GAAG,CAAE,SAAU,CAAE,CAAA9C,QAAA,eACtEhD,IAAA,CAACF,gBAAgB,EAACoE,KAAK,CAAE,CAAEG,KAAK,CAAE,SAAS,CAAEqB,SAAS,CAAE,UAAU,CAAEe,UAAU,CAAE,CAAE,CAAE,CAAE,CAAC,cACvFvG,KAAA,QAAKgE,KAAK,CAAE,CAAEO,QAAQ,CAAE,UAAU,CAAEJ,KAAK,CAAE,SAAU,CAAE,CAAArB,QAAA,eACnDhD,IAAA,MAAGkE,KAAK,CAAE,CAAEqC,UAAU,CAAE,KAAK,CAAEhC,YAAY,CAAE,SAAU,CAAE,CAAAvB,QAAA,CAAC,YAAU,CAAG,CAAC,CACvElC,aAAa,cACVd,IAAA,MAAGkE,KAAK,CAAE,CAAEwC,MAAM,CAAE,CAAE,CAAE,CAAA1D,QAAA,CAAC,oGAGzB,CAAG,CAAC,cAEJhD,IAAA,MAAGkE,KAAK,CAAE,CAAEwC,MAAM,CAAE,CAAE,CAAE,CAAA1D,QAAA,CAAC,sHAGzB,CAAG,CACN,EACA,CAAC,EACL,CAAC,CACL,CAAC,EACL,CAAC,EACL,CAAC,cAENhD,IAAA,QAAK+C,SAAS,CAAC,mBAAmB,CAAM,CAAC,EACxC,CAAC,CAEd,CAAC,CAED,cAAe,CAAA3C,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}