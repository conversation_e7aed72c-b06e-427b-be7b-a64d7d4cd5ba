{"ast": null, "code": "import React,{useState,useEffect}from'react';import axios from'axios';import Countdown from'react-countdown';import{FaEye,FaEdit,FaTrash,FaChevronLeft,FaChevronRight}from'react-icons/fa';import'./ChallengeManagement.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const API_BASE_URL='/backend';function ChallengeManagement(){const[challenges,setChallenges]=useState([]);const[teams,setTeams]=useState([]);const[error,setError]=useState('');const[success,setSuccess]=useState('');const[showEditModal,setShowEditModal]=useState(false);const[showPreviewModal,setShowPreviewModal]=useState(false);const[selectedChallenge,setSelectedChallenge]=useState(null);const[currentPage,setCurrentPage]=useState(1);const[editingChallenge,setEditingChallenge]=useState({team_a:'',team_b:'',odds_team_a:1.80,odds_team_b:1.80,start_time:'',end_time:'',match_date:'',status:'Open'});const[showDeleteModal,setShowDeleteModal]=useState(false);const[challengeToDelete,setChallengeToDelete]=useState(null);const ITEMS_PER_PAGE=10;useEffect(()=>{fetchChallenges();fetchTeams();// Set up more frequent challenge status check (every 5 seconds)\nconst statusCheckInterval=setInterval(()=>{checkChallengeStatus();},5000);// Check every 5 seconds\nreturn()=>clearInterval(statusCheckInterval);},[]);const fetchTeams=async()=>{try{const response=await axios.get(`${API_BASE_URL}/handlers/team_management.php`);if(response.data.status===200){setTeams(response.data.data);}}catch(err){console.error('Error fetching teams:',err);}};const fetchChallenges=async()=>{try{const response=await axios.get(`${API_BASE_URL}/handlers/challenge_management.php`);if(response.data.success){setChallenges(response.data.challenges);}}catch(err){setError('Failed to fetch challenges');}};const checkChallengeStatus=async()=>{try{const response=await axios.get(`${API_BASE_URL}/handlers/check_challenge_status.php`);if(response.data.remaining_expired>0){console.log(`Found ${response.data.remaining_expired} expired challenges that need closing`);}if(response.data.affected_rows>0){console.log(`Closed ${response.data.affected_rows} expired challenges`);fetchChallenges();// Refresh only if changes were made\n}}catch(err){console.error('Error checking challenge status:',err);}};const getTeamLogo=teamName=>{const team=teams.find(team=>team.name===teamName);return team?`${API_BASE_URL}/${team.logo}`:'';};const handleEdit=challenge=>{setEditingChallenge(challenge);setShowEditModal(true);};const handleUpdate=async()=>{try{const response=await axios.post(`${API_BASE_URL}/handlers/challenge_management.php`,editingChallenge);if(response.data.success){setSuccess('Challenge updated successfully!');fetchChallenges();setShowEditModal(false);}else{setError(response.data.message||'Failed to update challenge');}}catch(err){setError('Failed to update challenge');console.error('Update error:',err);}};const handleDeleteClick=challenge=>{setChallengeToDelete(challenge);setShowDeleteModal(true);};const handleDeleteConfirm=async()=>{if(!challengeToDelete)return;try{const response=await axios.delete(`${API_BASE_URL}/handlers/challenge_management.php?challenge_id=${challengeToDelete.challenge_id}`);if(response.data.success){setSuccess('Challenge deleted successfully!');fetchChallenges();}else{setError(response.data.message||'Failed to delete challenge');}}catch(err){setError('Failed to delete challenge');console.error('Delete error:',err);}setShowDeleteModal(false);setChallengeToDelete(null);};const isExpired=challenge=>{return new Date(challenge.end_time)<new Date()||challenge.status==='Expired';};const totalPages=Math.ceil(challenges.length/ITEMS_PER_PAGE);const indexOfLastItem=currentPage*ITEMS_PER_PAGE;const indexOfFirstItem=indexOfLastItem-ITEMS_PER_PAGE;const currentChallenges=challenges.slice(indexOfFirstItem,indexOfLastItem);return/*#__PURE__*/_jsxs(\"div\",{className:\"challenge-management\",children:[/*#__PURE__*/_jsx(\"h1\",{children:\"Challenge Management\"}),error&&/*#__PURE__*/_jsx(\"div\",{className:\"error-message\",children:error}),success&&/*#__PURE__*/_jsx(\"div\",{className:\"success-message\",children:success}),/*#__PURE__*/_jsxs(\"div\",{className:\"challenges-list\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"table-container\",children:/*#__PURE__*/_jsxs(\"table\",{className:\"challenge-management-table\",children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{children:\"#\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Matchup\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Time/Date\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Status\"}),/*#__PURE__*/_jsx(\"th\",{style:{width:'100px'},children:\"Actions\"})]})}),/*#__PURE__*/_jsx(\"tbody\",{children:currentChallenges.map((challenge,index)=>/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{className:\"index-column\",children:indexOfFirstItem+index+1}),/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsxs(\"div\",{className:\"matchup-grid\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"team-block\",children:[/*#__PURE__*/_jsx(\"img\",{src:getTeamLogo(challenge.team_a),alt:challenge.team_a,className:\"team-logo\"}),/*#__PURE__*/_jsx(\"div\",{className:\"team-name\",children:challenge.team_a}),/*#__PURE__*/_jsxs(\"div\",{className:\"team-odds\",children:[\"Odds: \",challenge.odds_team_a]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"vs-center\",children:\"VS\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"team-block\",children:[/*#__PURE__*/_jsx(\"img\",{src:getTeamLogo(challenge.team_b),alt:challenge.team_b,className:\"team-logo\"}),/*#__PURE__*/_jsx(\"div\",{className:\"team-name\",children:challenge.team_b}),/*#__PURE__*/_jsxs(\"div\",{className:\"team-odds\",children:[\"Odds: \",challenge.odds_team_b]})]})]})}),/*#__PURE__*/_jsx(\"td\",{className:\"time-cell\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"match-time-display\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"match-time\",children:[\"Match: \",new Date(challenge.match_date).toLocaleString('en-US',{month:'short',day:'numeric',hour:'numeric',minute:'2-digit',hour12:true})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"end-time\",children:[\"Time Left: \",/*#__PURE__*/_jsx(Countdown,{date:new Date(challenge.end_time),renderer:_ref=>{let{days,hours,minutes,seconds,completed}=_ref;return/*#__PURE__*/_jsx(CountdownRenderer,{days:days,hours:hours,minutes:minutes,seconds:seconds,completed:completed,date:challenge.end_time});}})]})]})}),/*#__PURE__*/_jsx(\"td\",{className:\"status-cell\",children:/*#__PURE__*/_jsx(\"span\",{className:`status-badge ${challenge.status}`,children:challenge.status})}),/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsxs(\"div\",{className:\"action-buttons\",children:[/*#__PURE__*/_jsx(\"button\",{className:\"icon-btn btn-preview\",title:\"Preview\",onClick:()=>{setSelectedChallenge(challenge);setShowPreviewModal(true);},children:/*#__PURE__*/_jsx(FaEye,{})}),/*#__PURE__*/_jsx(\"button\",{className:\"icon-btn btn-edit\",title:\"Edit\",onClick:()=>handleEdit(challenge),children:/*#__PURE__*/_jsx(FaEdit,{})}),isExpired(challenge)&&/*#__PURE__*/_jsx(\"button\",{className:\"icon-btn btn-delete\",title:\"Delete\",onClick:()=>handleDeleteClick(challenge),children:/*#__PURE__*/_jsx(FaTrash,{})})]})})]},challenge.challenge_id))})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"pagination\",children:[/*#__PURE__*/_jsx(\"button\",{className:\"icon-btn pagination-btn\",onClick:()=>setCurrentPage(prev=>Math.max(prev-1,1)),disabled:currentPage===1,title:\"Previous Page\",children:/*#__PURE__*/_jsx(FaChevronLeft,{})}),/*#__PURE__*/_jsxs(\"span\",{className:\"page-info\",children:[\"Page \",currentPage,\" of \",totalPages]}),/*#__PURE__*/_jsx(\"button\",{className:\"icon-btn pagination-btn\",onClick:()=>setCurrentPage(prev=>Math.min(prev+1,totalPages)),disabled:currentPage===totalPages,title:\"Next Page\",children:/*#__PURE__*/_jsx(FaChevronRight,{})})]})]}),showPreviewModal&&selectedChallenge&&/*#__PURE__*/_jsx(PreviewModal,{challenge:selectedChallenge,onClose:()=>setShowPreviewModal(false),teams:teams,getTeamLogo:getTeamLogo}),showEditModal&&/*#__PURE__*/_jsx(EditModal,{challenge:editingChallenge,onClose:()=>setShowEditModal(false),onUpdate:handleUpdate,onChange:(field,value)=>setEditingChallenge(prev=>({...prev,[field]:value}))}),showDeleteModal&&challengeToDelete&&/*#__PURE__*/_jsx(DeleteConfirmationModal,{challenge:challengeToDelete,onConfirm:handleDeleteConfirm,onCancel:()=>{setShowDeleteModal(false);setChallengeToDelete(null);}})]});}const CountdownRenderer=_ref2=>{let{days,hours,minutes,seconds,completed,date}=_ref2;if(completed){return/*#__PURE__*/_jsx(\"span\",{className:\"countdown-expired\",children:\"Expired\"});}return/*#__PURE__*/_jsx(\"div\",{className:\"countdown-container\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"countdown-time\",children:[days>0?`${days}d `:'',hours,\"h \",minutes,\"m \",seconds,\"s\"]})});};const PreviewModal=_ref3=>{let{challenge,onClose,teams,getTeamLogo}=_ref3;return/*#__PURE__*/_jsx(\"div\",{className:\"modal-overlay\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"preview-modal\",children:[/*#__PURE__*/_jsx(\"button\",{className:\"close-button\",onClick:onClose,children:\"\\xD7\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"preview-header\",children:[/*#__PURE__*/_jsx(\"h2\",{children:\"Match Preview\"}),/*#__PURE__*/_jsx(\"div\",{className:\"match-type-section\",children:/*#__PURE__*/_jsx(\"span\",{className:\"match-type-badge\",children:challenge.match_type==='full_time'?'Full Time':'Half Time'})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"match-preview-display\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"match-team-preview left-team\",children:[/*#__PURE__*/_jsx(\"img\",{src:getTeamLogo(challenge.team_a),alt:challenge.team_a,className:\"team-logo-large\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"team-name\",children:challenge.team_a}),/*#__PURE__*/_jsxs(\"p\",{className:\"team-odds\",children:[\"Win: \",challenge.odds_team_a]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"match-vs-preview\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"vs-text\",children:\"VS\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"match-odds\",children:[/*#__PURE__*/_jsxs(\"p\",{className:\"draw-odds\",children:[\"Draw: \",challenge.odds_draw||'0.8']}),/*#__PURE__*/_jsxs(\"p\",{className:\"lost-odds\",children:[\"Lost: \",challenge.odds_lost||'0.2']})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"match-team-preview right-team\",children:[/*#__PURE__*/_jsx(\"img\",{src:getTeamLogo(challenge.team_b),alt:challenge.team_b,className:\"team-logo-large\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"team-name\",children:challenge.team_b}),/*#__PURE__*/_jsxs(\"p\",{className:\"team-odds\",children:[\"Win: \",challenge.odds_team_b]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"match-details\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"detail-row\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"detail-item\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"label\",children:\"Start Time\"}),/*#__PURE__*/_jsx(\"span\",{className:\"value\",children:new Date(challenge.start_time).toLocaleString()})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"detail-item\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"label\",children:\"End Time\"}),/*#__PURE__*/_jsx(\"span\",{className:\"value\",children:new Date(challenge.end_time).toLocaleString()})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"detail-row\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"detail-item\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"label\",children:\"Match Date\"}),/*#__PURE__*/_jsx(\"span\",{className:\"value\",children:new Date(challenge.match_date).toLocaleString()})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"detail-item\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"label\",children:\"Status\"}),/*#__PURE__*/_jsx(\"span\",{className:\"value status-badge\",children:challenge.status})]})]})]})]})});};const EditModal=_ref4=>{let{challenge,onClose,onUpdate,onChange}=_ref4;return/*#__PURE__*/_jsx(\"div\",{className:\"modal-overlay\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"edit-modal\",children:[/*#__PURE__*/_jsx(\"button\",{className:\"close-button\",onClick:onClose,children:\"\\xD7\"}),/*#__PURE__*/_jsx(\"h2\",{children:\"Edit Challenge\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-content\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{children:\"Match Type\"}),/*#__PURE__*/_jsxs(\"select\",{value:challenge.match_type||'full_time',onChange:e=>onChange('match_type',e.target.value),children:[/*#__PURE__*/_jsx(\"option\",{value:\"full_time\",children:\"Full Time\"}),/*#__PURE__*/_jsx(\"option\",{value:\"half_time\",children:\"Half Time\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{children:\"Odds Team A\"}),/*#__PURE__*/_jsx(\"input\",{type:\"number\",step:\"0.01\",value:challenge.odds_team_a,onChange:e=>onChange('odds_team_a',e.target.value)})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{children:\"Odds Team B\"}),/*#__PURE__*/_jsx(\"input\",{type:\"number\",step:\"0.01\",value:challenge.odds_team_b,onChange:e=>onChange('odds_team_b',e.target.value)})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{children:\"Draw Odds\"}),/*#__PURE__*/_jsx(\"input\",{type:\"number\",step:\"0.01\",value:challenge.odds_draw||0.8,onChange:e=>onChange('odds_draw',e.target.value)})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{children:\"Lost Odds\"}),/*#__PURE__*/_jsx(\"input\",{type:\"number\",step:\"0.01\",value:challenge.odds_lost||0.2,onChange:e=>onChange('odds_lost',e.target.value)})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{children:\"Status\"}),/*#__PURE__*/_jsxs(\"select\",{value:challenge.status,onChange:e=>onChange('status',e.target.value),children:[/*#__PURE__*/_jsx(\"option\",{value:\"Open\",children:\"Open\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Closed\",children:\"Closed\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Settled\",children:\"Settled\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{children:\"Start Time\"}),/*#__PURE__*/_jsx(\"input\",{type:\"datetime-local\",value:challenge.start_time.slice(0,16),onChange:e=>onChange('start_time',e.target.value)})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{children:\"End Time\"}),/*#__PURE__*/_jsx(\"input\",{type:\"datetime-local\",value:challenge.end_time.slice(0,16),onChange:e=>onChange('end_time',e.target.value)})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{children:\"Match Date\"}),/*#__PURE__*/_jsx(\"input\",{type:\"datetime-local\",value:challenge.match_date.slice(0,16),onChange:e=>onChange('match_date',e.target.value)})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-actions\",children:[/*#__PURE__*/_jsx(\"button\",{className:\"cancel-button\",onClick:onClose,children:\"Cancel\"}),/*#__PURE__*/_jsx(\"button\",{className:\"save-button\",onClick:onUpdate,children:\"Save Changes\"})]})]})});};const DeleteConfirmationModal=_ref5=>{let{challenge,onConfirm,onCancel}=_ref5;return/*#__PURE__*/_jsx(\"div\",{className:\"modal-overlay\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"delete-confirmation-modal\",children:[/*#__PURE__*/_jsx(\"h2\",{children:\"Delete Challenge\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"modal-content\",children:[/*#__PURE__*/_jsx(\"p\",{children:\"Are you sure you want to delete this expired challenge?\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"challenge-preview\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"teams\",children:[/*#__PURE__*/_jsx(\"span\",{children:challenge.team_a}),/*#__PURE__*/_jsx(\"span\",{className:\"vs\",children:\"vs\"}),/*#__PURE__*/_jsx(\"span\",{children:challenge.team_b})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"match-date\",children:[\"Match Date: \",new Date(challenge.match_date).toLocaleString()]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"modal-actions\",children:[/*#__PURE__*/_jsx(\"button\",{className:\"cancel-button\",onClick:onCancel,children:\"Cancel\"}),/*#__PURE__*/_jsx(\"button\",{className:\"confirm-delete-button\",onClick:onConfirm,children:\"Delete Challenge\"})]})]})});};export default ChallengeManagement;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "Countdown", "FaEye", "FaEdit", "FaTrash", "FaChevronLeft", "FaChevronRight", "jsx", "_jsx", "jsxs", "_jsxs", "API_BASE_URL", "ChallengeManagement", "challenges", "setChallenges", "teams", "setTeams", "error", "setError", "success", "setSuccess", "showEditModal", "setShowEditModal", "showPreviewModal", "setShowPreviewModal", "selected<PERSON>hall<PERSON><PERSON>", "setSelectedChallenge", "currentPage", "setCurrentPage", "editingChallenge", "setEditingChallenge", "team_a", "team_b", "odds_team_a", "odds_team_b", "start_time", "end_time", "match_date", "status", "showDeleteModal", "setShowDeleteModal", "challengeToDelete", "setChallengeToDelete", "ITEMS_PER_PAGE", "fetchChallenges", "fetchTeams", "statusCheckInterval", "setInterval", "checkChallengeStatus", "clearInterval", "response", "get", "data", "err", "console", "remaining_expired", "log", "affected_rows", "getTeamLogo", "teamName", "team", "find", "name", "logo", "handleEdit", "challenge", "handleUpdate", "post", "message", "handleDeleteClick", "handleDeleteConfirm", "delete", "challenge_id", "isExpired", "Date", "totalPages", "Math", "ceil", "length", "indexOfLastItem", "indexOfFirstItem", "currentChallenges", "slice", "className", "children", "style", "width", "map", "index", "src", "alt", "toLocaleString", "month", "day", "hour", "minute", "hour12", "date", "renderer", "_ref", "days", "hours", "minutes", "seconds", "completed", "CountdownRenderer", "title", "onClick", "prev", "max", "disabled", "min", "PreviewModal", "onClose", "EditModal", "onUpdate", "onChange", "field", "value", "DeleteConfirmationModal", "onConfirm", "onCancel", "_ref2", "_ref3", "match_type", "odds_draw", "odds_lost", "_ref4", "e", "target", "type", "step", "_ref5"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/pages/ChallengeManagement.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport Countdown from 'react-countdown';\nimport { FaEye, FaEdit, FaTrash, FaChevronLeft, FaChevronRight } from 'react-icons/fa';\nimport './ChallengeManagement.css';\n\nconst API_BASE_URL = '/backend';\n\nfunction ChallengeManagement() {\n    const [challenges, setChallenges] = useState([]);\n    const [teams, setTeams] = useState([]);\n    const [error, setError] = useState('');\n    const [success, setSuccess] = useState('');\n    const [showEditModal, setShowEditModal] = useState(false);\n    const [showPreviewModal, setShowPreviewModal] = useState(false);\n    const [selectedChallenge, setSelectedChallenge] = useState(null);\n    const [currentPage, setCurrentPage] = useState(1);\n    const [editingChallenge, setEditingChallenge] = useState({\n        team_a: '',\n        team_b: '',\n        odds_team_a: 1.80,\n        odds_team_b: 1.80,\n        start_time: '',\n        end_time: '',\n        match_date: '',\n        status: 'Open'\n    });\n    const [showDeleteModal, setShowDeleteModal] = useState(false);\n    const [challengeToDelete, setChallengeToDelete] = useState(null);\n\n    const ITEMS_PER_PAGE = 10;\n\n    useEffect(() => {\n        fetchChallenges();\n        fetchTeams();\n\n        // Set up more frequent challenge status check (every 5 seconds)\n        const statusCheckInterval = setInterval(() => {\n            checkChallengeStatus();\n        }, 5000); // Check every 5 seconds\n\n        return () => clearInterval(statusCheckInterval);\n    }, []);\n\n    const fetchTeams = async () => {\n        try {\n            const response = await axios.get(`${API_BASE_URL}/handlers/team_management.php`);\n            if (response.data.status === 200) {\n                setTeams(response.data.data);\n            }\n        } catch (err) {\n            console.error('Error fetching teams:', err);\n        }\n    };\n\n    const fetchChallenges = async () => {\n        try {\n            const response = await axios.get(`${API_BASE_URL}/handlers/challenge_management.php`);\n            if (response.data.success) {\n                setChallenges(response.data.challenges);\n            }\n        } catch (err) {\n            setError('Failed to fetch challenges');\n        }\n    };\n\n    const checkChallengeStatus = async () => {\n        try {\n            const response = await axios.get(`${API_BASE_URL}/handlers/check_challenge_status.php`);\n            if (response.data.remaining_expired > 0) {\n                console.log(`Found ${response.data.remaining_expired} expired challenges that need closing`);\n            }\n            if (response.data.affected_rows > 0) {\n                console.log(`Closed ${response.data.affected_rows} expired challenges`);\n                fetchChallenges(); // Refresh only if changes were made\n            }\n        } catch (err) {\n            console.error('Error checking challenge status:', err);\n        }\n    };\n\n    const getTeamLogo = (teamName) => {\n        const team = teams.find(team => team.name === teamName);\n        return team ? `${API_BASE_URL}/${team.logo}` : '';\n    };\n\n    const handleEdit = (challenge) => {\n        setEditingChallenge(challenge);\n        setShowEditModal(true);\n    };\n\n    const handleUpdate = async () => {\n        try {\n            const response = await axios.post(\n                `${API_BASE_URL}/handlers/challenge_management.php`,\n                editingChallenge\n            );\n\n            if (response.data.success) {\n                setSuccess('Challenge updated successfully!');\n                fetchChallenges();\n                setShowEditModal(false);\n            } else {\n                setError(response.data.message || 'Failed to update challenge');\n            }\n        } catch (err) {\n            setError('Failed to update challenge');\n            console.error('Update error:', err);\n        }\n    };\n\n    const handleDeleteClick = (challenge) => {\n        setChallengeToDelete(challenge);\n        setShowDeleteModal(true);\n    };\n\n    const handleDeleteConfirm = async () => {\n        if (!challengeToDelete) return;\n\n        try {\n            const response = await axios.delete(\n                `${API_BASE_URL}/handlers/challenge_management.php?challenge_id=${challengeToDelete.challenge_id}`\n            );\n\n            if (response.data.success) {\n                setSuccess('Challenge deleted successfully!');\n                fetchChallenges();\n            } else {\n                setError(response.data.message || 'Failed to delete challenge');\n            }\n        } catch (err) {\n            setError('Failed to delete challenge');\n            console.error('Delete error:', err);\n        }\n        setShowDeleteModal(false);\n        setChallengeToDelete(null);\n    };\n\n    const isExpired = (challenge) => {\n        return new Date(challenge.end_time) < new Date() || challenge.status === 'Expired';\n    };\n\n    const totalPages = Math.ceil(challenges.length / ITEMS_PER_PAGE);\n    const indexOfLastItem = currentPage * ITEMS_PER_PAGE;\n    const indexOfFirstItem = indexOfLastItem - ITEMS_PER_PAGE;\n    const currentChallenges = challenges.slice(indexOfFirstItem, indexOfLastItem);\n\n    return (\n        <div className=\"challenge-management\">\n            <h1>Challenge Management</h1>\n            {error && <div className=\"error-message\">{error}</div>}\n            {success && <div className=\"success-message\">{success}</div>}\n\n            <div className=\"challenges-list\">\n                <div className=\"table-container\">\n                    <table className=\"challenge-management-table\">\n                        <thead>\n                            <tr>\n                                <th>#</th>\n                                <th>Matchup</th>\n                                <th>Time/Date</th>\n                                <th>Status</th>\n                                <th style={{ width: '100px' }}>Actions</th>\n                            </tr>\n                        </thead>\n                        <tbody>\n                            {currentChallenges.map((challenge, index) => (\n                                <tr key={challenge.challenge_id}>\n                                    <td className=\"index-column\">{indexOfFirstItem + index + 1}</td>\n                                    <td>\n                                        <div className=\"matchup-grid\">\n                                            <div className=\"team-block\">\n                                                <img src={getTeamLogo(challenge.team_a)}\n                                                     alt={challenge.team_a}\n                                                     className=\"team-logo\" />\n                                                <div className=\"team-name\">{challenge.team_a}</div>\n                                                <div className=\"team-odds\">Odds: {challenge.odds_team_a}</div>\n                                            </div>\n\n                                            <div className=\"vs-center\">VS</div>\n\n                                            <div className=\"team-block\">\n                                                <img src={getTeamLogo(challenge.team_b)}\n                                                     alt={challenge.team_b}\n                                                     className=\"team-logo\" />\n                                                <div className=\"team-name\">{challenge.team_b}</div>\n                                                <div className=\"team-odds\">Odds: {challenge.odds_team_b}</div>\n                                            </div>\n                                        </div>\n                                    </td>\n                                    <td className=\"time-cell\">\n                                        <div className=\"match-time-display\">\n                                            <div className=\"match-time\">\n                                                Match: {new Date(challenge.match_date).toLocaleString('en-US', {\n                                                    month: 'short',\n                                                    day: 'numeric',\n                                                    hour: 'numeric',\n                                                    minute: '2-digit',\n                                                    hour12: true\n                                                })}\n                                            </div>\n                                            <div className=\"end-time\">\n                                                Time Left: <Countdown\n                                                    date={new Date(challenge.end_time)}\n                                                    renderer={({ days, hours, minutes, seconds, completed }) =>\n                                                        <CountdownRenderer\n                                                            days={days}\n                                                            hours={hours}\n                                                            minutes={minutes}\n                                                            seconds={seconds}\n                                                            completed={completed}\n                                                            date={challenge.end_time}\n                                                        />\n                                                    }\n                                                />\n                                            </div>\n                                        </div>\n                                    </td>\n                                    <td className=\"status-cell\">\n                                        <span className={`status-badge ${challenge.status}`}>\n                                            {challenge.status}\n                                        </span>\n                                    </td>\n                                    <td>\n                                        <div className=\"action-buttons\">\n                                            <button\n                                                className=\"icon-btn btn-preview\"\n                                                title=\"Preview\"\n                                                onClick={() => {\n                                                    setSelectedChallenge(challenge);\n                                                    setShowPreviewModal(true);\n                                                }}>\n                                                <FaEye />\n                                            </button>\n                                            <button\n                                                className=\"icon-btn btn-edit\"\n                                                title=\"Edit\"\n                                                onClick={() => handleEdit(challenge)}>\n                                                <FaEdit />\n                                            </button>\n                                            {isExpired(challenge) && (\n                                                <button\n                                                    className=\"icon-btn btn-delete\"\n                                                    title=\"Delete\"\n                                                    onClick={() => handleDeleteClick(challenge)}>\n                                                    <FaTrash />\n                                                </button>\n                                            )}\n                                        </div>\n                                    </td>\n                                </tr>\n                            ))}\n                        </tbody>\n                    </table>\n                </div>\n                <div className=\"pagination\">\n                    <button\n                        className=\"icon-btn pagination-btn\"\n                        onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}\n                        disabled={currentPage === 1}\n                        title=\"Previous Page\"\n                    >\n                        <FaChevronLeft />\n                    </button>\n                    <span className=\"page-info\">\n                        Page {currentPage} of {totalPages}\n                    </span>\n                    <button\n                        className=\"icon-btn pagination-btn\"\n                        onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}\n                        disabled={currentPage === totalPages}\n                        title=\"Next Page\"\n                    >\n                        <FaChevronRight />\n                    </button>\n                </div>\n            </div>\n\n            {showPreviewModal && selectedChallenge && (\n                <PreviewModal\n                    challenge={selectedChallenge}\n                    onClose={() => setShowPreviewModal(false)}\n                    teams={teams}\n                    getTeamLogo={getTeamLogo}\n                />\n            )}\n\n            {showEditModal && (\n                <EditModal\n                    challenge={editingChallenge}\n                    onClose={() => setShowEditModal(false)}\n                    onUpdate={handleUpdate}\n                    onChange={(field, value) => setEditingChallenge(prev => ({\n                        ...prev,\n                        [field]: value\n                    }))}\n                />\n            )}\n\n            {showDeleteModal && challengeToDelete && (\n                <DeleteConfirmationModal\n                    challenge={challengeToDelete}\n                    onConfirm={handleDeleteConfirm}\n                    onCancel={() => {\n                        setShowDeleteModal(false);\n                        setChallengeToDelete(null);\n                    }}\n                />\n            )}\n        </div>\n    );\n}\n\nconst CountdownRenderer = ({ days, hours, minutes, seconds, completed, date }) => {\n    if (completed) {\n        return <span className=\"countdown-expired\">Expired</span>;\n    }\n\n    return (\n        <div className=\"countdown-container\">\n            <div className=\"countdown-time\">\n                {days > 0 ? `${days}d ` : ''}{hours}h {minutes}m {seconds}s\n            </div>\n        </div>\n    );\n};\n\nconst PreviewModal = ({ challenge, onClose, teams, getTeamLogo }) => (\n    <div className=\"modal-overlay\">\n        <div className=\"preview-modal\">\n            <button className=\"close-button\" onClick={onClose}>×</button>\n            <div className=\"preview-header\">\n                <h2>Match Preview</h2>\n                <div className=\"match-type-section\">\n                    <span className=\"match-type-badge\">\n                        {challenge.match_type === 'full_time' ? 'Full Time' : 'Half Time'}\n                    </span>\n                </div>\n            </div>\n            <div className=\"match-preview-display\">\n                <div className=\"match-team-preview left-team\">\n                    <img src={getTeamLogo(challenge.team_a)} alt={challenge.team_a} className=\"team-logo-large\" />\n                    <h3 className=\"team-name\">{challenge.team_a}</h3>\n                    <p className=\"team-odds\">Win: {challenge.odds_team_a}</p>\n                </div>\n                <div className=\"match-vs-preview\">\n                    <span className=\"vs-text\">VS</span>\n                    <div className=\"match-odds\">\n                        <p className=\"draw-odds\">Draw: {challenge.odds_draw || '0.8'}</p>\n                        <p className=\"lost-odds\">Lost: {challenge.odds_lost || '0.2'}</p>\n                    </div>\n                </div>\n                <div className=\"match-team-preview right-team\">\n                    <img src={getTeamLogo(challenge.team_b)} alt={challenge.team_b} className=\"team-logo-large\" />\n                    <h3 className=\"team-name\">{challenge.team_b}</h3>\n                    <p className=\"team-odds\">Win: {challenge.odds_team_b}</p>\n                </div>\n            </div>\n            <div className=\"match-details\">\n                <div className=\"detail-row\">\n                    <div className=\"detail-item\">\n                        <span className=\"label\">Start Time</span>\n                        <span className=\"value\">{new Date(challenge.start_time).toLocaleString()}</span>\n                    </div>\n                    <div className=\"detail-item\">\n                        <span className=\"label\">End Time</span>\n                        <span className=\"value\">{new Date(challenge.end_time).toLocaleString()}</span>\n                    </div>\n                </div>\n                <div className=\"detail-row\">\n                    <div className=\"detail-item\">\n                        <span className=\"label\">Match Date</span>\n                        <span className=\"value\">{new Date(challenge.match_date).toLocaleString()}</span>\n                    </div>\n                    <div className=\"detail-item\">\n                        <span className=\"label\">Status</span>\n                        <span className=\"value status-badge\">{challenge.status}</span>\n                    </div>\n                </div>\n            </div>\n        </div>\n    </div>\n);\n\nconst EditModal = ({ challenge, onClose, onUpdate, onChange }) => (\n    <div className=\"modal-overlay\">\n        <div className=\"edit-modal\">\n            <button className=\"close-button\" onClick={onClose}>×</button>\n            <h2>Edit Challenge</h2>\n            <div className=\"form-content\">\n                <div className=\"form-group\">\n                    <label>Match Type</label>\n                    <select\n                        value={challenge.match_type || 'full_time'}\n                        onChange={(e) => onChange('match_type', e.target.value)}\n                    >\n                        <option value=\"full_time\">Full Time</option>\n                        <option value=\"half_time\">Half Time</option>\n                    </select>\n                </div>\n                <div className=\"form-group\">\n                    <label>Odds Team A</label>\n                    <input\n                        type=\"number\"\n                        step=\"0.01\"\n                        value={challenge.odds_team_a}\n                        onChange={(e) => onChange('odds_team_a', e.target.value)}\n                    />\n                </div>\n                <div className=\"form-group\">\n                    <label>Odds Team B</label>\n                    <input\n                        type=\"number\"\n                        step=\"0.01\"\n                        value={challenge.odds_team_b}\n                        onChange={(e) => onChange('odds_team_b', e.target.value)}\n                    />\n                </div>\n                <div className=\"form-group\">\n                    <label>Draw Odds</label>\n                    <input\n                        type=\"number\"\n                        step=\"0.01\"\n                        value={challenge.odds_draw || 0.8}\n                        onChange={(e) => onChange('odds_draw', e.target.value)}\n                    />\n                </div>\n                <div className=\"form-group\">\n                    <label>Lost Odds</label>\n                    <input\n                        type=\"number\"\n                        step=\"0.01\"\n                        value={challenge.odds_lost || 0.2}\n                        onChange={(e) => onChange('odds_lost', e.target.value)}\n                    />\n                </div>\n                <div className=\"form-group\">\n                    <label>Status</label>\n                    <select\n                        value={challenge.status}\n                        onChange={(e) => onChange('status', e.target.value)}\n                    >\n                        <option value=\"Open\">Open</option>\n                        <option value=\"Closed\">Closed</option>\n                        <option value=\"Settled\">Settled</option>\n                    </select>\n                </div>\n                <div className=\"form-group\">\n                    <label>Start Time</label>\n                    <input\n                        type=\"datetime-local\"\n                        value={challenge.start_time.slice(0, 16)}\n                        onChange={(e) => onChange('start_time', e.target.value)}\n                    />\n                </div>\n                <div className=\"form-group\">\n                    <label>End Time</label>\n                    <input\n                        type=\"datetime-local\"\n                        value={challenge.end_time.slice(0, 16)}\n                        onChange={(e) => onChange('end_time', e.target.value)}\n                    />\n                </div>\n                <div className=\"form-group\">\n                    <label>Match Date</label>\n                    <input\n                        type=\"datetime-local\"\n                        value={challenge.match_date.slice(0, 16)}\n                        onChange={(e) => onChange('match_date', e.target.value)}\n                    />\n                </div>\n            </div>\n            <div className=\"form-actions\">\n                <button className=\"cancel-button\" onClick={onClose}>Cancel</button>\n                <button className=\"save-button\" onClick={onUpdate}>Save Changes</button>\n            </div>\n        </div>\n    </div>\n);\n\nconst DeleteConfirmationModal = ({ challenge, onConfirm, onCancel }) => (\n    <div className=\"modal-overlay\">\n        <div className=\"delete-confirmation-modal\">\n            <h2>Delete Challenge</h2>\n            <div className=\"modal-content\">\n                <p>Are you sure you want to delete this expired challenge?</p>\n                <div className=\"challenge-preview\">\n                    <div className=\"teams\">\n                        <span>{challenge.team_a}</span>\n                        <span className=\"vs\">vs</span>\n                        <span>{challenge.team_b}</span>\n                    </div>\n                    <div className=\"match-date\">\n                        Match Date: {new Date(challenge.match_date).toLocaleString()}\n                    </div>\n                </div>\n            </div>\n            <div className=\"modal-actions\">\n                <button className=\"cancel-button\" onClick={onCancel}>Cancel</button>\n                <button className=\"confirm-delete-button\" onClick={onConfirm}>Delete Challenge</button>\n            </div>\n        </div>\n    </div>\n);\n\nexport default ChallengeManagement;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,MAAO,CAAAC,KAAK,KAAM,OAAO,CACzB,MAAO,CAAAC,SAAS,KAAM,iBAAiB,CACvC,OAASC,KAAK,CAAEC,MAAM,CAAEC,OAAO,CAAEC,aAAa,CAAEC,cAAc,KAAQ,gBAAgB,CACtF,MAAO,2BAA2B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEnC,KAAM,CAAAC,YAAY,CAAG,UAAU,CAE/B,QAAS,CAAAC,mBAAmBA,CAAA,CAAG,CAC3B,KAAM,CAACC,UAAU,CAAEC,aAAa,CAAC,CAAGhB,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAACiB,KAAK,CAAEC,QAAQ,CAAC,CAAGlB,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAACmB,KAAK,CAAEC,QAAQ,CAAC,CAAGpB,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAACqB,OAAO,CAAEC,UAAU,CAAC,CAAGtB,QAAQ,CAAC,EAAE,CAAC,CAC1C,KAAM,CAACuB,aAAa,CAAEC,gBAAgB,CAAC,CAAGxB,QAAQ,CAAC,KAAK,CAAC,CACzD,KAAM,CAACyB,gBAAgB,CAAEC,mBAAmB,CAAC,CAAG1B,QAAQ,CAAC,KAAK,CAAC,CAC/D,KAAM,CAAC2B,iBAAiB,CAAEC,oBAAoB,CAAC,CAAG5B,QAAQ,CAAC,IAAI,CAAC,CAChE,KAAM,CAAC6B,WAAW,CAAEC,cAAc,CAAC,CAAG9B,QAAQ,CAAC,CAAC,CAAC,CACjD,KAAM,CAAC+B,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGhC,QAAQ,CAAC,CACrDiC,MAAM,CAAE,EAAE,CACVC,MAAM,CAAE,EAAE,CACVC,WAAW,CAAE,IAAI,CACjBC,WAAW,CAAE,IAAI,CACjBC,UAAU,CAAE,EAAE,CACdC,QAAQ,CAAE,EAAE,CACZC,UAAU,CAAE,EAAE,CACdC,MAAM,CAAE,MACZ,CAAC,CAAC,CACF,KAAM,CAACC,eAAe,CAAEC,kBAAkB,CAAC,CAAG1C,QAAQ,CAAC,KAAK,CAAC,CAC7D,KAAM,CAAC2C,iBAAiB,CAAEC,oBAAoB,CAAC,CAAG5C,QAAQ,CAAC,IAAI,CAAC,CAEhE,KAAM,CAAA6C,cAAc,CAAG,EAAE,CAEzB5C,SAAS,CAAC,IAAM,CACZ6C,eAAe,CAAC,CAAC,CACjBC,UAAU,CAAC,CAAC,CAEZ;AACA,KAAM,CAAAC,mBAAmB,CAAGC,WAAW,CAAC,IAAM,CAC1CC,oBAAoB,CAAC,CAAC,CAC1B,CAAC,CAAE,IAAI,CAAC,CAAE;AAEV,MAAO,IAAMC,aAAa,CAACH,mBAAmB,CAAC,CACnD,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAD,UAAU,CAAG,KAAAA,CAAA,GAAY,CAC3B,GAAI,CACA,KAAM,CAAAK,QAAQ,CAAG,KAAM,CAAAlD,KAAK,CAACmD,GAAG,CAAC,GAAGxC,YAAY,+BAA+B,CAAC,CAChF,GAAIuC,QAAQ,CAACE,IAAI,CAACd,MAAM,GAAK,GAAG,CAAE,CAC9BtB,QAAQ,CAACkC,QAAQ,CAACE,IAAI,CAACA,IAAI,CAAC,CAChC,CACJ,CAAE,MAAOC,GAAG,CAAE,CACVC,OAAO,CAACrC,KAAK,CAAC,uBAAuB,CAAEoC,GAAG,CAAC,CAC/C,CACJ,CAAC,CAED,KAAM,CAAAT,eAAe,CAAG,KAAAA,CAAA,GAAY,CAChC,GAAI,CACA,KAAM,CAAAM,QAAQ,CAAG,KAAM,CAAAlD,KAAK,CAACmD,GAAG,CAAC,GAAGxC,YAAY,oCAAoC,CAAC,CACrF,GAAIuC,QAAQ,CAACE,IAAI,CAACjC,OAAO,CAAE,CACvBL,aAAa,CAACoC,QAAQ,CAACE,IAAI,CAACvC,UAAU,CAAC,CAC3C,CACJ,CAAE,MAAOwC,GAAG,CAAE,CACVnC,QAAQ,CAAC,4BAA4B,CAAC,CAC1C,CACJ,CAAC,CAED,KAAM,CAAA8B,oBAAoB,CAAG,KAAAA,CAAA,GAAY,CACrC,GAAI,CACA,KAAM,CAAAE,QAAQ,CAAG,KAAM,CAAAlD,KAAK,CAACmD,GAAG,CAAC,GAAGxC,YAAY,sCAAsC,CAAC,CACvF,GAAIuC,QAAQ,CAACE,IAAI,CAACG,iBAAiB,CAAG,CAAC,CAAE,CACrCD,OAAO,CAACE,GAAG,CAAC,SAASN,QAAQ,CAACE,IAAI,CAACG,iBAAiB,uCAAuC,CAAC,CAChG,CACA,GAAIL,QAAQ,CAACE,IAAI,CAACK,aAAa,CAAG,CAAC,CAAE,CACjCH,OAAO,CAACE,GAAG,CAAC,UAAUN,QAAQ,CAACE,IAAI,CAACK,aAAa,qBAAqB,CAAC,CACvEb,eAAe,CAAC,CAAC,CAAE;AACvB,CACJ,CAAE,MAAOS,GAAG,CAAE,CACVC,OAAO,CAACrC,KAAK,CAAC,kCAAkC,CAAEoC,GAAG,CAAC,CAC1D,CACJ,CAAC,CAED,KAAM,CAAAK,WAAW,CAAIC,QAAQ,EAAK,CAC9B,KAAM,CAAAC,IAAI,CAAG7C,KAAK,CAAC8C,IAAI,CAACD,IAAI,EAAIA,IAAI,CAACE,IAAI,GAAKH,QAAQ,CAAC,CACvD,MAAO,CAAAC,IAAI,CAAG,GAAGjD,YAAY,IAAIiD,IAAI,CAACG,IAAI,EAAE,CAAG,EAAE,CACrD,CAAC,CAED,KAAM,CAAAC,UAAU,CAAIC,SAAS,EAAK,CAC9BnC,mBAAmB,CAACmC,SAAS,CAAC,CAC9B3C,gBAAgB,CAAC,IAAI,CAAC,CAC1B,CAAC,CAED,KAAM,CAAA4C,YAAY,CAAG,KAAAA,CAAA,GAAY,CAC7B,GAAI,CACA,KAAM,CAAAhB,QAAQ,CAAG,KAAM,CAAAlD,KAAK,CAACmE,IAAI,CAC7B,GAAGxD,YAAY,oCAAoC,CACnDkB,gBACJ,CAAC,CAED,GAAIqB,QAAQ,CAACE,IAAI,CAACjC,OAAO,CAAE,CACvBC,UAAU,CAAC,iCAAiC,CAAC,CAC7CwB,eAAe,CAAC,CAAC,CACjBtB,gBAAgB,CAAC,KAAK,CAAC,CAC3B,CAAC,IAAM,CACHJ,QAAQ,CAACgC,QAAQ,CAACE,IAAI,CAACgB,OAAO,EAAI,4BAA4B,CAAC,CACnE,CACJ,CAAE,MAAOf,GAAG,CAAE,CACVnC,QAAQ,CAAC,4BAA4B,CAAC,CACtCoC,OAAO,CAACrC,KAAK,CAAC,eAAe,CAAEoC,GAAG,CAAC,CACvC,CACJ,CAAC,CAED,KAAM,CAAAgB,iBAAiB,CAAIJ,SAAS,EAAK,CACrCvB,oBAAoB,CAACuB,SAAS,CAAC,CAC/BzB,kBAAkB,CAAC,IAAI,CAAC,CAC5B,CAAC,CAED,KAAM,CAAA8B,mBAAmB,CAAG,KAAAA,CAAA,GAAY,CACpC,GAAI,CAAC7B,iBAAiB,CAAE,OAExB,GAAI,CACA,KAAM,CAAAS,QAAQ,CAAG,KAAM,CAAAlD,KAAK,CAACuE,MAAM,CAC/B,GAAG5D,YAAY,mDAAmD8B,iBAAiB,CAAC+B,YAAY,EACpG,CAAC,CAED,GAAItB,QAAQ,CAACE,IAAI,CAACjC,OAAO,CAAE,CACvBC,UAAU,CAAC,iCAAiC,CAAC,CAC7CwB,eAAe,CAAC,CAAC,CACrB,CAAC,IAAM,CACH1B,QAAQ,CAACgC,QAAQ,CAACE,IAAI,CAACgB,OAAO,EAAI,4BAA4B,CAAC,CACnE,CACJ,CAAE,MAAOf,GAAG,CAAE,CACVnC,QAAQ,CAAC,4BAA4B,CAAC,CACtCoC,OAAO,CAACrC,KAAK,CAAC,eAAe,CAAEoC,GAAG,CAAC,CACvC,CACAb,kBAAkB,CAAC,KAAK,CAAC,CACzBE,oBAAoB,CAAC,IAAI,CAAC,CAC9B,CAAC,CAED,KAAM,CAAA+B,SAAS,CAAIR,SAAS,EAAK,CAC7B,MAAO,IAAI,CAAAS,IAAI,CAACT,SAAS,CAAC7B,QAAQ,CAAC,CAAG,GAAI,CAAAsC,IAAI,CAAC,CAAC,EAAIT,SAAS,CAAC3B,MAAM,GAAK,SAAS,CACtF,CAAC,CAED,KAAM,CAAAqC,UAAU,CAAGC,IAAI,CAACC,IAAI,CAAChE,UAAU,CAACiE,MAAM,CAAGnC,cAAc,CAAC,CAChE,KAAM,CAAAoC,eAAe,CAAGpD,WAAW,CAAGgB,cAAc,CACpD,KAAM,CAAAqC,gBAAgB,CAAGD,eAAe,CAAGpC,cAAc,CACzD,KAAM,CAAAsC,iBAAiB,CAAGpE,UAAU,CAACqE,KAAK,CAACF,gBAAgB,CAAED,eAAe,CAAC,CAE7E,mBACIrE,KAAA,QAAKyE,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACjC5E,IAAA,OAAA4E,QAAA,CAAI,sBAAoB,CAAI,CAAC,CAC5BnE,KAAK,eAAIT,IAAA,QAAK2E,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAEnE,KAAK,CAAM,CAAC,CACrDE,OAAO,eAAIX,IAAA,QAAK2E,SAAS,CAAC,iBAAiB,CAAAC,QAAA,CAAEjE,OAAO,CAAM,CAAC,cAE5DT,KAAA,QAAKyE,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC5B5E,IAAA,QAAK2E,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC5B1E,KAAA,UAAOyE,SAAS,CAAC,4BAA4B,CAAAC,QAAA,eACzC5E,IAAA,UAAA4E,QAAA,cACI1E,KAAA,OAAA0E,QAAA,eACI5E,IAAA,OAAA4E,QAAA,CAAI,GAAC,CAAI,CAAC,cACV5E,IAAA,OAAA4E,QAAA,CAAI,SAAO,CAAI,CAAC,cAChB5E,IAAA,OAAA4E,QAAA,CAAI,WAAS,CAAI,CAAC,cAClB5E,IAAA,OAAA4E,QAAA,CAAI,QAAM,CAAI,CAAC,cACf5E,IAAA,OAAI6E,KAAK,CAAE,CAAEC,KAAK,CAAE,OAAQ,CAAE,CAAAF,QAAA,CAAC,SAAO,CAAI,CAAC,EAC3C,CAAC,CACF,CAAC,cACR5E,IAAA,UAAA4E,QAAA,CACKH,iBAAiB,CAACM,GAAG,CAAC,CAACtB,SAAS,CAAEuB,KAAK,gBACpC9E,KAAA,OAAA0E,QAAA,eACI5E,IAAA,OAAI2E,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAEJ,gBAAgB,CAAGQ,KAAK,CAAG,CAAC,CAAK,CAAC,cAChEhF,IAAA,OAAA4E,QAAA,cACI1E,KAAA,QAAKyE,SAAS,CAAC,cAAc,CAAAC,QAAA,eACzB1E,KAAA,QAAKyE,SAAS,CAAC,YAAY,CAAAC,QAAA,eACvB5E,IAAA,QAAKiF,GAAG,CAAE/B,WAAW,CAACO,SAAS,CAAClC,MAAM,CAAE,CACnC2D,GAAG,CAAEzB,SAAS,CAAClC,MAAO,CACtBoD,SAAS,CAAC,WAAW,CAAE,CAAC,cAC7B3E,IAAA,QAAK2E,SAAS,CAAC,WAAW,CAAAC,QAAA,CAAEnB,SAAS,CAAClC,MAAM,CAAM,CAAC,cACnDrB,KAAA,QAAKyE,SAAS,CAAC,WAAW,CAAAC,QAAA,EAAC,QAAM,CAACnB,SAAS,CAAChC,WAAW,EAAM,CAAC,EAC7D,CAAC,cAENzB,IAAA,QAAK2E,SAAS,CAAC,WAAW,CAAAC,QAAA,CAAC,IAAE,CAAK,CAAC,cAEnC1E,KAAA,QAAKyE,SAAS,CAAC,YAAY,CAAAC,QAAA,eACvB5E,IAAA,QAAKiF,GAAG,CAAE/B,WAAW,CAACO,SAAS,CAACjC,MAAM,CAAE,CACnC0D,GAAG,CAAEzB,SAAS,CAACjC,MAAO,CACtBmD,SAAS,CAAC,WAAW,CAAE,CAAC,cAC7B3E,IAAA,QAAK2E,SAAS,CAAC,WAAW,CAAAC,QAAA,CAAEnB,SAAS,CAACjC,MAAM,CAAM,CAAC,cACnDtB,KAAA,QAAKyE,SAAS,CAAC,WAAW,CAAAC,QAAA,EAAC,QAAM,CAACnB,SAAS,CAAC/B,WAAW,EAAM,CAAC,EAC7D,CAAC,EACL,CAAC,CACN,CAAC,cACL1B,IAAA,OAAI2E,SAAS,CAAC,WAAW,CAAAC,QAAA,cACrB1E,KAAA,QAAKyE,SAAS,CAAC,oBAAoB,CAAAC,QAAA,eAC/B1E,KAAA,QAAKyE,SAAS,CAAC,YAAY,CAAAC,QAAA,EAAC,SACjB,CAAC,GAAI,CAAAV,IAAI,CAACT,SAAS,CAAC5B,UAAU,CAAC,CAACsD,cAAc,CAAC,OAAO,CAAE,CAC3DC,KAAK,CAAE,OAAO,CACdC,GAAG,CAAE,SAAS,CACdC,IAAI,CAAE,SAAS,CACfC,MAAM,CAAE,SAAS,CACjBC,MAAM,CAAE,IACZ,CAAC,CAAC,EACD,CAAC,cACNtF,KAAA,QAAKyE,SAAS,CAAC,UAAU,CAAAC,QAAA,EAAC,aACX,cAAA5E,IAAA,CAACP,SAAS,EACjBgG,IAAI,CAAE,GAAI,CAAAvB,IAAI,CAACT,SAAS,CAAC7B,QAAQ,CAAE,CACnC8D,QAAQ,CAAEC,IAAA,MAAC,CAAEC,IAAI,CAAEC,KAAK,CAAEC,OAAO,CAAEC,OAAO,CAAEC,SAAU,CAAC,CAAAL,IAAA,oBACnD3F,IAAA,CAACiG,iBAAiB,EACdL,IAAI,CAAEA,IAAK,CACXC,KAAK,CAAEA,KAAM,CACbC,OAAO,CAAEA,OAAQ,CACjBC,OAAO,CAAEA,OAAQ,CACjBC,SAAS,CAAEA,SAAU,CACrBP,IAAI,CAAEhC,SAAS,CAAC7B,QAAS,CAC5B,CAAC,EACL,CACJ,CAAC,EACD,CAAC,EACL,CAAC,CACN,CAAC,cACL5B,IAAA,OAAI2E,SAAS,CAAC,aAAa,CAAAC,QAAA,cACvB5E,IAAA,SAAM2E,SAAS,CAAE,gBAAgBlB,SAAS,CAAC3B,MAAM,EAAG,CAAA8C,QAAA,CAC/CnB,SAAS,CAAC3B,MAAM,CACf,CAAC,CACP,CAAC,cACL9B,IAAA,OAAA4E,QAAA,cACI1E,KAAA,QAAKyE,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC3B5E,IAAA,WACI2E,SAAS,CAAC,sBAAsB,CAChCuB,KAAK,CAAC,SAAS,CACfC,OAAO,CAAEA,CAAA,GAAM,CACXjF,oBAAoB,CAACuC,SAAS,CAAC,CAC/BzC,mBAAmB,CAAC,IAAI,CAAC,CAC7B,CAAE,CAAA4D,QAAA,cACF5E,IAAA,CAACN,KAAK,GAAE,CAAC,CACL,CAAC,cACTM,IAAA,WACI2E,SAAS,CAAC,mBAAmB,CAC7BuB,KAAK,CAAC,MAAM,CACZC,OAAO,CAAEA,CAAA,GAAM3C,UAAU,CAACC,SAAS,CAAE,CAAAmB,QAAA,cACrC5E,IAAA,CAACL,MAAM,GAAE,CAAC,CACN,CAAC,CACRsE,SAAS,CAACR,SAAS,CAAC,eACjBzD,IAAA,WACI2E,SAAS,CAAC,qBAAqB,CAC/BuB,KAAK,CAAC,QAAQ,CACdC,OAAO,CAAEA,CAAA,GAAMtC,iBAAiB,CAACJ,SAAS,CAAE,CAAAmB,QAAA,cAC5C5E,IAAA,CAACJ,OAAO,GAAE,CAAC,CACP,CACX,EACA,CAAC,CACN,CAAC,GAlFA6D,SAAS,CAACO,YAmFf,CACP,CAAC,CACC,CAAC,EACL,CAAC,CACP,CAAC,cACN9D,KAAA,QAAKyE,SAAS,CAAC,YAAY,CAAAC,QAAA,eACvB5E,IAAA,WACI2E,SAAS,CAAC,yBAAyB,CACnCwB,OAAO,CAAEA,CAAA,GAAM/E,cAAc,CAACgF,IAAI,EAAIhC,IAAI,CAACiC,GAAG,CAACD,IAAI,CAAG,CAAC,CAAE,CAAC,CAAC,CAAE,CAC7DE,QAAQ,CAAEnF,WAAW,GAAK,CAAE,CAC5B+E,KAAK,CAAC,eAAe,CAAAtB,QAAA,cAErB5E,IAAA,CAACH,aAAa,GAAE,CAAC,CACb,CAAC,cACTK,KAAA,SAAMyE,SAAS,CAAC,WAAW,CAAAC,QAAA,EAAC,OACnB,CAACzD,WAAW,CAAC,MAAI,CAACgD,UAAU,EAC/B,CAAC,cACPnE,IAAA,WACI2E,SAAS,CAAC,yBAAyB,CACnCwB,OAAO,CAAEA,CAAA,GAAM/E,cAAc,CAACgF,IAAI,EAAIhC,IAAI,CAACmC,GAAG,CAACH,IAAI,CAAG,CAAC,CAAEjC,UAAU,CAAC,CAAE,CACtEmC,QAAQ,CAAEnF,WAAW,GAAKgD,UAAW,CACrC+B,KAAK,CAAC,WAAW,CAAAtB,QAAA,cAEjB5E,IAAA,CAACF,cAAc,GAAE,CAAC,CACd,CAAC,EACR,CAAC,EACL,CAAC,CAELiB,gBAAgB,EAAIE,iBAAiB,eAClCjB,IAAA,CAACwG,YAAY,EACT/C,SAAS,CAAExC,iBAAkB,CAC7BwF,OAAO,CAAEA,CAAA,GAAMzF,mBAAmB,CAAC,KAAK,CAAE,CAC1CT,KAAK,CAAEA,KAAM,CACb2C,WAAW,CAAEA,WAAY,CAC5B,CACJ,CAEArC,aAAa,eACVb,IAAA,CAAC0G,SAAS,EACNjD,SAAS,CAAEpC,gBAAiB,CAC5BoF,OAAO,CAAEA,CAAA,GAAM3F,gBAAgB,CAAC,KAAK,CAAE,CACvC6F,QAAQ,CAAEjD,YAAa,CACvBkD,QAAQ,CAAEA,CAACC,KAAK,CAAEC,KAAK,GAAKxF,mBAAmB,CAAC8E,IAAI,GAAK,CACrD,GAAGA,IAAI,CACP,CAACS,KAAK,EAAGC,KACb,CAAC,CAAC,CAAE,CACP,CACJ,CAEA/E,eAAe,EAAIE,iBAAiB,eACjCjC,IAAA,CAAC+G,uBAAuB,EACpBtD,SAAS,CAAExB,iBAAkB,CAC7B+E,SAAS,CAAElD,mBAAoB,CAC/BmD,QAAQ,CAAEA,CAAA,GAAM,CACZjF,kBAAkB,CAAC,KAAK,CAAC,CACzBE,oBAAoB,CAAC,IAAI,CAAC,CAC9B,CAAE,CACL,CACJ,EACA,CAAC,CAEd,CAEA,KAAM,CAAA+D,iBAAiB,CAAGiB,KAAA,EAAwD,IAAvD,CAAEtB,IAAI,CAAEC,KAAK,CAAEC,OAAO,CAAEC,OAAO,CAAEC,SAAS,CAAEP,IAAK,CAAC,CAAAyB,KAAA,CACzE,GAAIlB,SAAS,CAAE,CACX,mBAAOhG,IAAA,SAAM2E,SAAS,CAAC,mBAAmB,CAAAC,QAAA,CAAC,SAAO,CAAM,CAAC,CAC7D,CAEA,mBACI5E,IAAA,QAAK2E,SAAS,CAAC,qBAAqB,CAAAC,QAAA,cAChC1E,KAAA,QAAKyE,SAAS,CAAC,gBAAgB,CAAAC,QAAA,EAC1BgB,IAAI,CAAG,CAAC,CAAG,GAAGA,IAAI,IAAI,CAAG,EAAE,CAAEC,KAAK,CAAC,IAAE,CAACC,OAAO,CAAC,IAAE,CAACC,OAAO,CAAC,GAC9D,EAAK,CAAC,CACL,CAAC,CAEd,CAAC,CAED,KAAM,CAAAS,YAAY,CAAGW,KAAA,MAAC,CAAE1D,SAAS,CAAEgD,OAAO,CAAElG,KAAK,CAAE2C,WAAY,CAAC,CAAAiE,KAAA,oBAC5DnH,IAAA,QAAK2E,SAAS,CAAC,eAAe,CAAAC,QAAA,cAC1B1E,KAAA,QAAKyE,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC1B5E,IAAA,WAAQ2E,SAAS,CAAC,cAAc,CAACwB,OAAO,CAAEM,OAAQ,CAAA7B,QAAA,CAAC,MAAC,CAAQ,CAAC,cAC7D1E,KAAA,QAAKyE,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC3B5E,IAAA,OAAA4E,QAAA,CAAI,eAAa,CAAI,CAAC,cACtB5E,IAAA,QAAK2E,SAAS,CAAC,oBAAoB,CAAAC,QAAA,cAC/B5E,IAAA,SAAM2E,SAAS,CAAC,kBAAkB,CAAAC,QAAA,CAC7BnB,SAAS,CAAC2D,UAAU,GAAK,WAAW,CAAG,WAAW,CAAG,WAAW,CAC/D,CAAC,CACN,CAAC,EACL,CAAC,cACNlH,KAAA,QAAKyE,SAAS,CAAC,uBAAuB,CAAAC,QAAA,eAClC1E,KAAA,QAAKyE,SAAS,CAAC,8BAA8B,CAAAC,QAAA,eACzC5E,IAAA,QAAKiF,GAAG,CAAE/B,WAAW,CAACO,SAAS,CAAClC,MAAM,CAAE,CAAC2D,GAAG,CAAEzB,SAAS,CAAClC,MAAO,CAACoD,SAAS,CAAC,iBAAiB,CAAE,CAAC,cAC9F3E,IAAA,OAAI2E,SAAS,CAAC,WAAW,CAAAC,QAAA,CAAEnB,SAAS,CAAClC,MAAM,CAAK,CAAC,cACjDrB,KAAA,MAAGyE,SAAS,CAAC,WAAW,CAAAC,QAAA,EAAC,OAAK,CAACnB,SAAS,CAAChC,WAAW,EAAI,CAAC,EACxD,CAAC,cACNvB,KAAA,QAAKyE,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC7B5E,IAAA,SAAM2E,SAAS,CAAC,SAAS,CAAAC,QAAA,CAAC,IAAE,CAAM,CAAC,cACnC1E,KAAA,QAAKyE,SAAS,CAAC,YAAY,CAAAC,QAAA,eACvB1E,KAAA,MAAGyE,SAAS,CAAC,WAAW,CAAAC,QAAA,EAAC,QAAM,CAACnB,SAAS,CAAC4D,SAAS,EAAI,KAAK,EAAI,CAAC,cACjEnH,KAAA,MAAGyE,SAAS,CAAC,WAAW,CAAAC,QAAA,EAAC,QAAM,CAACnB,SAAS,CAAC6D,SAAS,EAAI,KAAK,EAAI,CAAC,EAChE,CAAC,EACL,CAAC,cACNpH,KAAA,QAAKyE,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC1C5E,IAAA,QAAKiF,GAAG,CAAE/B,WAAW,CAACO,SAAS,CAACjC,MAAM,CAAE,CAAC0D,GAAG,CAAEzB,SAAS,CAACjC,MAAO,CAACmD,SAAS,CAAC,iBAAiB,CAAE,CAAC,cAC9F3E,IAAA,OAAI2E,SAAS,CAAC,WAAW,CAAAC,QAAA,CAAEnB,SAAS,CAACjC,MAAM,CAAK,CAAC,cACjDtB,KAAA,MAAGyE,SAAS,CAAC,WAAW,CAAAC,QAAA,EAAC,OAAK,CAACnB,SAAS,CAAC/B,WAAW,EAAI,CAAC,EACxD,CAAC,EACL,CAAC,cACNxB,KAAA,QAAKyE,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC1B1E,KAAA,QAAKyE,SAAS,CAAC,YAAY,CAAAC,QAAA,eACvB1E,KAAA,QAAKyE,SAAS,CAAC,aAAa,CAAAC,QAAA,eACxB5E,IAAA,SAAM2E,SAAS,CAAC,OAAO,CAAAC,QAAA,CAAC,YAAU,CAAM,CAAC,cACzC5E,IAAA,SAAM2E,SAAS,CAAC,OAAO,CAAAC,QAAA,CAAE,GAAI,CAAAV,IAAI,CAACT,SAAS,CAAC9B,UAAU,CAAC,CAACwD,cAAc,CAAC,CAAC,CAAO,CAAC,EAC/E,CAAC,cACNjF,KAAA,QAAKyE,SAAS,CAAC,aAAa,CAAAC,QAAA,eACxB5E,IAAA,SAAM2E,SAAS,CAAC,OAAO,CAAAC,QAAA,CAAC,UAAQ,CAAM,CAAC,cACvC5E,IAAA,SAAM2E,SAAS,CAAC,OAAO,CAAAC,QAAA,CAAE,GAAI,CAAAV,IAAI,CAACT,SAAS,CAAC7B,QAAQ,CAAC,CAACuD,cAAc,CAAC,CAAC,CAAO,CAAC,EAC7E,CAAC,EACL,CAAC,cACNjF,KAAA,QAAKyE,SAAS,CAAC,YAAY,CAAAC,QAAA,eACvB1E,KAAA,QAAKyE,SAAS,CAAC,aAAa,CAAAC,QAAA,eACxB5E,IAAA,SAAM2E,SAAS,CAAC,OAAO,CAAAC,QAAA,CAAC,YAAU,CAAM,CAAC,cACzC5E,IAAA,SAAM2E,SAAS,CAAC,OAAO,CAAAC,QAAA,CAAE,GAAI,CAAAV,IAAI,CAACT,SAAS,CAAC5B,UAAU,CAAC,CAACsD,cAAc,CAAC,CAAC,CAAO,CAAC,EAC/E,CAAC,cACNjF,KAAA,QAAKyE,SAAS,CAAC,aAAa,CAAAC,QAAA,eACxB5E,IAAA,SAAM2E,SAAS,CAAC,OAAO,CAAAC,QAAA,CAAC,QAAM,CAAM,CAAC,cACrC5E,IAAA,SAAM2E,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAEnB,SAAS,CAAC3B,MAAM,CAAO,CAAC,EAC7D,CAAC,EACL,CAAC,EACL,CAAC,EACL,CAAC,CACL,CAAC,EACT,CAED,KAAM,CAAA4E,SAAS,CAAGa,KAAA,MAAC,CAAE9D,SAAS,CAAEgD,OAAO,CAAEE,QAAQ,CAAEC,QAAS,CAAC,CAAAW,KAAA,oBACzDvH,IAAA,QAAK2E,SAAS,CAAC,eAAe,CAAAC,QAAA,cAC1B1E,KAAA,QAAKyE,SAAS,CAAC,YAAY,CAAAC,QAAA,eACvB5E,IAAA,WAAQ2E,SAAS,CAAC,cAAc,CAACwB,OAAO,CAAEM,OAAQ,CAAA7B,QAAA,CAAC,MAAC,CAAQ,CAAC,cAC7D5E,IAAA,OAAA4E,QAAA,CAAI,gBAAc,CAAI,CAAC,cACvB1E,KAAA,QAAKyE,SAAS,CAAC,cAAc,CAAAC,QAAA,eACzB1E,KAAA,QAAKyE,SAAS,CAAC,YAAY,CAAAC,QAAA,eACvB5E,IAAA,UAAA4E,QAAA,CAAO,YAAU,CAAO,CAAC,cACzB1E,KAAA,WACI4G,KAAK,CAAErD,SAAS,CAAC2D,UAAU,EAAI,WAAY,CAC3CR,QAAQ,CAAGY,CAAC,EAAKZ,QAAQ,CAAC,YAAY,CAAEY,CAAC,CAACC,MAAM,CAACX,KAAK,CAAE,CAAAlC,QAAA,eAExD5E,IAAA,WAAQ8G,KAAK,CAAC,WAAW,CAAAlC,QAAA,CAAC,WAAS,CAAQ,CAAC,cAC5C5E,IAAA,WAAQ8G,KAAK,CAAC,WAAW,CAAAlC,QAAA,CAAC,WAAS,CAAQ,CAAC,EACxC,CAAC,EACR,CAAC,cACN1E,KAAA,QAAKyE,SAAS,CAAC,YAAY,CAAAC,QAAA,eACvB5E,IAAA,UAAA4E,QAAA,CAAO,aAAW,CAAO,CAAC,cAC1B5E,IAAA,UACI0H,IAAI,CAAC,QAAQ,CACbC,IAAI,CAAC,MAAM,CACXb,KAAK,CAAErD,SAAS,CAAChC,WAAY,CAC7BmF,QAAQ,CAAGY,CAAC,EAAKZ,QAAQ,CAAC,aAAa,CAAEY,CAAC,CAACC,MAAM,CAACX,KAAK,CAAE,CAC5D,CAAC,EACD,CAAC,cACN5G,KAAA,QAAKyE,SAAS,CAAC,YAAY,CAAAC,QAAA,eACvB5E,IAAA,UAAA4E,QAAA,CAAO,aAAW,CAAO,CAAC,cAC1B5E,IAAA,UACI0H,IAAI,CAAC,QAAQ,CACbC,IAAI,CAAC,MAAM,CACXb,KAAK,CAAErD,SAAS,CAAC/B,WAAY,CAC7BkF,QAAQ,CAAGY,CAAC,EAAKZ,QAAQ,CAAC,aAAa,CAAEY,CAAC,CAACC,MAAM,CAACX,KAAK,CAAE,CAC5D,CAAC,EACD,CAAC,cACN5G,KAAA,QAAKyE,SAAS,CAAC,YAAY,CAAAC,QAAA,eACvB5E,IAAA,UAAA4E,QAAA,CAAO,WAAS,CAAO,CAAC,cACxB5E,IAAA,UACI0H,IAAI,CAAC,QAAQ,CACbC,IAAI,CAAC,MAAM,CACXb,KAAK,CAAErD,SAAS,CAAC4D,SAAS,EAAI,GAAI,CAClCT,QAAQ,CAAGY,CAAC,EAAKZ,QAAQ,CAAC,WAAW,CAAEY,CAAC,CAACC,MAAM,CAACX,KAAK,CAAE,CAC1D,CAAC,EACD,CAAC,cACN5G,KAAA,QAAKyE,SAAS,CAAC,YAAY,CAAAC,QAAA,eACvB5E,IAAA,UAAA4E,QAAA,CAAO,WAAS,CAAO,CAAC,cACxB5E,IAAA,UACI0H,IAAI,CAAC,QAAQ,CACbC,IAAI,CAAC,MAAM,CACXb,KAAK,CAAErD,SAAS,CAAC6D,SAAS,EAAI,GAAI,CAClCV,QAAQ,CAAGY,CAAC,EAAKZ,QAAQ,CAAC,WAAW,CAAEY,CAAC,CAACC,MAAM,CAACX,KAAK,CAAE,CAC1D,CAAC,EACD,CAAC,cACN5G,KAAA,QAAKyE,SAAS,CAAC,YAAY,CAAAC,QAAA,eACvB5E,IAAA,UAAA4E,QAAA,CAAO,QAAM,CAAO,CAAC,cACrB1E,KAAA,WACI4G,KAAK,CAAErD,SAAS,CAAC3B,MAAO,CACxB8E,QAAQ,CAAGY,CAAC,EAAKZ,QAAQ,CAAC,QAAQ,CAAEY,CAAC,CAACC,MAAM,CAACX,KAAK,CAAE,CAAAlC,QAAA,eAEpD5E,IAAA,WAAQ8G,KAAK,CAAC,MAAM,CAAAlC,QAAA,CAAC,MAAI,CAAQ,CAAC,cAClC5E,IAAA,WAAQ8G,KAAK,CAAC,QAAQ,CAAAlC,QAAA,CAAC,QAAM,CAAQ,CAAC,cACtC5E,IAAA,WAAQ8G,KAAK,CAAC,SAAS,CAAAlC,QAAA,CAAC,SAAO,CAAQ,CAAC,EACpC,CAAC,EACR,CAAC,cACN1E,KAAA,QAAKyE,SAAS,CAAC,YAAY,CAAAC,QAAA,eACvB5E,IAAA,UAAA4E,QAAA,CAAO,YAAU,CAAO,CAAC,cACzB5E,IAAA,UACI0H,IAAI,CAAC,gBAAgB,CACrBZ,KAAK,CAAErD,SAAS,CAAC9B,UAAU,CAAC+C,KAAK,CAAC,CAAC,CAAE,EAAE,CAAE,CACzCkC,QAAQ,CAAGY,CAAC,EAAKZ,QAAQ,CAAC,YAAY,CAAEY,CAAC,CAACC,MAAM,CAACX,KAAK,CAAE,CAC3D,CAAC,EACD,CAAC,cACN5G,KAAA,QAAKyE,SAAS,CAAC,YAAY,CAAAC,QAAA,eACvB5E,IAAA,UAAA4E,QAAA,CAAO,UAAQ,CAAO,CAAC,cACvB5E,IAAA,UACI0H,IAAI,CAAC,gBAAgB,CACrBZ,KAAK,CAAErD,SAAS,CAAC7B,QAAQ,CAAC8C,KAAK,CAAC,CAAC,CAAE,EAAE,CAAE,CACvCkC,QAAQ,CAAGY,CAAC,EAAKZ,QAAQ,CAAC,UAAU,CAAEY,CAAC,CAACC,MAAM,CAACX,KAAK,CAAE,CACzD,CAAC,EACD,CAAC,cACN5G,KAAA,QAAKyE,SAAS,CAAC,YAAY,CAAAC,QAAA,eACvB5E,IAAA,UAAA4E,QAAA,CAAO,YAAU,CAAO,CAAC,cACzB5E,IAAA,UACI0H,IAAI,CAAC,gBAAgB,CACrBZ,KAAK,CAAErD,SAAS,CAAC5B,UAAU,CAAC6C,KAAK,CAAC,CAAC,CAAE,EAAE,CAAE,CACzCkC,QAAQ,CAAGY,CAAC,EAAKZ,QAAQ,CAAC,YAAY,CAAEY,CAAC,CAACC,MAAM,CAACX,KAAK,CAAE,CAC3D,CAAC,EACD,CAAC,EACL,CAAC,cACN5G,KAAA,QAAKyE,SAAS,CAAC,cAAc,CAAAC,QAAA,eACzB5E,IAAA,WAAQ2E,SAAS,CAAC,eAAe,CAACwB,OAAO,CAAEM,OAAQ,CAAA7B,QAAA,CAAC,QAAM,CAAQ,CAAC,cACnE5E,IAAA,WAAQ2E,SAAS,CAAC,aAAa,CAACwB,OAAO,CAAEQ,QAAS,CAAA/B,QAAA,CAAC,cAAY,CAAQ,CAAC,EACvE,CAAC,EACL,CAAC,CACL,CAAC,EACT,CAED,KAAM,CAAAmC,uBAAuB,CAAGa,KAAA,MAAC,CAAEnE,SAAS,CAAEuD,SAAS,CAAEC,QAAS,CAAC,CAAAW,KAAA,oBAC/D5H,IAAA,QAAK2E,SAAS,CAAC,eAAe,CAAAC,QAAA,cAC1B1E,KAAA,QAAKyE,SAAS,CAAC,2BAA2B,CAAAC,QAAA,eACtC5E,IAAA,OAAA4E,QAAA,CAAI,kBAAgB,CAAI,CAAC,cACzB1E,KAAA,QAAKyE,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC1B5E,IAAA,MAAA4E,QAAA,CAAG,yDAAuD,CAAG,CAAC,cAC9D1E,KAAA,QAAKyE,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAC9B1E,KAAA,QAAKyE,SAAS,CAAC,OAAO,CAAAC,QAAA,eAClB5E,IAAA,SAAA4E,QAAA,CAAOnB,SAAS,CAAClC,MAAM,CAAO,CAAC,cAC/BvB,IAAA,SAAM2E,SAAS,CAAC,IAAI,CAAAC,QAAA,CAAC,IAAE,CAAM,CAAC,cAC9B5E,IAAA,SAAA4E,QAAA,CAAOnB,SAAS,CAACjC,MAAM,CAAO,CAAC,EAC9B,CAAC,cACNtB,KAAA,QAAKyE,SAAS,CAAC,YAAY,CAAAC,QAAA,EAAC,cACZ,CAAC,GAAI,CAAAV,IAAI,CAACT,SAAS,CAAC5B,UAAU,CAAC,CAACsD,cAAc,CAAC,CAAC,EAC3D,CAAC,EACL,CAAC,EACL,CAAC,cACNjF,KAAA,QAAKyE,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC1B5E,IAAA,WAAQ2E,SAAS,CAAC,eAAe,CAACwB,OAAO,CAAEc,QAAS,CAAArC,QAAA,CAAC,QAAM,CAAQ,CAAC,cACpE5E,IAAA,WAAQ2E,SAAS,CAAC,uBAAuB,CAACwB,OAAO,CAAEa,SAAU,CAAApC,QAAA,CAAC,kBAAgB,CAAQ,CAAC,EACtF,CAAC,EACL,CAAC,CACL,CAAC,EACT,CAED,cAAe,CAAAxE,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}