{"ast": null, "code": "var _jsxFileName = \"C:\\\\MAMP\\\\htdocs\\\\FanBet247\\\\frontend\\\\src\\\\pages\\\\Admin2FASettings.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { FaShieldAlt, FaKey, FaQrcode, FaCopy, FaCheck, FaSpinner, FaExclamationTriangle, FaDownload, FaTrash } from 'react-icons/fa';\nimport { Admin2FASetup, AdminAuthPreferences } from '../components/Admin';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst API_BASE_URL = '/backend';\nconst Admin2FASettings = () => {\n  _s();\n  var _authPreferences$auth;\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  const [adminId, setAdminId] = useState(null);\n  const [adminData, setAdminData] = useState(null);\n  const [authPreferences, setAuthPreferences] = useState(null);\n  const [tfaSetup, setTfaSetup] = useState(null);\n  const [backupCodes, setBackupCodes] = useState([]);\n  const [showSetup, setShowSetup] = useState(false);\n  const [globalSettings, setGlobalSettings] = useState({});\n  useEffect(() => {\n    // Get admin ID from localStorage\n    const storedAdminId = localStorage.getItem('adminId');\n    if (storedAdminId) {\n      setAdminId(storedAdminId);\n      fetchAdminData(storedAdminId);\n    } else {\n      setError('Admin session not found. Please log in again.');\n      setLoading(false);\n    }\n  }, []);\n  const fetchAdminData = async id => {\n    try {\n      setLoading(true);\n      setError('');\n\n      // Get admin auth preferences\n      const response = await axios.get(`${API_BASE_URL}/handlers/admin_auth_preferences.php?adminId=${id}`);\n      if (response.data.success) {\n        setAdminData(response.data.admin);\n        setAuthPreferences(response.data.auth_settings);\n        setTfaSetup(response.data.two_factor_setup);\n        setGlobalSettings(response.data.global_settings);\n\n        // Get backup codes if 2FA is set up\n        if (response.data.two_factor_setup && response.data.two_factor_setup.setup_completed) {\n          fetchBackupCodes(id);\n        }\n      } else {\n        setError(response.data.message || 'Failed to load admin data');\n      }\n    } catch (err) {\n      setError('Failed to load admin data');\n      console.error('Error fetching admin data:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const fetchBackupCodes = async id => {\n    try {\n      const response = await axios.get(`${API_BASE_URL}/handlers/admin_backup_codes.php?adminId=${id}`);\n      if (response.data.success) {\n        setBackupCodes(response.data.backup_codes || []);\n      }\n    } catch (err) {\n      console.error('Error fetching backup codes:', err);\n    }\n  };\n  const handleSetupComplete = setupData => {\n    setShowSetup(false);\n    setSuccess('2FA has been set up successfully!');\n\n    // Refresh admin data\n    fetchAdminData(adminId);\n    setTimeout(() => setSuccess(''), 5000);\n  };\n  const handleDisable2FA = async () => {\n    if (!window.confirm('Are you sure you want to disable 2FA? This will reduce your account security.')) {\n      return;\n    }\n    const currentPassword = window.prompt('Please enter your current password to confirm:');\n    if (!currentPassword) {\n      return;\n    }\n    try {\n      setLoading(true);\n      const response = await axios.post(`${API_BASE_URL}/handlers/admin_disable_2fa.php`, {\n        admin_id: adminId,\n        current_password: currentPassword\n      });\n      if (response.data.success) {\n        setSuccess('2FA has been disabled successfully');\n        fetchAdminData(adminId);\n      } else {\n        setError(response.data.message || 'Failed to disable 2FA');\n      }\n    } catch (err) {\n      setError('Failed to disable 2FA');\n      console.error('Error disabling 2FA:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleRegenerateBackupCodes = async () => {\n    if (!window.confirm('Are you sure you want to regenerate backup codes? Your current codes will no longer work.')) {\n      return;\n    }\n    try {\n      setLoading(true);\n      const response = await axios.post(`${API_BASE_URL}/handlers/admin_backup_codes.php?adminId=${adminId}`, {\n        require_verification: false\n      });\n      if (response.data.success) {\n        setBackupCodes(response.data.backup_codes);\n        setSuccess('Backup codes regenerated successfully');\n      } else {\n        setError(response.data.message || 'Failed to regenerate backup codes');\n      }\n    } catch (err) {\n      setError('Failed to regenerate backup codes');\n      console.error('Error regenerating backup codes:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const downloadBackupCodes = () => {\n    const content = `FanBet247 Admin 2FA Backup Codes\\nGenerated: ${new Date().toLocaleString()}\\nAdmin: ${adminData === null || adminData === void 0 ? void 0 : adminData.username}\\n\\n${backupCodes.join('\\n')}\\n\\nKeep these codes safe and secure. Each code can only be used once.`;\n    const blob = new Blob([content], {\n      type: 'text/plain'\n    });\n    const url = URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = `fanbet247-backup-codes-${adminData === null || adminData === void 0 ? void 0 : adminData.username}.txt`;\n    document.body.appendChild(a);\n    a.click();\n    document.body.removeChild(a);\n    URL.revokeObjectURL(url);\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(FaSpinner, {\n          className: \"animate-spin text-4xl text-blue-600 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Loading 2FA settings...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 154,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 153,\n      columnNumber: 13\n    }, this);\n  }\n  if (showSetup) {\n    return /*#__PURE__*/_jsxDEV(Admin2FASetup, {\n      adminId: adminId,\n      username: adminData === null || adminData === void 0 ? void 0 : adminData.username,\n      onSuccess: handleSetupComplete,\n      onBack: () => setShowSetup(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 164,\n      columnNumber: 13\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50 py-8\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm border p-6 mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-3 mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(FaShieldAlt, {\n            className: \"text-green-500 text-2xl\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-2xl font-bold text-gray-800\",\n              children: \"Two-Factor Authentication\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: \"Secure your admin account with Google Authenticator\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gray-50 rounded-lg p-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-gray-600\",\n                children: \"Admin:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 190,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"ml-2 font-medium\",\n                children: adminData === null || adminData === void 0 ? void 0 : adminData.username\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-gray-600\",\n                children: \"Email:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 194,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"ml-2 font-medium\",\n                children: adminData === null || adminData === void 0 ? void 0 : adminData.email\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 195,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-gray-600\",\n                children: \"Current Method:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 198,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"ml-2 font-medium capitalize\",\n                children: (authPreferences === null || authPreferences === void 0 ? void 0 : (_authPreferences$auth = authPreferences.auth_method) === null || _authPreferences$auth === void 0 ? void 0 : _authPreferences$auth.replace('_', ' ')) || 'Password Only'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 199,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 17\n      }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-6 bg-red-50 border border-red-200 rounded-lg p-4 flex items-center gap-3\",\n        children: [/*#__PURE__*/_jsxDEV(FaExclamationTriangle, {\n          className: \"text-red-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-red-700\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 209,\n        columnNumber: 21\n      }, this), success && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-6 bg-green-50 border border-green-200 rounded-lg p-4 flex items-center gap-3\",\n        children: [/*#__PURE__*/_jsxDEV(FaCheck, {\n          className: \"text-green-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-green-700\",\n          children: success\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 218,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 216,\n        columnNumber: 21\n      }, this), globalSettings.admin_2fa_enabled !== 'true' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-6 bg-yellow-50 border border-yellow-200 rounded-lg p-4 flex items-center gap-3\",\n        children: [/*#__PURE__*/_jsxDEV(FaExclamationTriangle, {\n          className: \"text-yellow-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 225,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-yellow-800 font-medium\",\n            children: \"2FA Not Enabled Globally\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-yellow-700 text-sm mt-1\",\n            children: \"2FA is not enabled globally. Contact your system administrator to enable 2FA.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 228,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 224,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-6\",\n        children: /*#__PURE__*/_jsxDEV(AdminAuthPreferences, {\n          adminId: adminId,\n          onUpdate: preferences => {\n            setAuthPreferences(preferences);\n            if (preferences.auth_method === '2fa' && !(tfaSetup !== null && tfaSetup !== void 0 && tfaSetup.setup_completed)) {\n              setShowSetup(true);\n            }\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 236,\n        columnNumber: 17\n      }, this), globalSettings.admin_2fa_enabled === 'true' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm border p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2\",\n          children: [/*#__PURE__*/_jsxDEV(FaKey, {\n            className: \"text-blue-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 29\n          }, this), \"2FA Configuration\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 251,\n          columnNumber: 25\n        }, this), !(tfaSetup !== null && tfaSetup !== void 0 && tfaSetup.setup_completed) ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center py-8\",\n          children: [/*#__PURE__*/_jsxDEV(FaQrcode, {\n            className: \"text-6xl text-gray-300 mx-auto mb-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 258,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-medium text-gray-800 mb-2\",\n            children: \"2FA Not Set Up\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 259,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600 mb-6\",\n            children: \"Set up Google Authenticator to secure your account with 2FA\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowSetup(true),\n            className: \"px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 flex items-center gap-2 mx-auto\",\n            children: [/*#__PURE__*/_jsxDEV(FaShieldAlt, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 267,\n              columnNumber: 37\n            }, this), \"Set Up 2FA\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 29\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-green-50 border border-green-200 rounded-lg p-4\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center gap-3\",\n              children: [/*#__PURE__*/_jsxDEV(FaCheck, {\n                className: \"text-green-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 276,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-green-800 font-medium\",\n                  children: \"2FA is Active\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 278,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-green-700 text-sm\",\n                  children: \"Your account is protected with Google Authenticator\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 279,\n                  columnNumber: 45\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 277,\n                columnNumber: 41\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 275,\n              columnNumber: 37\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 274,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-md font-medium text-gray-800 mb-3\",\n              children: \"Backup Codes\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 288,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-600 mb-4\",\n              children: \"Use these codes if you lose access to your authenticator app. Each code can only be used once.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 289,\n              columnNumber: 37\n            }, this), backupCodes.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gray-50 rounded-lg p-4 mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-2 gap-2 font-mono text-sm mb-4\",\n                children: backupCodes.map((code, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-white p-2 rounded border text-center\",\n                  children: code\n                }, index, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 297,\n                  columnNumber: 53\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 295,\n                columnNumber: 45\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex gap-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: downloadBackupCodes,\n                  className: \"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700\",\n                  children: [/*#__PURE__*/_jsxDEV(FaDownload, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 308,\n                    columnNumber: 53\n                  }, this), \"Download Codes\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 304,\n                  columnNumber: 49\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleRegenerateBackupCodes,\n                  className: \"flex items-center gap-2 px-4 py-2 bg-yellow-600 text-white rounded hover:bg-yellow-700\",\n                  children: [/*#__PURE__*/_jsxDEV(FaKey, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 315,\n                    columnNumber: 53\n                  }, this), \"Regenerate Codes\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 311,\n                  columnNumber: 49\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 303,\n                columnNumber: 45\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 294,\n              columnNumber: 41\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-yellow-800\",\n                children: \"No backup codes available\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 322,\n                columnNumber: 45\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: handleRegenerateBackupCodes,\n                className: \"mt-2 px-4 py-2 bg-yellow-600 text-white rounded hover:bg-yellow-700\",\n                children: \"Generate Backup Codes\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 323,\n                columnNumber: 45\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 321,\n              columnNumber: 41\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 287,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"border-t pt-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-md font-medium text-gray-800 mb-3 text-red-600\",\n              children: \"Danger Zone\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 335,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-red-50 border border-red-200 rounded-lg p-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-red-800 text-sm mb-3\",\n                children: \"Disabling 2FA will reduce your account security. Only disable if absolutely necessary.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 337,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: handleDisable2FA,\n                className: \"flex items-center gap-2 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700\",\n                children: [/*#__PURE__*/_jsxDEV(FaTrash, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 344,\n                  columnNumber: 45\n                }, this), \"Disable 2FA\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 340,\n                columnNumber: 41\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 336,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 334,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 29\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 250,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 175,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 174,\n    columnNumber: 9\n  }, this);\n};\n_s(Admin2FASettings, \"P8TmBKTm4xD4XYCmK0LGhSn8UTk=\");\n_c = Admin2FASettings;\nexport default Admin2FASettings;\nvar _c;\n$RefreshReg$(_c, \"Admin2FASettings\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "FaShieldAlt", "FaKey", "FaQrcode", "FaCopy", "FaCheck", "FaSpinner", "FaExclamationTriangle", "FaDownload", "FaTrash", "Admin2FASetup", "AdminAuthPreferences", "jsxDEV", "_jsxDEV", "API_BASE_URL", "Admin2FASettings", "_s", "_authPreferences$auth", "loading", "setLoading", "error", "setError", "success", "setSuccess", "adminId", "setAdminId", "adminData", "setAdminData", "authPreferences", "setAuthPreferences", "tfaSetup", "setTfaSetup", "backupCodes", "setBackupCodes", "showSetup", "setShowSetup", "globalSettings", "setGlobalSettings", "storedAdminId", "localStorage", "getItem", "fetchAdminData", "id", "response", "get", "data", "admin", "auth_settings", "two_factor_setup", "global_settings", "setup_completed", "fetchBackupCodes", "message", "err", "console", "backup_codes", "handleSetupComplete", "setupData", "setTimeout", "handleDisable2FA", "window", "confirm", "currentPassword", "prompt", "post", "admin_id", "current_password", "handleRegenerateBackupCodes", "require_verification", "downloadBackupCodes", "content", "Date", "toLocaleString", "username", "join", "blob", "Blob", "type", "url", "URL", "createObjectURL", "a", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSuccess", "onBack", "email", "auth_method", "replace", "admin_2fa_enabled", "onUpdate", "preferences", "onClick", "length", "map", "code", "index", "_c", "$RefreshReg$"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/pages/Admin2FASettings.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { FaShieldAlt, FaKey, FaQrcode, FaCopy, FaCheck, FaSpinner, FaExclamationTriangle, FaDownload, FaTrash } from 'react-icons/fa';\nimport { Admin2FASetup, AdminAuthPreferences } from '../components/Admin';\n\nconst API_BASE_URL = '/backend';\n\nconst Admin2FASettings = () => {\n    const [loading, setLoading] = useState(true);\n    const [error, setError] = useState('');\n    const [success, setSuccess] = useState('');\n    const [adminId, setAdminId] = useState(null);\n    const [adminData, setAdminData] = useState(null);\n    const [authPreferences, setAuthPreferences] = useState(null);\n    const [tfaSetup, setTfaSetup] = useState(null);\n    const [backupCodes, setBackupCodes] = useState([]);\n    const [showSetup, setShowSetup] = useState(false);\n    const [globalSettings, setGlobalSettings] = useState({});\n\n    useEffect(() => {\n        // Get admin ID from localStorage\n        const storedAdminId = localStorage.getItem('adminId');\n        if (storedAdminId) {\n            setAdminId(storedAdminId);\n            fetchAdminData(storedAdminId);\n        } else {\n            setError('Admin session not found. Please log in again.');\n            setLoading(false);\n        }\n    }, []);\n\n    const fetchAdminData = async (id) => {\n        try {\n            setLoading(true);\n            setError('');\n\n            // Get admin auth preferences\n            const response = await axios.get(`${API_BASE_URL}/handlers/admin_auth_preferences.php?adminId=${id}`);\n\n            if (response.data.success) {\n                setAdminData(response.data.admin);\n                setAuthPreferences(response.data.auth_settings);\n                setTfaSetup(response.data.two_factor_setup);\n                setGlobalSettings(response.data.global_settings);\n\n                // Get backup codes if 2FA is set up\n                if (response.data.two_factor_setup && response.data.two_factor_setup.setup_completed) {\n                    fetchBackupCodes(id);\n                }\n            } else {\n                setError(response.data.message || 'Failed to load admin data');\n            }\n        } catch (err) {\n            setError('Failed to load admin data');\n            console.error('Error fetching admin data:', err);\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const fetchBackupCodes = async (id) => {\n        try {\n            const response = await axios.get(`${API_BASE_URL}/handlers/admin_backup_codes.php?adminId=${id}`);\n            if (response.data.success) {\n                setBackupCodes(response.data.backup_codes || []);\n            }\n        } catch (err) {\n            console.error('Error fetching backup codes:', err);\n        }\n    };\n\n    const handleSetupComplete = (setupData) => {\n        setShowSetup(false);\n        setSuccess('2FA has been set up successfully!');\n        \n        // Refresh admin data\n        fetchAdminData(adminId);\n        \n        setTimeout(() => setSuccess(''), 5000);\n    };\n\n    const handleDisable2FA = async () => {\n        if (!window.confirm('Are you sure you want to disable 2FA? This will reduce your account security.')) {\n            return;\n        }\n\n        const currentPassword = window.prompt('Please enter your current password to confirm:');\n        if (!currentPassword) {\n            return;\n        }\n\n        try {\n            setLoading(true);\n            const response = await axios.post(`${API_BASE_URL}/handlers/admin_disable_2fa.php`, {\n                admin_id: adminId,\n                current_password: currentPassword\n            });\n\n            if (response.data.success) {\n                setSuccess('2FA has been disabled successfully');\n                fetchAdminData(adminId);\n            } else {\n                setError(response.data.message || 'Failed to disable 2FA');\n            }\n        } catch (err) {\n            setError('Failed to disable 2FA');\n            console.error('Error disabling 2FA:', err);\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const handleRegenerateBackupCodes = async () => {\n        if (!window.confirm('Are you sure you want to regenerate backup codes? Your current codes will no longer work.')) {\n            return;\n        }\n\n        try {\n            setLoading(true);\n            const response = await axios.post(`${API_BASE_URL}/handlers/admin_backup_codes.php?adminId=${adminId}`, {\n                require_verification: false\n            });\n\n            if (response.data.success) {\n                setBackupCodes(response.data.backup_codes);\n                setSuccess('Backup codes regenerated successfully');\n            } else {\n                setError(response.data.message || 'Failed to regenerate backup codes');\n            }\n        } catch (err) {\n            setError('Failed to regenerate backup codes');\n            console.error('Error regenerating backup codes:', err);\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const downloadBackupCodes = () => {\n        const content = `FanBet247 Admin 2FA Backup Codes\\nGenerated: ${new Date().toLocaleString()}\\nAdmin: ${adminData?.username}\\n\\n${backupCodes.join('\\n')}\\n\\nKeep these codes safe and secure. Each code can only be used once.`;\n        const blob = new Blob([content], { type: 'text/plain' });\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement('a');\n        a.href = url;\n        a.download = `fanbet247-backup-codes-${adminData?.username}.txt`;\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n        URL.revokeObjectURL(url);\n    };\n\n    if (loading) {\n        return (\n            <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n                <div className=\"text-center\">\n                    <FaSpinner className=\"animate-spin text-4xl text-blue-600 mx-auto mb-4\" />\n                    <p className=\"text-gray-600\">Loading 2FA settings...</p>\n                </div>\n            </div>\n        );\n    }\n\n    if (showSetup) {\n        return (\n            <Admin2FASetup\n                adminId={adminId}\n                username={adminData?.username}\n                onSuccess={handleSetupComplete}\n                onBack={() => setShowSetup(false)}\n            />\n        );\n    }\n\n    return (\n        <div className=\"min-h-screen bg-gray-50 py-8\">\n            <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\">\n                {/* Header */}\n                <div className=\"bg-white rounded-lg shadow-sm border p-6 mb-6\">\n                    <div className=\"flex items-center gap-3 mb-4\">\n                        <FaShieldAlt className=\"text-green-500 text-2xl\" />\n                        <div>\n                            <h1 className=\"text-2xl font-bold text-gray-800\">Two-Factor Authentication</h1>\n                            <p className=\"text-gray-600\">Secure your admin account with Google Authenticator</p>\n                        </div>\n                    </div>\n\n                    {/* Admin Info */}\n                    <div className=\"bg-gray-50 rounded-lg p-4\">\n                        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm\">\n                            <div>\n                                <span className=\"text-gray-600\">Admin:</span>\n                                <span className=\"ml-2 font-medium\">{adminData?.username}</span>\n                            </div>\n                            <div>\n                                <span className=\"text-gray-600\">Email:</span>\n                                <span className=\"ml-2 font-medium\">{adminData?.email}</span>\n                            </div>\n                            <div>\n                                <span className=\"text-gray-600\">Current Method:</span>\n                                <span className=\"ml-2 font-medium capitalize\">\n                                    {authPreferences?.auth_method?.replace('_', ' ') || 'Password Only'}\n                                </span>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n\n                {/* Error/Success Messages */}\n                {error && (\n                    <div className=\"mb-6 bg-red-50 border border-red-200 rounded-lg p-4 flex items-center gap-3\">\n                        <FaExclamationTriangle className=\"text-red-500\" />\n                        <span className=\"text-red-700\">{error}</span>\n                    </div>\n                )}\n\n                {success && (\n                    <div className=\"mb-6 bg-green-50 border border-green-200 rounded-lg p-4 flex items-center gap-3\">\n                        <FaCheck className=\"text-green-500\" />\n                        <span className=\"text-green-700\">{success}</span>\n                    </div>\n                )}\n\n                {/* Global 2FA Status */}\n                {globalSettings.admin_2fa_enabled !== 'true' && (\n                    <div className=\"mb-6 bg-yellow-50 border border-yellow-200 rounded-lg p-4 flex items-center gap-3\">\n                        <FaExclamationTriangle className=\"text-yellow-500\" />\n                        <div>\n                            <p className=\"text-yellow-800 font-medium\">2FA Not Enabled Globally</p>\n                            <p className=\"text-yellow-700 text-sm mt-1\">\n                                2FA is not enabled globally. Contact your system administrator to enable 2FA.\n                            </p>\n                        </div>\n                    </div>\n                )}\n\n                {/* Authentication Preferences */}\n                <div className=\"mb-6\">\n                    <AdminAuthPreferences\n                        adminId={adminId}\n                        onUpdate={(preferences) => {\n                            setAuthPreferences(preferences);\n                            if (preferences.auth_method === '2fa' && !tfaSetup?.setup_completed) {\n                                setShowSetup(true);\n                            }\n                        }}\n                    />\n                </div>\n\n                {/* 2FA Setup Section */}\n                {globalSettings.admin_2fa_enabled === 'true' && (\n                    <div className=\"bg-white rounded-lg shadow-sm border p-6\">\n                        <h2 className=\"text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2\">\n                            <FaKey className=\"text-blue-500\" />\n                            2FA Configuration\n                        </h2>\n\n                        {!tfaSetup?.setup_completed ? (\n                            <div className=\"text-center py-8\">\n                                <FaQrcode className=\"text-6xl text-gray-300 mx-auto mb-4\" />\n                                <h3 className=\"text-lg font-medium text-gray-800 mb-2\">2FA Not Set Up</h3>\n                                <p className=\"text-gray-600 mb-6\">\n                                    Set up Google Authenticator to secure your account with 2FA\n                                </p>\n                                <button\n                                    onClick={() => setShowSetup(true)}\n                                    className=\"px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 flex items-center gap-2 mx-auto\"\n                                >\n                                    <FaShieldAlt />\n                                    Set Up 2FA\n                                </button>\n                            </div>\n                        ) : (\n                            <div className=\"space-y-6\">\n                                {/* 2FA Status */}\n                                <div className=\"bg-green-50 border border-green-200 rounded-lg p-4\">\n                                    <div className=\"flex items-center gap-3\">\n                                        <FaCheck className=\"text-green-500\" />\n                                        <div>\n                                            <p className=\"text-green-800 font-medium\">2FA is Active</p>\n                                            <p className=\"text-green-700 text-sm\">\n                                                Your account is protected with Google Authenticator\n                                            </p>\n                                        </div>\n                                    </div>\n                                </div>\n\n                                {/* Backup Codes */}\n                                <div>\n                                    <h3 className=\"text-md font-medium text-gray-800 mb-3\">Backup Codes</h3>\n                                    <p className=\"text-sm text-gray-600 mb-4\">\n                                        Use these codes if you lose access to your authenticator app. Each code can only be used once.\n                                    </p>\n\n                                    {backupCodes.length > 0 ? (\n                                        <div className=\"bg-gray-50 rounded-lg p-4 mb-4\">\n                                            <div className=\"grid grid-cols-2 gap-2 font-mono text-sm mb-4\">\n                                                {backupCodes.map((code, index) => (\n                                                    <div key={index} className=\"bg-white p-2 rounded border text-center\">\n                                                        {code}\n                                                    </div>\n                                                ))}\n                                            </div>\n                                            \n                                            <div className=\"flex gap-3\">\n                                                <button\n                                                    onClick={downloadBackupCodes}\n                                                    className=\"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700\"\n                                                >\n                                                    <FaDownload />\n                                                    Download Codes\n                                                </button>\n                                                <button\n                                                    onClick={handleRegenerateBackupCodes}\n                                                    className=\"flex items-center gap-2 px-4 py-2 bg-yellow-600 text-white rounded hover:bg-yellow-700\"\n                                                >\n                                                    <FaKey />\n                                                    Regenerate Codes\n                                                </button>\n                                            </div>\n                                        </div>\n                                    ) : (\n                                        <div className=\"bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4\">\n                                            <p className=\"text-yellow-800\">No backup codes available</p>\n                                            <button\n                                                onClick={handleRegenerateBackupCodes}\n                                                className=\"mt-2 px-4 py-2 bg-yellow-600 text-white rounded hover:bg-yellow-700\"\n                                            >\n                                                Generate Backup Codes\n                                            </button>\n                                        </div>\n                                    )}\n                                </div>\n\n                                {/* Disable 2FA */}\n                                <div className=\"border-t pt-6\">\n                                    <h3 className=\"text-md font-medium text-gray-800 mb-3 text-red-600\">Danger Zone</h3>\n                                    <div className=\"bg-red-50 border border-red-200 rounded-lg p-4\">\n                                        <p className=\"text-red-800 text-sm mb-3\">\n                                            Disabling 2FA will reduce your account security. Only disable if absolutely necessary.\n                                        </p>\n                                        <button\n                                            onClick={handleDisable2FA}\n                                            className=\"flex items-center gap-2 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700\"\n                                        >\n                                            <FaTrash />\n                                            Disable 2FA\n                                        </button>\n                                    </div>\n                                </div>\n                            </div>\n                        )}\n                    </div>\n                )}\n            </div>\n        </div>\n    );\n};\n\nexport default Admin2FASettings;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,OAAO,EAAEC,SAAS,EAAEC,qBAAqB,EAAEC,UAAU,EAAEC,OAAO,QAAQ,gBAAgB;AACrI,SAASC,aAAa,EAAEC,oBAAoB,QAAQ,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1E,MAAMC,YAAY,GAAG,UAAU;AAE/B,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA;EAC3B,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACsB,KAAK,EAAEC,QAAQ,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACwB,OAAO,EAAEC,UAAU,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC0B,OAAO,EAAEC,UAAU,CAAC,GAAG3B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC4B,SAAS,EAAEC,YAAY,CAAC,GAAG7B,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAAC8B,eAAe,EAAEC,kBAAkB,CAAC,GAAG/B,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACgC,QAAQ,EAAEC,WAAW,CAAC,GAAGjC,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACkC,WAAW,EAAEC,cAAc,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACoC,SAAS,EAAEC,YAAY,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACsC,cAAc,EAAEC,iBAAiB,CAAC,GAAGvC,QAAQ,CAAC,CAAC,CAAC,CAAC;EAExDC,SAAS,CAAC,MAAM;IACZ;IACA,MAAMuC,aAAa,GAAGC,YAAY,CAACC,OAAO,CAAC,SAAS,CAAC;IACrD,IAAIF,aAAa,EAAE;MACfb,UAAU,CAACa,aAAa,CAAC;MACzBG,cAAc,CAACH,aAAa,CAAC;IACjC,CAAC,MAAM;MACHjB,QAAQ,CAAC,+CAA+C,CAAC;MACzDF,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMsB,cAAc,GAAG,MAAOC,EAAE,IAAK;IACjC,IAAI;MACAvB,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,EAAE,CAAC;;MAEZ;MACA,MAAMsB,QAAQ,GAAG,MAAM3C,KAAK,CAAC4C,GAAG,CAAC,GAAG9B,YAAY,gDAAgD4B,EAAE,EAAE,CAAC;MAErG,IAAIC,QAAQ,CAACE,IAAI,CAACvB,OAAO,EAAE;QACvBK,YAAY,CAACgB,QAAQ,CAACE,IAAI,CAACC,KAAK,CAAC;QACjCjB,kBAAkB,CAACc,QAAQ,CAACE,IAAI,CAACE,aAAa,CAAC;QAC/ChB,WAAW,CAACY,QAAQ,CAACE,IAAI,CAACG,gBAAgB,CAAC;QAC3CX,iBAAiB,CAACM,QAAQ,CAACE,IAAI,CAACI,eAAe,CAAC;;QAEhD;QACA,IAAIN,QAAQ,CAACE,IAAI,CAACG,gBAAgB,IAAIL,QAAQ,CAACE,IAAI,CAACG,gBAAgB,CAACE,eAAe,EAAE;UAClFC,gBAAgB,CAACT,EAAE,CAAC;QACxB;MACJ,CAAC,MAAM;QACHrB,QAAQ,CAACsB,QAAQ,CAACE,IAAI,CAACO,OAAO,IAAI,2BAA2B,CAAC;MAClE;IACJ,CAAC,CAAC,OAAOC,GAAG,EAAE;MACVhC,QAAQ,CAAC,2BAA2B,CAAC;MACrCiC,OAAO,CAAClC,KAAK,CAAC,4BAA4B,EAAEiC,GAAG,CAAC;IACpD,CAAC,SAAS;MACNlC,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,MAAMgC,gBAAgB,GAAG,MAAOT,EAAE,IAAK;IACnC,IAAI;MACA,MAAMC,QAAQ,GAAG,MAAM3C,KAAK,CAAC4C,GAAG,CAAC,GAAG9B,YAAY,4CAA4C4B,EAAE,EAAE,CAAC;MACjG,IAAIC,QAAQ,CAACE,IAAI,CAACvB,OAAO,EAAE;QACvBW,cAAc,CAACU,QAAQ,CAACE,IAAI,CAACU,YAAY,IAAI,EAAE,CAAC;MACpD;IACJ,CAAC,CAAC,OAAOF,GAAG,EAAE;MACVC,OAAO,CAAClC,KAAK,CAAC,8BAA8B,EAAEiC,GAAG,CAAC;IACtD;EACJ,CAAC;EAED,MAAMG,mBAAmB,GAAIC,SAAS,IAAK;IACvCtB,YAAY,CAAC,KAAK,CAAC;IACnBZ,UAAU,CAAC,mCAAmC,CAAC;;IAE/C;IACAkB,cAAc,CAACjB,OAAO,CAAC;IAEvBkC,UAAU,CAAC,MAAMnC,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;EAC1C,CAAC;EAED,MAAMoC,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI,CAACC,MAAM,CAACC,OAAO,CAAC,+EAA+E,CAAC,EAAE;MAClG;IACJ;IAEA,MAAMC,eAAe,GAAGF,MAAM,CAACG,MAAM,CAAC,gDAAgD,CAAC;IACvF,IAAI,CAACD,eAAe,EAAE;MAClB;IACJ;IAEA,IAAI;MACA3C,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMwB,QAAQ,GAAG,MAAM3C,KAAK,CAACgE,IAAI,CAAC,GAAGlD,YAAY,iCAAiC,EAAE;QAChFmD,QAAQ,EAAEzC,OAAO;QACjB0C,gBAAgB,EAAEJ;MACtB,CAAC,CAAC;MAEF,IAAInB,QAAQ,CAACE,IAAI,CAACvB,OAAO,EAAE;QACvBC,UAAU,CAAC,oCAAoC,CAAC;QAChDkB,cAAc,CAACjB,OAAO,CAAC;MAC3B,CAAC,MAAM;QACHH,QAAQ,CAACsB,QAAQ,CAACE,IAAI,CAACO,OAAO,IAAI,uBAAuB,CAAC;MAC9D;IACJ,CAAC,CAAC,OAAOC,GAAG,EAAE;MACVhC,QAAQ,CAAC,uBAAuB,CAAC;MACjCiC,OAAO,CAAClC,KAAK,CAAC,sBAAsB,EAAEiC,GAAG,CAAC;IAC9C,CAAC,SAAS;MACNlC,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,MAAMgD,2BAA2B,GAAG,MAAAA,CAAA,KAAY;IAC5C,IAAI,CAACP,MAAM,CAACC,OAAO,CAAC,2FAA2F,CAAC,EAAE;MAC9G;IACJ;IAEA,IAAI;MACA1C,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMwB,QAAQ,GAAG,MAAM3C,KAAK,CAACgE,IAAI,CAAC,GAAGlD,YAAY,4CAA4CU,OAAO,EAAE,EAAE;QACpG4C,oBAAoB,EAAE;MAC1B,CAAC,CAAC;MAEF,IAAIzB,QAAQ,CAACE,IAAI,CAACvB,OAAO,EAAE;QACvBW,cAAc,CAACU,QAAQ,CAACE,IAAI,CAACU,YAAY,CAAC;QAC1ChC,UAAU,CAAC,uCAAuC,CAAC;MACvD,CAAC,MAAM;QACHF,QAAQ,CAACsB,QAAQ,CAACE,IAAI,CAACO,OAAO,IAAI,mCAAmC,CAAC;MAC1E;IACJ,CAAC,CAAC,OAAOC,GAAG,EAAE;MACVhC,QAAQ,CAAC,mCAAmC,CAAC;MAC7CiC,OAAO,CAAClC,KAAK,CAAC,kCAAkC,EAAEiC,GAAG,CAAC;IAC1D,CAAC,SAAS;MACNlC,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,MAAMkD,mBAAmB,GAAGA,CAAA,KAAM;IAC9B,MAAMC,OAAO,GAAG,gDAAgD,IAAIC,IAAI,CAAC,CAAC,CAACC,cAAc,CAAC,CAAC,YAAY9C,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAE+C,QAAQ,OAAOzC,WAAW,CAAC0C,IAAI,CAAC,IAAI,CAAC,wEAAwE;IAC/N,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACN,OAAO,CAAC,EAAE;MAAEO,IAAI,EAAE;IAAa,CAAC,CAAC;IACxD,MAAMC,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACL,IAAI,CAAC;IACrC,MAAMM,CAAC,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACrCF,CAAC,CAACG,IAAI,GAAGN,GAAG;IACZG,CAAC,CAACI,QAAQ,GAAG,0BAA0B3D,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAE+C,QAAQ,MAAM;IAChES,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,CAAC,CAAC;IAC5BA,CAAC,CAACO,KAAK,CAAC,CAAC;IACTN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,CAAC,CAAC;IAC5BF,GAAG,CAACW,eAAe,CAACZ,GAAG,CAAC;EAC5B,CAAC;EAED,IAAI5D,OAAO,EAAE;IACT,oBACIL,OAAA;MAAK8E,SAAS,EAAC,0DAA0D;MAAAC,QAAA,eACrE/E,OAAA;QAAK8E,SAAS,EAAC,aAAa;QAAAC,QAAA,gBACxB/E,OAAA,CAACP,SAAS;UAACqF,SAAS,EAAC;QAAkD;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1EnF,OAAA;UAAG8E,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAuB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd;EAEA,IAAI9D,SAAS,EAAE;IACX,oBACIrB,OAAA,CAACH,aAAa;MACVc,OAAO,EAAEA,OAAQ;MACjBiD,QAAQ,EAAE/C,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAE+C,QAAS;MAC9BwB,SAAS,EAAEzC,mBAAoB;MAC/B0C,MAAM,EAAEA,CAAA,KAAM/D,YAAY,CAAC,KAAK;IAAE;MAAA0D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrC,CAAC;EAEV;EAEA,oBACInF,OAAA;IAAK8E,SAAS,EAAC,8BAA8B;IAAAC,QAAA,eACzC/E,OAAA;MAAK8E,SAAS,EAAC,wCAAwC;MAAAC,QAAA,gBAEnD/E,OAAA;QAAK8E,SAAS,EAAC,+CAA+C;QAAAC,QAAA,gBAC1D/E,OAAA;UAAK8E,SAAS,EAAC,8BAA8B;UAAAC,QAAA,gBACzC/E,OAAA,CAACZ,WAAW;YAAC0F,SAAS,EAAC;UAAyB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACnDnF,OAAA;YAAA+E,QAAA,gBACI/E,OAAA;cAAI8E,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAC;YAAyB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/EnF,OAAA;cAAG8E,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAmD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAGNnF,OAAA;UAAK8E,SAAS,EAAC,2BAA2B;UAAAC,QAAA,eACtC/E,OAAA;YAAK8E,SAAS,EAAC,+CAA+C;YAAAC,QAAA,gBAC1D/E,OAAA;cAAA+E,QAAA,gBACI/E,OAAA;gBAAM8E,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC7CnF,OAAA;gBAAM8E,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,EAAElE,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAE+C;cAAQ;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9D,CAAC,eACNnF,OAAA;cAAA+E,QAAA,gBACI/E,OAAA;gBAAM8E,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC7CnF,OAAA;gBAAM8E,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,EAAElE,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEyE;cAAK;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3D,CAAC,eACNnF,OAAA;cAAA+E,QAAA,gBACI/E,OAAA;gBAAM8E,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACtDnF,OAAA;gBAAM8E,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,EACxC,CAAAhE,eAAe,aAAfA,eAAe,wBAAAX,qBAAA,GAAfW,eAAe,CAAEwE,WAAW,cAAAnF,qBAAA,uBAA5BA,qBAAA,CAA8BoF,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,KAAI;cAAe;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,EAGL5E,KAAK,iBACFP,OAAA;QAAK8E,SAAS,EAAC,6EAA6E;QAAAC,QAAA,gBACxF/E,OAAA,CAACN,qBAAqB;UAACoF,SAAS,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAClDnF,OAAA;UAAM8E,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAExE;QAAK;UAAAyE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C,CACR,EAEA1E,OAAO,iBACJT,OAAA;QAAK8E,SAAS,EAAC,iFAAiF;QAAAC,QAAA,gBAC5F/E,OAAA,CAACR,OAAO;UAACsF,SAAS,EAAC;QAAgB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACtCnF,OAAA;UAAM8E,SAAS,EAAC,gBAAgB;UAAAC,QAAA,EAAEtE;QAAO;UAAAuE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD,CACR,EAGA5D,cAAc,CAACkE,iBAAiB,KAAK,MAAM,iBACxCzF,OAAA;QAAK8E,SAAS,EAAC,mFAAmF;QAAAC,QAAA,gBAC9F/E,OAAA,CAACN,qBAAqB;UAACoF,SAAS,EAAC;QAAiB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACrDnF,OAAA;UAAA+E,QAAA,gBACI/E,OAAA;YAAG8E,SAAS,EAAC,6BAA6B;YAAAC,QAAA,EAAC;UAAwB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACvEnF,OAAA;YAAG8E,SAAS,EAAC,8BAA8B;YAAAC,QAAA,EAAC;UAE5C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CACR,eAGDnF,OAAA;QAAK8E,SAAS,EAAC,MAAM;QAAAC,QAAA,eACjB/E,OAAA,CAACF,oBAAoB;UACjBa,OAAO,EAAEA,OAAQ;UACjB+E,QAAQ,EAAGC,WAAW,IAAK;YACvB3E,kBAAkB,CAAC2E,WAAW,CAAC;YAC/B,IAAIA,WAAW,CAACJ,WAAW,KAAK,KAAK,IAAI,EAACtE,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEoB,eAAe,GAAE;cACjEf,YAAY,CAAC,IAAI,CAAC;YACtB;UACJ;QAAE;UAAA0D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,EAGL5D,cAAc,CAACkE,iBAAiB,KAAK,MAAM,iBACxCzF,OAAA;QAAK8E,SAAS,EAAC,0CAA0C;QAAAC,QAAA,gBACrD/E,OAAA;UAAI8E,SAAS,EAAC,kEAAkE;UAAAC,QAAA,gBAC5E/E,OAAA,CAACX,KAAK;YAACyF,SAAS,EAAC;UAAe;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,qBAEvC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAEJ,EAAClE,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEoB,eAAe,iBACvBrC,OAAA;UAAK8E,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC7B/E,OAAA,CAACV,QAAQ;YAACwF,SAAS,EAAC;UAAqC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5DnF,OAAA;YAAI8E,SAAS,EAAC,wCAAwC;YAAAC,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1EnF,OAAA;YAAG8E,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAC;UAElC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJnF,OAAA;YACI4F,OAAO,EAAEA,CAAA,KAAMtE,YAAY,CAAC,IAAI,CAAE;YAClCwD,SAAS,EAAC,iGAAiG;YAAAC,QAAA,gBAE3G/E,OAAA,CAACZ,WAAW;cAAA4F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,cAEnB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,gBAENnF,OAAA;UAAK8E,SAAS,EAAC,WAAW;UAAAC,QAAA,gBAEtB/E,OAAA;YAAK8E,SAAS,EAAC,oDAAoD;YAAAC,QAAA,eAC/D/E,OAAA;cAAK8E,SAAS,EAAC,yBAAyB;cAAAC,QAAA,gBACpC/E,OAAA,CAACR,OAAO;gBAACsF,SAAS,EAAC;cAAgB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACtCnF,OAAA;gBAAA+E,QAAA,gBACI/E,OAAA;kBAAG8E,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAC3DnF,OAAA;kBAAG8E,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,EAAC;gBAEtC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAGNnF,OAAA;YAAA+E,QAAA,gBACI/E,OAAA;cAAI8E,SAAS,EAAC,wCAAwC;cAAAC,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxEnF,OAAA;cAAG8E,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAE1C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,EAEHhE,WAAW,CAAC0E,MAAM,GAAG,CAAC,gBACnB7F,OAAA;cAAK8E,SAAS,EAAC,gCAAgC;cAAAC,QAAA,gBAC3C/E,OAAA;gBAAK8E,SAAS,EAAC,+CAA+C;gBAAAC,QAAA,EACzD5D,WAAW,CAAC2E,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACzBhG,OAAA;kBAAiB8E,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,EAC/DgB;gBAAI,GADCC,KAAK;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEV,CACR;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eAENnF,OAAA;gBAAK8E,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACvB/E,OAAA;kBACI4F,OAAO,EAAEpC,mBAAoB;kBAC7BsB,SAAS,EAAC,oFAAoF;kBAAAC,QAAA,gBAE9F/E,OAAA,CAACL,UAAU;oBAAAqF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,kBAElB;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTnF,OAAA;kBACI4F,OAAO,EAAEtC,2BAA4B;kBACrCwB,SAAS,EAAC,wFAAwF;kBAAAC,QAAA,gBAElG/E,OAAA,CAACX,KAAK;oBAAA2F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,oBAEb;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,gBAENnF,OAAA;cAAK8E,SAAS,EAAC,2DAA2D;cAAAC,QAAA,gBACtE/E,OAAA;gBAAG8E,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,EAAC;cAAyB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC5DnF,OAAA;gBACI4F,OAAO,EAAEtC,2BAA4B;gBACrCwB,SAAS,EAAC,qEAAqE;gBAAAC,QAAA,EAClF;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CACR;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eAGNnF,OAAA;YAAK8E,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC1B/E,OAAA;cAAI8E,SAAS,EAAC,qDAAqD;cAAAC,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpFnF,OAAA;cAAK8E,SAAS,EAAC,gDAAgD;cAAAC,QAAA,gBAC3D/E,OAAA;gBAAG8E,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EAAC;cAEzC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJnF,OAAA;gBACI4F,OAAO,EAAE9C,gBAAiB;gBAC1BgC,SAAS,EAAC,kFAAkF;gBAAAC,QAAA,gBAE5F/E,OAAA,CAACJ,OAAO;kBAAAoF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAEf;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CACR;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAAChF,EAAA,CA5VID,gBAAgB;AAAA+F,EAAA,GAAhB/F,gBAAgB;AA8VtB,eAAeA,gBAAgB;AAAC,IAAA+F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}