{"ast": null, "code": "import React from'react';import{BrowserRouter as Router,Route,Routes,Navigate}from'react-router-dom';import'./utils/axiosConfig';// Import axios configuration\nimport{UserProvider}from'./context/UserContext';import{ErrorProvider}from'./contexts/ErrorContext';import{SiteConfigProvider}from'./contexts/SiteConfigContext';import'./styles/SmoothScroll.css';// Import smooth scrolling styles\n// Layouts\nimport AdminLayout from'./components/AdminLayout';import UserLayout from'./components/UserLayout';// Components\nimport Messages from'./components/Messages/Messages';// Public Pages\nimport WelcomeSplash from'./pages/WelcomeSplash';import AdminLoginPage from'./pages/AdminLoginPage';import UserLogin from'./pages/UserLogin';import UserRegistration from'./pages/UserRegistration';// Admin Pages\nimport AdminDashboard from'./pages/AdminDashboard';import ChallengeSystem from'./pages/ChallengeSystem';import UserManagement from'./pages/UserManagement';import UserDetails from'./pages/UserDetails';import BetManagement from'./pages/BetManagement';import TransactionManagement from'./pages/TransactionManagement';import LeaderboardManagement from'./pages/LeaderboardManagement';import SystemSettings from'./pages/SystemSettings';import SMTPSettings from'./pages/SMTPSettings';import SecuritySettings from'./pages/SecuritySettings';import Admin2FASettings from'./pages/Admin2FASettings';import GeneralSettings from'./pages/GeneralSettings';import NotificationSettings from'./pages/NotificationSettings';import ReportsAnalytics from'./pages/ReportsAnalytics';import AdminLeaderboard from'./pages/AdminLeaderboard';import AdminReports from'./pages/AdminReports';import AddUser from'./pages/AddUser';import PaymentMethods from'./pages/PaymentMethods';import CreditUser from'./pages/CreditUser';import DebitUser from'./pages/DebitUser';import TeamManagement from'./pages/TeamManagement';import ChallengeManagement from'./pages/ChallengeManagement';import CreditChallenge from'./pages/CreditChallenge';import LeagueManagement from'./pages/LeagueManagement';import LeagueSeasonManagement from'./pages/LeagueSeasonManagement';import CreateLeague from'./pages/CreateLeague';import LeagueDetails from'./pages/LeagueDetails';import LeagueUserManagement from'./pages/LeagueUserManagement';// User Pages\nimport UserDashboard from'./pages/UserDashboard';import JoinChallenge from'./pages/JoinChallenge';import JoinChallenge2 from'./pages/JoinChallenge2';import ViewBets from'./pages/ViewBets';import IncomingBets from'./pages/IncomingBets';import Profile from'./pages/Profile';import AcceptedBets from'./pages/AcceptedBets';import PaymentHistory from'./pages/PaymentHistory';import Leaderboard from'./pages/Leaderboard';import ChangePassword from'./pages/ChangePassword';import Deposit from'./pages/Deposit';import Withdraw from'./pages/Withdraw';import Friends from'./pages/Friends';import FriendRequests from'./pages/FriendRequests';import LeagueHome from'./pages/LeagueHome';import LeagueSelection from'./pages/LeagueSelection';import UserAchievements from'./pages/UserAchievements';import SeasonHistory from'./pages/SeasonHistory';import MyLeagues from'./pages/MyLeagues';import Transfer from'./pages/Transfer';import CreditWallet from'./pages/CreditWallet';import CreditHistory from'./pages/CreditHistory';import Challenges from'./pages/Challenges';import RecentBets from'./pages/RecentBets';// Protected Route Component for Users\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const ProtectedRoute=_ref=>{let{children}=_ref;const isAuthenticated=localStorage.getItem('userId');if(!isAuthenticated){// Save the current path for redirect after login\nconst currentPath=window.location.pathname;if(currentPath!=='/login'){sessionStorage.setItem('redirectAfterLogin',currentPath);}return/*#__PURE__*/_jsx(Navigate,{to:\"/login\",replace:true});}return children;};// Protected Route Component for Admin\nconst AdminProtectedRoute=_ref2=>{let{children}=_ref2;const isAdminAuthenticated=localStorage.getItem('adminId');if(!isAdminAuthenticated){return/*#__PURE__*/_jsx(Navigate,{to:\"/admin/login\",replace:true});}return children;};function App(){return/*#__PURE__*/_jsx(UserProvider,{children:/*#__PURE__*/_jsx(ErrorProvider,{children:/*#__PURE__*/_jsx(SiteConfigProvider,{children:/*#__PURE__*/_jsx(\"div\",{className:\"App\",children:/*#__PURE__*/_jsx(Router,{children:/*#__PURE__*/_jsxs(Routes,{children:[/*#__PURE__*/_jsx(Route,{path:\"/\",element:/*#__PURE__*/_jsx(WelcomeSplash,{})}),/*#__PURE__*/_jsx(Route,{path:\"/login\",element:/*#__PURE__*/_jsx(UserLogin,{})}),/*#__PURE__*/_jsx(Route,{path:\"/register\",element:/*#__PURE__*/_jsx(UserRegistration,{})}),/*#__PURE__*/_jsx(Route,{path:\"/admin/login\",element:/*#__PURE__*/_jsx(AdminLoginPage,{})}),/*#__PURE__*/_jsxs(Route,{path:\"/admin\",element:/*#__PURE__*/_jsx(AdminProtectedRoute,{children:/*#__PURE__*/_jsx(AdminLayout,{})}),children:[/*#__PURE__*/_jsx(Route,{index:true,element:/*#__PURE__*/_jsx(AdminDashboard,{})}),/*#__PURE__*/_jsx(Route,{path:\"dashboard\",element:/*#__PURE__*/_jsx(AdminDashboard,{})}),/*#__PURE__*/_jsx(Route,{path:\"challenge-system\",element:/*#__PURE__*/_jsx(ChallengeSystem,{})}),/*#__PURE__*/_jsx(Route,{path:\"challenge-management\",element:/*#__PURE__*/_jsx(ChallengeManagement,{})}),/*#__PURE__*/_jsx(Route,{path:\"credit-challenge\",element:/*#__PURE__*/_jsx(CreditChallenge,{})}),/*#__PURE__*/_jsx(Route,{path:\"team-management\",element:/*#__PURE__*/_jsx(TeamManagement,{})}),/*#__PURE__*/_jsx(Route,{path:\"league-management\",element:/*#__PURE__*/_jsx(LeagueManagement,{})}),/*#__PURE__*/_jsx(Route,{path:\"league-management/create\",element:/*#__PURE__*/_jsx(CreateLeague,{})}),/*#__PURE__*/_jsx(Route,{path:\"league-seasons\",element:/*#__PURE__*/_jsx(LeagueSeasonManagement,{})}),/*#__PURE__*/_jsx(Route,{path:\"league-divisions\",element:/*#__PURE__*/_jsx(LeagueManagement,{})}),/*#__PURE__*/_jsx(Route,{path:\"league-rewards\",element:/*#__PURE__*/_jsx(LeagueManagement,{})}),/*#__PURE__*/_jsx(Route,{path:\"league-management/:leagueId/seasons\",element:/*#__PURE__*/_jsx(LeagueSeasonManagement,{})}),/*#__PURE__*/_jsx(Route,{path:\"league-users\",element:/*#__PURE__*/_jsx(LeagueUserManagement,{})}),/*#__PURE__*/_jsx(Route,{path:\"users\",element:/*#__PURE__*/_jsx(UserManagement,{})}),/*#__PURE__*/_jsx(Route,{path:\"users/:userId\",element:/*#__PURE__*/_jsx(UserDetails,{})}),/*#__PURE__*/_jsx(Route,{path:\"add-user\",element:/*#__PURE__*/_jsx(AddUser,{})}),/*#__PURE__*/_jsx(Route,{path:\"credit-user\",element:/*#__PURE__*/_jsx(CreditUser,{})}),/*#__PURE__*/_jsx(Route,{path:\"debit-user\",element:/*#__PURE__*/_jsx(DebitUser,{})}),/*#__PURE__*/_jsx(Route,{path:\"payment-methods\",element:/*#__PURE__*/_jsx(PaymentMethods,{})}),/*#__PURE__*/_jsx(Route,{path:\"bets\",element:/*#__PURE__*/_jsx(BetManagement,{})}),/*#__PURE__*/_jsx(Route,{path:\"transactions\",element:/*#__PURE__*/_jsx(TransactionManagement,{})}),/*#__PURE__*/_jsx(Route,{path:\"leaderboard\",element:/*#__PURE__*/_jsx(AdminLeaderboard,{})}),/*#__PURE__*/_jsx(Route,{path:\"leaderboard-management\",element:/*#__PURE__*/_jsx(LeaderboardManagement,{})}),/*#__PURE__*/_jsx(Route,{path:\"reports\",element:/*#__PURE__*/_jsx(AdminReports,{})}),/*#__PURE__*/_jsx(Route,{path:\"reports-analytics\",element:/*#__PURE__*/_jsx(ReportsAnalytics,{})}),/*#__PURE__*/_jsx(Route,{path:\"settings\",element:/*#__PURE__*/_jsx(SystemSettings,{})}),/*#__PURE__*/_jsx(Route,{path:\"general-settings\",element:/*#__PURE__*/_jsx(GeneralSettings,{})}),/*#__PURE__*/_jsx(Route,{path:\"smtp-settings\",element:/*#__PURE__*/_jsx(SMTPSettings,{})}),/*#__PURE__*/_jsx(Route,{path:\"security-settings\",element:/*#__PURE__*/_jsx(SecuritySettings,{})}),/*#__PURE__*/_jsx(Route,{path:\"2fa-settings\",element:/*#__PURE__*/_jsx(Admin2FASettings,{})}),/*#__PURE__*/_jsx(Route,{path:\"notification-settings\",element:/*#__PURE__*/_jsx(NotificationSettings,{})})]}),/*#__PURE__*/_jsxs(Route,{path:\"/user\",element:/*#__PURE__*/_jsx(ProtectedRoute,{children:/*#__PURE__*/_jsx(UserLayout,{})}),children:[/*#__PURE__*/_jsx(Route,{index:true,element:/*#__PURE__*/_jsx(UserDashboard,{})}),/*#__PURE__*/_jsx(Route,{path:\"dashboard\",element:/*#__PURE__*/_jsx(UserDashboard,{})}),/*#__PURE__*/_jsx(Route,{path:\"bets\",element:/*#__PURE__*/_jsx(ViewBets,{})}),/*#__PURE__*/_jsx(Route,{path:\"bets/outgoing\",element:/*#__PURE__*/_jsx(ViewBets,{})}),/*#__PURE__*/_jsx(Route,{path:\"bets/incoming\",element:/*#__PURE__*/_jsx(IncomingBets,{})}),/*#__PURE__*/_jsx(Route,{path:\"bets/accepted\",element:/*#__PURE__*/_jsx(AcceptedBets,{})}),/*#__PURE__*/_jsx(Route,{path:\"payment-history\",element:/*#__PURE__*/_jsx(PaymentHistory,{})}),/*#__PURE__*/_jsx(Route,{path:\"leaderboard\",element:/*#__PURE__*/_jsx(Leaderboard,{})}),/*#__PURE__*/_jsx(Route,{path:\"profile\",element:/*#__PURE__*/_jsx(Profile,{})}),/*#__PURE__*/_jsx(Route,{path:\"profile/:username\",element:/*#__PURE__*/_jsx(Profile,{})}),/*#__PURE__*/_jsx(Route,{path:\"change-password\",element:/*#__PURE__*/_jsx(ChangePassword,{})}),/*#__PURE__*/_jsx(Route,{path:\"deposit\",element:/*#__PURE__*/_jsx(Deposit,{})}),/*#__PURE__*/_jsx(Route,{path:\"withdraw\",element:/*#__PURE__*/_jsx(Withdraw,{})}),/*#__PURE__*/_jsx(Route,{path:\"friends\",element:/*#__PURE__*/_jsx(Friends,{})}),/*#__PURE__*/_jsx(Route,{path:\"friend-requests\",element:/*#__PURE__*/_jsx(FriendRequests,{})}),/*#__PURE__*/_jsx(Route,{path:\"join-challenge/:challengeId\",element:/*#__PURE__*/_jsx(JoinChallenge,{})}),/*#__PURE__*/_jsx(Route,{path:\"join-challenge2/:challengeId/:betId/:uniqueCode/:user1Id\",element:/*#__PURE__*/_jsx(JoinChallenge2,{})}),/*#__PURE__*/_jsx(Route,{path:\"messages\",element:/*#__PURE__*/_jsx(Messages,{})}),/*#__PURE__*/_jsx(Route,{path:\"challenges\",element:/*#__PURE__*/_jsx(Challenges,{})}),/*#__PURE__*/_jsx(Route,{path:\"recent-bets\",element:/*#__PURE__*/_jsx(RecentBets,{})}),/*#__PURE__*/_jsx(Route,{path:\"leagues\",element:/*#__PURE__*/_jsx(LeagueHome,{})}),/*#__PURE__*/_jsx(Route,{path:\"my-leagues\",element:/*#__PURE__*/_jsx(MyLeagues,{})}),/*#__PURE__*/_jsx(Route,{path:\"leagues/:leagueId\",element:/*#__PURE__*/_jsx(LeagueDetails,{})}),/*#__PURE__*/_jsx(Route,{path:\"leagues/achievements\",element:/*#__PURE__*/_jsx(UserAchievements,{})}),/*#__PURE__*/_jsx(Route,{path:\"leagues/seasons\",element:/*#__PURE__*/_jsx(SeasonHistory,{})}),/*#__PURE__*/_jsx(Route,{path:\"league\",element:/*#__PURE__*/_jsx(Navigate,{to:\"/user/leagues\",replace:true})}),/*#__PURE__*/_jsx(Route,{path:\"league/:leagueId/selection\",element:/*#__PURE__*/_jsx(Navigate,{to:\"/user/leagues/:leagueId\",replace:true})}),/*#__PURE__*/_jsx(Route,{path:\"league/:leagueId/leaderboard\",element:/*#__PURE__*/_jsx(Navigate,{to:\"/user/leagues/:leagueId/leaderboard\",replace:true})}),/*#__PURE__*/_jsx(Route,{path:\"achievements\",element:/*#__PURE__*/_jsx(Navigate,{to:\"/user/leagues/achievements\",replace:true})}),/*#__PURE__*/_jsx(Route,{path:\"season-history\",element:/*#__PURE__*/_jsx(Navigate,{to:\"/user/leagues/seasons\",replace:true})}),/*#__PURE__*/_jsx(Route,{path:\"transfer\",element:/*#__PURE__*/_jsx(Transfer,{})}),/*#__PURE__*/_jsx(Route,{path:\"wallet\",element:/*#__PURE__*/_jsx(CreditWallet,{})}),/*#__PURE__*/_jsx(Route,{path:\"credit-history\",element:/*#__PURE__*/_jsx(CreditHistory,{})})]}),/*#__PURE__*/_jsx(Route,{path:\"*\",element:localStorage.getItem('adminId')?/*#__PURE__*/_jsx(Navigate,{to:\"/admin/dashboard\",replace:true}):localStorage.getItem('userId')&&localStorage.getItem('userToken')?/*#__PURE__*/_jsx(Navigate,{to:\"/user/dashboard\",replace:true}):/*#__PURE__*/_jsx(Navigate,{to:\"/login\",replace:true})})]})})})})})});}export default App;", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Route", "Routes", "Navigate", "UserProvider", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SiteConfigProvider", "AdminLayout", "UserLayout", "Messages", "WelcomeSplash", "AdminLoginPage", "UserLogin", "UserRegistration", "AdminDashboard", "ChallengeSystem", "UserManagement", "UserDetails", "BetManagement", "TransactionManagement", "LeaderboardManagement", "SystemSettings", "SMTPSettings", "SecuritySettings", "Admin2FASettings", "GeneralSettings", "NotificationSettings", "ReportsAnalytics", "AdminLeaderboard", "AdminReports", "AddUser", "PaymentMethods", "CreditUser", "DebitUser", "TeamManagement", "ChallengeManagement", "CreditChallenge", "LeagueManagement", "LeagueSeasonManagement", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "LeagueDetails", "LeagueUserManagement", "UserDashboard", "JoinChall<PERSON>e", "JoinChallenge2", "ViewBets", "IncomingBets", "Profile", "AcceptedBets", "PaymentHistory", "Leaderboard", "ChangePassword", "<PERSON><PERSON><PERSON><PERSON>", "Withdraw", "Friends", "FriendRequests", "LeagueHome", "LeagueSelection", "UserAchievements", "SeasonHistory", "MyLeagues", "Transfer", "CreditWallet", "CreditHistory", "Challenges", "RecentBets", "jsx", "_jsx", "jsxs", "_jsxs", "ProtectedRoute", "_ref", "children", "isAuthenticated", "localStorage", "getItem", "currentPath", "window", "location", "pathname", "sessionStorage", "setItem", "to", "replace", "AdminProtectedRoute", "_ref2", "isAdminAuthenticated", "App", "className", "path", "element", "index"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/App.js"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Route, Routes, Navigate } from 'react-router-dom';\nimport './utils/axiosConfig';  // Import axios configuration\nimport { UserProvider } from './context/UserContext';\nimport { ErrorProvider } from './contexts/ErrorContext';\nimport { SiteConfigProvider } from './contexts/SiteConfigContext';\nimport './styles/SmoothScroll.css'; // Import smooth scrolling styles\n\n// Layouts\nimport AdminLayout from './components/AdminLayout';\nimport UserLayout from './components/UserLayout';\n\n// Components\nimport Messages from './components/Messages/Messages';\n\n// Public Pages\nimport WelcomeSplash from './pages/WelcomeSplash';\nimport AdminLoginPage from './pages/AdminLoginPage';\nimport UserLogin from './pages/UserLogin';\nimport UserRegistration from './pages/UserRegistration';\n\n// Admin Pages\nimport AdminDashboard from './pages/AdminDashboard';\nimport ChallengeSystem from './pages/ChallengeSystem';\nimport UserManagement from './pages/UserManagement';\nimport UserDetails from './pages/UserDetails';\nimport BetManagement from './pages/BetManagement';\nimport TransactionManagement from './pages/TransactionManagement';\nimport LeaderboardManagement from './pages/LeaderboardManagement';\nimport SystemSettings from './pages/SystemSettings';\nimport SMTPSettings from './pages/SMTPSettings';\nimport SecuritySettings from './pages/SecuritySettings';\nimport Admin2FASettings from './pages/Admin2FASettings';\nimport GeneralSettings from './pages/GeneralSettings';\nimport NotificationSettings from './pages/NotificationSettings';\nimport ReportsAnalytics from './pages/ReportsAnalytics';\nimport AdminLeaderboard from './pages/AdminLeaderboard';\nimport AdminReports from './pages/AdminReports';\nimport AddUser from './pages/AddUser';\nimport PaymentMethods from './pages/PaymentMethods';\nimport CreditUser from './pages/CreditUser';\nimport DebitUser from './pages/DebitUser';\nimport TeamManagement from './pages/TeamManagement';\nimport ChallengeManagement from './pages/ChallengeManagement';\nimport CreditChallenge from './pages/CreditChallenge';\nimport LeagueManagement from './pages/LeagueManagement';\nimport LeagueSeasonManagement from './pages/LeagueSeasonManagement';\nimport CreateLeague from './pages/CreateLeague';\nimport LeagueDetails from './pages/LeagueDetails';\nimport LeagueUserManagement from './pages/LeagueUserManagement';\n\n// User Pages\nimport UserDashboard from './pages/UserDashboard';\nimport JoinChallenge from './pages/JoinChallenge';\nimport JoinChallenge2 from './pages/JoinChallenge2';\nimport ViewBets from './pages/ViewBets';\nimport IncomingBets from './pages/IncomingBets';\nimport Profile from './pages/Profile';\nimport AcceptedBets from './pages/AcceptedBets';\nimport PaymentHistory from './pages/PaymentHistory';\nimport Leaderboard from './pages/Leaderboard';\nimport ChangePassword from './pages/ChangePassword';\nimport Deposit from './pages/Deposit';\nimport Withdraw from './pages/Withdraw';\nimport Friends from './pages/Friends';\nimport FriendRequests from './pages/FriendRequests';\nimport LeagueHome from './pages/LeagueHome';\nimport LeagueSelection from './pages/LeagueSelection';\nimport UserAchievements from './pages/UserAchievements';\nimport SeasonHistory from './pages/SeasonHistory';\nimport MyLeagues from './pages/MyLeagues';\nimport Transfer from './pages/Transfer';\nimport CreditWallet from './pages/CreditWallet';\nimport CreditHistory from './pages/CreditHistory';\nimport Challenges from './pages/Challenges';\nimport RecentBets from './pages/RecentBets';\n\n// Protected Route Component for Users\nconst ProtectedRoute = ({ children }) => {\n  const isAuthenticated = localStorage.getItem('userId');\n\n  if (!isAuthenticated) {\n    // Save the current path for redirect after login\n    const currentPath = window.location.pathname;\n    if (currentPath !== '/login') {\n      sessionStorage.setItem('redirectAfterLogin', currentPath);\n    }\n    return <Navigate to=\"/login\" replace />;\n  }\n\n  return children;\n};\n\n// Protected Route Component for Admin\nconst AdminProtectedRoute = ({ children }) => {\n  const isAdminAuthenticated = localStorage.getItem('adminId');\n\n  if (!isAdminAuthenticated) {\n    return <Navigate to=\"/admin/login\" replace />;\n  }\n\n  return children;\n};\n\nfunction App() {\n  return (\n    <UserProvider>\n      <ErrorProvider>\n        <SiteConfigProvider>\n          <div className=\"App\">\n            <Router>\n            <Routes>\n              {/* Public Routes */}\n              <Route path=\"/\" element={<WelcomeSplash />} />\n              <Route path=\"/login\" element={<UserLogin />} />\n              <Route path=\"/register\" element={<UserRegistration />} />\n              <Route path=\"/admin/login\" element={<AdminLoginPage />} />\n\n              {/* Admin Routes */}\n              <Route path=\"/admin\" element={\n                <AdminProtectedRoute>\n                  <AdminLayout />\n                </AdminProtectedRoute>\n              }>\n                <Route index element={<AdminDashboard />} />\n                <Route path=\"dashboard\" element={<AdminDashboard />} />\n                <Route path=\"challenge-system\" element={<ChallengeSystem />} />\n                <Route path=\"challenge-management\" element={<ChallengeManagement />} />\n                <Route path=\"credit-challenge\" element={<CreditChallenge />} />\n                <Route path=\"team-management\" element={<TeamManagement />} />\n                {/* League Management Routes */}\n                <Route path=\"league-management\" element={<LeagueManagement />} />\n                <Route path=\"league-management/create\" element={<CreateLeague />} />\n                <Route path=\"league-seasons\" element={<LeagueSeasonManagement />} />\n                <Route path=\"league-divisions\" element={<LeagueManagement />} />\n                <Route path=\"league-rewards\" element={<LeagueManagement />} />\n                <Route path=\"league-management/:leagueId/seasons\" element={<LeagueSeasonManagement />} />\n                <Route path=\"league-users\" element={<LeagueUserManagement />} />\n                {/* Other Admin Routes */}\n                <Route path=\"users\" element={<UserManagement />} />\n                <Route path=\"users/:userId\" element={<UserDetails />} />\n                <Route path=\"add-user\" element={<AddUser />} />\n                <Route path=\"credit-user\" element={<CreditUser />} />\n                <Route path=\"debit-user\" element={<DebitUser />} />\n                <Route path=\"payment-methods\" element={<PaymentMethods />} />\n                <Route path=\"bets\" element={<BetManagement />} />\n                <Route path=\"transactions\" element={<TransactionManagement />} />\n                <Route path=\"leaderboard\" element={<AdminLeaderboard />} />\n                <Route path=\"leaderboard-management\" element={<LeaderboardManagement />} />\n                <Route path=\"reports\" element={<AdminReports />} />\n                <Route path=\"reports-analytics\" element={<ReportsAnalytics />} />\n                <Route path=\"settings\" element={<SystemSettings />} />\n                <Route path=\"general-settings\" element={<GeneralSettings />} />\n                <Route path=\"smtp-settings\" element={<SMTPSettings />} />\n                <Route path=\"security-settings\" element={<SecuritySettings />} />\n                <Route path=\"2fa-settings\" element={<Admin2FASettings />} />\n                <Route path=\"notification-settings\" element={<NotificationSettings />} />\n              </Route>\n\n              {/* User Routes */}\n              <Route\n                path=\"/user\"\n                element={\n                  <ProtectedRoute>\n                    <UserLayout />\n                  </ProtectedRoute>\n                }\n              >\n                <Route index element={<UserDashboard />} />\n                <Route path=\"dashboard\" element={<UserDashboard />} />\n                <Route path=\"bets\" element={<ViewBets />} />\n                <Route path=\"bets/outgoing\" element={<ViewBets />} />\n                <Route path=\"bets/incoming\" element={<IncomingBets />} />\n                <Route path=\"bets/accepted\" element={<AcceptedBets />} />\n                <Route path=\"payment-history\" element={<PaymentHistory />} />\n                <Route path=\"leaderboard\" element={<Leaderboard />} />\n                <Route path=\"profile\" element={<Profile />} />\n                <Route path=\"profile/:username\" element={<Profile />} />\n                <Route path=\"change-password\" element={<ChangePassword />} />\n                <Route path=\"deposit\" element={<Deposit />} />\n                <Route path=\"withdraw\" element={<Withdraw />} />\n                <Route path=\"friends\" element={<Friends />} />\n                <Route path=\"friend-requests\" element={<FriendRequests />} />\n                <Route path=\"join-challenge/:challengeId\" element={<JoinChallenge />} />\n                <Route path=\"join-challenge2/:challengeId/:betId/:uniqueCode/:user1Id\" element={<JoinChallenge2 />} />\n                <Route path=\"messages\" element={<Messages />} />\n                <Route path=\"challenges\" element={<Challenges />} />\n                <Route path=\"recent-bets\" element={<RecentBets />} />\n                {/* League Routes */}\n                <Route path=\"leagues\" element={<LeagueHome />} />\n                <Route path=\"my-leagues\" element={<MyLeagues />} />\n                <Route path=\"leagues/:leagueId\" element={<LeagueDetails />} />\n                <Route path=\"leagues/achievements\" element={<UserAchievements />} />\n                <Route path=\"leagues/seasons\" element={<SeasonHistory />} />\n                {/* Legacy League Routes - Keep for backward compatibility */}\n                <Route path=\"league\" element={<Navigate to=\"/user/leagues\" replace />} />\n                <Route path=\"league/:leagueId/selection\" element={<Navigate to=\"/user/leagues/:leagueId\" replace />} />\n                <Route path=\"league/:leagueId/leaderboard\" element={<Navigate to=\"/user/leagues/:leagueId/leaderboard\" replace />} />\n                <Route path=\"achievements\" element={<Navigate to=\"/user/leagues/achievements\" replace />} />\n                <Route path=\"season-history\" element={<Navigate to=\"/user/leagues/seasons\" replace />} />\n                <Route path=\"transfer\" element={<Transfer />} />\n                <Route path=\"wallet\" element={<CreditWallet />} />\n                <Route path=\"credit-history\" element={<CreditHistory />} />\n              </Route>\n\n              {/* Catch all route - redirect to appropriate dashboard if authenticated, otherwise to login */}\n              <Route\n                path=\"*\"\n                element={\n                  localStorage.getItem('adminId') ? (\n                    <Navigate to=\"/admin/dashboard\" replace />\n                  ) : localStorage.getItem('userId') && localStorage.getItem('userToken') ? (\n                    <Navigate to=\"/user/dashboard\" replace />\n                  ) : (\n                    <Navigate to=\"/login\" replace />\n                  )\n                }\n              />\n            </Routes>\n          </Router>\n        </div>\n      </SiteConfigProvider>\n    </ErrorProvider>\n  </UserProvider>\n  );\n}\n\nexport default App;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,aAAa,GAAI,CAAAC,MAAM,CAAEC,KAAK,CAAEC,MAAM,CAAEC,QAAQ,KAAQ,kBAAkB,CACnF,MAAO,qBAAqB,CAAG;AAC/B,OAASC,YAAY,KAAQ,uBAAuB,CACpD,OAASC,aAAa,KAAQ,yBAAyB,CACvD,OAASC,kBAAkB,KAAQ,8BAA8B,CACjE,MAAO,2BAA2B,CAAE;AAEpC;AACA,MAAO,CAAAC,WAAW,KAAM,0BAA0B,CAClD,MAAO,CAAAC,UAAU,KAAM,yBAAyB,CAEhD;AACA,MAAO,CAAAC,QAAQ,KAAM,gCAAgC,CAErD;AACA,MAAO,CAAAC,aAAa,KAAM,uBAAuB,CACjD,MAAO,CAAAC,cAAc,KAAM,wBAAwB,CACnD,MAAO,CAAAC,SAAS,KAAM,mBAAmB,CACzC,MAAO,CAAAC,gBAAgB,KAAM,0BAA0B,CAEvD;AACA,MAAO,CAAAC,cAAc,KAAM,wBAAwB,CACnD,MAAO,CAAAC,eAAe,KAAM,yBAAyB,CACrD,MAAO,CAAAC,cAAc,KAAM,wBAAwB,CACnD,MAAO,CAAAC,WAAW,KAAM,qBAAqB,CAC7C,MAAO,CAAAC,aAAa,KAAM,uBAAuB,CACjD,MAAO,CAAAC,qBAAqB,KAAM,+BAA+B,CACjE,MAAO,CAAAC,qBAAqB,KAAM,+BAA+B,CACjE,MAAO,CAAAC,cAAc,KAAM,wBAAwB,CACnD,MAAO,CAAAC,YAAY,KAAM,sBAAsB,CAC/C,MAAO,CAAAC,gBAAgB,KAAM,0BAA0B,CACvD,MAAO,CAAAC,gBAAgB,KAAM,0BAA0B,CACvD,MAAO,CAAAC,eAAe,KAAM,yBAAyB,CACrD,MAAO,CAAAC,oBAAoB,KAAM,8BAA8B,CAC/D,MAAO,CAAAC,gBAAgB,KAAM,0BAA0B,CACvD,MAAO,CAAAC,gBAAgB,KAAM,0BAA0B,CACvD,MAAO,CAAAC,YAAY,KAAM,sBAAsB,CAC/C,MAAO,CAAAC,OAAO,KAAM,iBAAiB,CACrC,MAAO,CAAAC,cAAc,KAAM,wBAAwB,CACnD,MAAO,CAAAC,UAAU,KAAM,oBAAoB,CAC3C,MAAO,CAAAC,SAAS,KAAM,mBAAmB,CACzC,MAAO,CAAAC,cAAc,KAAM,wBAAwB,CACnD,MAAO,CAAAC,mBAAmB,KAAM,6BAA6B,CAC7D,MAAO,CAAAC,eAAe,KAAM,yBAAyB,CACrD,MAAO,CAAAC,gBAAgB,KAAM,0BAA0B,CACvD,MAAO,CAAAC,sBAAsB,KAAM,gCAAgC,CACnE,MAAO,CAAAC,YAAY,KAAM,sBAAsB,CAC/C,MAAO,CAAAC,aAAa,KAAM,uBAAuB,CACjD,MAAO,CAAAC,oBAAoB,KAAM,8BAA8B,CAE/D;AACA,MAAO,CAAAC,aAAa,KAAM,uBAAuB,CACjD,MAAO,CAAAC,aAAa,KAAM,uBAAuB,CACjD,MAAO,CAAAC,cAAc,KAAM,wBAAwB,CACnD,MAAO,CAAAC,QAAQ,KAAM,kBAAkB,CACvC,MAAO,CAAAC,YAAY,KAAM,sBAAsB,CAC/C,MAAO,CAAAC,OAAO,KAAM,iBAAiB,CACrC,MAAO,CAAAC,YAAY,KAAM,sBAAsB,CAC/C,MAAO,CAAAC,cAAc,KAAM,wBAAwB,CACnD,MAAO,CAAAC,WAAW,KAAM,qBAAqB,CAC7C,MAAO,CAAAC,cAAc,KAAM,wBAAwB,CACnD,MAAO,CAAAC,OAAO,KAAM,iBAAiB,CACrC,MAAO,CAAAC,QAAQ,KAAM,kBAAkB,CACvC,MAAO,CAAAC,OAAO,KAAM,iBAAiB,CACrC,MAAO,CAAAC,cAAc,KAAM,wBAAwB,CACnD,MAAO,CAAAC,UAAU,KAAM,oBAAoB,CAC3C,MAAO,CAAAC,eAAe,KAAM,yBAAyB,CACrD,MAAO,CAAAC,gBAAgB,KAAM,0BAA0B,CACvD,MAAO,CAAAC,aAAa,KAAM,uBAAuB,CACjD,MAAO,CAAAC,SAAS,KAAM,mBAAmB,CACzC,MAAO,CAAAC,QAAQ,KAAM,kBAAkB,CACvC,MAAO,CAAAC,YAAY,KAAM,sBAAsB,CAC/C,MAAO,CAAAC,aAAa,KAAM,uBAAuB,CACjD,MAAO,CAAAC,UAAU,KAAM,oBAAoB,CAC3C,MAAO,CAAAC,UAAU,KAAM,oBAAoB,CAE3C;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBACA,KAAM,CAAAC,cAAc,CAAGC,IAAA,EAAkB,IAAjB,CAAEC,QAAS,CAAC,CAAAD,IAAA,CAClC,KAAM,CAAAE,eAAe,CAAGC,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC,CAEtD,GAAI,CAACF,eAAe,CAAE,CACpB;AACA,KAAM,CAAAG,WAAW,CAAGC,MAAM,CAACC,QAAQ,CAACC,QAAQ,CAC5C,GAAIH,WAAW,GAAK,QAAQ,CAAE,CAC5BI,cAAc,CAACC,OAAO,CAAC,oBAAoB,CAAEL,WAAW,CAAC,CAC3D,CACA,mBAAOT,IAAA,CAAChE,QAAQ,EAAC+E,EAAE,CAAC,QAAQ,CAACC,OAAO,MAAE,CAAC,CACzC,CAEA,MAAO,CAAAX,QAAQ,CACjB,CAAC,CAED;AACA,KAAM,CAAAY,mBAAmB,CAAGC,KAAA,EAAkB,IAAjB,CAAEb,QAAS,CAAC,CAAAa,KAAA,CACvC,KAAM,CAAAC,oBAAoB,CAAGZ,YAAY,CAACC,OAAO,CAAC,SAAS,CAAC,CAE5D,GAAI,CAACW,oBAAoB,CAAE,CACzB,mBAAOnB,IAAA,CAAChE,QAAQ,EAAC+E,EAAE,CAAC,cAAc,CAACC,OAAO,MAAE,CAAC,CAC/C,CAEA,MAAO,CAAAX,QAAQ,CACjB,CAAC,CAED,QAAS,CAAAe,GAAGA,CAAA,CAAG,CACb,mBACEpB,IAAA,CAAC/D,YAAY,EAAAoE,QAAA,cACXL,IAAA,CAAC9D,aAAa,EAAAmE,QAAA,cACZL,IAAA,CAAC7D,kBAAkB,EAAAkE,QAAA,cACjBL,IAAA,QAAKqB,SAAS,CAAC,KAAK,CAAAhB,QAAA,cAClBL,IAAA,CAACnE,MAAM,EAAAwE,QAAA,cACPH,KAAA,CAACnE,MAAM,EAAAsE,QAAA,eAELL,IAAA,CAAClE,KAAK,EAACwF,IAAI,CAAC,GAAG,CAACC,OAAO,cAAEvB,IAAA,CAACzD,aAAa,GAAE,CAAE,CAAE,CAAC,cAC9CyD,IAAA,CAAClE,KAAK,EAACwF,IAAI,CAAC,QAAQ,CAACC,OAAO,cAAEvB,IAAA,CAACvD,SAAS,GAAE,CAAE,CAAE,CAAC,cAC/CuD,IAAA,CAAClE,KAAK,EAACwF,IAAI,CAAC,WAAW,CAACC,OAAO,cAAEvB,IAAA,CAACtD,gBAAgB,GAAE,CAAE,CAAE,CAAC,cACzDsD,IAAA,CAAClE,KAAK,EAACwF,IAAI,CAAC,cAAc,CAACC,OAAO,cAAEvB,IAAA,CAACxD,cAAc,GAAE,CAAE,CAAE,CAAC,cAG1D0D,KAAA,CAACpE,KAAK,EAACwF,IAAI,CAAC,QAAQ,CAACC,OAAO,cAC1BvB,IAAA,CAACiB,mBAAmB,EAAAZ,QAAA,cAClBL,IAAA,CAAC5D,WAAW,GAAE,CAAC,CACI,CACtB,CAAAiE,QAAA,eACCL,IAAA,CAAClE,KAAK,EAAC0F,KAAK,MAACD,OAAO,cAAEvB,IAAA,CAACrD,cAAc,GAAE,CAAE,CAAE,CAAC,cAC5CqD,IAAA,CAAClE,KAAK,EAACwF,IAAI,CAAC,WAAW,CAACC,OAAO,cAAEvB,IAAA,CAACrD,cAAc,GAAE,CAAE,CAAE,CAAC,cACvDqD,IAAA,CAAClE,KAAK,EAACwF,IAAI,CAAC,kBAAkB,CAACC,OAAO,cAAEvB,IAAA,CAACpD,eAAe,GAAE,CAAE,CAAE,CAAC,cAC/DoD,IAAA,CAAClE,KAAK,EAACwF,IAAI,CAAC,sBAAsB,CAACC,OAAO,cAAEvB,IAAA,CAAChC,mBAAmB,GAAE,CAAE,CAAE,CAAC,cACvEgC,IAAA,CAAClE,KAAK,EAACwF,IAAI,CAAC,kBAAkB,CAACC,OAAO,cAAEvB,IAAA,CAAC/B,eAAe,GAAE,CAAE,CAAE,CAAC,cAC/D+B,IAAA,CAAClE,KAAK,EAACwF,IAAI,CAAC,iBAAiB,CAACC,OAAO,cAAEvB,IAAA,CAACjC,cAAc,GAAE,CAAE,CAAE,CAAC,cAE7DiC,IAAA,CAAClE,KAAK,EAACwF,IAAI,CAAC,mBAAmB,CAACC,OAAO,cAAEvB,IAAA,CAAC9B,gBAAgB,GAAE,CAAE,CAAE,CAAC,cACjE8B,IAAA,CAAClE,KAAK,EAACwF,IAAI,CAAC,0BAA0B,CAACC,OAAO,cAAEvB,IAAA,CAAC5B,YAAY,GAAE,CAAE,CAAE,CAAC,cACpE4B,IAAA,CAAClE,KAAK,EAACwF,IAAI,CAAC,gBAAgB,CAACC,OAAO,cAAEvB,IAAA,CAAC7B,sBAAsB,GAAE,CAAE,CAAE,CAAC,cACpE6B,IAAA,CAAClE,KAAK,EAACwF,IAAI,CAAC,kBAAkB,CAACC,OAAO,cAAEvB,IAAA,CAAC9B,gBAAgB,GAAE,CAAE,CAAE,CAAC,cAChE8B,IAAA,CAAClE,KAAK,EAACwF,IAAI,CAAC,gBAAgB,CAACC,OAAO,cAAEvB,IAAA,CAAC9B,gBAAgB,GAAE,CAAE,CAAE,CAAC,cAC9D8B,IAAA,CAAClE,KAAK,EAACwF,IAAI,CAAC,qCAAqC,CAACC,OAAO,cAAEvB,IAAA,CAAC7B,sBAAsB,GAAE,CAAE,CAAE,CAAC,cACzF6B,IAAA,CAAClE,KAAK,EAACwF,IAAI,CAAC,cAAc,CAACC,OAAO,cAAEvB,IAAA,CAAC1B,oBAAoB,GAAE,CAAE,CAAE,CAAC,cAEhE0B,IAAA,CAAClE,KAAK,EAACwF,IAAI,CAAC,OAAO,CAACC,OAAO,cAAEvB,IAAA,CAACnD,cAAc,GAAE,CAAE,CAAE,CAAC,cACnDmD,IAAA,CAAClE,KAAK,EAACwF,IAAI,CAAC,eAAe,CAACC,OAAO,cAAEvB,IAAA,CAAClD,WAAW,GAAE,CAAE,CAAE,CAAC,cACxDkD,IAAA,CAAClE,KAAK,EAACwF,IAAI,CAAC,UAAU,CAACC,OAAO,cAAEvB,IAAA,CAACrC,OAAO,GAAE,CAAE,CAAE,CAAC,cAC/CqC,IAAA,CAAClE,KAAK,EAACwF,IAAI,CAAC,aAAa,CAACC,OAAO,cAAEvB,IAAA,CAACnC,UAAU,GAAE,CAAE,CAAE,CAAC,cACrDmC,IAAA,CAAClE,KAAK,EAACwF,IAAI,CAAC,YAAY,CAACC,OAAO,cAAEvB,IAAA,CAAClC,SAAS,GAAE,CAAE,CAAE,CAAC,cACnDkC,IAAA,CAAClE,KAAK,EAACwF,IAAI,CAAC,iBAAiB,CAACC,OAAO,cAAEvB,IAAA,CAACpC,cAAc,GAAE,CAAE,CAAE,CAAC,cAC7DoC,IAAA,CAAClE,KAAK,EAACwF,IAAI,CAAC,MAAM,CAACC,OAAO,cAAEvB,IAAA,CAACjD,aAAa,GAAE,CAAE,CAAE,CAAC,cACjDiD,IAAA,CAAClE,KAAK,EAACwF,IAAI,CAAC,cAAc,CAACC,OAAO,cAAEvB,IAAA,CAAChD,qBAAqB,GAAE,CAAE,CAAE,CAAC,cACjEgD,IAAA,CAAClE,KAAK,EAACwF,IAAI,CAAC,aAAa,CAACC,OAAO,cAAEvB,IAAA,CAACvC,gBAAgB,GAAE,CAAE,CAAE,CAAC,cAC3DuC,IAAA,CAAClE,KAAK,EAACwF,IAAI,CAAC,wBAAwB,CAACC,OAAO,cAAEvB,IAAA,CAAC/C,qBAAqB,GAAE,CAAE,CAAE,CAAC,cAC3E+C,IAAA,CAAClE,KAAK,EAACwF,IAAI,CAAC,SAAS,CAACC,OAAO,cAAEvB,IAAA,CAACtC,YAAY,GAAE,CAAE,CAAE,CAAC,cACnDsC,IAAA,CAAClE,KAAK,EAACwF,IAAI,CAAC,mBAAmB,CAACC,OAAO,cAAEvB,IAAA,CAACxC,gBAAgB,GAAE,CAAE,CAAE,CAAC,cACjEwC,IAAA,CAAClE,KAAK,EAACwF,IAAI,CAAC,UAAU,CAACC,OAAO,cAAEvB,IAAA,CAAC9C,cAAc,GAAE,CAAE,CAAE,CAAC,cACtD8C,IAAA,CAAClE,KAAK,EAACwF,IAAI,CAAC,kBAAkB,CAACC,OAAO,cAAEvB,IAAA,CAAC1C,eAAe,GAAE,CAAE,CAAE,CAAC,cAC/D0C,IAAA,CAAClE,KAAK,EAACwF,IAAI,CAAC,eAAe,CAACC,OAAO,cAAEvB,IAAA,CAAC7C,YAAY,GAAE,CAAE,CAAE,CAAC,cACzD6C,IAAA,CAAClE,KAAK,EAACwF,IAAI,CAAC,mBAAmB,CAACC,OAAO,cAAEvB,IAAA,CAAC5C,gBAAgB,GAAE,CAAE,CAAE,CAAC,cACjE4C,IAAA,CAAClE,KAAK,EAACwF,IAAI,CAAC,cAAc,CAACC,OAAO,cAAEvB,IAAA,CAAC3C,gBAAgB,GAAE,CAAE,CAAE,CAAC,cAC5D2C,IAAA,CAAClE,KAAK,EAACwF,IAAI,CAAC,uBAAuB,CAACC,OAAO,cAAEvB,IAAA,CAACzC,oBAAoB,GAAE,CAAE,CAAE,CAAC,EACpE,CAAC,cAGR2C,KAAA,CAACpE,KAAK,EACJwF,IAAI,CAAC,OAAO,CACZC,OAAO,cACLvB,IAAA,CAACG,cAAc,EAAAE,QAAA,cACbL,IAAA,CAAC3D,UAAU,GAAE,CAAC,CACA,CACjB,CAAAgE,QAAA,eAEDL,IAAA,CAAClE,KAAK,EAAC0F,KAAK,MAACD,OAAO,cAAEvB,IAAA,CAACzB,aAAa,GAAE,CAAE,CAAE,CAAC,cAC3CyB,IAAA,CAAClE,KAAK,EAACwF,IAAI,CAAC,WAAW,CAACC,OAAO,cAAEvB,IAAA,CAACzB,aAAa,GAAE,CAAE,CAAE,CAAC,cACtDyB,IAAA,CAAClE,KAAK,EAACwF,IAAI,CAAC,MAAM,CAACC,OAAO,cAAEvB,IAAA,CAACtB,QAAQ,GAAE,CAAE,CAAE,CAAC,cAC5CsB,IAAA,CAAClE,KAAK,EAACwF,IAAI,CAAC,eAAe,CAACC,OAAO,cAAEvB,IAAA,CAACtB,QAAQ,GAAE,CAAE,CAAE,CAAC,cACrDsB,IAAA,CAAClE,KAAK,EAACwF,IAAI,CAAC,eAAe,CAACC,OAAO,cAAEvB,IAAA,CAACrB,YAAY,GAAE,CAAE,CAAE,CAAC,cACzDqB,IAAA,CAAClE,KAAK,EAACwF,IAAI,CAAC,eAAe,CAACC,OAAO,cAAEvB,IAAA,CAACnB,YAAY,GAAE,CAAE,CAAE,CAAC,cACzDmB,IAAA,CAAClE,KAAK,EAACwF,IAAI,CAAC,iBAAiB,CAACC,OAAO,cAAEvB,IAAA,CAAClB,cAAc,GAAE,CAAE,CAAE,CAAC,cAC7DkB,IAAA,CAAClE,KAAK,EAACwF,IAAI,CAAC,aAAa,CAACC,OAAO,cAAEvB,IAAA,CAACjB,WAAW,GAAE,CAAE,CAAE,CAAC,cACtDiB,IAAA,CAAClE,KAAK,EAACwF,IAAI,CAAC,SAAS,CAACC,OAAO,cAAEvB,IAAA,CAACpB,OAAO,GAAE,CAAE,CAAE,CAAC,cAC9CoB,IAAA,CAAClE,KAAK,EAACwF,IAAI,CAAC,mBAAmB,CAACC,OAAO,cAAEvB,IAAA,CAACpB,OAAO,GAAE,CAAE,CAAE,CAAC,cACxDoB,IAAA,CAAClE,KAAK,EAACwF,IAAI,CAAC,iBAAiB,CAACC,OAAO,cAAEvB,IAAA,CAAChB,cAAc,GAAE,CAAE,CAAE,CAAC,cAC7DgB,IAAA,CAAClE,KAAK,EAACwF,IAAI,CAAC,SAAS,CAACC,OAAO,cAAEvB,IAAA,CAACf,OAAO,GAAE,CAAE,CAAE,CAAC,cAC9Ce,IAAA,CAAClE,KAAK,EAACwF,IAAI,CAAC,UAAU,CAACC,OAAO,cAAEvB,IAAA,CAACd,QAAQ,GAAE,CAAE,CAAE,CAAC,cAChDc,IAAA,CAAClE,KAAK,EAACwF,IAAI,CAAC,SAAS,CAACC,OAAO,cAAEvB,IAAA,CAACb,OAAO,GAAE,CAAE,CAAE,CAAC,cAC9Ca,IAAA,CAAClE,KAAK,EAACwF,IAAI,CAAC,iBAAiB,CAACC,OAAO,cAAEvB,IAAA,CAACZ,cAAc,GAAE,CAAE,CAAE,CAAC,cAC7DY,IAAA,CAAClE,KAAK,EAACwF,IAAI,CAAC,6BAA6B,CAACC,OAAO,cAAEvB,IAAA,CAACxB,aAAa,GAAE,CAAE,CAAE,CAAC,cACxEwB,IAAA,CAAClE,KAAK,EAACwF,IAAI,CAAC,0DAA0D,CAACC,OAAO,cAAEvB,IAAA,CAACvB,cAAc,GAAE,CAAE,CAAE,CAAC,cACtGuB,IAAA,CAAClE,KAAK,EAACwF,IAAI,CAAC,UAAU,CAACC,OAAO,cAAEvB,IAAA,CAAC1D,QAAQ,GAAE,CAAE,CAAE,CAAC,cAChD0D,IAAA,CAAClE,KAAK,EAACwF,IAAI,CAAC,YAAY,CAACC,OAAO,cAAEvB,IAAA,CAACH,UAAU,GAAE,CAAE,CAAE,CAAC,cACpDG,IAAA,CAAClE,KAAK,EAACwF,IAAI,CAAC,aAAa,CAACC,OAAO,cAAEvB,IAAA,CAACF,UAAU,GAAE,CAAE,CAAE,CAAC,cAErDE,IAAA,CAAClE,KAAK,EAACwF,IAAI,CAAC,SAAS,CAACC,OAAO,cAAEvB,IAAA,CAACX,UAAU,GAAE,CAAE,CAAE,CAAC,cACjDW,IAAA,CAAClE,KAAK,EAACwF,IAAI,CAAC,YAAY,CAACC,OAAO,cAAEvB,IAAA,CAACP,SAAS,GAAE,CAAE,CAAE,CAAC,cACnDO,IAAA,CAAClE,KAAK,EAACwF,IAAI,CAAC,mBAAmB,CAACC,OAAO,cAAEvB,IAAA,CAAC3B,aAAa,GAAE,CAAE,CAAE,CAAC,cAC9D2B,IAAA,CAAClE,KAAK,EAACwF,IAAI,CAAC,sBAAsB,CAACC,OAAO,cAAEvB,IAAA,CAACT,gBAAgB,GAAE,CAAE,CAAE,CAAC,cACpES,IAAA,CAAClE,KAAK,EAACwF,IAAI,CAAC,iBAAiB,CAACC,OAAO,cAAEvB,IAAA,CAACR,aAAa,GAAE,CAAE,CAAE,CAAC,cAE5DQ,IAAA,CAAClE,KAAK,EAACwF,IAAI,CAAC,QAAQ,CAACC,OAAO,cAAEvB,IAAA,CAAChE,QAAQ,EAAC+E,EAAE,CAAC,eAAe,CAACC,OAAO,MAAE,CAAE,CAAE,CAAC,cACzEhB,IAAA,CAAClE,KAAK,EAACwF,IAAI,CAAC,4BAA4B,CAACC,OAAO,cAAEvB,IAAA,CAAChE,QAAQ,EAAC+E,EAAE,CAAC,yBAAyB,CAACC,OAAO,MAAE,CAAE,CAAE,CAAC,cACvGhB,IAAA,CAAClE,KAAK,EAACwF,IAAI,CAAC,8BAA8B,CAACC,OAAO,cAAEvB,IAAA,CAAChE,QAAQ,EAAC+E,EAAE,CAAC,qCAAqC,CAACC,OAAO,MAAE,CAAE,CAAE,CAAC,cACrHhB,IAAA,CAAClE,KAAK,EAACwF,IAAI,CAAC,cAAc,CAACC,OAAO,cAAEvB,IAAA,CAAChE,QAAQ,EAAC+E,EAAE,CAAC,4BAA4B,CAACC,OAAO,MAAE,CAAE,CAAE,CAAC,cAC5FhB,IAAA,CAAClE,KAAK,EAACwF,IAAI,CAAC,gBAAgB,CAACC,OAAO,cAAEvB,IAAA,CAAChE,QAAQ,EAAC+E,EAAE,CAAC,uBAAuB,CAACC,OAAO,MAAE,CAAE,CAAE,CAAC,cACzFhB,IAAA,CAAClE,KAAK,EAACwF,IAAI,CAAC,UAAU,CAACC,OAAO,cAAEvB,IAAA,CAACN,QAAQ,GAAE,CAAE,CAAE,CAAC,cAChDM,IAAA,CAAClE,KAAK,EAACwF,IAAI,CAAC,QAAQ,CAACC,OAAO,cAAEvB,IAAA,CAACL,YAAY,GAAE,CAAE,CAAE,CAAC,cAClDK,IAAA,CAAClE,KAAK,EAACwF,IAAI,CAAC,gBAAgB,CAACC,OAAO,cAAEvB,IAAA,CAACJ,aAAa,GAAE,CAAE,CAAE,CAAC,EACtD,CAAC,cAGRI,IAAA,CAAClE,KAAK,EACJwF,IAAI,CAAC,GAAG,CACRC,OAAO,CACLhB,YAAY,CAACC,OAAO,CAAC,SAAS,CAAC,cAC7BR,IAAA,CAAChE,QAAQ,EAAC+E,EAAE,CAAC,kBAAkB,CAACC,OAAO,MAAE,CAAC,CACxCT,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC,EAAID,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC,cACrER,IAAA,CAAChE,QAAQ,EAAC+E,EAAE,CAAC,iBAAiB,CAACC,OAAO,MAAE,CAAC,cAEzChB,IAAA,CAAChE,QAAQ,EAAC+E,EAAE,CAAC,QAAQ,CAACC,OAAO,MAAE,CAElC,CACF,CAAC,EACI,CAAC,CACH,CAAC,CACN,CAAC,CACY,CAAC,CACR,CAAC,CACJ,CAAC,CAEjB,CAEA,cAAe,CAAAI,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}