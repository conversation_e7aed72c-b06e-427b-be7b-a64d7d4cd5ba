{"ast": null, "code": "var _jsxFileName = \"C:\\\\MAMP\\\\htdocs\\\\FanBet247\\\\frontend\\\\src\\\\pages\\\\TeamManagement.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport './TeamManagement.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst API_BASE_URL = '/backend';\nfunction TeamManagement() {\n  _s();\n  const [teams, setTeams] = useState([]);\n  const [newTeam, setNewTeam] = useState({\n    name: '',\n    logo: null\n  });\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  const [editingTeamId, setEditingTeamId] = useState(null);\n  const [editingTeam, setEditingTeam] = useState({\n    name: '',\n    logo: null\n  });\n  useEffect(() => {\n    fetchTeams();\n  }, []);\n  const fetchTeams = async () => {\n    try {\n      const response = await axios.get(`${API_BASE_URL}/handlers/team_management.php`);\n      setTeams(response.data.data);\n    } catch (err) {\n      setError('Failed to fetch teams');\n      setTeams([]);\n    }\n  };\n  const handleInputChange = e => {\n    const {\n      name,\n      value,\n      type\n    } = e.target;\n    setNewTeam(prev => ({\n      ...prev,\n      [name]: type === 'file' ? e.target.files[0] : value\n    }));\n  };\n  const handleEditInputChange = e => {\n    const {\n      name,\n      value,\n      type\n    } = e.target;\n    setEditingTeam(prev => ({\n      ...prev,\n      [name]: type === 'file' ? e.target.files[0] : value\n    }));\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setError('');\n    setSuccess('');\n    if (!newTeam.name || !newTeam.logo) {\n      setError('Team name and logo are required.');\n      return;\n    }\n\n    // File type validation\n    const allowedFileTypes = ['image/svg+xml', 'image/png', 'image/jpeg', 'image/jpg'];\n    if (!allowedFileTypes.includes(newTeam.logo.type)) {\n      setError('Only SVG, PNG, and JPG files are allowed.');\n      return;\n    }\n    try {\n      const formData = new FormData();\n      formData.append('name', newTeam.name);\n      formData.append('logo', newTeam.logo);\n      const response = await axios.post(`${API_BASE_URL}/handlers/team_management.php`, formData, {\n        headers: {\n          'Content-Type': 'multipart/form-data'\n        }\n      });\n      if (response.data.success) {\n        setSuccess('Team created successfully!');\n        fetchTeams(); // Refresh the team list\n\n        // Clear the form completely\n        setNewTeam({\n          name: '',\n          logo: null\n        });\n\n        // Clear the file input field\n        const fileInput = document.getElementById('logo');\n        if (fileInput) {\n          fileInput.value = '';\n        }\n\n        // Clear messages after 3 seconds\n        setTimeout(() => {\n          setSuccess('');\n          setError('');\n        }, 3000);\n      } else {\n        setError(response.data.message || 'Failed to create team');\n      }\n    } catch (err) {\n      setError('Failed to create team');\n    }\n  };\n  const handleDelete = async id => {\n    try {\n      await axios.delete(`${API_BASE_URL}/handlers/team_management.php?id=${id}`);\n      fetchTeams();\n    } catch (err) {\n      setError('Failed to delete team');\n    }\n  };\n  const handleEdit = async id => {\n    setEditingTeamId(id);\n    const teamToEdit = teams.find(team => team.id === id);\n    setEditingTeam({\n      name: teamToEdit.name,\n      logo: teamToEdit.logo\n    });\n  };\n  const handleUpdateTeam = async e => {\n    e.preventDefault();\n    try {\n      const formData = new FormData();\n      formData.append('name', editingTeam.name);\n      if (editingTeam.logo instanceof File) {\n        formData.append('logo', editingTeam.logo);\n      }\n      const response = await axios.post(`${API_BASE_URL}/handlers/update_team.php?id=${editingTeamId}`, formData, {\n        headers: {\n          'Content-Type': 'multipart/form-data'\n        }\n      });\n      if (response.data.success) {\n        setSuccess('Team updated successfully!');\n        fetchTeams();\n        setEditingTeamId(null);\n        setEditingTeam({\n          name: '',\n          logo: null\n        });\n      } else {\n        setError(response.data.message || 'Failed to update team');\n      }\n    } catch (err) {\n      setError('Failed to update team');\n      console.error(err);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"team-management-container\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"team-management-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"team-management-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Add New Team\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 11\n        }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"error-message\",\n          style: {\n            backgroundColor: '#fee2e2',\n            color: '#b91c1c',\n            padding: '12px 16px',\n            borderRadius: '6px',\n            marginBottom: '16px',\n            border: '1px solid #fecaca',\n            display: 'flex',\n            alignItems: 'center',\n            gap: '8px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              backgroundColor: '#dc2626',\n              color: 'white',\n              width: '20px',\n              height: '20px',\n              borderRadius: '50%',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              fontSize: '12px',\n              fontWeight: 'bold'\n            },\n            children: \"!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 15\n          }, this), error]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 13\n        }, this), success && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"success-message\",\n          style: {\n            backgroundColor: '#dcfce7',\n            color: '#166534',\n            padding: '12px 16px',\n            borderRadius: '6px',\n            marginBottom: '16px',\n            border: '1px solid #bbf7d0',\n            display: 'flex',\n            alignItems: 'center',\n            gap: '8px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              backgroundColor: '#16a34a',\n              color: 'white',\n              width: '20px',\n              height: '20px',\n              borderRadius: '50%',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              fontSize: '12px',\n              fontWeight: 'bold'\n            },\n            children: \"\\u2713\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 15\n          }, this), success]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          encType: \"multipart/form-data\",\n          className: \"team-form\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"name\",\n              children: \"Team Name:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"name\",\n              name: \"name\",\n              value: newTeam.name,\n              onChange: handleInputChange,\n              required: true,\n              className: \"form-input\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"logo\",\n              children: \"Team Logo:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"file\",\n              id: \"logo\",\n              name: \"logo\",\n              onChange: handleInputChange,\n              accept: \".svg,.png,.jpg,.jpeg\",\n              required: true,\n              className: \"form-input\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n              className: \"file-hint\",\n              children: \"Only SVG, PNG, and JPG files are allowed\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 221,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            className: \"submit-button\",\n            style: {\n              backgroundColor: '#166534',\n              color: 'white'\n            },\n            children: \"Add Team\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 141,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"team-management-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Existing Teams\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 228,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"teams-table-container\",\n          children: /*#__PURE__*/_jsxDEV(\"table\", {\n            className: \"teams-table\",\n            children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n              style: {\n                backgroundColor: '#166534',\n                color: 'white'\n              },\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"ID\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 233,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Logo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 234,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 235,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Actions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 236,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n              children: teams.map(team => /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  children: team.id\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 242,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: `${API_BASE_URL}/${team.logo}`,\n                    alt: team.name,\n                    className: \"team-logo-image\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 244,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 243,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: team.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 250,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"action-buttons\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleDelete(team.id),\n                    className: \"delete-button\",\n                    style: {\n                      backgroundColor: '#dc2626',\n                      color: 'white',\n                      marginRight: '8px'\n                    },\n                    children: \"Delete\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 252,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleEdit(team.id),\n                    className: \"edit-button\",\n                    style: {\n                      backgroundColor: '#166534',\n                      color: 'white'\n                    },\n                    children: \"Edit\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 253,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 251,\n                  columnNumber: 21\n                }, this)]\n              }, team.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 239,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 229,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 227,\n        columnNumber: 9\n      }, this), editingTeamId && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-overlay\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Edit Team\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handleUpdateTeam,\n            encType: \"multipart/form-data\",\n            className: \"team-form\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"editName\",\n                children: \"Team Name:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 268,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                id: \"editName\",\n                name: \"name\",\n                value: editingTeam.name,\n                onChange: handleEditInputChange,\n                required: true,\n                className: \"form-input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 269,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 267,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"editLogo\",\n                children: \"Team Logo:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 280,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"file\",\n                id: \"editLogo\",\n                name: \"logo\",\n                onChange: handleEditInputChange,\n                accept: \".svg,.png,.jpg,.jpeg\",\n                className: \"form-input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 281,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                className: \"file-hint\",\n                children: \"Only SVG, PNG, and JPG files are allowed\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 289,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"modal-buttons\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"submit\",\n                className: \"update-button\",\n                style: {\n                  backgroundColor: '#166534',\n                  color: 'white',\n                  marginRight: '8px'\n                },\n                children: \"Update Team\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 292,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                onClick: () => setEditingTeamId(null),\n                className: \"cancel-button\",\n                style: {\n                  backgroundColor: '#6b7280',\n                  color: 'white'\n                },\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 293,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 291,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 263,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 140,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 139,\n    columnNumber: 5\n  }, this);\n}\n_s(TeamManagement, \"xIIOemUlEJkC4W9805rNJ7vjLOg=\");\n_c = TeamManagement;\nexport default TeamManagement;\nvar _c;\n$RefreshReg$(_c, \"TeamManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "jsxDEV", "_jsxDEV", "API_BASE_URL", "TeamManagement", "_s", "teams", "setTeams", "newTeam", "setNewTeam", "name", "logo", "error", "setError", "success", "setSuccess", "editingTeamId", "setEditingTeamId", "editingTeam", "setEditingTeam", "fetchTeams", "response", "get", "data", "err", "handleInputChange", "e", "value", "type", "target", "prev", "files", "handleEditInputChange", "handleSubmit", "preventDefault", "allowedFileTypes", "includes", "formData", "FormData", "append", "post", "headers", "fileInput", "document", "getElementById", "setTimeout", "message", "handleDelete", "id", "delete", "handleEdit", "teamToEdit", "find", "team", "handleUpdateTeam", "File", "console", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "backgroundColor", "color", "padding", "borderRadius", "marginBottom", "border", "display", "alignItems", "gap", "width", "height", "justifyContent", "fontSize", "fontWeight", "onSubmit", "encType", "htmlFor", "onChange", "required", "accept", "map", "src", "alt", "onClick", "marginRight", "_c", "$RefreshReg$"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/pages/TeamManagement.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport './TeamManagement.css';\n\nconst API_BASE_URL = '/backend';\n\nfunction TeamManagement() {\n  const [teams, setTeams] = useState([]);\n  const [newTeam, setNewTeam] = useState({ name: '', logo: null });\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  const [editingTeamId, setEditingTeamId] = useState(null);\n  const [editingTeam, setEditingTeam] = useState({ name: '', logo: null });\n\n  useEffect(() => {\n    fetchTeams();\n  }, []);\n\n  const fetchTeams = async () => {\n    try {\n      const response = await axios.get(`${API_BASE_URL}/handlers/team_management.php`);\n      setTeams(response.data.data);\n    } catch (err) {\n      setError('Failed to fetch teams');\n      setTeams([]);\n    }\n  };\n\n  const handleInputChange = (e) => {\n    const { name, value, type } = e.target;\n    setNewTeam(prev => ({\n      ...prev,\n      [name]: type === 'file' ? e.target.files[0] : value\n    }));\n  };\n\n  const handleEditInputChange = (e) => {\n    const { name, value, type } = e.target;\n    setEditingTeam(prev => ({\n      ...prev,\n      [name]: type === 'file' ? e.target.files[0] : value\n    }));\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setError('');\n    setSuccess('');\n\n    if (!newTeam.name || !newTeam.logo) {\n      setError('Team name and logo are required.');\n      return;\n    }\n\n    // File type validation\n    const allowedFileTypes = ['image/svg+xml', 'image/png', 'image/jpeg', 'image/jpg'];\n    if (!allowedFileTypes.includes(newTeam.logo.type)) {\n      setError('Only SVG, PNG, and JPG files are allowed.');\n      return;\n    }\n\n    try {\n      const formData = new FormData();\n      formData.append('name', newTeam.name);\n      formData.append('logo', newTeam.logo);\n\n      const response = await axios.post(`${API_BASE_URL}/handlers/team_management.php`, formData, {\n        headers: { 'Content-Type': 'multipart/form-data' }\n      });\n      if (response.data.success) {\n        setSuccess('Team created successfully!');\n        fetchTeams(); // Refresh the team list\n\n        // Clear the form completely\n        setNewTeam({ name: '', logo: null });\n\n        // Clear the file input field\n        const fileInput = document.getElementById('logo');\n        if (fileInput) {\n          fileInput.value = '';\n        }\n\n        // Clear messages after 3 seconds\n        setTimeout(() => {\n          setSuccess('');\n          setError('');\n        }, 3000);\n      } else {\n        setError(response.data.message || 'Failed to create team');\n      }\n    } catch (err) {\n      setError('Failed to create team');\n    }\n  };\n\n  const handleDelete = async (id) => {\n    try {\n      await axios.delete(`${API_BASE_URL}/handlers/team_management.php?id=${id}`);\n      fetchTeams();\n    } catch (err) {\n      setError('Failed to delete team');\n    }\n  };\n\n  const handleEdit = async (id) => {\n    setEditingTeamId(id);\n    const teamToEdit = teams.find(team => team.id === id);\n    setEditingTeam({ name: teamToEdit.name, logo: teamToEdit.logo });\n  };\n\n  const handleUpdateTeam = async (e) => {\n    e.preventDefault();\n    try {\n      const formData = new FormData();\n      formData.append('name', editingTeam.name);\n      if (editingTeam.logo instanceof File) {\n        formData.append('logo', editingTeam.logo);\n      }\n\n      const response = await axios.post(`${API_BASE_URL}/handlers/update_team.php?id=${editingTeamId}`, formData, {\n        headers: { 'Content-Type': 'multipart/form-data' }\n      });\n\n      if (response.data.success) {\n        setSuccess('Team updated successfully!');\n        fetchTeams();\n        setEditingTeamId(null);\n        setEditingTeam({ name: '', logo: null });\n      } else {\n        setError(response.data.message || 'Failed to update team');\n      }\n    } catch (err) {\n      setError('Failed to update team');\n      console.error(err);\n    }\n  };\n\n  return (\n    <div className=\"team-management-container\">\n      <div className=\"team-management-content\">\n        <div className=\"team-management-card\">\n          <h2>Add New Team</h2>\n          {error && (\n            <div className=\"error-message\" style={{\n              backgroundColor: '#fee2e2',\n              color: '#b91c1c',\n              padding: '12px 16px',\n              borderRadius: '6px',\n              marginBottom: '16px',\n              border: '1px solid #fecaca',\n              display: 'flex',\n              alignItems: 'center',\n              gap: '8px'\n            }}>\n              <span style={{\n                backgroundColor: '#dc2626',\n                color: 'white',\n                width: '20px',\n                height: '20px',\n                borderRadius: '50%',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                fontSize: '12px',\n                fontWeight: 'bold'\n              }}>!</span>\n              {error}\n            </div>\n          )}\n          {success && (\n            <div className=\"success-message\" style={{\n              backgroundColor: '#dcfce7',\n              color: '#166534',\n              padding: '12px 16px',\n              borderRadius: '6px',\n              marginBottom: '16px',\n              border: '1px solid #bbf7d0',\n              display: 'flex',\n              alignItems: 'center',\n              gap: '8px'\n            }}>\n              <span style={{\n                backgroundColor: '#16a34a',\n                color: 'white',\n                width: '20px',\n                height: '20px',\n                borderRadius: '50%',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                fontSize: '12px',\n                fontWeight: 'bold'\n              }}>✓</span>\n              {success}\n            </div>\n          )}\n          <form onSubmit={handleSubmit} encType=\"multipart/form-data\" className=\"team-form\">\n            <div className=\"form-group\">\n              <label htmlFor=\"name\">Team Name:</label>\n              <input\n                type=\"text\"\n                id=\"name\"\n                name=\"name\"\n                value={newTeam.name}\n                onChange={handleInputChange}\n                required\n                className=\"form-input\"\n              />\n            </div>\n            <div className=\"form-group\">\n              <label htmlFor=\"logo\">Team Logo:</label>\n              <input\n                type=\"file\"\n                id=\"logo\"\n                name=\"logo\"\n                onChange={handleInputChange}\n                accept=\".svg,.png,.jpg,.jpeg\"\n                required\n                className=\"form-input\"\n              />\n              <small className=\"file-hint\">Only SVG, PNG, and JPG files are allowed</small>\n            </div>\n            <button type=\"submit\" className=\"submit-button\" style={{ backgroundColor: '#166534', color: 'white' }}>Add Team</button>\n          </form>\n        </div>\n\n        <div className=\"team-management-card\">\n          <h2>Existing Teams</h2>\n          <div className=\"teams-table-container\">\n            <table className=\"teams-table\">\n              <thead style={{ backgroundColor: '#166534', color: 'white' }}>\n                <tr>\n                  <th>ID</th>\n                  <th>Logo</th>\n                  <th>Name</th>\n                  <th>Actions</th>\n                </tr>\n              </thead>\n              <tbody>\n                {teams.map(team => (\n                  <tr key={team.id}>\n                    <td>{team.id}</td>\n                    <td>\n                      <img\n                        src={`${API_BASE_URL}/${team.logo}`}\n                        alt={team.name}\n                        className=\"team-logo-image\"\n                      />\n                    </td>\n                    <td>{team.name}</td>\n                    <td className=\"action-buttons\">\n                      <button onClick={() => handleDelete(team.id)} className=\"delete-button\" style={{ backgroundColor: '#dc2626', color: 'white', marginRight: '8px' }}>Delete</button>\n                      <button onClick={() => handleEdit(team.id)} className=\"edit-button\" style={{ backgroundColor: '#166534', color: 'white' }}>Edit</button>\n                    </td>\n                  </tr>\n                ))}\n              </tbody>\n            </table>\n          </div>\n        </div>\n\n        {editingTeamId && (\n          <div className=\"modal-overlay\">\n            <div className=\"modal-content\">\n              <h3>Edit Team</h3>\n              <form onSubmit={handleUpdateTeam} encType=\"multipart/form-data\" className=\"team-form\">\n                <div className=\"form-group\">\n                  <label htmlFor=\"editName\">Team Name:</label>\n                  <input\n                    type=\"text\"\n                    id=\"editName\"\n                    name=\"name\"\n                    value={editingTeam.name}\n                    onChange={handleEditInputChange}\n                    required\n                    className=\"form-input\"\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label htmlFor=\"editLogo\">Team Logo:</label>\n                  <input\n                    type=\"file\"\n                    id=\"editLogo\"\n                    name=\"logo\"\n                    onChange={handleEditInputChange}\n                    accept=\".svg,.png,.jpg,.jpeg\"\n                    className=\"form-input\"\n                  />\n                  <small className=\"file-hint\">Only SVG, PNG, and JPG files are allowed</small>\n                </div>\n                <div className=\"modal-buttons\">\n                  <button type=\"submit\" className=\"update-button\" style={{ backgroundColor: '#166534', color: 'white', marginRight: '8px' }}>Update Team</button>\n                  <button type=\"button\" onClick={() => setEditingTeamId(null)} className=\"cancel-button\" style={{ backgroundColor: '#6b7280', color: 'white' }}>Cancel</button>\n                </div>\n              </form>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n\nexport default TeamManagement;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAO,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9B,MAAMC,YAAY,GAAG,UAAU;AAE/B,SAASC,cAAcA,CAAA,EAAG;EAAAC,EAAA;EACxB,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGT,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACU,OAAO,EAAEC,UAAU,CAAC,GAAGX,QAAQ,CAAC;IAAEY,IAAI,EAAE,EAAE;IAAEC,IAAI,EAAE;EAAK,CAAC,CAAC;EAChE,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACgB,OAAO,EAAEC,UAAU,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACkB,aAAa,EAAEC,gBAAgB,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACoB,WAAW,EAAEC,cAAc,CAAC,GAAGrB,QAAQ,CAAC;IAAEY,IAAI,EAAE,EAAE;IAAEC,IAAI,EAAE;EAAK,CAAC,CAAC;EAExEZ,SAAS,CAAC,MAAM;IACdqB,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMrB,KAAK,CAACsB,GAAG,CAAC,GAAGnB,YAAY,+BAA+B,CAAC;MAChFI,QAAQ,CAACc,QAAQ,CAACE,IAAI,CAACA,IAAI,CAAC;IAC9B,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZX,QAAQ,CAAC,uBAAuB,CAAC;MACjCN,QAAQ,CAAC,EAAE,CAAC;IACd;EACF,CAAC;EAED,MAAMkB,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEhB,IAAI;MAAEiB,KAAK;MAAEC;IAAK,CAAC,GAAGF,CAAC,CAACG,MAAM;IACtCpB,UAAU,CAACqB,IAAI,KAAK;MAClB,GAAGA,IAAI;MACP,CAACpB,IAAI,GAAGkB,IAAI,KAAK,MAAM,GAAGF,CAAC,CAACG,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,GAAGJ;IAChD,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMK,qBAAqB,GAAIN,CAAC,IAAK;IACnC,MAAM;MAAEhB,IAAI;MAAEiB,KAAK;MAAEC;IAAK,CAAC,GAAGF,CAAC,CAACG,MAAM;IACtCV,cAAc,CAACW,IAAI,KAAK;MACtB,GAAGA,IAAI;MACP,CAACpB,IAAI,GAAGkB,IAAI,KAAK,MAAM,GAAGF,CAAC,CAACG,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,GAAGJ;IAChD,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMM,YAAY,GAAG,MAAOP,CAAC,IAAK;IAChCA,CAAC,CAACQ,cAAc,CAAC,CAAC;IAClBrB,QAAQ,CAAC,EAAE,CAAC;IACZE,UAAU,CAAC,EAAE,CAAC;IAEd,IAAI,CAACP,OAAO,CAACE,IAAI,IAAI,CAACF,OAAO,CAACG,IAAI,EAAE;MAClCE,QAAQ,CAAC,kCAAkC,CAAC;MAC5C;IACF;;IAEA;IACA,MAAMsB,gBAAgB,GAAG,CAAC,eAAe,EAAE,WAAW,EAAE,YAAY,EAAE,WAAW,CAAC;IAClF,IAAI,CAACA,gBAAgB,CAACC,QAAQ,CAAC5B,OAAO,CAACG,IAAI,CAACiB,IAAI,CAAC,EAAE;MACjDf,QAAQ,CAAC,2CAA2C,CAAC;MACrD;IACF;IAEA,IAAI;MACF,MAAMwB,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAE/B,OAAO,CAACE,IAAI,CAAC;MACrC2B,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAE/B,OAAO,CAACG,IAAI,CAAC;MAErC,MAAMU,QAAQ,GAAG,MAAMrB,KAAK,CAACwC,IAAI,CAAC,GAAGrC,YAAY,+BAA+B,EAAEkC,QAAQ,EAAE;QAC1FI,OAAO,EAAE;UAAE,cAAc,EAAE;QAAsB;MACnD,CAAC,CAAC;MACF,IAAIpB,QAAQ,CAACE,IAAI,CAACT,OAAO,EAAE;QACzBC,UAAU,CAAC,4BAA4B,CAAC;QACxCK,UAAU,CAAC,CAAC,CAAC,CAAC;;QAEd;QACAX,UAAU,CAAC;UAAEC,IAAI,EAAE,EAAE;UAAEC,IAAI,EAAE;QAAK,CAAC,CAAC;;QAEpC;QACA,MAAM+B,SAAS,GAAGC,QAAQ,CAACC,cAAc,CAAC,MAAM,CAAC;QACjD,IAAIF,SAAS,EAAE;UACbA,SAAS,CAACf,KAAK,GAAG,EAAE;QACtB;;QAEA;QACAkB,UAAU,CAAC,MAAM;UACf9B,UAAU,CAAC,EAAE,CAAC;UACdF,QAAQ,CAAC,EAAE,CAAC;QACd,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,MAAM;QACLA,QAAQ,CAACQ,QAAQ,CAACE,IAAI,CAACuB,OAAO,IAAI,uBAAuB,CAAC;MAC5D;IACF,CAAC,CAAC,OAAOtB,GAAG,EAAE;MACZX,QAAQ,CAAC,uBAAuB,CAAC;IACnC;EACF,CAAC;EAED,MAAMkC,YAAY,GAAG,MAAOC,EAAE,IAAK;IACjC,IAAI;MACF,MAAMhD,KAAK,CAACiD,MAAM,CAAC,GAAG9C,YAAY,oCAAoC6C,EAAE,EAAE,CAAC;MAC3E5B,UAAU,CAAC,CAAC;IACd,CAAC,CAAC,OAAOI,GAAG,EAAE;MACZX,QAAQ,CAAC,uBAAuB,CAAC;IACnC;EACF,CAAC;EAED,MAAMqC,UAAU,GAAG,MAAOF,EAAE,IAAK;IAC/B/B,gBAAgB,CAAC+B,EAAE,CAAC;IACpB,MAAMG,UAAU,GAAG7C,KAAK,CAAC8C,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACL,EAAE,KAAKA,EAAE,CAAC;IACrD7B,cAAc,CAAC;MAAET,IAAI,EAAEyC,UAAU,CAACzC,IAAI;MAAEC,IAAI,EAAEwC,UAAU,CAACxC;IAAK,CAAC,CAAC;EAClE,CAAC;EAED,MAAM2C,gBAAgB,GAAG,MAAO5B,CAAC,IAAK;IACpCA,CAAC,CAACQ,cAAc,CAAC,CAAC;IAClB,IAAI;MACF,MAAMG,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAErB,WAAW,CAACR,IAAI,CAAC;MACzC,IAAIQ,WAAW,CAACP,IAAI,YAAY4C,IAAI,EAAE;QACpClB,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAErB,WAAW,CAACP,IAAI,CAAC;MAC3C;MAEA,MAAMU,QAAQ,GAAG,MAAMrB,KAAK,CAACwC,IAAI,CAAC,GAAGrC,YAAY,gCAAgCa,aAAa,EAAE,EAAEqB,QAAQ,EAAE;QAC1GI,OAAO,EAAE;UAAE,cAAc,EAAE;QAAsB;MACnD,CAAC,CAAC;MAEF,IAAIpB,QAAQ,CAACE,IAAI,CAACT,OAAO,EAAE;QACzBC,UAAU,CAAC,4BAA4B,CAAC;QACxCK,UAAU,CAAC,CAAC;QACZH,gBAAgB,CAAC,IAAI,CAAC;QACtBE,cAAc,CAAC;UAAET,IAAI,EAAE,EAAE;UAAEC,IAAI,EAAE;QAAK,CAAC,CAAC;MAC1C,CAAC,MAAM;QACLE,QAAQ,CAACQ,QAAQ,CAACE,IAAI,CAACuB,OAAO,IAAI,uBAAuB,CAAC;MAC5D;IACF,CAAC,CAAC,OAAOtB,GAAG,EAAE;MACZX,QAAQ,CAAC,uBAAuB,CAAC;MACjC2C,OAAO,CAAC5C,KAAK,CAACY,GAAG,CAAC;IACpB;EACF,CAAC;EAED,oBACEtB,OAAA;IAAKuD,SAAS,EAAC,2BAA2B;IAAAC,QAAA,eACxCxD,OAAA;MAAKuD,SAAS,EAAC,yBAAyB;MAAAC,QAAA,gBACtCxD,OAAA;QAAKuD,SAAS,EAAC,sBAAsB;QAAAC,QAAA,gBACnCxD,OAAA;UAAAwD,QAAA,EAAI;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EACpBlD,KAAK,iBACJV,OAAA;UAAKuD,SAAS,EAAC,eAAe;UAACM,KAAK,EAAE;YACpCC,eAAe,EAAE,SAAS;YAC1BC,KAAK,EAAE,SAAS;YAChBC,OAAO,EAAE,WAAW;YACpBC,YAAY,EAAE,KAAK;YACnBC,YAAY,EAAE,MAAM;YACpBC,MAAM,EAAE,mBAAmB;YAC3BC,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBC,GAAG,EAAE;UACP,CAAE;UAAAd,QAAA,gBACAxD,OAAA;YAAM6D,KAAK,EAAE;cACXC,eAAe,EAAE,SAAS;cAC1BC,KAAK,EAAE,OAAO;cACdQ,KAAK,EAAE,MAAM;cACbC,MAAM,EAAE,MAAM;cACdP,YAAY,EAAE,KAAK;cACnBG,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBI,cAAc,EAAE,QAAQ;cACxBC,QAAQ,EAAE,MAAM;cAChBC,UAAU,EAAE;YACd,CAAE;YAAAnB,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,EACVlD,KAAK;QAAA;UAAA+C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EACAhD,OAAO,iBACNZ,OAAA;UAAKuD,SAAS,EAAC,iBAAiB;UAACM,KAAK,EAAE;YACtCC,eAAe,EAAE,SAAS;YAC1BC,KAAK,EAAE,SAAS;YAChBC,OAAO,EAAE,WAAW;YACpBC,YAAY,EAAE,KAAK;YACnBC,YAAY,EAAE,MAAM;YACpBC,MAAM,EAAE,mBAAmB;YAC3BC,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBC,GAAG,EAAE;UACP,CAAE;UAAAd,QAAA,gBACAxD,OAAA;YAAM6D,KAAK,EAAE;cACXC,eAAe,EAAE,SAAS;cAC1BC,KAAK,EAAE,OAAO;cACdQ,KAAK,EAAE,MAAM;cACbC,MAAM,EAAE,MAAM;cACdP,YAAY,EAAE,KAAK;cACnBG,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBI,cAAc,EAAE,QAAQ;cACxBC,QAAQ,EAAE,MAAM;cAChBC,UAAU,EAAE;YACd,CAAE;YAAAnB,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,EACVhD,OAAO;QAAA;UAAA6C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CACN,eACD5D,OAAA;UAAM4E,QAAQ,EAAE7C,YAAa;UAAC8C,OAAO,EAAC,qBAAqB;UAACtB,SAAS,EAAC,WAAW;UAAAC,QAAA,gBAC/ExD,OAAA;YAAKuD,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBxD,OAAA;cAAO8E,OAAO,EAAC,MAAM;cAAAtB,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACxC5D,OAAA;cACE0B,IAAI,EAAC,MAAM;cACXoB,EAAE,EAAC,MAAM;cACTtC,IAAI,EAAC,MAAM;cACXiB,KAAK,EAAEnB,OAAO,CAACE,IAAK;cACpBuE,QAAQ,EAAExD,iBAAkB;cAC5ByD,QAAQ;cACRzB,SAAS,EAAC;YAAY;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACN5D,OAAA;YAAKuD,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBxD,OAAA;cAAO8E,OAAO,EAAC,MAAM;cAAAtB,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACxC5D,OAAA;cACE0B,IAAI,EAAC,MAAM;cACXoB,EAAE,EAAC,MAAM;cACTtC,IAAI,EAAC,MAAM;cACXuE,QAAQ,EAAExD,iBAAkB;cAC5B0D,MAAM,EAAC,sBAAsB;cAC7BD,QAAQ;cACRzB,SAAS,EAAC;YAAY;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC,eACF5D,OAAA;cAAOuD,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAwC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1E,CAAC,eACN5D,OAAA;YAAQ0B,IAAI,EAAC,QAAQ;YAAC6B,SAAS,EAAC,eAAe;YAACM,KAAK,EAAE;cAAEC,eAAe,EAAE,SAAS;cAAEC,KAAK,EAAE;YAAQ,CAAE;YAAAP,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAEN5D,OAAA;QAAKuD,SAAS,EAAC,sBAAsB;QAAAC,QAAA,gBACnCxD,OAAA;UAAAwD,QAAA,EAAI;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvB5D,OAAA;UAAKuD,SAAS,EAAC,uBAAuB;UAAAC,QAAA,eACpCxD,OAAA;YAAOuD,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC5BxD,OAAA;cAAO6D,KAAK,EAAE;gBAAEC,eAAe,EAAE,SAAS;gBAAEC,KAAK,EAAE;cAAQ,CAAE;cAAAP,QAAA,eAC3DxD,OAAA;gBAAAwD,QAAA,gBACExD,OAAA;kBAAAwD,QAAA,EAAI;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACX5D,OAAA;kBAAAwD,QAAA,EAAI;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACb5D,OAAA;kBAAAwD,QAAA,EAAI;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACb5D,OAAA;kBAAAwD,QAAA,EAAI;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACR5D,OAAA;cAAAwD,QAAA,EACGpD,KAAK,CAAC8E,GAAG,CAAC/B,IAAI,iBACbnD,OAAA;gBAAAwD,QAAA,gBACExD,OAAA;kBAAAwD,QAAA,EAAKL,IAAI,CAACL;gBAAE;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAClB5D,OAAA;kBAAAwD,QAAA,eACExD,OAAA;oBACEmF,GAAG,EAAE,GAAGlF,YAAY,IAAIkD,IAAI,CAAC1C,IAAI,EAAG;oBACpC2E,GAAG,EAAEjC,IAAI,CAAC3C,IAAK;oBACf+C,SAAS,EAAC;kBAAiB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,eACL5D,OAAA;kBAAAwD,QAAA,EAAKL,IAAI,CAAC3C;gBAAI;kBAAAiD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACpB5D,OAAA;kBAAIuD,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC5BxD,OAAA;oBAAQqF,OAAO,EAAEA,CAAA,KAAMxC,YAAY,CAACM,IAAI,CAACL,EAAE,CAAE;oBAACS,SAAS,EAAC,eAAe;oBAACM,KAAK,EAAE;sBAAEC,eAAe,EAAE,SAAS;sBAAEC,KAAK,EAAE,OAAO;sBAAEuB,WAAW,EAAE;oBAAM,CAAE;oBAAA9B,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAClK5D,OAAA;oBAAQqF,OAAO,EAAEA,CAAA,KAAMrC,UAAU,CAACG,IAAI,CAACL,EAAE,CAAE;oBAACS,SAAS,EAAC,aAAa;oBAACM,KAAK,EAAE;sBAAEC,eAAe,EAAE,SAAS;sBAAEC,KAAK,EAAE;oBAAQ,CAAE;oBAAAP,QAAA,EAAC;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtI,CAAC;cAAA,GAbET,IAAI,CAACL,EAAE;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAcZ,CACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAEL9C,aAAa,iBACZd,OAAA;QAAKuD,SAAS,EAAC,eAAe;QAAAC,QAAA,eAC5BxD,OAAA;UAAKuD,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BxD,OAAA;YAAAwD,QAAA,EAAI;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClB5D,OAAA;YAAM4E,QAAQ,EAAExB,gBAAiB;YAACyB,OAAO,EAAC,qBAAqB;YAACtB,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACnFxD,OAAA;cAAKuD,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBxD,OAAA;gBAAO8E,OAAO,EAAC,UAAU;gBAAAtB,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC5C5D,OAAA;gBACE0B,IAAI,EAAC,MAAM;gBACXoB,EAAE,EAAC,UAAU;gBACbtC,IAAI,EAAC,MAAM;gBACXiB,KAAK,EAAET,WAAW,CAACR,IAAK;gBACxBuE,QAAQ,EAAEjD,qBAAsB;gBAChCkD,QAAQ;gBACRzB,SAAS,EAAC;cAAY;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN5D,OAAA;cAAKuD,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBxD,OAAA;gBAAO8E,OAAO,EAAC,UAAU;gBAAAtB,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC5C5D,OAAA;gBACE0B,IAAI,EAAC,MAAM;gBACXoB,EAAE,EAAC,UAAU;gBACbtC,IAAI,EAAC,MAAM;gBACXuE,QAAQ,EAAEjD,qBAAsB;gBAChCmD,MAAM,EAAC,sBAAsB;gBAC7B1B,SAAS,EAAC;cAAY;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB,CAAC,eACF5D,OAAA;gBAAOuD,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAC;cAAwC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1E,CAAC,eACN5D,OAAA;cAAKuD,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5BxD,OAAA;gBAAQ0B,IAAI,EAAC,QAAQ;gBAAC6B,SAAS,EAAC,eAAe;gBAACM,KAAK,EAAE;kBAAEC,eAAe,EAAE,SAAS;kBAAEC,KAAK,EAAE,OAAO;kBAAEuB,WAAW,EAAE;gBAAM,CAAE;gBAAA9B,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC/I5D,OAAA;gBAAQ0B,IAAI,EAAC,QAAQ;gBAAC2D,OAAO,EAAEA,CAAA,KAAMtE,gBAAgB,CAAC,IAAI,CAAE;gBAACwC,SAAS,EAAC,eAAe;gBAACM,KAAK,EAAE;kBAAEC,eAAe,EAAE,SAAS;kBAAEC,KAAK,EAAE;gBAAQ,CAAE;gBAAAP,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1J,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACzD,EAAA,CAvSQD,cAAc;AAAAqF,EAAA,GAAdrF,cAAc;AAySvB,eAAeA,cAAc;AAAC,IAAAqF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}