{"ast": null, "code": "import React,{useState,useEffect}from'react';import axios from'axios';import'./TeamManagement.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const API_BASE_URL='/backend';function TeamManagement(){const[teams,setTeams]=useState([]);const[newTeam,setNewTeam]=useState({name:'',logo:null});const[error,setError]=useState('');const[success,setSuccess]=useState('');const[editingTeamId,setEditingTeamId]=useState(null);const[editingTeam,setEditingTeam]=useState({name:'',logo:null});useEffect(()=>{fetchTeams();},[]);const fetchTeams=async()=>{try{const response=await axios.get(`${API_BASE_URL}/handlers/team_management.php`);setTeams(response.data.data);}catch(err){setError('Failed to fetch teams');setTeams([]);}};const handleInputChange=e=>{const{name,value,type}=e.target;setNewTeam(prev=>({...prev,[name]:type==='file'?e.target.files[0]:value}));};const handleEditInputChange=e=>{const{name,value,type}=e.target;setEditingTeam(prev=>({...prev,[name]:type==='file'?e.target.files[0]:value}));};const handleSubmit=async e=>{e.preventDefault();setError('');setSuccess('');if(!newTeam.name||!newTeam.logo){setError('Team name and logo are required.');return;}// File type validation\nconst allowedFileTypes=['image/svg+xml','image/png','image/jpeg','image/jpg'];if(!allowedFileTypes.includes(newTeam.logo.type)){setError('Only SVG, PNG, and JPG files are allowed.');return;}try{const formData=new FormData();formData.append('name',newTeam.name);formData.append('logo',newTeam.logo);const response=await axios.post(`${API_BASE_URL}/handlers/team_management.php`,formData,{headers:{'Content-Type':'multipart/form-data'}});if(response.data.success){setSuccess('Team created successfully!');// Clear the form completely first\nsetNewTeam({name:'',logo:null});// Clear the file input field\nconst fileInput=document.getElementById('logo');if(fileInput){fileInput.value='';}// Force refresh the team list after a short delay to ensure backend processing is complete\nsetTimeout(()=>{fetchTeams();},100);// Clear messages after 3 seconds\nsetTimeout(()=>{setSuccess('');setError('');},3000);}else{setError(response.data.message||'Failed to create team');}}catch(err){setError('Failed to create team');}};const handleDelete=async id=>{try{await axios.delete(`${API_BASE_URL}/handlers/team_management.php?id=${id}`);fetchTeams();}catch(err){setError('Failed to delete team');}};const handleEdit=async id=>{setEditingTeamId(id);const teamToEdit=teams.find(team=>team.id===id);setEditingTeam({name:teamToEdit.name,logo:teamToEdit.logo});};const handleUpdateTeam=async e=>{e.preventDefault();try{const formData=new FormData();formData.append('name',editingTeam.name);if(editingTeam.logo instanceof File){formData.append('logo',editingTeam.logo);}const response=await axios.post(`${API_BASE_URL}/handlers/update_team.php?id=${editingTeamId}`,formData,{headers:{'Content-Type':'multipart/form-data'}});if(response.data.success){setSuccess('Team updated successfully!');fetchTeams();setEditingTeamId(null);setEditingTeam({name:'',logo:null});}else{setError(response.data.message||'Failed to update team');}}catch(err){setError('Failed to update team');console.error(err);}};return/*#__PURE__*/_jsx(\"div\",{className:\"team-management-container\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"team-management-content\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"team-management-card\",children:[/*#__PURE__*/_jsx(\"h2\",{children:\"Add New Team\"}),error&&/*#__PURE__*/_jsxs(\"div\",{className:\"error-message\",style:{backgroundColor:'#fee2e2',color:'#b91c1c',padding:'12px 16px',borderRadius:'6px',marginBottom:'16px',border:'1px solid #fecaca',display:'flex',alignItems:'center',gap:'8px'},children:[/*#__PURE__*/_jsx(\"span\",{style:{backgroundColor:'#dc2626',color:'white',width:'20px',height:'20px',borderRadius:'50%',display:'flex',alignItems:'center',justifyContent:'center',fontSize:'12px',fontWeight:'bold'},children:\"!\"}),error]}),success&&/*#__PURE__*/_jsxs(\"div\",{className:\"success-message\",style:{backgroundColor:'#dcfce7',color:'#166534',padding:'12px 16px',borderRadius:'6px',marginBottom:'16px',border:'1px solid #bbf7d0',display:'flex',alignItems:'center',gap:'8px'},children:[/*#__PURE__*/_jsx(\"span\",{style:{backgroundColor:'#16a34a',color:'white',width:'20px',height:'20px',borderRadius:'50%',display:'flex',alignItems:'center',justifyContent:'center',fontSize:'12px',fontWeight:'bold'},children:\"\\u2713\"}),success]}),/*#__PURE__*/_jsxs(\"form\",{onSubmit:handleSubmit,encType:\"multipart/form-data\",className:\"team-form\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"name\",children:\"Team Name:\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",id:\"name\",name:\"name\",value:newTeam.name,onChange:handleInputChange,required:true,className:\"form-input\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"logo\",children:\"Team Logo:\"}),/*#__PURE__*/_jsx(\"input\",{type:\"file\",id:\"logo\",name:\"logo\",onChange:handleInputChange,accept:\".svg,.png,.jpg,.jpeg\",required:true,className:\"form-input\"}),/*#__PURE__*/_jsx(\"small\",{className:\"file-hint\",children:\"Only SVG, PNG, and JPG files are allowed\"})]}),/*#__PURE__*/_jsx(\"button\",{type:\"submit\",className:\"submit-button\",style:{backgroundColor:'#166534',color:'white'},children:\"Add Team\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"team-management-card\",children:[/*#__PURE__*/_jsx(\"h2\",{children:\"Existing Teams\"}),/*#__PURE__*/_jsx(\"div\",{className:\"teams-table-container\",children:/*#__PURE__*/_jsxs(\"table\",{className:\"teams-table\",children:[/*#__PURE__*/_jsx(\"thead\",{style:{backgroundColor:'#166534',color:'white'},children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{children:\"ID\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Logo\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Name\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Actions\"})]})}),/*#__PURE__*/_jsx(\"tbody\",{children:teams.map(team=>/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{children:team.id}),/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsx(\"img\",{src:`${API_BASE_URL}/${team.logo}`,alt:team.name,className:\"team-logo-image\"})}),/*#__PURE__*/_jsx(\"td\",{children:team.name}),/*#__PURE__*/_jsxs(\"td\",{className:\"action-buttons\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>handleDelete(team.id),className:\"delete-button\",style:{backgroundColor:'#dc2626',color:'white',marginRight:'8px'},children:\"Delete\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>handleEdit(team.id),className:\"edit-button\",style:{backgroundColor:'#166534',color:'white'},children:\"Edit\"})]})]},team.id))})]})})]}),editingTeamId&&/*#__PURE__*/_jsx(\"div\",{className:\"modal-overlay\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"modal-content\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"Edit Team\"}),/*#__PURE__*/_jsxs(\"form\",{onSubmit:handleUpdateTeam,encType:\"multipart/form-data\",className:\"team-form\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"editName\",children:\"Team Name:\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",id:\"editName\",name:\"name\",value:editingTeam.name,onChange:handleEditInputChange,required:true,className:\"form-input\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"editLogo\",children:\"Team Logo:\"}),/*#__PURE__*/_jsx(\"input\",{type:\"file\",id:\"editLogo\",name:\"logo\",onChange:handleEditInputChange,accept:\".svg,.png,.jpg,.jpeg\",className:\"form-input\"}),/*#__PURE__*/_jsx(\"small\",{className:\"file-hint\",children:\"Only SVG, PNG, and JPG files are allowed\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"modal-buttons\",children:[/*#__PURE__*/_jsx(\"button\",{type:\"submit\",className:\"update-button\",style:{backgroundColor:'#166534',color:'white',marginRight:'8px'},children:\"Update Team\"}),/*#__PURE__*/_jsx(\"button\",{type:\"button\",onClick:()=>setEditingTeamId(null),className:\"cancel-button\",style:{backgroundColor:'#6b7280',color:'white'},children:\"Cancel\"})]})]})]})})]})});}export default TeamManagement;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "jsx", "_jsx", "jsxs", "_jsxs", "API_BASE_URL", "TeamManagement", "teams", "setTeams", "newTeam", "setNewTeam", "name", "logo", "error", "setError", "success", "setSuccess", "editingTeamId", "setEditingTeamId", "editingTeam", "setEditingTeam", "fetchTeams", "response", "get", "data", "err", "handleInputChange", "e", "value", "type", "target", "prev", "files", "handleEditInputChange", "handleSubmit", "preventDefault", "allowedFileTypes", "includes", "formData", "FormData", "append", "post", "headers", "fileInput", "document", "getElementById", "setTimeout", "message", "handleDelete", "id", "delete", "handleEdit", "teamToEdit", "find", "team", "handleUpdateTeam", "File", "console", "className", "children", "style", "backgroundColor", "color", "padding", "borderRadius", "marginBottom", "border", "display", "alignItems", "gap", "width", "height", "justifyContent", "fontSize", "fontWeight", "onSubmit", "encType", "htmlFor", "onChange", "required", "accept", "map", "src", "alt", "onClick", "marginRight"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/pages/TeamManagement.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport './TeamManagement.css';\n\nconst API_BASE_URL = '/backend';\n\nfunction TeamManagement() {\n  const [teams, setTeams] = useState([]);\n  const [newTeam, setNewTeam] = useState({ name: '', logo: null });\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  const [editingTeamId, setEditingTeamId] = useState(null);\n  const [editingTeam, setEditingTeam] = useState({ name: '', logo: null });\n\n  useEffect(() => {\n    fetchTeams();\n  }, []);\n\n  const fetchTeams = async () => {\n    try {\n      const response = await axios.get(`${API_BASE_URL}/handlers/team_management.php`);\n      setTeams(response.data.data);\n    } catch (err) {\n      setError('Failed to fetch teams');\n      setTeams([]);\n    }\n  };\n\n  const handleInputChange = (e) => {\n    const { name, value, type } = e.target;\n    setNewTeam(prev => ({\n      ...prev,\n      [name]: type === 'file' ? e.target.files[0] : value\n    }));\n  };\n\n  const handleEditInputChange = (e) => {\n    const { name, value, type } = e.target;\n    setEditingTeam(prev => ({\n      ...prev,\n      [name]: type === 'file' ? e.target.files[0] : value\n    }));\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setError('');\n    setSuccess('');\n\n    if (!newTeam.name || !newTeam.logo) {\n      setError('Team name and logo are required.');\n      return;\n    }\n\n    // File type validation\n    const allowedFileTypes = ['image/svg+xml', 'image/png', 'image/jpeg', 'image/jpg'];\n    if (!allowedFileTypes.includes(newTeam.logo.type)) {\n      setError('Only SVG, PNG, and JPG files are allowed.');\n      return;\n    }\n\n    try {\n      const formData = new FormData();\n      formData.append('name', newTeam.name);\n      formData.append('logo', newTeam.logo);\n\n      const response = await axios.post(`${API_BASE_URL}/handlers/team_management.php`, formData, {\n        headers: { 'Content-Type': 'multipart/form-data' }\n      });\n      if (response.data.success) {\n        setSuccess('Team created successfully!');\n\n        // Clear the form completely first\n        setNewTeam({ name: '', logo: null });\n\n        // Clear the file input field\n        const fileInput = document.getElementById('logo');\n        if (fileInput) {\n          fileInput.value = '';\n        }\n\n        // Force refresh the team list after a short delay to ensure backend processing is complete\n        setTimeout(() => {\n          fetchTeams();\n        }, 100);\n\n        // Clear messages after 3 seconds\n        setTimeout(() => {\n          setSuccess('');\n          setError('');\n        }, 3000);\n      } else {\n        setError(response.data.message || 'Failed to create team');\n      }\n    } catch (err) {\n      setError('Failed to create team');\n    }\n  };\n\n  const handleDelete = async (id) => {\n    try {\n      await axios.delete(`${API_BASE_URL}/handlers/team_management.php?id=${id}`);\n      fetchTeams();\n    } catch (err) {\n      setError('Failed to delete team');\n    }\n  };\n\n  const handleEdit = async (id) => {\n    setEditingTeamId(id);\n    const teamToEdit = teams.find(team => team.id === id);\n    setEditingTeam({ name: teamToEdit.name, logo: teamToEdit.logo });\n  };\n\n  const handleUpdateTeam = async (e) => {\n    e.preventDefault();\n    try {\n      const formData = new FormData();\n      formData.append('name', editingTeam.name);\n      if (editingTeam.logo instanceof File) {\n        formData.append('logo', editingTeam.logo);\n      }\n\n      const response = await axios.post(`${API_BASE_URL}/handlers/update_team.php?id=${editingTeamId}`, formData, {\n        headers: { 'Content-Type': 'multipart/form-data' }\n      });\n\n      if (response.data.success) {\n        setSuccess('Team updated successfully!');\n        fetchTeams();\n        setEditingTeamId(null);\n        setEditingTeam({ name: '', logo: null });\n      } else {\n        setError(response.data.message || 'Failed to update team');\n      }\n    } catch (err) {\n      setError('Failed to update team');\n      console.error(err);\n    }\n  };\n\n  return (\n    <div className=\"team-management-container\">\n      <div className=\"team-management-content\">\n        <div className=\"team-management-card\">\n          <h2>Add New Team</h2>\n          {error && (\n            <div className=\"error-message\" style={{\n              backgroundColor: '#fee2e2',\n              color: '#b91c1c',\n              padding: '12px 16px',\n              borderRadius: '6px',\n              marginBottom: '16px',\n              border: '1px solid #fecaca',\n              display: 'flex',\n              alignItems: 'center',\n              gap: '8px'\n            }}>\n              <span style={{\n                backgroundColor: '#dc2626',\n                color: 'white',\n                width: '20px',\n                height: '20px',\n                borderRadius: '50%',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                fontSize: '12px',\n                fontWeight: 'bold'\n              }}>!</span>\n              {error}\n            </div>\n          )}\n          {success && (\n            <div className=\"success-message\" style={{\n              backgroundColor: '#dcfce7',\n              color: '#166534',\n              padding: '12px 16px',\n              borderRadius: '6px',\n              marginBottom: '16px',\n              border: '1px solid #bbf7d0',\n              display: 'flex',\n              alignItems: 'center',\n              gap: '8px'\n            }}>\n              <span style={{\n                backgroundColor: '#16a34a',\n                color: 'white',\n                width: '20px',\n                height: '20px',\n                borderRadius: '50%',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                fontSize: '12px',\n                fontWeight: 'bold'\n              }}>✓</span>\n              {success}\n            </div>\n          )}\n          <form onSubmit={handleSubmit} encType=\"multipart/form-data\" className=\"team-form\">\n            <div className=\"form-group\">\n              <label htmlFor=\"name\">Team Name:</label>\n              <input\n                type=\"text\"\n                id=\"name\"\n                name=\"name\"\n                value={newTeam.name}\n                onChange={handleInputChange}\n                required\n                className=\"form-input\"\n              />\n            </div>\n            <div className=\"form-group\">\n              <label htmlFor=\"logo\">Team Logo:</label>\n              <input\n                type=\"file\"\n                id=\"logo\"\n                name=\"logo\"\n                onChange={handleInputChange}\n                accept=\".svg,.png,.jpg,.jpeg\"\n                required\n                className=\"form-input\"\n              />\n              <small className=\"file-hint\">Only SVG, PNG, and JPG files are allowed</small>\n            </div>\n            <button type=\"submit\" className=\"submit-button\" style={{ backgroundColor: '#166534', color: 'white' }}>Add Team</button>\n          </form>\n        </div>\n\n        <div className=\"team-management-card\">\n          <h2>Existing Teams</h2>\n          <div className=\"teams-table-container\">\n            <table className=\"teams-table\">\n              <thead style={{ backgroundColor: '#166534', color: 'white' }}>\n                <tr>\n                  <th>ID</th>\n                  <th>Logo</th>\n                  <th>Name</th>\n                  <th>Actions</th>\n                </tr>\n              </thead>\n              <tbody>\n                {teams.map(team => (\n                  <tr key={team.id}>\n                    <td>{team.id}</td>\n                    <td>\n                      <img\n                        src={`${API_BASE_URL}/${team.logo}`}\n                        alt={team.name}\n                        className=\"team-logo-image\"\n                      />\n                    </td>\n                    <td>{team.name}</td>\n                    <td className=\"action-buttons\">\n                      <button onClick={() => handleDelete(team.id)} className=\"delete-button\" style={{ backgroundColor: '#dc2626', color: 'white', marginRight: '8px' }}>Delete</button>\n                      <button onClick={() => handleEdit(team.id)} className=\"edit-button\" style={{ backgroundColor: '#166534', color: 'white' }}>Edit</button>\n                    </td>\n                  </tr>\n                ))}\n              </tbody>\n            </table>\n          </div>\n        </div>\n\n        {editingTeamId && (\n          <div className=\"modal-overlay\">\n            <div className=\"modal-content\">\n              <h3>Edit Team</h3>\n              <form onSubmit={handleUpdateTeam} encType=\"multipart/form-data\" className=\"team-form\">\n                <div className=\"form-group\">\n                  <label htmlFor=\"editName\">Team Name:</label>\n                  <input\n                    type=\"text\"\n                    id=\"editName\"\n                    name=\"name\"\n                    value={editingTeam.name}\n                    onChange={handleEditInputChange}\n                    required\n                    className=\"form-input\"\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label htmlFor=\"editLogo\">Team Logo:</label>\n                  <input\n                    type=\"file\"\n                    id=\"editLogo\"\n                    name=\"logo\"\n                    onChange={handleEditInputChange}\n                    accept=\".svg,.png,.jpg,.jpeg\"\n                    className=\"form-input\"\n                  />\n                  <small className=\"file-hint\">Only SVG, PNG, and JPG files are allowed</small>\n                </div>\n                <div className=\"modal-buttons\">\n                  <button type=\"submit\" className=\"update-button\" style={{ backgroundColor: '#166534', color: 'white', marginRight: '8px' }}>Update Team</button>\n                  <button type=\"button\" onClick={() => setEditingTeamId(null)} className=\"cancel-button\" style={{ backgroundColor: '#6b7280', color: 'white' }}>Cancel</button>\n                </div>\n              </form>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n\nexport default TeamManagement;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,MAAO,CAAAC,KAAK,KAAM,OAAO,CACzB,MAAO,sBAAsB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE9B,KAAM,CAAAC,YAAY,CAAG,UAAU,CAE/B,QAAS,CAAAC,cAAcA,CAAA,CAAG,CACxB,KAAM,CAACC,KAAK,CAAEC,QAAQ,CAAC,CAAGV,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAACW,OAAO,CAAEC,UAAU,CAAC,CAAGZ,QAAQ,CAAC,CAAEa,IAAI,CAAE,EAAE,CAAEC,IAAI,CAAE,IAAK,CAAC,CAAC,CAChE,KAAM,CAACC,KAAK,CAAEC,QAAQ,CAAC,CAAGhB,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAACiB,OAAO,CAAEC,UAAU,CAAC,CAAGlB,QAAQ,CAAC,EAAE,CAAC,CAC1C,KAAM,CAACmB,aAAa,CAAEC,gBAAgB,CAAC,CAAGpB,QAAQ,CAAC,IAAI,CAAC,CACxD,KAAM,CAACqB,WAAW,CAAEC,cAAc,CAAC,CAAGtB,QAAQ,CAAC,CAAEa,IAAI,CAAE,EAAE,CAAEC,IAAI,CAAE,IAAK,CAAC,CAAC,CAExEb,SAAS,CAAC,IAAM,CACdsB,UAAU,CAAC,CAAC,CACd,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAA,UAAU,CAAG,KAAAA,CAAA,GAAY,CAC7B,GAAI,CACF,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAtB,KAAK,CAACuB,GAAG,CAAC,GAAGlB,YAAY,+BAA+B,CAAC,CAChFG,QAAQ,CAACc,QAAQ,CAACE,IAAI,CAACA,IAAI,CAAC,CAC9B,CAAE,MAAOC,GAAG,CAAE,CACZX,QAAQ,CAAC,uBAAuB,CAAC,CACjCN,QAAQ,CAAC,EAAE,CAAC,CACd,CACF,CAAC,CAED,KAAM,CAAAkB,iBAAiB,CAAIC,CAAC,EAAK,CAC/B,KAAM,CAAEhB,IAAI,CAAEiB,KAAK,CAAEC,IAAK,CAAC,CAAGF,CAAC,CAACG,MAAM,CACtCpB,UAAU,CAACqB,IAAI,GAAK,CAClB,GAAGA,IAAI,CACP,CAACpB,IAAI,EAAGkB,IAAI,GAAK,MAAM,CAAGF,CAAC,CAACG,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAAGJ,KAChD,CAAC,CAAC,CAAC,CACL,CAAC,CAED,KAAM,CAAAK,qBAAqB,CAAIN,CAAC,EAAK,CACnC,KAAM,CAAEhB,IAAI,CAAEiB,KAAK,CAAEC,IAAK,CAAC,CAAGF,CAAC,CAACG,MAAM,CACtCV,cAAc,CAACW,IAAI,GAAK,CACtB,GAAGA,IAAI,CACP,CAACpB,IAAI,EAAGkB,IAAI,GAAK,MAAM,CAAGF,CAAC,CAACG,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAAGJ,KAChD,CAAC,CAAC,CAAC,CACL,CAAC,CAED,KAAM,CAAAM,YAAY,CAAG,KAAO,CAAAP,CAAC,EAAK,CAChCA,CAAC,CAACQ,cAAc,CAAC,CAAC,CAClBrB,QAAQ,CAAC,EAAE,CAAC,CACZE,UAAU,CAAC,EAAE,CAAC,CAEd,GAAI,CAACP,OAAO,CAACE,IAAI,EAAI,CAACF,OAAO,CAACG,IAAI,CAAE,CAClCE,QAAQ,CAAC,kCAAkC,CAAC,CAC5C,OACF,CAEA;AACA,KAAM,CAAAsB,gBAAgB,CAAG,CAAC,eAAe,CAAE,WAAW,CAAE,YAAY,CAAE,WAAW,CAAC,CAClF,GAAI,CAACA,gBAAgB,CAACC,QAAQ,CAAC5B,OAAO,CAACG,IAAI,CAACiB,IAAI,CAAC,CAAE,CACjDf,QAAQ,CAAC,2CAA2C,CAAC,CACrD,OACF,CAEA,GAAI,CACF,KAAM,CAAAwB,QAAQ,CAAG,GAAI,CAAAC,QAAQ,CAAC,CAAC,CAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,CAAE/B,OAAO,CAACE,IAAI,CAAC,CACrC2B,QAAQ,CAACE,MAAM,CAAC,MAAM,CAAE/B,OAAO,CAACG,IAAI,CAAC,CAErC,KAAM,CAAAU,QAAQ,CAAG,KAAM,CAAAtB,KAAK,CAACyC,IAAI,CAAC,GAAGpC,YAAY,+BAA+B,CAAEiC,QAAQ,CAAE,CAC1FI,OAAO,CAAE,CAAE,cAAc,CAAE,qBAAsB,CACnD,CAAC,CAAC,CACF,GAAIpB,QAAQ,CAACE,IAAI,CAACT,OAAO,CAAE,CACzBC,UAAU,CAAC,4BAA4B,CAAC,CAExC;AACAN,UAAU,CAAC,CAAEC,IAAI,CAAE,EAAE,CAAEC,IAAI,CAAE,IAAK,CAAC,CAAC,CAEpC;AACA,KAAM,CAAA+B,SAAS,CAAGC,QAAQ,CAACC,cAAc,CAAC,MAAM,CAAC,CACjD,GAAIF,SAAS,CAAE,CACbA,SAAS,CAACf,KAAK,CAAG,EAAE,CACtB,CAEA;AACAkB,UAAU,CAAC,IAAM,CACfzB,UAAU,CAAC,CAAC,CACd,CAAC,CAAE,GAAG,CAAC,CAEP;AACAyB,UAAU,CAAC,IAAM,CACf9B,UAAU,CAAC,EAAE,CAAC,CACdF,QAAQ,CAAC,EAAE,CAAC,CACd,CAAC,CAAE,IAAI,CAAC,CACV,CAAC,IAAM,CACLA,QAAQ,CAACQ,QAAQ,CAACE,IAAI,CAACuB,OAAO,EAAI,uBAAuB,CAAC,CAC5D,CACF,CAAE,MAAOtB,GAAG,CAAE,CACZX,QAAQ,CAAC,uBAAuB,CAAC,CACnC,CACF,CAAC,CAED,KAAM,CAAAkC,YAAY,CAAG,KAAO,CAAAC,EAAE,EAAK,CACjC,GAAI,CACF,KAAM,CAAAjD,KAAK,CAACkD,MAAM,CAAC,GAAG7C,YAAY,oCAAoC4C,EAAE,EAAE,CAAC,CAC3E5B,UAAU,CAAC,CAAC,CACd,CAAE,MAAOI,GAAG,CAAE,CACZX,QAAQ,CAAC,uBAAuB,CAAC,CACnC,CACF,CAAC,CAED,KAAM,CAAAqC,UAAU,CAAG,KAAO,CAAAF,EAAE,EAAK,CAC/B/B,gBAAgB,CAAC+B,EAAE,CAAC,CACpB,KAAM,CAAAG,UAAU,CAAG7C,KAAK,CAAC8C,IAAI,CAACC,IAAI,EAAIA,IAAI,CAACL,EAAE,GAAKA,EAAE,CAAC,CACrD7B,cAAc,CAAC,CAAET,IAAI,CAAEyC,UAAU,CAACzC,IAAI,CAAEC,IAAI,CAAEwC,UAAU,CAACxC,IAAK,CAAC,CAAC,CAClE,CAAC,CAED,KAAM,CAAA2C,gBAAgB,CAAG,KAAO,CAAA5B,CAAC,EAAK,CACpCA,CAAC,CAACQ,cAAc,CAAC,CAAC,CAClB,GAAI,CACF,KAAM,CAAAG,QAAQ,CAAG,GAAI,CAAAC,QAAQ,CAAC,CAAC,CAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,CAAErB,WAAW,CAACR,IAAI,CAAC,CACzC,GAAIQ,WAAW,CAACP,IAAI,WAAY,CAAA4C,IAAI,CAAE,CACpClB,QAAQ,CAACE,MAAM,CAAC,MAAM,CAAErB,WAAW,CAACP,IAAI,CAAC,CAC3C,CAEA,KAAM,CAAAU,QAAQ,CAAG,KAAM,CAAAtB,KAAK,CAACyC,IAAI,CAAC,GAAGpC,YAAY,gCAAgCY,aAAa,EAAE,CAAEqB,QAAQ,CAAE,CAC1GI,OAAO,CAAE,CAAE,cAAc,CAAE,qBAAsB,CACnD,CAAC,CAAC,CAEF,GAAIpB,QAAQ,CAACE,IAAI,CAACT,OAAO,CAAE,CACzBC,UAAU,CAAC,4BAA4B,CAAC,CACxCK,UAAU,CAAC,CAAC,CACZH,gBAAgB,CAAC,IAAI,CAAC,CACtBE,cAAc,CAAC,CAAET,IAAI,CAAE,EAAE,CAAEC,IAAI,CAAE,IAAK,CAAC,CAAC,CAC1C,CAAC,IAAM,CACLE,QAAQ,CAACQ,QAAQ,CAACE,IAAI,CAACuB,OAAO,EAAI,uBAAuB,CAAC,CAC5D,CACF,CAAE,MAAOtB,GAAG,CAAE,CACZX,QAAQ,CAAC,uBAAuB,CAAC,CACjC2C,OAAO,CAAC5C,KAAK,CAACY,GAAG,CAAC,CACpB,CACF,CAAC,CAED,mBACEvB,IAAA,QAAKwD,SAAS,CAAC,2BAA2B,CAAAC,QAAA,cACxCvD,KAAA,QAAKsD,SAAS,CAAC,yBAAyB,CAAAC,QAAA,eACtCvD,KAAA,QAAKsD,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACnCzD,IAAA,OAAAyD,QAAA,CAAI,cAAY,CAAI,CAAC,CACpB9C,KAAK,eACJT,KAAA,QAAKsD,SAAS,CAAC,eAAe,CAACE,KAAK,CAAE,CACpCC,eAAe,CAAE,SAAS,CAC1BC,KAAK,CAAE,SAAS,CAChBC,OAAO,CAAE,WAAW,CACpBC,YAAY,CAAE,KAAK,CACnBC,YAAY,CAAE,MAAM,CACpBC,MAAM,CAAE,mBAAmB,CAC3BC,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBC,GAAG,CAAE,KACP,CAAE,CAAAV,QAAA,eACAzD,IAAA,SAAM0D,KAAK,CAAE,CACXC,eAAe,CAAE,SAAS,CAC1BC,KAAK,CAAE,OAAO,CACdQ,KAAK,CAAE,MAAM,CACbC,MAAM,CAAE,MAAM,CACdP,YAAY,CAAE,KAAK,CACnBG,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBI,cAAc,CAAE,QAAQ,CACxBC,QAAQ,CAAE,MAAM,CAChBC,UAAU,CAAE,MACd,CAAE,CAAAf,QAAA,CAAC,GAAC,CAAM,CAAC,CACV9C,KAAK,EACH,CACN,CACAE,OAAO,eACNX,KAAA,QAAKsD,SAAS,CAAC,iBAAiB,CAACE,KAAK,CAAE,CACtCC,eAAe,CAAE,SAAS,CAC1BC,KAAK,CAAE,SAAS,CAChBC,OAAO,CAAE,WAAW,CACpBC,YAAY,CAAE,KAAK,CACnBC,YAAY,CAAE,MAAM,CACpBC,MAAM,CAAE,mBAAmB,CAC3BC,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBC,GAAG,CAAE,KACP,CAAE,CAAAV,QAAA,eACAzD,IAAA,SAAM0D,KAAK,CAAE,CACXC,eAAe,CAAE,SAAS,CAC1BC,KAAK,CAAE,OAAO,CACdQ,KAAK,CAAE,MAAM,CACbC,MAAM,CAAE,MAAM,CACdP,YAAY,CAAE,KAAK,CACnBG,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBI,cAAc,CAAE,QAAQ,CACxBC,QAAQ,CAAE,MAAM,CAChBC,UAAU,CAAE,MACd,CAAE,CAAAf,QAAA,CAAC,QAAC,CAAM,CAAC,CACV5C,OAAO,EACL,CACN,cACDX,KAAA,SAAMuE,QAAQ,CAAEzC,YAAa,CAAC0C,OAAO,CAAC,qBAAqB,CAAClB,SAAS,CAAC,WAAW,CAAAC,QAAA,eAC/EvD,KAAA,QAAKsD,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzBzD,IAAA,UAAO2E,OAAO,CAAC,MAAM,CAAAlB,QAAA,CAAC,YAAU,CAAO,CAAC,cACxCzD,IAAA,UACE2B,IAAI,CAAC,MAAM,CACXoB,EAAE,CAAC,MAAM,CACTtC,IAAI,CAAC,MAAM,CACXiB,KAAK,CAAEnB,OAAO,CAACE,IAAK,CACpBmE,QAAQ,CAAEpD,iBAAkB,CAC5BqD,QAAQ,MACRrB,SAAS,CAAC,YAAY,CACvB,CAAC,EACC,CAAC,cACNtD,KAAA,QAAKsD,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzBzD,IAAA,UAAO2E,OAAO,CAAC,MAAM,CAAAlB,QAAA,CAAC,YAAU,CAAO,CAAC,cACxCzD,IAAA,UACE2B,IAAI,CAAC,MAAM,CACXoB,EAAE,CAAC,MAAM,CACTtC,IAAI,CAAC,MAAM,CACXmE,QAAQ,CAAEpD,iBAAkB,CAC5BsD,MAAM,CAAC,sBAAsB,CAC7BD,QAAQ,MACRrB,SAAS,CAAC,YAAY,CACvB,CAAC,cACFxD,IAAA,UAAOwD,SAAS,CAAC,WAAW,CAAAC,QAAA,CAAC,0CAAwC,CAAO,CAAC,EAC1E,CAAC,cACNzD,IAAA,WAAQ2B,IAAI,CAAC,QAAQ,CAAC6B,SAAS,CAAC,eAAe,CAACE,KAAK,CAAE,CAAEC,eAAe,CAAE,SAAS,CAAEC,KAAK,CAAE,OAAQ,CAAE,CAAAH,QAAA,CAAC,UAAQ,CAAQ,CAAC,EACpH,CAAC,EACJ,CAAC,cAENvD,KAAA,QAAKsD,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACnCzD,IAAA,OAAAyD,QAAA,CAAI,gBAAc,CAAI,CAAC,cACvBzD,IAAA,QAAKwD,SAAS,CAAC,uBAAuB,CAAAC,QAAA,cACpCvD,KAAA,UAAOsD,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC5BzD,IAAA,UAAO0D,KAAK,CAAE,CAAEC,eAAe,CAAE,SAAS,CAAEC,KAAK,CAAE,OAAQ,CAAE,CAAAH,QAAA,cAC3DvD,KAAA,OAAAuD,QAAA,eACEzD,IAAA,OAAAyD,QAAA,CAAI,IAAE,CAAI,CAAC,cACXzD,IAAA,OAAAyD,QAAA,CAAI,MAAI,CAAI,CAAC,cACbzD,IAAA,OAAAyD,QAAA,CAAI,MAAI,CAAI,CAAC,cACbzD,IAAA,OAAAyD,QAAA,CAAI,SAAO,CAAI,CAAC,EACd,CAAC,CACA,CAAC,cACRzD,IAAA,UAAAyD,QAAA,CACGpD,KAAK,CAAC0E,GAAG,CAAC3B,IAAI,eACblD,KAAA,OAAAuD,QAAA,eACEzD,IAAA,OAAAyD,QAAA,CAAKL,IAAI,CAACL,EAAE,CAAK,CAAC,cAClB/C,IAAA,OAAAyD,QAAA,cACEzD,IAAA,QACEgF,GAAG,CAAE,GAAG7E,YAAY,IAAIiD,IAAI,CAAC1C,IAAI,EAAG,CACpCuE,GAAG,CAAE7B,IAAI,CAAC3C,IAAK,CACf+C,SAAS,CAAC,iBAAiB,CAC5B,CAAC,CACA,CAAC,cACLxD,IAAA,OAAAyD,QAAA,CAAKL,IAAI,CAAC3C,IAAI,CAAK,CAAC,cACpBP,KAAA,OAAIsD,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC5BzD,IAAA,WAAQkF,OAAO,CAAEA,CAAA,GAAMpC,YAAY,CAACM,IAAI,CAACL,EAAE,CAAE,CAACS,SAAS,CAAC,eAAe,CAACE,KAAK,CAAE,CAAEC,eAAe,CAAE,SAAS,CAAEC,KAAK,CAAE,OAAO,CAAEuB,WAAW,CAAE,KAAM,CAAE,CAAA1B,QAAA,CAAC,QAAM,CAAQ,CAAC,cAClKzD,IAAA,WAAQkF,OAAO,CAAEA,CAAA,GAAMjC,UAAU,CAACG,IAAI,CAACL,EAAE,CAAE,CAACS,SAAS,CAAC,aAAa,CAACE,KAAK,CAAE,CAAEC,eAAe,CAAE,SAAS,CAAEC,KAAK,CAAE,OAAQ,CAAE,CAAAH,QAAA,CAAC,MAAI,CAAQ,CAAC,EACtI,CAAC,GAbEL,IAAI,CAACL,EAcV,CACL,CAAC,CACG,CAAC,EACH,CAAC,CACL,CAAC,EACH,CAAC,CAELhC,aAAa,eACZf,IAAA,QAAKwD,SAAS,CAAC,eAAe,CAAAC,QAAA,cAC5BvD,KAAA,QAAKsD,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BzD,IAAA,OAAAyD,QAAA,CAAI,WAAS,CAAI,CAAC,cAClBvD,KAAA,SAAMuE,QAAQ,CAAEpB,gBAAiB,CAACqB,OAAO,CAAC,qBAAqB,CAAClB,SAAS,CAAC,WAAW,CAAAC,QAAA,eACnFvD,KAAA,QAAKsD,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzBzD,IAAA,UAAO2E,OAAO,CAAC,UAAU,CAAAlB,QAAA,CAAC,YAAU,CAAO,CAAC,cAC5CzD,IAAA,UACE2B,IAAI,CAAC,MAAM,CACXoB,EAAE,CAAC,UAAU,CACbtC,IAAI,CAAC,MAAM,CACXiB,KAAK,CAAET,WAAW,CAACR,IAAK,CACxBmE,QAAQ,CAAE7C,qBAAsB,CAChC8C,QAAQ,MACRrB,SAAS,CAAC,YAAY,CACvB,CAAC,EACC,CAAC,cACNtD,KAAA,QAAKsD,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzBzD,IAAA,UAAO2E,OAAO,CAAC,UAAU,CAAAlB,QAAA,CAAC,YAAU,CAAO,CAAC,cAC5CzD,IAAA,UACE2B,IAAI,CAAC,MAAM,CACXoB,EAAE,CAAC,UAAU,CACbtC,IAAI,CAAC,MAAM,CACXmE,QAAQ,CAAE7C,qBAAsB,CAChC+C,MAAM,CAAC,sBAAsB,CAC7BtB,SAAS,CAAC,YAAY,CACvB,CAAC,cACFxD,IAAA,UAAOwD,SAAS,CAAC,WAAW,CAAAC,QAAA,CAAC,0CAAwC,CAAO,CAAC,EAC1E,CAAC,cACNvD,KAAA,QAAKsD,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BzD,IAAA,WAAQ2B,IAAI,CAAC,QAAQ,CAAC6B,SAAS,CAAC,eAAe,CAACE,KAAK,CAAE,CAAEC,eAAe,CAAE,SAAS,CAAEC,KAAK,CAAE,OAAO,CAAEuB,WAAW,CAAE,KAAM,CAAE,CAAA1B,QAAA,CAAC,aAAW,CAAQ,CAAC,cAC/IzD,IAAA,WAAQ2B,IAAI,CAAC,QAAQ,CAACuD,OAAO,CAAEA,CAAA,GAAMlE,gBAAgB,CAAC,IAAI,CAAE,CAACwC,SAAS,CAAC,eAAe,CAACE,KAAK,CAAE,CAAEC,eAAe,CAAE,SAAS,CAAEC,KAAK,CAAE,OAAQ,CAAE,CAAAH,QAAA,CAAC,QAAM,CAAQ,CAAC,EAC1J,CAAC,EACF,CAAC,EACJ,CAAC,CACH,CACN,EACE,CAAC,CACH,CAAC,CAEV,CAEA,cAAe,CAAArD,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}