import React, { useState, useEffect } from 'react';
import { useNavigate, NavLink } from 'react-router-dom';
import {
    FaGamepad,
    FaUsers,
    FaCog,
    FaChartBar,
    FaMoneyBill,
    FaTrophy,
    FaHome,
    FaUserPlus,
    FaCreditCard,
    FaFutbol,
    FaTasks,
    FaCoins,
    FaCalendarAlt,
    FaUserFriends,
    FaCashRegister,
    FaExchangeAlt,
    FaMedal,
    FaWrench,
    FaChartLine,
    FaShieldAlt,
    FaSignOutAlt,
    FaDice,
    FaBars,
    FaTimes
} from 'react-icons/fa';
import './AdminSidebar.css';
import { useSiteConfig } from '../contexts/SiteConfigContext';

function Sidebar() {
    const navigate = useNavigate();
    const { config } = useSiteConfig();
    const [isCollapsed, setIsCollapsed] = useState(false);
    const [isMobile, setIsMobile] = useState(false);

    // Check screen size and set mobile state
    useEffect(() => {
        const checkScreenSize = () => {
            const screenWidth = window.innerWidth;
            const isMobileScreen = screenWidth <= 1024; // 13 inches and smaller
            setIsMobile(isMobileScreen);

            // Auto-collapse on small screens
            if (isMobileScreen) {
                setIsCollapsed(true);
            }
        };

        checkScreenSize();
        window.addEventListener('resize', checkScreenSize);
        return () => window.removeEventListener('resize', checkScreenSize);
    }, []);

    const toggleSidebar = () => {
        setIsCollapsed(!isCollapsed);
    };

    // Define all menu items as a flat list
    const menuItems = [
        { link: '/admin/dashboard', text: 'Overview', icon: <FaHome /> },

        // Challenges
        { link: '/admin/challenge-system', text: 'Challenge System', icon: <FaFutbol /> },
        { link: '/admin/challenge-management', text: 'Challenge Management', icon: <FaTasks /> },
        { link: '/admin/credit-challenge', text: 'Credit Challenge', icon: <FaCoins /> },
        { link: '/admin/team-management', text: 'Team Management', icon: <FaShieldAlt /> },
        { link: '/admin/bets', text: 'Bet Management', icon: <FaDice /> },

        // Users
        { link: '/admin/users', text: 'User Management', icon: <FaUsers /> },
        { link: '/admin/add-user', text: 'Add User', icon: <FaUserPlus /> },
        { link: '/admin/credit-user', text: 'Credit User', icon: <FaCreditCard /> },

        // Leagues
        { link: '/admin/league-management', text: 'League Management', icon: <FaTrophy /> },
        { link: '/admin/league-seasons', text: 'Season Management', icon: <FaCalendarAlt /> },
        { link: '/admin/league-users', text: 'League Users', icon: <FaUserFriends /> },

        // Finance
        { link: '/admin/payment-methods', text: 'Payment Methods', icon: <FaCashRegister /> },
        { link: '/admin/transactions', text: 'Transactions', icon: <FaExchangeAlt /> },

        // System
        { link: '/admin/leaderboard', text: 'Leaderboard Management', icon: <FaMedal /> },
        { link: '/admin/settings', text: 'System Settings', icon: <FaWrench /> },
        { link: '/admin/security-settings', text: 'Security Settings', icon: <FaShieldAlt /> },
        { link: '/admin/2fa-settings', text: '2FA Settings', icon: <FaShieldAlt /> },
        { link: '/admin/reports', text: 'Reports and Analytics', icon: <FaChartLine /> }
    ];

    return (
        <>
            {/* Mobile Toggle Button */}
            {isMobile && (
                <button
                    className="mobile-sidebar-toggle"
                    onClick={toggleSidebar}
                    aria-label="Toggle sidebar"
                >
                    {isCollapsed ? <FaBars /> : <FaTimes />}
                </button>
            )}

            <div className={`admin-sidebar ${isCollapsed ? 'collapsed' : ''} ${isMobile ? 'mobile' : ''}`}>
                <div className="sidebar-header">
                    {/* Desktop Toggle Button */}
                    {!isMobile && (
                        <button
                            className="desktop-sidebar-toggle"
                            onClick={toggleSidebar}
                            aria-label="Toggle sidebar"
                        >
                            {isCollapsed ? <FaBars /> : <FaTimes />}
                        </button>
                    )}

                    <div className="logo">
                        {!isCollapsed && config.site_logo ? (
                            <img
                                src={`/backend/${config.site_logo}`}
                                alt={config.site_name || "Site Logo"}
                                className="logo-icon"
                                onError={(e) => {
                                    e.target.style.display = 'none';
                                    e.target.nextSibling.style.display = 'block';
                                }}
                            />
                        ) : !isCollapsed ? (
                            <span className="logo-text">{config.site_name || "FanBet247"}</span>
                        ) : (
                            <span className="logo-collapsed">F</span>
                        )}
                    </div>
                </div>

                <nav className="admin-sidebar-nav simple-menu">
                    {menuItems.map((item, index) => (
                        <NavLink
                            key={index}
                            to={item.link}
                            className={({ isActive }) =>
                                `simple-nav-item ${isActive ? 'active' : ''}`
                            }
                            end
                            title={isCollapsed ? item.text : ''}
                        >
                            <span className="simple-nav-item-icon">{item.icon}</span>
                            {!isCollapsed && <span className="simple-nav-item-text">{item.text}</span>}
                        </NavLink>
                    ))}

                    {/* Logout button at bottom of sidebar */}
                    <div className="sidebar-footer">
                        <button
                            className="logout-button"
                            onClick={() => navigate('/admin/login')}
                            style={{ backgroundColor: '#dc2626', color: 'white' }}
                            title={isCollapsed ? 'Logout' : ''}
                        >
                            <span className="logout-icon" style={{ color: 'white' }}>
                                <FaSignOutAlt />
                            </span>
                            {!isCollapsed && <span className="logout-text" style={{ color: 'white' }}>Logout</span>}
                        </button>
                    </div>
                </nav>
            </div>
        </>
    );
}

export default Sidebar;