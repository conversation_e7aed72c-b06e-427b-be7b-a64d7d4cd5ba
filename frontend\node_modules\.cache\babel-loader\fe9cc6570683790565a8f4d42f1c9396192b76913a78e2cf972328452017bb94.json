{"ast": null, "code": "var _jsxFileName = \"C:\\\\MAMP\\\\htdocs\\\\FanBet247\\\\frontend\\\\src\\\\pages\\\\SecuritySettings.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { FaShieldAlt, FaCheck, FaTimes, FaSave, FaKey, FaUserShield, FaExclamationTriangle, FaInfoCircle } from 'react-icons/fa';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst API_BASE_URL = '/backend';\nfunction SecuritySettings() {\n  _s();\n  const [settings, setSettings] = useState({\n    enable_2fa: 'false',\n    allowed_auth_methods: 'email_otp,google_auth',\n    otp_expiry_time: '300',\n    max_otp_attempts: '3',\n    lockout_time: '1800',\n    password_min_length: '8',\n    require_special_chars: 'true',\n    session_timeout: '3600',\n    max_login_attempts: '5'\n  });\n  const [loading, setLoading] = useState(true);\n  const [saving, setSaving] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  useEffect(() => {\n    fetchSettings();\n  }, []);\n  const fetchSettings = async () => {\n    try {\n      setLoading(true);\n      const response = await axios.get(`${API_BASE_URL}/handlers/get_security_settings.php`);\n      if (response.data.success && response.data.settings) {\n        const settingsData = {};\n        Object.keys(response.data.settings).forEach(key => {\n          settingsData[key] = response.data.settings[key].value;\n        });\n        setSettings(settingsData);\n      }\n    } catch (err) {\n      setError('Failed to load security settings');\n      console.error('Error fetching settings:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleInputChange = e => {\n    const {\n      name,\n      value,\n      type,\n      checked\n    } = e.target;\n    setSettings(prev => ({\n      ...prev,\n      [name]: type === 'checkbox' ? checked ? 'true' : 'false' : value\n    }));\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setError('');\n    setSuccess('');\n    try {\n      setSaving(true);\n      const response = await axios.post(`${API_BASE_URL}/handlers/update_security_settings.php`, {\n        settings: settings\n      });\n      if (response.data.success) {\n        setSuccess('Security settings saved successfully!');\n        setTimeout(() => setSuccess(''), 3000);\n      } else {\n        setError(response.data.message || 'Failed to save security settings');\n      }\n    } catch (err) {\n      setError('Failed to save security settings');\n      console.error('Error saving settings:', err);\n    } finally {\n      setSaving(false);\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-center h-64\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-lg text-gray-600\",\n          children: \"Loading security settings...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 13\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"p-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-2xl font-bold text-gray-800 flex items-center gap-3\",\n        children: [/*#__PURE__*/_jsxDEV(FaShieldAlt, {\n          className: \"text-blue-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 21\n        }, this), \"Security Settings\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-600 mt-2\",\n        children: \"Configure two-factor authentication (2FA) options and other security features.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 94,\n      columnNumber: 13\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-6 bg-red-50 border border-red-200 rounded-lg p-4 flex items-center gap-3\",\n      children: [/*#__PURE__*/_jsxDEV(FaTimes, {\n        className: \"text-red-500\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"text-red-700\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 106,\n      columnNumber: 17\n    }, this), success && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-6 bg-green-50 border border-green-200 rounded-lg p-4 flex items-center gap-3\",\n      children: [/*#__PURE__*/_jsxDEV(FaCheck, {\n        className: \"text-green-500\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 114,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"text-green-700\",\n        children: success\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 113,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg shadow-sm border\",\n      children: /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        className: \"p-6 space-y-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-lg font-semibold text-gray-800 mb-4\",\n            children: \"Two-Factor Authentication\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                id: \"enable_2fa\",\n                name: \"enable_2fa\",\n                checked: settings.enable_2fa === 'true',\n                onChange: handleInputChange,\n                className: \"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 127,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"enable_2fa\",\n                className: \"ml-2 block text-sm text-gray-900\",\n                children: \"Enable Two-Factor Authentication for users\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 135,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 126,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Allowed Authentication Methods\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 141,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                name: \"allowed_auth_methods\",\n                value: settings.allowed_auth_methods,\n                onChange: handleInputChange,\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                placeholder: \"email_otp,google_auth\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 144,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-gray-500 mt-1\",\n                children: \"Comma-separated list of allowed methods\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 152,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 140,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-lg font-semibold text-gray-800 mb-4\",\n            children: \"OTP Settings\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"OTP Expiry Time (seconds)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 162,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                name: \"otp_expiry_time\",\n                value: settings.otp_expiry_time,\n                onChange: handleInputChange,\n                min: \"60\",\n                max: \"3600\",\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 165,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Max OTP Attempts\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 177,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                name: \"max_otp_attempts\",\n                value: settings.max_otp_attempts,\n                onChange: handleInputChange,\n                min: \"1\",\n                max: \"10\",\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 180,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-lg font-semibold text-gray-800 mb-4\",\n            children: \"Password Security\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Minimum Password Length\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 198,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                name: \"password_min_length\",\n                value: settings.password_min_length,\n                onChange: handleInputChange,\n                min: \"6\",\n                max: \"50\",\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 201,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                id: \"require_special_chars\",\n                name: \"require_special_chars\",\n                checked: settings.require_special_chars === 'true',\n                onChange: handleInputChange,\n                className: \"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 213,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"require_special_chars\",\n                className: \"ml-2 block text-sm text-gray-900\",\n                children: \"Require special characters in passwords\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 221,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-lg font-semibold text-gray-800 mb-4\",\n            children: \"Login Security\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Max Login Attempts\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                name: \"max_login_attempts\",\n                value: settings.max_login_attempts,\n                onChange: handleInputChange,\n                min: \"1\",\n                max: \"20\",\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 236,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Lockout Time (seconds)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                name: \"lockout_time\",\n                value: settings.lockout_time,\n                onChange: handleInputChange,\n                min: \"300\",\n                max: \"86400\",\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 251,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Session Timeout (seconds)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 263,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                name: \"session_timeout\",\n                value: settings.session_timeout,\n                onChange: handleInputChange,\n                min: \"300\",\n                max: \"86400\",\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 266,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 262,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 229,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-end pt-6 border-t\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            disabled: saving,\n            className: \"flex items-center gap-2 px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n            children: [/*#__PURE__*/_jsxDEV(FaSave, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 29\n            }, this), saving ? 'Saving...' : 'Save Security Settings']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 280,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 121,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 120,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 92,\n    columnNumber: 9\n  }, this);\n}\n_s(SecuritySettings, \"b4+GKuA1exoQe5fXv0Icc6DKjd0=\");\n_c = SecuritySettings;\nexport default SecuritySettings;\nvar _c;\n$RefreshReg$(_c, \"SecuritySettings\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "FaShieldAlt", "FaCheck", "FaTimes", "FaSave", "FaKey", "FaUserShield", "FaExclamationTriangle", "FaInfoCircle", "jsxDEV", "_jsxDEV", "API_BASE_URL", "SecuritySettings", "_s", "settings", "setSettings", "enable_2fa", "allowed_auth_methods", "otp_expiry_time", "max_otp_attempts", "lockout_time", "password_min_length", "require_special_chars", "session_timeout", "max_login_attempts", "loading", "setLoading", "saving", "setSaving", "error", "setError", "success", "setSuccess", "fetchSettings", "response", "get", "data", "settingsData", "Object", "keys", "for<PERSON>ach", "key", "value", "err", "console", "handleInputChange", "e", "name", "type", "checked", "target", "prev", "handleSubmit", "preventDefault", "post", "setTimeout", "message", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "id", "onChange", "htmlFor", "placeholder", "min", "max", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/pages/SecuritySettings.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { FaShieldAlt, FaCheck, FaTimes, FaSave, FaKey, FaUserShield, FaExclamationTriangle, FaInfoCircle } from 'react-icons/fa';\n\nconst API_BASE_URL = '/backend';\n\nfunction SecuritySettings() {\n    const [settings, setSettings] = useState({\n        enable_2fa: 'false',\n        allowed_auth_methods: 'email_otp,google_auth',\n        otp_expiry_time: '300',\n        max_otp_attempts: '3',\n        lockout_time: '1800',\n        password_min_length: '8',\n        require_special_chars: 'true',\n        session_timeout: '3600',\n        max_login_attempts: '5'\n    });\n    const [loading, setLoading] = useState(true);\n    const [saving, setSaving] = useState(false);\n    const [error, setError] = useState('');\n    const [success, setSuccess] = useState('');\n\n    useEffect(() => {\n        fetchSettings();\n    }, []);\n\n    const fetchSettings = async () => {\n        try {\n            setLoading(true);\n            const response = await axios.get(`${API_BASE_URL}/handlers/get_security_settings.php`);\n\n            if (response.data.success && response.data.settings) {\n                const settingsData = {};\n                Object.keys(response.data.settings).forEach(key => {\n                    settingsData[key] = response.data.settings[key].value;\n                });\n                setSettings(settingsData);\n            }\n        } catch (err) {\n            setError('Failed to load security settings');\n            console.error('Error fetching settings:', err);\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const handleInputChange = (e) => {\n        const { name, value, type, checked } = e.target;\n        setSettings(prev => ({\n            ...prev,\n            [name]: type === 'checkbox' ? (checked ? 'true' : 'false') : value\n        }));\n    };\n\n    const handleSubmit = async (e) => {\n        e.preventDefault();\n        setError('');\n        setSuccess('');\n\n        try {\n            setSaving(true);\n            const response = await axios.post(`${API_BASE_URL}/handlers/update_security_settings.php`, {\n                settings: settings\n            });\n\n            if (response.data.success) {\n                setSuccess('Security settings saved successfully!');\n                setTimeout(() => setSuccess(''), 3000);\n            } else {\n                setError(response.data.message || 'Failed to save security settings');\n            }\n        } catch (err) {\n            setError('Failed to save security settings');\n            console.error('Error saving settings:', err);\n        } finally {\n            setSaving(false);\n        }\n    };\n\n    if (loading) {\n        return (\n            <div className=\"p-6\">\n                <div className=\"flex items-center justify-center h-64\">\n                    <div className=\"text-lg text-gray-600\">Loading security settings...</div>\n                </div>\n            </div>\n        );\n    }\n\n    return (\n        <div className=\"p-6\">\n            {/* Header */}\n            <div className=\"mb-8\">\n                <h1 className=\"text-2xl font-bold text-gray-800 flex items-center gap-3\">\n                    <FaShieldAlt className=\"text-blue-500\" />\n                    Security Settings\n                </h1>\n                <p className=\"text-gray-600 mt-2\">\n                    Configure two-factor authentication (2FA) options and other security features.\n                </p>\n            </div>\n\n            {/* Alerts */}\n            {error && (\n                <div className=\"mb-6 bg-red-50 border border-red-200 rounded-lg p-4 flex items-center gap-3\">\n                    <FaTimes className=\"text-red-500\" />\n                    <span className=\"text-red-700\">{error}</span>\n                </div>\n            )}\n\n            {success && (\n                <div className=\"mb-6 bg-green-50 border border-green-200 rounded-lg p-4 flex items-center gap-3\">\n                    <FaCheck className=\"text-green-500\" />\n                    <span className=\"text-green-700\">{success}</span>\n                </div>\n            )}\n\n            {/* Settings Form */}\n            <div className=\"bg-white rounded-lg shadow-sm border\">\n                <form onSubmit={handleSubmit} className=\"p-6 space-y-6\">\n                    {/* Two-Factor Authentication */}\n                    <div>\n                        <h2 className=\"text-lg font-semibold text-gray-800 mb-4\">Two-Factor Authentication</h2>\n                        <div className=\"space-y-4\">\n                            <div className=\"flex items-center\">\n                                <input\n                                    type=\"checkbox\"\n                                    id=\"enable_2fa\"\n                                    name=\"enable_2fa\"\n                                    checked={settings.enable_2fa === 'true'}\n                                    onChange={handleInputChange}\n                                    className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                                />\n                                <label htmlFor=\"enable_2fa\" className=\"ml-2 block text-sm text-gray-900\">\n                                    Enable Two-Factor Authentication for users\n                                </label>\n                            </div>\n                            \n                            <div>\n                                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                                    Allowed Authentication Methods\n                                </label>\n                                <input\n                                    type=\"text\"\n                                    name=\"allowed_auth_methods\"\n                                    value={settings.allowed_auth_methods}\n                                    onChange={handleInputChange}\n                                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                                    placeholder=\"email_otp,google_auth\"\n                                />\n                                <p className=\"text-xs text-gray-500 mt-1\">Comma-separated list of allowed methods</p>\n                            </div>\n                        </div>\n                    </div>\n\n                    {/* OTP Settings */}\n                    <div>\n                        <h2 className=\"text-lg font-semibold text-gray-800 mb-4\">OTP Settings</h2>\n                        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                            <div>\n                                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                                    OTP Expiry Time (seconds)\n                                </label>\n                                <input\n                                    type=\"number\"\n                                    name=\"otp_expiry_time\"\n                                    value={settings.otp_expiry_time}\n                                    onChange={handleInputChange}\n                                    min=\"60\"\n                                    max=\"3600\"\n                                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                                />\n                            </div>\n                            \n                            <div>\n                                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                                    Max OTP Attempts\n                                </label>\n                                <input\n                                    type=\"number\"\n                                    name=\"max_otp_attempts\"\n                                    value={settings.max_otp_attempts}\n                                    onChange={handleInputChange}\n                                    min=\"1\"\n                                    max=\"10\"\n                                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                                />\n                            </div>\n                        </div>\n                    </div>\n\n                    {/* Password Security */}\n                    <div>\n                        <h2 className=\"text-lg font-semibold text-gray-800 mb-4\">Password Security</h2>\n                        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                            <div>\n                                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                                    Minimum Password Length\n                                </label>\n                                <input\n                                    type=\"number\"\n                                    name=\"password_min_length\"\n                                    value={settings.password_min_length}\n                                    onChange={handleInputChange}\n                                    min=\"6\"\n                                    max=\"50\"\n                                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                                />\n                            </div>\n                            \n                            <div className=\"flex items-center\">\n                                <input\n                                    type=\"checkbox\"\n                                    id=\"require_special_chars\"\n                                    name=\"require_special_chars\"\n                                    checked={settings.require_special_chars === 'true'}\n                                    onChange={handleInputChange}\n                                    className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                                />\n                                <label htmlFor=\"require_special_chars\" className=\"ml-2 block text-sm text-gray-900\">\n                                    Require special characters in passwords\n                                </label>\n                            </div>\n                        </div>\n                    </div>\n\n                    {/* Login Security */}\n                    <div>\n                        <h2 className=\"text-lg font-semibold text-gray-800 mb-4\">Login Security</h2>\n                        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n                            <div>\n                                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                                    Max Login Attempts\n                                </label>\n                                <input\n                                    type=\"number\"\n                                    name=\"max_login_attempts\"\n                                    value={settings.max_login_attempts}\n                                    onChange={handleInputChange}\n                                    min=\"1\"\n                                    max=\"20\"\n                                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                                />\n                            </div>\n                            \n                            <div>\n                                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                                    Lockout Time (seconds)\n                                </label>\n                                <input\n                                    type=\"number\"\n                                    name=\"lockout_time\"\n                                    value={settings.lockout_time}\n                                    onChange={handleInputChange}\n                                    min=\"300\"\n                                    max=\"86400\"\n                                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                                />\n                            </div>\n                            \n                            <div>\n                                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                                    Session Timeout (seconds)\n                                </label>\n                                <input\n                                    type=\"number\"\n                                    name=\"session_timeout\"\n                                    value={settings.session_timeout}\n                                    onChange={handleInputChange}\n                                    min=\"300\"\n                                    max=\"86400\"\n                                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                                />\n                            </div>\n                        </div>\n                    </div>\n\n                    {/* Submit Button */}\n                    <div className=\"flex justify-end pt-6 border-t\">\n                        <button\n                            type=\"submit\"\n                            disabled={saving}\n                            className=\"flex items-center gap-2 px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n                        >\n                            <FaSave />\n                            {saving ? 'Saving...' : 'Save Security Settings'}\n                        </button>\n                    </div>\n                </form>\n            </div>\n        </div>\n    );\n}\n\nexport default SecuritySettings;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,EAAEC,OAAO,EAAEC,OAAO,EAAEC,MAAM,EAAEC,KAAK,EAAEC,YAAY,EAAEC,qBAAqB,EAAEC,YAAY,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjI,MAAMC,YAAY,GAAG,UAAU;AAE/B,SAASC,gBAAgBA,CAAA,EAAG;EAAAC,EAAA;EACxB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGjB,QAAQ,CAAC;IACrCkB,UAAU,EAAE,OAAO;IACnBC,oBAAoB,EAAE,uBAAuB;IAC7CC,eAAe,EAAE,KAAK;IACtBC,gBAAgB,EAAE,GAAG;IACrBC,YAAY,EAAE,MAAM;IACpBC,mBAAmB,EAAE,GAAG;IACxBC,qBAAqB,EAAE,MAAM;IAC7BC,eAAe,EAAE,MAAM;IACvBC,kBAAkB,EAAE;EACxB,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC6B,MAAM,EAAEC,SAAS,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAAC+B,KAAK,EAAEC,QAAQ,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACiC,OAAO,EAAEC,UAAU,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EAE1CC,SAAS,CAAC,MAAM;IACZkC,aAAa,CAAC,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,aAAa,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACAP,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMQ,QAAQ,GAAG,MAAMlC,KAAK,CAACmC,GAAG,CAAC,GAAGxB,YAAY,qCAAqC,CAAC;MAEtF,IAAIuB,QAAQ,CAACE,IAAI,CAACL,OAAO,IAAIG,QAAQ,CAACE,IAAI,CAACtB,QAAQ,EAAE;QACjD,MAAMuB,YAAY,GAAG,CAAC,CAAC;QACvBC,MAAM,CAACC,IAAI,CAACL,QAAQ,CAACE,IAAI,CAACtB,QAAQ,CAAC,CAAC0B,OAAO,CAACC,GAAG,IAAI;UAC/CJ,YAAY,CAACI,GAAG,CAAC,GAAGP,QAAQ,CAACE,IAAI,CAACtB,QAAQ,CAAC2B,GAAG,CAAC,CAACC,KAAK;QACzD,CAAC,CAAC;QACF3B,WAAW,CAACsB,YAAY,CAAC;MAC7B;IACJ,CAAC,CAAC,OAAOM,GAAG,EAAE;MACVb,QAAQ,CAAC,kCAAkC,CAAC;MAC5Cc,OAAO,CAACf,KAAK,CAAC,0BAA0B,EAAEc,GAAG,CAAC;IAClD,CAAC,SAAS;MACNjB,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,MAAMmB,iBAAiB,GAAIC,CAAC,IAAK;IAC7B,MAAM;MAAEC,IAAI;MAAEL,KAAK;MAAEM,IAAI;MAAEC;IAAQ,CAAC,GAAGH,CAAC,CAACI,MAAM;IAC/CnC,WAAW,CAACoC,IAAI,KAAK;MACjB,GAAGA,IAAI;MACP,CAACJ,IAAI,GAAGC,IAAI,KAAK,UAAU,GAAIC,OAAO,GAAG,MAAM,GAAG,OAAO,GAAIP;IACjE,CAAC,CAAC,CAAC;EACP,CAAC;EAED,MAAMU,YAAY,GAAG,MAAON,CAAC,IAAK;IAC9BA,CAAC,CAACO,cAAc,CAAC,CAAC;IAClBvB,QAAQ,CAAC,EAAE,CAAC;IACZE,UAAU,CAAC,EAAE,CAAC;IAEd,IAAI;MACAJ,SAAS,CAAC,IAAI,CAAC;MACf,MAAMM,QAAQ,GAAG,MAAMlC,KAAK,CAACsD,IAAI,CAAC,GAAG3C,YAAY,wCAAwC,EAAE;QACvFG,QAAQ,EAAEA;MACd,CAAC,CAAC;MAEF,IAAIoB,QAAQ,CAACE,IAAI,CAACL,OAAO,EAAE;QACvBC,UAAU,CAAC,uCAAuC,CAAC;QACnDuB,UAAU,CAAC,MAAMvB,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;MAC1C,CAAC,MAAM;QACHF,QAAQ,CAACI,QAAQ,CAACE,IAAI,CAACoB,OAAO,IAAI,kCAAkC,CAAC;MACzE;IACJ,CAAC,CAAC,OAAOb,GAAG,EAAE;MACVb,QAAQ,CAAC,kCAAkC,CAAC;MAC5Cc,OAAO,CAACf,KAAK,CAAC,wBAAwB,EAAEc,GAAG,CAAC;IAChD,CAAC,SAAS;MACNf,SAAS,CAAC,KAAK,CAAC;IACpB;EACJ,CAAC;EAED,IAAIH,OAAO,EAAE;IACT,oBACIf,OAAA;MAAK+C,SAAS,EAAC,KAAK;MAAAC,QAAA,eAChBhD,OAAA;QAAK+C,SAAS,EAAC,uCAAuC;QAAAC,QAAA,eAClDhD,OAAA;UAAK+C,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAA4B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd;EAEA,oBACIpD,OAAA;IAAK+C,SAAS,EAAC,KAAK;IAAAC,QAAA,gBAEhBhD,OAAA;MAAK+C,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACjBhD,OAAA;QAAI+C,SAAS,EAAC,0DAA0D;QAAAC,QAAA,gBACpEhD,OAAA,CAACT,WAAW;UAACwD,SAAS,EAAC;QAAe;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,qBAE7C;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLpD,OAAA;QAAG+C,SAAS,EAAC,oBAAoB;QAAAC,QAAA,EAAC;MAElC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLjC,KAAK,iBACFnB,OAAA;MAAK+C,SAAS,EAAC,6EAA6E;MAAAC,QAAA,gBACxFhD,OAAA,CAACP,OAAO;QAACsD,SAAS,EAAC;MAAc;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACpCpD,OAAA;QAAM+C,SAAS,EAAC,cAAc;QAAAC,QAAA,EAAE7B;MAAK;QAAA8B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5C,CACR,EAEA/B,OAAO,iBACJrB,OAAA;MAAK+C,SAAS,EAAC,iFAAiF;MAAAC,QAAA,gBAC5FhD,OAAA,CAACR,OAAO;QAACuD,SAAS,EAAC;MAAgB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACtCpD,OAAA;QAAM+C,SAAS,EAAC,gBAAgB;QAAAC,QAAA,EAAE3B;MAAO;QAAA4B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChD,CACR,eAGDpD,OAAA;MAAK+C,SAAS,EAAC,sCAAsC;MAAAC,QAAA,eACjDhD,OAAA;QAAMqD,QAAQ,EAAEX,YAAa;QAACK,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAEnDhD,OAAA;UAAAgD,QAAA,gBACIhD,OAAA;YAAI+C,SAAS,EAAC,0CAA0C;YAAAC,QAAA,EAAC;UAAyB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvFpD,OAAA;YAAK+C,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACtBhD,OAAA;cAAK+C,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAC9BhD,OAAA;gBACIsC,IAAI,EAAC,UAAU;gBACfgB,EAAE,EAAC,YAAY;gBACfjB,IAAI,EAAC,YAAY;gBACjBE,OAAO,EAAEnC,QAAQ,CAACE,UAAU,KAAK,MAAO;gBACxCiD,QAAQ,EAAEpB,iBAAkB;gBAC5BY,SAAS,EAAC;cAAmE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChF,CAAC,eACFpD,OAAA;gBAAOwD,OAAO,EAAC,YAAY;gBAACT,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAAC;cAEzE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC,eAENpD,OAAA;cAAAgD,QAAA,gBACIhD,OAAA;gBAAO+C,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRpD,OAAA;gBACIsC,IAAI,EAAC,MAAM;gBACXD,IAAI,EAAC,sBAAsB;gBAC3BL,KAAK,EAAE5B,QAAQ,CAACG,oBAAqB;gBACrCgD,QAAQ,EAAEpB,iBAAkB;gBAC5BY,SAAS,EAAC,2GAA2G;gBACrHU,WAAW,EAAC;cAAuB;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC,eACFpD,OAAA;gBAAG+C,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAAuC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAGNpD,OAAA;UAAAgD,QAAA,gBACIhD,OAAA;YAAI+C,SAAS,EAAC,0CAA0C;YAAAC,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1EpD,OAAA;YAAK+C,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBAClDhD,OAAA;cAAAgD,QAAA,gBACIhD,OAAA;gBAAO+C,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRpD,OAAA;gBACIsC,IAAI,EAAC,QAAQ;gBACbD,IAAI,EAAC,iBAAiB;gBACtBL,KAAK,EAAE5B,QAAQ,CAACI,eAAgB;gBAChC+C,QAAQ,EAAEpB,iBAAkB;gBAC5BuB,GAAG,EAAC,IAAI;gBACRC,GAAG,EAAC,MAAM;gBACVZ,SAAS,EAAC;cAA2G;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAENpD,OAAA;cAAAgD,QAAA,gBACIhD,OAAA;gBAAO+C,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRpD,OAAA;gBACIsC,IAAI,EAAC,QAAQ;gBACbD,IAAI,EAAC,kBAAkB;gBACvBL,KAAK,EAAE5B,QAAQ,CAACK,gBAAiB;gBACjC8C,QAAQ,EAAEpB,iBAAkB;gBAC5BuB,GAAG,EAAC,GAAG;gBACPC,GAAG,EAAC,IAAI;gBACRZ,SAAS,EAAC;cAA2G;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAGNpD,OAAA;UAAAgD,QAAA,gBACIhD,OAAA;YAAI+C,SAAS,EAAC,0CAA0C;YAAAC,QAAA,EAAC;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC/EpD,OAAA;YAAK+C,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBAClDhD,OAAA;cAAAgD,QAAA,gBACIhD,OAAA;gBAAO+C,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRpD,OAAA;gBACIsC,IAAI,EAAC,QAAQ;gBACbD,IAAI,EAAC,qBAAqB;gBAC1BL,KAAK,EAAE5B,QAAQ,CAACO,mBAAoB;gBACpC4C,QAAQ,EAAEpB,iBAAkB;gBAC5BuB,GAAG,EAAC,GAAG;gBACPC,GAAG,EAAC,IAAI;gBACRZ,SAAS,EAAC;cAA2G;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAENpD,OAAA;cAAK+C,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAC9BhD,OAAA;gBACIsC,IAAI,EAAC,UAAU;gBACfgB,EAAE,EAAC,uBAAuB;gBAC1BjB,IAAI,EAAC,uBAAuB;gBAC5BE,OAAO,EAAEnC,QAAQ,CAACQ,qBAAqB,KAAK,MAAO;gBACnD2C,QAAQ,EAAEpB,iBAAkB;gBAC5BY,SAAS,EAAC;cAAmE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChF,CAAC,eACFpD,OAAA;gBAAOwD,OAAO,EAAC,uBAAuB;gBAACT,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAAC;cAEpF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAGNpD,OAAA;UAAAgD,QAAA,gBACIhD,OAAA;YAAI+C,SAAS,EAAC,0CAA0C;YAAAC,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5EpD,OAAA;YAAK+C,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBAClDhD,OAAA;cAAAgD,QAAA,gBACIhD,OAAA;gBAAO+C,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRpD,OAAA;gBACIsC,IAAI,EAAC,QAAQ;gBACbD,IAAI,EAAC,oBAAoB;gBACzBL,KAAK,EAAE5B,QAAQ,CAACU,kBAAmB;gBACnCyC,QAAQ,EAAEpB,iBAAkB;gBAC5BuB,GAAG,EAAC,GAAG;gBACPC,GAAG,EAAC,IAAI;gBACRZ,SAAS,EAAC;cAA2G;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAENpD,OAAA;cAAAgD,QAAA,gBACIhD,OAAA;gBAAO+C,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRpD,OAAA;gBACIsC,IAAI,EAAC,QAAQ;gBACbD,IAAI,EAAC,cAAc;gBACnBL,KAAK,EAAE5B,QAAQ,CAACM,YAAa;gBAC7B6C,QAAQ,EAAEpB,iBAAkB;gBAC5BuB,GAAG,EAAC,KAAK;gBACTC,GAAG,EAAC,OAAO;gBACXZ,SAAS,EAAC;cAA2G;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAENpD,OAAA;cAAAgD,QAAA,gBACIhD,OAAA;gBAAO+C,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRpD,OAAA;gBACIsC,IAAI,EAAC,QAAQ;gBACbD,IAAI,EAAC,iBAAiB;gBACtBL,KAAK,EAAE5B,QAAQ,CAACS,eAAgB;gBAChC0C,QAAQ,EAAEpB,iBAAkB;gBAC5BuB,GAAG,EAAC,KAAK;gBACTC,GAAG,EAAC,OAAO;gBACXZ,SAAS,EAAC;cAA2G;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAGNpD,OAAA;UAAK+C,SAAS,EAAC,gCAAgC;UAAAC,QAAA,eAC3ChD,OAAA;YACIsC,IAAI,EAAC,QAAQ;YACbsB,QAAQ,EAAE3C,MAAO;YACjB8B,SAAS,EAAC,yJAAyJ;YAAAC,QAAA,gBAEnKhD,OAAA,CAACN,MAAM;cAAAuD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EACTnC,MAAM,GAAG,WAAW,GAAG,wBAAwB;UAAA;YAAAgC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd;AAACjD,EAAA,CA/RQD,gBAAgB;AAAA2D,EAAA,GAAhB3D,gBAAgB;AAiSzB,eAAeA,gBAAgB;AAAC,IAAA2D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}