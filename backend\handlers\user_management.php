<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: GET, PUT, POST, DELETE, OPTIONS");
header("Access-Control-Max-Age: 3600");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

include_once '../includes/db_connect.php';
$conn = getDBConnection();

function sendResponse($status, $message, $data = null) {
    http_response_code($status);
    echo json_encode([
        'success' => $status === 200,
        'message' => $message,
        'data' => $data
    ]);
    exit();
}

switch ($_SERVER['REQUEST_METHOD']) {
    case 'GET':
        // Fetch all users
        try {
            $stmt = $conn->prepare("SELECT user_id, username, full_name, email, favorite_team, balance FROM users");
            $stmt->execute();
            $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
            sendResponse(200, "Users fetched successfully", $users);
        } catch (PDOException $e) {
            sendResponse(500, "Failed to fetch users: " . $e->getMessage());
        }
        break;

    case 'PUT':
        // Update user
        $data = json_decode(file_get_contents("php://input"), true);
        $id = isset($_GET['id']) ? $_GET['id'] : null;

        if (!$id) {
            sendResponse(400, "User ID is required");
        }

        try {
            $stmt = $conn->prepare("UPDATE users SET username = :username, full_name = :full_name, email = :email, favorite_team = :favorite_team, balance = :balance WHERE user_id = :id");
            $stmt->bindParam(':id', $id);
            $stmt->bindParam(':username', $data['username']);
            $stmt->bindParam(':full_name', $data['full_name']);
            $stmt->bindParam(':email', $data['email']);
            $stmt->bindParam(':favorite_team', $data['favorite_team']);
            $stmt->bindParam(':balance', $data['balance']);

            if ($stmt->execute()) {
                sendResponse(200, "User updated successfully");
            } else {
                sendResponse(500, "Failed to update user");
            }
        } catch (PDOException $e) {
            sendResponse(500, "Failed to update user: " . $e->getMessage());
        }
        break;

    case 'DELETE':
        // Delete user
        $id = isset($_GET['id']) ? $_GET['id'] : null;

        if (!$id) {
            sendResponse(400, "User ID is required");
        }

        try {
            $stmt = $conn->prepare("DELETE FROM users WHERE user_id = :id");
            $stmt->bindParam(':id', $id);

            if ($stmt->execute()) {
                sendResponse(200, "User deleted successfully");
            } else {
                sendResponse(500, "Failed to delete user");
            }
        } catch (PDOException $e) {
            sendResponse(500, "Failed to delete user: " . $e->getMessage());
        }
        break;

    default:
        sendResponse(405, "Method not allowed");
        break;
}
