{"ast": null, "code": "var _jsxFileName = \"C:\\\\MAMP\\\\htdocs\\\\FanBet247\\\\frontend\\\\src\\\\components\\\\Admin\\\\AdminAuthPreferences.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { FaShieldAlt, FaKey, FaEnvelope, FaCheck, FaTimes, FaSave, FaSpinner, FaExclamationTriangle, FaCog } from 'react-icons/fa';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst API_BASE_URL = '/backend';\nconst AdminAuthPreferences = ({\n  adminId,\n  onUpdate\n}) => {\n  _s();\n  const [preferences, setPreferences] = useState({\n    auth_method: 'password_only',\n    two_factor_enabled: false,\n    can_use_otp: false,\n    can_use_2fa: false\n  });\n  const [loading, setLoading] = useState(true);\n  const [saving, setSaving] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  const [adminInfo, setAdminInfo] = useState({});\n  const [twoFactorSetup, setTwoFactorSetup] = useState({});\n  useEffect(() => {\n    fetchPreferences();\n  }, [adminId]);\n  const fetchPreferences = async () => {\n    try {\n      setLoading(true);\n      setError('');\n      const response = await axios.get(`${API_BASE_URL}/handlers/admin_auth_preferences.php?adminId=${adminId}`);\n      if (response.data.success) {\n        setAdminInfo(response.data.admin);\n        setPreferences({\n          auth_method: response.data.auth_settings.auth_method || 'password_only',\n          two_factor_enabled: response.data.auth_settings.two_factor_enabled || false,\n          can_use_otp: response.data.can_use_otp,\n          can_use_2fa: response.data.can_use_2fa\n        });\n        setTwoFactorSetup(response.data.two_factor_setup);\n      } else {\n        setError(response.data.message || 'Failed to load authentication preferences');\n      }\n    } catch (err) {\n      setError('Failed to load authentication preferences');\n      console.error('Error fetching preferences:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const updatePreferences = async () => {\n    try {\n      setSaving(true);\n      setError('');\n      const response = await axios.post(`${API_BASE_URL}/handlers/admin_auth_preferences.php?adminId=${adminId}`, {\n        auth_method: preferences.auth_method,\n        enable_two_factor: preferences.two_factor_enabled\n      });\n      if (response.data.success) {\n        setSuccess('Authentication preferences updated successfully!');\n        setTimeout(() => setSuccess(''), 3000);\n        if (onUpdate) {\n          onUpdate(preferences);\n        }\n      } else {\n        setError(response.data.message || 'Failed to update preferences');\n      }\n    } catch (err) {\n      setError('Failed to update authentication preferences');\n      console.error('Error updating preferences:', err);\n    } finally {\n      setSaving(false);\n    }\n  };\n  const handleAuthMethodChange = method => {\n    setPreferences(prev => ({\n      ...prev,\n      auth_method: method,\n      two_factor_enabled: method === '2fa'\n    }));\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg shadow-sm border p-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-center h-32\",\n        children: /*#__PURE__*/_jsxDEV(FaSpinner, {\n          className: \"animate-spin text-2xl text-blue-600\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 90,\n      columnNumber: 13\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-white rounded-lg shadow-sm border\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-3 mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(FaShieldAlt, {\n          className: \"text-blue-500 text-xl\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-gray-800\",\n            children: \"Authentication Preferences\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-600\",\n            children: \"Configure your personal authentication method\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 105,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 17\n      }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-6 bg-red-50 border border-red-200 rounded-lg p-4 flex items-center gap-3\",\n        children: [/*#__PURE__*/_jsxDEV(FaExclamationTriangle, {\n          className: \"text-red-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-red-700 text-sm\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 111,\n        columnNumber: 21\n      }, this), success && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-6 bg-green-50 border border-green-200 rounded-lg p-4 flex items-center gap-3\",\n        children: [/*#__PURE__*/_jsxDEV(FaCheck, {\n          className: \"text-green-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-green-700 text-sm\",\n          children: success\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 119,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-6 bg-gray-50 rounded-lg p-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"font-medium text-gray-800 mb-2\",\n          children: \"Current Status\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 127,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-2 text-sm\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-gray-600\",\n              children: \"Authentication Method:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 130,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-medium capitalize\",\n              children: preferences.auth_method.replace('_', ' ')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-gray-600\",\n              children: \"2FA Setup:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: `font-medium ${twoFactorSetup.setup_completed ? 'text-green-600' : 'text-gray-500'}`,\n              children: twoFactorSetup.setup_completed ? 'Completed' : 'Not Set Up'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 126,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"font-medium text-gray-800\",\n          children: \"Choose Authentication Method\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"border rounded-lg p-4\",\n          children: /*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"flex items-start gap-3 cursor-pointer\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"radio\",\n              name: \"auth_method\",\n              value: \"password_only\",\n              checked: preferences.auth_method === 'password_only',\n              onChange: e => handleAuthMethodChange(e.target.value),\n              className: \"mt-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center gap-2\",\n                children: [/*#__PURE__*/_jsxDEV(FaKey, {\n                  className: \"text-gray-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 159,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium\",\n                  children: \"Password Only\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 160,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 158,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600 mt-1\",\n                children: \"Use only your username and password to log in\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 162,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"border rounded-lg p-4\",\n          children: /*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"flex items-start gap-3 cursor-pointer\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"radio\",\n              name: \"auth_method\",\n              value: \"otp\",\n              checked: preferences.auth_method === 'otp',\n              onChange: e => handleAuthMethodChange(e.target.value),\n              disabled: !preferences.can_use_otp,\n              className: \"mt-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center gap-2\",\n                children: [/*#__PURE__*/_jsxDEV(FaEnvelope, {\n                  className: \"text-blue-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 183,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium\",\n                  children: \"Email OTP\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 184,\n                  columnNumber: 37\n                }, this), !preferences.can_use_otp && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-xs bg-gray-200 text-gray-600 px-2 py-1 rounded\",\n                  children: \"Disabled\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 186,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 182,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600 mt-1\",\n                children: \"Receive a one-time password via email after entering your password\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 189,\n                columnNumber: 33\n              }, this), !preferences.can_use_otp && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-red-600 mt-1\",\n                children: \"OTP is not enabled globally. Contact system administrator.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 193,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"border rounded-lg p-4\",\n          children: /*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"flex items-start gap-3 cursor-pointer\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"radio\",\n              name: \"auth_method\",\n              value: \"2fa\",\n              checked: preferences.auth_method === '2fa',\n              onChange: e => handleAuthMethodChange(e.target.value),\n              disabled: !preferences.can_use_2fa,\n              className: \"mt-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center gap-2\",\n                children: [/*#__PURE__*/_jsxDEV(FaShieldAlt, {\n                  className: \"text-green-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 215,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium\",\n                  children: \"Two-Factor Authentication\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 216,\n                  columnNumber: 37\n                }, this), !preferences.can_use_2fa && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-xs bg-gray-200 text-gray-600 px-2 py-1 rounded\",\n                  children: \"Disabled\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 218,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 214,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600 mt-1\",\n                children: \"Use Google Authenticator for enhanced security\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 221,\n                columnNumber: 33\n              }, this), !preferences.can_use_2fa && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-red-600 mt-1\",\n                children: \"2FA is not enabled globally. Contact system administrator.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 225,\n                columnNumber: 37\n              }, this), preferences.auth_method === '2fa' && !twoFactorSetup.setup_completed && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-yellow-600 mt-1\",\n                children: \"You will need to complete 2FA setup on your next login.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 230,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 213,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-end pt-6 border-t mt-6\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: updatePreferences,\n          disabled: saving,\n          className: \"flex items-center gap-2 px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n          children: [saving ? /*#__PURE__*/_jsxDEV(FaSpinner, {\n            className: \"animate-spin\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 35\n          }, this) : /*#__PURE__*/_jsxDEV(FaSave, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 76\n          }, this), saving ? 'Saving...' : 'Save Preferences']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 240,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 100,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 99,\n    columnNumber: 9\n  }, this);\n};\n_s(AdminAuthPreferences, \"MA8dhE1EKYFn6sFOlzmlabUkVh4=\");\n_c = AdminAuthPreferences;\nexport default AdminAuthPreferences;\nvar _c;\n$RefreshReg$(_c, \"AdminAuthPreferences\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "FaShieldAlt", "FaKey", "FaEnvelope", "FaCheck", "FaTimes", "FaSave", "FaSpinner", "FaExclamationTriangle", "FaCog", "jsxDEV", "_jsxDEV", "API_BASE_URL", "AdminAuthPreferences", "adminId", "onUpdate", "_s", "preferences", "setPreferences", "auth_method", "two_factor_enabled", "can_use_otp", "can_use_2fa", "loading", "setLoading", "saving", "setSaving", "error", "setError", "success", "setSuccess", "adminInfo", "setAdminInfo", "twoFactorSetup", "setTwoFactorSetup", "fetchPreferences", "response", "get", "data", "admin", "auth_settings", "two_factor_setup", "message", "err", "console", "updatePreferences", "post", "enable_two_factor", "setTimeout", "handleAuthMethodChange", "method", "prev", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "replace", "setup_completed", "type", "name", "value", "checked", "onChange", "e", "target", "disabled", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/components/Admin/AdminAuthPreferences.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { FaShieldAlt, <PERSON>a<PERSON>ey, FaEnvelope, FaCheck, FaTimes, FaSave, FaSpinner, FaExclamationTriangle, FaCog } from 'react-icons/fa';\n\nconst API_BASE_URL = '/backend';\n\nconst AdminAuthPreferences = ({ adminId, onUpdate }) => {\n    const [preferences, setPreferences] = useState({\n        auth_method: 'password_only',\n        two_factor_enabled: false,\n        can_use_otp: false,\n        can_use_2fa: false\n    });\n    const [loading, setLoading] = useState(true);\n    const [saving, setSaving] = useState(false);\n    const [error, setError] = useState('');\n    const [success, setSuccess] = useState('');\n    const [adminInfo, setAdminInfo] = useState({});\n    const [twoFactorSetup, setTwoFactorSetup] = useState({});\n\n    useEffect(() => {\n        fetchPreferences();\n    }, [adminId]);\n\n    const fetchPreferences = async () => {\n        try {\n            setLoading(true);\n            setError('');\n\n            const response = await axios.get(`${API_BASE_URL}/handlers/admin_auth_preferences.php?adminId=${adminId}`);\n\n            if (response.data.success) {\n                setAdminInfo(response.data.admin);\n                setPreferences({\n                    auth_method: response.data.auth_settings.auth_method || 'password_only',\n                    two_factor_enabled: response.data.auth_settings.two_factor_enabled || false,\n                    can_use_otp: response.data.can_use_otp,\n                    can_use_2fa: response.data.can_use_2fa\n                });\n                setTwoFactorSetup(response.data.two_factor_setup);\n            } else {\n                setError(response.data.message || 'Failed to load authentication preferences');\n            }\n        } catch (err) {\n            setError('Failed to load authentication preferences');\n            console.error('Error fetching preferences:', err);\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const updatePreferences = async () => {\n        try {\n            setSaving(true);\n            setError('');\n\n            const response = await axios.post(`${API_BASE_URL}/handlers/admin_auth_preferences.php?adminId=${adminId}`, {\n                auth_method: preferences.auth_method,\n                enable_two_factor: preferences.two_factor_enabled\n            });\n\n            if (response.data.success) {\n                setSuccess('Authentication preferences updated successfully!');\n                setTimeout(() => setSuccess(''), 3000);\n                \n                if (onUpdate) {\n                    onUpdate(preferences);\n                }\n            } else {\n                setError(response.data.message || 'Failed to update preferences');\n            }\n        } catch (err) {\n            setError('Failed to update authentication preferences');\n            console.error('Error updating preferences:', err);\n        } finally {\n            setSaving(false);\n        }\n    };\n\n    const handleAuthMethodChange = (method) => {\n        setPreferences(prev => ({\n            ...prev,\n            auth_method: method,\n            two_factor_enabled: method === '2fa'\n        }));\n    };\n\n    if (loading) {\n        return (\n            <div className=\"bg-white rounded-lg shadow-sm border p-6\">\n                <div className=\"flex items-center justify-center h-32\">\n                    <FaSpinner className=\"animate-spin text-2xl text-blue-600\" />\n                </div>\n            </div>\n        );\n    }\n\n    return (\n        <div className=\"bg-white rounded-lg shadow-sm border\">\n            <div className=\"p-6\">\n                <div className=\"flex items-center gap-3 mb-6\">\n                    <FaShieldAlt className=\"text-blue-500 text-xl\" />\n                    <div>\n                        <h3 className=\"text-lg font-semibold text-gray-800\">Authentication Preferences</h3>\n                        <p className=\"text-sm text-gray-600\">Configure your personal authentication method</p>\n                    </div>\n                </div>\n\n                {/* Error Message */}\n                {error && (\n                    <div className=\"mb-6 bg-red-50 border border-red-200 rounded-lg p-4 flex items-center gap-3\">\n                        <FaExclamationTriangle className=\"text-red-500\" />\n                        <span className=\"text-red-700 text-sm\">{error}</span>\n                    </div>\n                )}\n\n                {/* Success Message */}\n                {success && (\n                    <div className=\"mb-6 bg-green-50 border border-green-200 rounded-lg p-4 flex items-center gap-3\">\n                        <FaCheck className=\"text-green-500\" />\n                        <span className=\"text-green-700 text-sm\">{success}</span>\n                    </div>\n                )}\n\n                {/* Current Status */}\n                <div className=\"mb-6 bg-gray-50 rounded-lg p-4\">\n                    <h4 className=\"font-medium text-gray-800 mb-2\">Current Status</h4>\n                    <div className=\"space-y-2 text-sm\">\n                        <div className=\"flex justify-between\">\n                            <span className=\"text-gray-600\">Authentication Method:</span>\n                            <span className=\"font-medium capitalize\">{preferences.auth_method.replace('_', ' ')}</span>\n                        </div>\n                        <div className=\"flex justify-between\">\n                            <span className=\"text-gray-600\">2FA Setup:</span>\n                            <span className={`font-medium ${twoFactorSetup.setup_completed ? 'text-green-600' : 'text-gray-500'}`}>\n                                {twoFactorSetup.setup_completed ? 'Completed' : 'Not Set Up'}\n                            </span>\n                        </div>\n                    </div>\n                </div>\n\n                {/* Authentication Method Selection */}\n                <div className=\"space-y-4\">\n                    <h4 className=\"font-medium text-gray-800\">Choose Authentication Method</h4>\n                    \n                    {/* Password Only */}\n                    <div className=\"border rounded-lg p-4\">\n                        <label className=\"flex items-start gap-3 cursor-pointer\">\n                            <input\n                                type=\"radio\"\n                                name=\"auth_method\"\n                                value=\"password_only\"\n                                checked={preferences.auth_method === 'password_only'}\n                                onChange={(e) => handleAuthMethodChange(e.target.value)}\n                                className=\"mt-1\"\n                            />\n                            <div className=\"flex-1\">\n                                <div className=\"flex items-center gap-2\">\n                                    <FaKey className=\"text-gray-500\" />\n                                    <span className=\"font-medium\">Password Only</span>\n                                </div>\n                                <p className=\"text-sm text-gray-600 mt-1\">\n                                    Use only your username and password to log in\n                                </p>\n                            </div>\n                        </label>\n                    </div>\n\n                    {/* OTP */}\n                    <div className=\"border rounded-lg p-4\">\n                        <label className=\"flex items-start gap-3 cursor-pointer\">\n                            <input\n                                type=\"radio\"\n                                name=\"auth_method\"\n                                value=\"otp\"\n                                checked={preferences.auth_method === 'otp'}\n                                onChange={(e) => handleAuthMethodChange(e.target.value)}\n                                disabled={!preferences.can_use_otp}\n                                className=\"mt-1\"\n                            />\n                            <div className=\"flex-1\">\n                                <div className=\"flex items-center gap-2\">\n                                    <FaEnvelope className=\"text-blue-500\" />\n                                    <span className=\"font-medium\">Email OTP</span>\n                                    {!preferences.can_use_otp && (\n                                        <span className=\"text-xs bg-gray-200 text-gray-600 px-2 py-1 rounded\">Disabled</span>\n                                    )}\n                                </div>\n                                <p className=\"text-sm text-gray-600 mt-1\">\n                                    Receive a one-time password via email after entering your password\n                                </p>\n                                {!preferences.can_use_otp && (\n                                    <p className=\"text-xs text-red-600 mt-1\">\n                                        OTP is not enabled globally. Contact system administrator.\n                                    </p>\n                                )}\n                            </div>\n                        </label>\n                    </div>\n\n                    {/* 2FA */}\n                    <div className=\"border rounded-lg p-4\">\n                        <label className=\"flex items-start gap-3 cursor-pointer\">\n                            <input\n                                type=\"radio\"\n                                name=\"auth_method\"\n                                value=\"2fa\"\n                                checked={preferences.auth_method === '2fa'}\n                                onChange={(e) => handleAuthMethodChange(e.target.value)}\n                                disabled={!preferences.can_use_2fa}\n                                className=\"mt-1\"\n                            />\n                            <div className=\"flex-1\">\n                                <div className=\"flex items-center gap-2\">\n                                    <FaShieldAlt className=\"text-green-500\" />\n                                    <span className=\"font-medium\">Two-Factor Authentication</span>\n                                    {!preferences.can_use_2fa && (\n                                        <span className=\"text-xs bg-gray-200 text-gray-600 px-2 py-1 rounded\">Disabled</span>\n                                    )}\n                                </div>\n                                <p className=\"text-sm text-gray-600 mt-1\">\n                                    Use Google Authenticator for enhanced security\n                                </p>\n                                {!preferences.can_use_2fa && (\n                                    <p className=\"text-xs text-red-600 mt-1\">\n                                        2FA is not enabled globally. Contact system administrator.\n                                    </p>\n                                )}\n                                {preferences.auth_method === '2fa' && !twoFactorSetup.setup_completed && (\n                                    <p className=\"text-xs text-yellow-600 mt-1\">\n                                        You will need to complete 2FA setup on your next login.\n                                    </p>\n                                )}\n                            </div>\n                        </label>\n                    </div>\n                </div>\n\n                {/* Save Button */}\n                <div className=\"flex justify-end pt-6 border-t mt-6\">\n                    <button\n                        onClick={updatePreferences}\n                        disabled={saving}\n                        className=\"flex items-center gap-2 px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n                    >\n                        {saving ? <FaSpinner className=\"animate-spin\" /> : <FaSave />}\n                        {saving ? 'Saving...' : 'Save Preferences'}\n                    </button>\n                </div>\n            </div>\n        </div>\n    );\n};\n\nexport default AdminAuthPreferences;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,EAAEC,KAAK,EAAEC,UAAU,EAAEC,OAAO,EAAEC,OAAO,EAAEC,MAAM,EAAEC,SAAS,EAAEC,qBAAqB,EAAEC,KAAK,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnI,MAAMC,YAAY,GAAG,UAAU;AAE/B,MAAMC,oBAAoB,GAAGA,CAAC;EAAEC,OAAO;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACpD,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGpB,QAAQ,CAAC;IAC3CqB,WAAW,EAAE,eAAe;IAC5BC,kBAAkB,EAAE,KAAK;IACzBC,WAAW,EAAE,KAAK;IAClBC,WAAW,EAAE;EACjB,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG1B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC2B,MAAM,EAAEC,SAAS,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAAC6B,KAAK,EAAEC,QAAQ,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC+B,OAAO,EAAEC,UAAU,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACiC,SAAS,EAAEC,YAAY,CAAC,GAAGlC,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC9C,MAAM,CAACmC,cAAc,EAAEC,iBAAiB,CAAC,GAAGpC,QAAQ,CAAC,CAAC,CAAC,CAAC;EAExDC,SAAS,CAAC,MAAM;IACZoC,gBAAgB,CAAC,CAAC;EACtB,CAAC,EAAE,CAACrB,OAAO,CAAC,CAAC;EAEb,MAAMqB,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACAX,UAAU,CAAC,IAAI,CAAC;MAChBI,QAAQ,CAAC,EAAE,CAAC;MAEZ,MAAMQ,QAAQ,GAAG,MAAMpC,KAAK,CAACqC,GAAG,CAAC,GAAGzB,YAAY,gDAAgDE,OAAO,EAAE,CAAC;MAE1G,IAAIsB,QAAQ,CAACE,IAAI,CAACT,OAAO,EAAE;QACvBG,YAAY,CAACI,QAAQ,CAACE,IAAI,CAACC,KAAK,CAAC;QACjCrB,cAAc,CAAC;UACXC,WAAW,EAAEiB,QAAQ,CAACE,IAAI,CAACE,aAAa,CAACrB,WAAW,IAAI,eAAe;UACvEC,kBAAkB,EAAEgB,QAAQ,CAACE,IAAI,CAACE,aAAa,CAACpB,kBAAkB,IAAI,KAAK;UAC3EC,WAAW,EAAEe,QAAQ,CAACE,IAAI,CAACjB,WAAW;UACtCC,WAAW,EAAEc,QAAQ,CAACE,IAAI,CAAChB;QAC/B,CAAC,CAAC;QACFY,iBAAiB,CAACE,QAAQ,CAACE,IAAI,CAACG,gBAAgB,CAAC;MACrD,CAAC,MAAM;QACHb,QAAQ,CAACQ,QAAQ,CAACE,IAAI,CAACI,OAAO,IAAI,2CAA2C,CAAC;MAClF;IACJ,CAAC,CAAC,OAAOC,GAAG,EAAE;MACVf,QAAQ,CAAC,2CAA2C,CAAC;MACrDgB,OAAO,CAACjB,KAAK,CAAC,6BAA6B,EAAEgB,GAAG,CAAC;IACrD,CAAC,SAAS;MACNnB,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,MAAMqB,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACAnB,SAAS,CAAC,IAAI,CAAC;MACfE,QAAQ,CAAC,EAAE,CAAC;MAEZ,MAAMQ,QAAQ,GAAG,MAAMpC,KAAK,CAAC8C,IAAI,CAAC,GAAGlC,YAAY,gDAAgDE,OAAO,EAAE,EAAE;QACxGK,WAAW,EAAEF,WAAW,CAACE,WAAW;QACpC4B,iBAAiB,EAAE9B,WAAW,CAACG;MACnC,CAAC,CAAC;MAEF,IAAIgB,QAAQ,CAACE,IAAI,CAACT,OAAO,EAAE;QACvBC,UAAU,CAAC,kDAAkD,CAAC;QAC9DkB,UAAU,CAAC,MAAMlB,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;QAEtC,IAAIf,QAAQ,EAAE;UACVA,QAAQ,CAACE,WAAW,CAAC;QACzB;MACJ,CAAC,MAAM;QACHW,QAAQ,CAACQ,QAAQ,CAACE,IAAI,CAACI,OAAO,IAAI,8BAA8B,CAAC;MACrE;IACJ,CAAC,CAAC,OAAOC,GAAG,EAAE;MACVf,QAAQ,CAAC,6CAA6C,CAAC;MACvDgB,OAAO,CAACjB,KAAK,CAAC,6BAA6B,EAAEgB,GAAG,CAAC;IACrD,CAAC,SAAS;MACNjB,SAAS,CAAC,KAAK,CAAC;IACpB;EACJ,CAAC;EAED,MAAMuB,sBAAsB,GAAIC,MAAM,IAAK;IACvChC,cAAc,CAACiC,IAAI,KAAK;MACpB,GAAGA,IAAI;MACPhC,WAAW,EAAE+B,MAAM;MACnB9B,kBAAkB,EAAE8B,MAAM,KAAK;IACnC,CAAC,CAAC,CAAC;EACP,CAAC;EAED,IAAI3B,OAAO,EAAE;IACT,oBACIZ,OAAA;MAAKyC,SAAS,EAAC,0CAA0C;MAAAC,QAAA,eACrD1C,OAAA;QAAKyC,SAAS,EAAC,uCAAuC;QAAAC,QAAA,eAClD1C,OAAA,CAACJ,SAAS;UAAC6C,SAAS,EAAC;QAAqC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5D;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd;EAEA,oBACI9C,OAAA;IAAKyC,SAAS,EAAC,sCAAsC;IAAAC,QAAA,eACjD1C,OAAA;MAAKyC,SAAS,EAAC,KAAK;MAAAC,QAAA,gBAChB1C,OAAA;QAAKyC,SAAS,EAAC,8BAA8B;QAAAC,QAAA,gBACzC1C,OAAA,CAACV,WAAW;UAACmD,SAAS,EAAC;QAAuB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACjD9C,OAAA;UAAA0C,QAAA,gBACI1C,OAAA;YAAIyC,SAAS,EAAC,qCAAqC;YAAAC,QAAA,EAAC;UAA0B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnF9C,OAAA;YAAGyC,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAA6C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,EAGL9B,KAAK,iBACFhB,OAAA;QAAKyC,SAAS,EAAC,6EAA6E;QAAAC,QAAA,gBACxF1C,OAAA,CAACH,qBAAqB;UAAC4C,SAAS,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAClD9C,OAAA;UAAMyC,SAAS,EAAC,sBAAsB;UAAAC,QAAA,EAAE1B;QAAK;UAAA2B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpD,CACR,EAGA5B,OAAO,iBACJlB,OAAA;QAAKyC,SAAS,EAAC,iFAAiF;QAAAC,QAAA,gBAC5F1C,OAAA,CAACP,OAAO;UAACgD,SAAS,EAAC;QAAgB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACtC9C,OAAA;UAAMyC,SAAS,EAAC,wBAAwB;UAAAC,QAAA,EAAExB;QAAO;UAAAyB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxD,CACR,eAGD9C,OAAA;QAAKyC,SAAS,EAAC,gCAAgC;QAAAC,QAAA,gBAC3C1C,OAAA;UAAIyC,SAAS,EAAC,gCAAgC;UAAAC,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClE9C,OAAA;UAAKyC,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAC9B1C,OAAA;YAAKyC,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACjC1C,OAAA;cAAMyC,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAsB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC7D9C,OAAA;cAAMyC,SAAS,EAAC,wBAAwB;cAAAC,QAAA,EAAEpC,WAAW,CAACE,WAAW,CAACuC,OAAO,CAAC,GAAG,EAAE,GAAG;YAAC;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1F,CAAC,eACN9C,OAAA;YAAKyC,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACjC1C,OAAA;cAAMyC,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACjD9C,OAAA;cAAMyC,SAAS,EAAE,eAAenB,cAAc,CAAC0B,eAAe,GAAG,gBAAgB,GAAG,eAAe,EAAG;cAAAN,QAAA,EACjGpB,cAAc,CAAC0B,eAAe,GAAG,WAAW,GAAG;YAAY;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGN9C,OAAA;QAAKyC,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACtB1C,OAAA;UAAIyC,SAAS,EAAC,2BAA2B;UAAAC,QAAA,EAAC;QAA4B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAG3E9C,OAAA;UAAKyC,SAAS,EAAC,uBAAuB;UAAAC,QAAA,eAClC1C,OAAA;YAAOyC,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBACpD1C,OAAA;cACIiD,IAAI,EAAC,OAAO;cACZC,IAAI,EAAC,aAAa;cAClBC,KAAK,EAAC,eAAe;cACrBC,OAAO,EAAE9C,WAAW,CAACE,WAAW,KAAK,eAAgB;cACrD6C,QAAQ,EAAGC,CAAC,IAAKhB,sBAAsB,CAACgB,CAAC,CAACC,MAAM,CAACJ,KAAK,CAAE;cACxDV,SAAS,EAAC;YAAM;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC,eACF9C,OAAA;cAAKyC,SAAS,EAAC,QAAQ;cAAAC,QAAA,gBACnB1C,OAAA;gBAAKyC,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,gBACpC1C,OAAA,CAACT,KAAK;kBAACkD,SAAS,EAAC;gBAAe;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACnC9C,OAAA;kBAAMyC,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC,eACN9C,OAAA;gBAAGyC,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAE1C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eAGN9C,OAAA;UAAKyC,SAAS,EAAC,uBAAuB;UAAAC,QAAA,eAClC1C,OAAA;YAAOyC,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBACpD1C,OAAA;cACIiD,IAAI,EAAC,OAAO;cACZC,IAAI,EAAC,aAAa;cAClBC,KAAK,EAAC,KAAK;cACXC,OAAO,EAAE9C,WAAW,CAACE,WAAW,KAAK,KAAM;cAC3C6C,QAAQ,EAAGC,CAAC,IAAKhB,sBAAsB,CAACgB,CAAC,CAACC,MAAM,CAACJ,KAAK,CAAE;cACxDK,QAAQ,EAAE,CAAClD,WAAW,CAACI,WAAY;cACnC+B,SAAS,EAAC;YAAM;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC,eACF9C,OAAA;cAAKyC,SAAS,EAAC,QAAQ;cAAAC,QAAA,gBACnB1C,OAAA;gBAAKyC,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,gBACpC1C,OAAA,CAACR,UAAU;kBAACiD,SAAS,EAAC;gBAAe;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACxC9C,OAAA;kBAAMyC,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,EAC7C,CAACxC,WAAW,CAACI,WAAW,iBACrBV,OAAA;kBAAMyC,SAAS,EAAC,qDAAqD;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CACvF;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACN9C,OAAA;gBAAGyC,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAE1C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,EACH,CAACxC,WAAW,CAACI,WAAW,iBACrBV,OAAA;gBAAGyC,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EAAC;cAEzC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eAGN9C,OAAA;UAAKyC,SAAS,EAAC,uBAAuB;UAAAC,QAAA,eAClC1C,OAAA;YAAOyC,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBACpD1C,OAAA;cACIiD,IAAI,EAAC,OAAO;cACZC,IAAI,EAAC,aAAa;cAClBC,KAAK,EAAC,KAAK;cACXC,OAAO,EAAE9C,WAAW,CAACE,WAAW,KAAK,KAAM;cAC3C6C,QAAQ,EAAGC,CAAC,IAAKhB,sBAAsB,CAACgB,CAAC,CAACC,MAAM,CAACJ,KAAK,CAAE;cACxDK,QAAQ,EAAE,CAAClD,WAAW,CAACK,WAAY;cACnC8B,SAAS,EAAC;YAAM;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC,eACF9C,OAAA;cAAKyC,SAAS,EAAC,QAAQ;cAAAC,QAAA,gBACnB1C,OAAA;gBAAKyC,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,gBACpC1C,OAAA,CAACV,WAAW;kBAACmD,SAAS,EAAC;gBAAgB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC1C9C,OAAA;kBAAMyC,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAC;gBAAyB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,EAC7D,CAACxC,WAAW,CAACK,WAAW,iBACrBX,OAAA;kBAAMyC,SAAS,EAAC,qDAAqD;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CACvF;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACN9C,OAAA;gBAAGyC,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAE1C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,EACH,CAACxC,WAAW,CAACK,WAAW,iBACrBX,OAAA;gBAAGyC,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EAAC;cAEzC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CACN,EACAxC,WAAW,CAACE,WAAW,KAAK,KAAK,IAAI,CAACc,cAAc,CAAC0B,eAAe,iBACjEhD,OAAA;gBAAGyC,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,EAAC;cAE5C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGN9C,OAAA;QAAKyC,SAAS,EAAC,qCAAqC;QAAAC,QAAA,eAChD1C,OAAA;UACIyD,OAAO,EAAEvB,iBAAkB;UAC3BsB,QAAQ,EAAE1C,MAAO;UACjB2B,SAAS,EAAC,yJAAyJ;UAAAC,QAAA,GAElK5B,MAAM,gBAAGd,OAAA,CAACJ,SAAS;YAAC6C,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAG9C,OAAA,CAACL,MAAM;YAAAgD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EAC5DhC,MAAM,GAAG,WAAW,GAAG,kBAAkB;QAAA;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAACzC,EAAA,CAtPIH,oBAAoB;AAAAwD,EAAA,GAApBxD,oBAAoB;AAwP1B,eAAeA,oBAAoB;AAAC,IAAAwD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}