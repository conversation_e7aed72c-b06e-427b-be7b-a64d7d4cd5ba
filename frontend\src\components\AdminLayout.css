/* Force reset any conflicting styles */
html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  scroll-behavior: smooth;
  overflow-x: hidden;
}

/* Main layout container */
.admin-layout {
  display: flex;
  min-height: 100vh;
  background-color: #f5f5f5;
  overflow-x: hidden;
  overflow-y: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

/* Main wrapper - adjust margin based on sidebar width */
.main-wrapper {
  flex: 1;
  margin-left: 320px;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow-x: hidden;
  overflow-y: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
  transition: margin-left 0.3s ease;
}

/* Content area */
.content {
  flex: 1;
  padding: 1.5rem;
  background-color: #f5f5f5;
  overflow-y: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.content::-webkit-scrollbar {
  width: 0;
  height: 0;
  display: none;
}

/* Main content area */
main {
  min-height: calc(100vh - 180px);
}

/* Footer positioning */
.admin-layout .admin-footer {
  margin-top: auto;
  background: #fff;
  padding: 15px 0;
  border-top: 1px solid #e0e0e0;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .main-wrapper {
    margin-left: 0;
    padding-top: 60px; /* Space for mobile toggle button */
  }

  .content {
    padding: 1rem;
  }
}

@media (max-width: 768px) {
  .main-wrapper {
    padding-top: 50px; /* Adjust for smaller mobile toggle */
  }

  .content {
    padding: 0.75rem;
  }
}

/* Adjust main wrapper for collapsed sidebar on desktop */
@media (min-width: 1025px) {
  .main-wrapper.sidebar-collapsed {
    margin-left: 80px;
  }
}

/* Content Card Styles */
.content-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 24px;
  margin-bottom: 24px;
}

/* Form Styles */
.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
}

.form-control {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

/* Button Styles */
.btn {
  padding: 10px 20px;
  border-radius: 4px;
  border: none;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.btn-primary {
  background-color: #2c5f2d;
  color: white;
}

.btn-primary:hover {
  background-color: #234b24;
}

/* Message Styles */
.success-message {
  background-color: #d4edda;
  color: #155724;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.error-message {
  background-color: #f8d7da;
  color: #721c24;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 20px;
}

/* Hide scrollbars completely */
.admin-layout .content::-webkit-scrollbar,
.admin-layout .main-wrapper::-webkit-scrollbar,
.admin-layout::-webkit-scrollbar {
  width: 0 !important;
  height: 0 !important;
  display: none !important;
}

.admin-layout,
.admin-layout .main-wrapper,
.admin-layout .content {
  scrollbar-width: none !important;
  -ms-overflow-style: none !important;
}

/* Prevent scroll on body when modal is open */
body.modal-open {
  overflow: hidden !important;
}

/* Notification container */
.notification-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
  display: flex;
  flex-direction: column;
  gap: 10px;
  max-width: 350px;
}

/* Animation for notifications */
@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.notification-container .alert-message {
  animation: slideIn 0.3s ease forwards;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
