{"ast": null, "code": "var _jsxFileName = \"C:\\\\MAMP\\\\htdocs\\\\FanBet247\\\\frontend\\\\src\\\\components\\\\Admin\\\\Admin2FAVerification.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport axios from 'axios';\nimport { FaShieldAlt, Fa<PERSON>ey, FaSpinner, FaExclamationTriangle, FaArrowLeft, FaQuestionCircle } from 'react-icons/fa';\nimport '../../pages/AdminLoginPage.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst API_BASE_URL = '/backend';\nconst Admin2FAVerification = ({\n  adminId,\n  username,\n  onSuccess,\n  onBack\n}) => {\n  _s();\n  const [verificationCode, setVerificationCode] = useState('');\n  const [backupCode, setBackupCode] = useState('');\n  const [useBackupCode, setUseBackupCode] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  const verify2FA = async () => {\n    const code = useBackupCode ? backupCode : verificationCode;\n    if (!code || (useBackupCode ? code.length !== 8 : code.length !== 6)) {\n      setError(useBackupCode ? 'Please enter a valid 8-character backup code' : 'Please enter a valid 6-digit code');\n      return;\n    }\n    try {\n      setLoading(true);\n      setError('');\n      const response = await axios.post(`${API_BASE_URL}/handlers/admin_verify_2fa.php`, {\n        admin_id: adminId,\n        code: code,\n        is_backup_code: useBackupCode\n      });\n      if (response.data.success) {\n        setSuccess('2FA verification successful!');\n\n        // Show warning if backup codes are running low\n        if (response.data.backup_code_warning) {\n          setTimeout(() => {\n            alert(response.data.backup_code_warning);\n          }, 1000);\n        }\n        setTimeout(() => {\n          onSuccess({\n            admin_id: response.data.admin_id,\n            username: response.data.username,\n            role: response.data.role,\n            session_token: response.data.session_token,\n            auth_method: response.data.auth_method,\n            used_backup_code: response.data.used_backup_code,\n            remaining_backup_codes: response.data.remaining_backup_codes\n          });\n        }, 1000);\n      } else {\n        setError(response.data.message || 'Invalid verification code');\n        // Clear the input on error\n        if (useBackupCode) {\n          setBackupCode('');\n        } else {\n          setVerificationCode('');\n        }\n      }\n    } catch (err) {\n      setError('Failed to verify 2FA code. Please try again.');\n      console.error('2FA verification error:', err);\n      if (useBackupCode) {\n        setBackupCode('');\n      } else {\n        setVerificationCode('');\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleKeyPress = e => {\n    if (e.key === 'Enter') {\n      verify2FA();\n    }\n  };\n  const handleSubmit = e => {\n    e.preventDefault();\n    verify2FA();\n  };\n  const toggleBackupCode = () => {\n    setUseBackupCode(!useBackupCode);\n    setVerificationCode('');\n    setBackupCode('');\n    setError('');\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"admin-login-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"login-left-panel\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"login-logo\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"logo-icon\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            strokeWidth: \"2\",\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            children: [/*#__PURE__*/_jsxDEV(\"rect\", {\n              x: \"2\",\n              y: \"3\",\n              width: \"20\",\n              height: \"14\",\n              rx: \"2\",\n              ry: \"2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 101,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n              x1: \"8\",\n              y1: \"21\",\n              x2: \"16\",\n              y2: \"21\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 102,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n              x1: \"12\",\n              y1: \"17\",\n              x2: \"12\",\n              y2: \"21\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 103,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 100,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"FanBet247\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 98,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"login-form-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Two-Factor Authentication\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"login-subtitle\",\n          children: useBackupCode ? 'Enter one of your backup codes to continue' : 'Enter the 6-digit code from your Google Authenticator app'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"security-notice\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"security-icon\",\n            children: /*#__PURE__*/_jsxDEV(FaShieldAlt, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [\"Logged in as: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: username\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 123,\n              columnNumber: 45\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 123,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 21\n        }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"error-message\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 127,\n          columnNumber: 31\n        }, this), success && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            padding: '0.75rem',\n            backgroundColor: 'rgba(34, 197, 94, 0.1)',\n            color: '#16a34a',\n            borderRadius: '0.5rem',\n            marginBottom: '1.5rem',\n            textAlign: 'center',\n            fontSize: '0.875rem',\n            border: '1px solid rgba(34, 197, 94, 0.2)'\n          },\n          children: success\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: useBackupCode ? /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"backupCode\",\n                children: \"Backup Code\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 150,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"input-container\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  id: \"backupCode\",\n                  maxLength: \"8\",\n                  value: backupCode,\n                  onChange: e => setBackupCode(e.target.value.toUpperCase().replace(/[^A-Z0-9]/g, '')),\n                  onKeyPress: handleKeyPress,\n                  placeholder: \"XXXXXXXX\",\n                  disabled: loading,\n                  style: {\n                    textAlign: 'center',\n                    fontSize: '1.125rem',\n                    fontFamily: 'monospace',\n                    letterSpacing: '0.1em'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 152,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"input-icon\",\n                  children: /*#__PURE__*/_jsxDEV(FaKey, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 169,\n                    columnNumber: 45\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 168,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 151,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  fontSize: '0.75rem',\n                  color: '#6b7280',\n                  marginTop: '0.25rem'\n                },\n                children: \"Enter one of your 8-character backup codes\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 33\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"verificationCode\",\n                children: \"Authenticator Code\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 178,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"input-container\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  id: \"verificationCode\",\n                  maxLength: \"6\",\n                  value: verificationCode,\n                  onChange: e => setVerificationCode(e.target.value.replace(/\\D/g, '')),\n                  onKeyPress: handleKeyPress,\n                  placeholder: \"000000\",\n                  disabled: loading,\n                  style: {\n                    textAlign: 'center',\n                    fontSize: '1.25rem',\n                    fontFamily: 'monospace',\n                    letterSpacing: '0.2em'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 180,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"input-icon\",\n                  children: /*#__PURE__*/_jsxDEV(FaShieldAlt, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 197,\n                    columnNumber: 45\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 196,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 179,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  fontSize: '0.75rem',\n                  color: '#6b7280',\n                  marginTop: '0.25rem'\n                },\n                children: \"Enter the 6-digit code from Google Authenticator\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 200,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'center',\n              marginBottom: '1.5rem'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              onClick: toggleBackupCode,\n              disabled: loading,\n              style: {\n                display: 'inline-flex',\n                alignItems: 'center',\n                gap: '0.5rem',\n                color: '#2C5F2D',\n                fontSize: '0.875rem',\n                textDecoration: 'none',\n                transition: 'color 0.3s ease',\n                background: 'none',\n                border: 'none',\n                cursor: 'pointer',\n                padding: '0.5rem',\n                borderRadius: '0.375rem',\n                whiteSpace: 'nowrap'\n              },\n              onMouseOver: e => e.target.style.color = '#224924',\n              onMouseOut: e => e.target.style.color = '#2C5F2D',\n              children: [/*#__PURE__*/_jsxDEV(FaQuestionCircle, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 234,\n                columnNumber: 33\n              }, this), useBackupCode ? 'Use Authenticator App' : 'Use Backup Code']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            className: \"login-button\",\n            disabled: loading || (useBackupCode ? backupCode.length !== 8 : verificationCode.length !== 6),\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              gap: '0.5rem'\n            },\n            children: [loading && /*#__PURE__*/_jsxDEV(FaSpinner, {\n              className: \"animate-spin\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 251,\n              columnNumber: 41\n            }, this), loading ? 'Verifying...' : 'Verify Code']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            padding: '0.75rem 1rem',\n            backgroundColor: 'rgba(59, 130, 246, 0.1)',\n            border: '1px solid rgba(59, 130, 246, 0.2)',\n            borderRadius: '0.5rem',\n            marginTop: '1.5rem'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'flex-start',\n              gap: '0.75rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(FaQuestionCircle, {\n              style: {\n                color: '#3b82f6',\n                marginTop: '0.125rem',\n                flexShrink: 0\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 265,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '0.875rem',\n                color: '#1e40af'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  fontWeight: '500',\n                  marginBottom: '0.25rem'\n                },\n                children: \"Need help?\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 267,\n                columnNumber: 33\n              }, this), useBackupCode ? /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  margin: 0\n                },\n                children: \"Backup codes are 8-character codes you saved when setting up 2FA. Each code can only be used once.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 269,\n                columnNumber: 37\n              }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  margin: 0\n                },\n                children: \"Open your Google Authenticator app and find the 6-digit code for FanBet247 Admin. The code changes every 30 seconds.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 274,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 266,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 264,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 97,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"login-right-panel\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 285,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 96,\n    columnNumber: 9\n  }, this);\n};\n_s(Admin2FAVerification, \"bHNbUW6wTAsJStfuZDmvVCMqYag=\");\n_c = Admin2FAVerification;\nexport default Admin2FAVerification;\nvar _c;\n$RefreshReg$(_c, \"Admin2FAVerification\");", "map": {"version": 3, "names": ["React", "useState", "axios", "FaShieldAlt", "FaKey", "FaSpinner", "FaExclamationTriangle", "FaArrowLeft", "FaQuestionCircle", "jsxDEV", "_jsxDEV", "API_BASE_URL", "Admin2FAVerification", "adminId", "username", "onSuccess", "onBack", "_s", "verificationCode", "setVerificationCode", "backupCode", "setBackupCode", "useBackupCode", "setUseBackupCode", "loading", "setLoading", "error", "setError", "success", "setSuccess", "verify2FA", "code", "length", "response", "post", "admin_id", "is_backup_code", "data", "backup_code_warning", "setTimeout", "alert", "role", "session_token", "auth_method", "used_backup_code", "remaining_backup_codes", "message", "err", "console", "handleKeyPress", "e", "key", "handleSubmit", "preventDefault", "toggleBackupCode", "className", "children", "xmlns", "viewBox", "fill", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "x", "y", "width", "height", "rx", "ry", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "x1", "y1", "x2", "y2", "style", "padding", "backgroundColor", "color", "borderRadius", "marginBottom", "textAlign", "fontSize", "border", "onSubmit", "htmlFor", "type", "id", "max<PERSON><PERSON><PERSON>", "value", "onChange", "target", "toUpperCase", "replace", "onKeyPress", "placeholder", "disabled", "fontFamily", "letterSpacing", "marginTop", "onClick", "display", "alignItems", "gap", "textDecoration", "transition", "background", "cursor", "whiteSpace", "onMouseOver", "onMouseOut", "justifyContent", "flexShrink", "fontWeight", "margin", "_c", "$RefreshReg$"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/components/Admin/Admin2FAVerification.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport axios from 'axios';\nimport { Fa<PERSON><PERSON><PERSON><PERSON>lt, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, FaExclamation<PERSON>riangle, FaArrowLeft, FaQuestionCircle } from 'react-icons/fa';\nimport '../../pages/AdminLoginPage.css';\n\nconst API_BASE_URL = '/backend';\n\nconst Admin2FAVerification = ({ adminId, username, onSuccess, onBack }) => {\n    const [verificationCode, setVerificationCode] = useState('');\n    const [backupCode, setBackupCode] = useState('');\n    const [useBackupCode, setUseBackupCode] = useState(false);\n    const [loading, setLoading] = useState(false);\n    const [error, setError] = useState('');\n    const [success, setSuccess] = useState('');\n\n    const verify2FA = async () => {\n        const code = useBackupCode ? backupCode : verificationCode;\n        \n        if (!code || (useBackupCode ? code.length !== 8 : code.length !== 6)) {\n            setError(useBackupCode ? 'Please enter a valid 8-character backup code' : 'Please enter a valid 6-digit code');\n            return;\n        }\n\n        try {\n            setLoading(true);\n            setError('');\n\n            const response = await axios.post(`${API_BASE_URL}/handlers/admin_verify_2fa.php`, {\n                admin_id: adminId,\n                code: code,\n                is_backup_code: useBackupCode\n            });\n\n            if (response.data.success) {\n                setSuccess('2FA verification successful!');\n                \n                // Show warning if backup codes are running low\n                if (response.data.backup_code_warning) {\n                    setTimeout(() => {\n                        alert(response.data.backup_code_warning);\n                    }, 1000);\n                }\n                \n                setTimeout(() => {\n                    onSuccess({\n                        admin_id: response.data.admin_id,\n                        username: response.data.username,\n                        role: response.data.role,\n                        session_token: response.data.session_token,\n                        auth_method: response.data.auth_method,\n                        used_backup_code: response.data.used_backup_code,\n                        remaining_backup_codes: response.data.remaining_backup_codes\n                    });\n                }, 1000);\n            } else {\n                setError(response.data.message || 'Invalid verification code');\n                // Clear the input on error\n                if (useBackupCode) {\n                    setBackupCode('');\n                } else {\n                    setVerificationCode('');\n                }\n            }\n        } catch (err) {\n            setError('Failed to verify 2FA code. Please try again.');\n            console.error('2FA verification error:', err);\n            if (useBackupCode) {\n                setBackupCode('');\n            } else {\n                setVerificationCode('');\n            }\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const handleKeyPress = (e) => {\n        if (e.key === 'Enter') {\n            verify2FA();\n        }\n    };\n\n    const handleSubmit = (e) => {\n        e.preventDefault();\n        verify2FA();\n    };\n\n    const toggleBackupCode = () => {\n        setUseBackupCode(!useBackupCode);\n        setVerificationCode('');\n        setBackupCode('');\n        setError('');\n    };\n\n    return (\n        <div className=\"admin-login-container\">\n            <div className=\"login-left-panel\">\n                <div className=\"login-logo\">\n                    <div className=\"logo-icon\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\">\n                            <rect x=\"2\" y=\"3\" width=\"20\" height=\"14\" rx=\"2\" ry=\"2\"></rect>\n                            <line x1=\"8\" y1=\"21\" x2=\"16\" y2=\"21\"></line>\n                            <line x1=\"12\" y1=\"17\" x2=\"12\" y2=\"21\"></line>\n                        </svg>\n                    </div>\n                    <h1>FanBet247</h1>\n                </div>\n\n                <div className=\"login-form-container\">\n                    <h2>Two-Factor Authentication</h2>\n                    <p className=\"login-subtitle\">\n                        {useBackupCode\n                            ? 'Enter one of your backup codes to continue'\n                            : 'Enter the 6-digit code from your Google Authenticator app'\n                        }\n                    </p>\n\n                    {/* User Info */}\n                    <div className=\"security-notice\">\n                        <div className=\"security-icon\">\n                            <FaShieldAlt />\n                        </div>\n                        <span>Logged in as: <strong>{username}</strong></span>\n                    </div>\n\n                    {/* Error Message */}\n                    {error && <div className=\"error-message\">{error}</div>}\n\n                    {/* Success Message */}\n                    {success && (\n                        <div style={{\n                            padding: '0.75rem',\n                            backgroundColor: 'rgba(34, 197, 94, 0.1)',\n                            color: '#16a34a',\n                            borderRadius: '0.5rem',\n                            marginBottom: '1.5rem',\n                            textAlign: 'center',\n                            fontSize: '0.875rem',\n                            border: '1px solid rgba(34, 197, 94, 0.2)'\n                        }}>\n                            {success}\n                        </div>\n                    )}\n\n                    <form onSubmit={handleSubmit}>\n                        {/* Verification Input */}\n                        <div className=\"form-group\">\n                            {useBackupCode ? (\n                                <div>\n                                    <label htmlFor=\"backupCode\">Backup Code</label>\n                                    <div className=\"input-container\">\n                                        <input\n                                            type=\"text\"\n                                            id=\"backupCode\"\n                                            maxLength=\"8\"\n                                            value={backupCode}\n                                            onChange={(e) => setBackupCode(e.target.value.toUpperCase().replace(/[^A-Z0-9]/g, ''))}\n                                            onKeyPress={handleKeyPress}\n                                            placeholder=\"XXXXXXXX\"\n                                            disabled={loading}\n                                            style={{\n                                                textAlign: 'center',\n                                                fontSize: '1.125rem',\n                                                fontFamily: 'monospace',\n                                                letterSpacing: '0.1em'\n                                            }}\n                                        />\n                                        <div className=\"input-icon\">\n                                            <FaKey />\n                                        </div>\n                                    </div>\n                                    <p style={{ fontSize: '0.75rem', color: '#6b7280', marginTop: '0.25rem' }}>\n                                        Enter one of your 8-character backup codes\n                                    </p>\n                                </div>\n                            ) : (\n                                <div>\n                                    <label htmlFor=\"verificationCode\">Authenticator Code</label>\n                                    <div className=\"input-container\">\n                                        <input\n                                            type=\"text\"\n                                            id=\"verificationCode\"\n                                            maxLength=\"6\"\n                                            value={verificationCode}\n                                            onChange={(e) => setVerificationCode(e.target.value.replace(/\\D/g, ''))}\n                                            onKeyPress={handleKeyPress}\n                                            placeholder=\"000000\"\n                                            disabled={loading}\n                                            style={{\n                                                textAlign: 'center',\n                                                fontSize: '1.25rem',\n                                                fontFamily: 'monospace',\n                                                letterSpacing: '0.2em'\n                                            }}\n                                        />\n                                        <div className=\"input-icon\">\n                                            <FaShieldAlt />\n                                        </div>\n                                    </div>\n                                    <p style={{ fontSize: '0.75rem', color: '#6b7280', marginTop: '0.25rem' }}>\n                                        Enter the 6-digit code from Google Authenticator\n                                    </p>\n                                </div>\n                            )}\n                        </div>\n\n                        {/* Toggle Backup Code Link */}\n                        <div style={{\n                            textAlign: 'center',\n                            marginBottom: '1.5rem'\n                        }}>\n                            <button\n                                type=\"button\"\n                                onClick={toggleBackupCode}\n                                disabled={loading}\n                                style={{\n                                    display: 'inline-flex',\n                                    alignItems: 'center',\n                                    gap: '0.5rem',\n                                    color: '#2C5F2D',\n                                    fontSize: '0.875rem',\n                                    textDecoration: 'none',\n                                    transition: 'color 0.3s ease',\n                                    background: 'none',\n                                    border: 'none',\n                                    cursor: 'pointer',\n                                    padding: '0.5rem',\n                                    borderRadius: '0.375rem',\n                                    whiteSpace: 'nowrap'\n                                }}\n                                onMouseOver={(e) => e.target.style.color = '#224924'}\n                                onMouseOut={(e) => e.target.style.color = '#2C5F2D'}\n                            >\n                                <FaQuestionCircle />\n                                {useBackupCode ? 'Use Authenticator App' : 'Use Backup Code'}\n                            </button>\n                        </div>\n\n                        {/* Verify Button */}\n                        <button\n                            type=\"submit\"\n                            className=\"login-button\"\n                            disabled={loading || (useBackupCode ? backupCode.length !== 8 : verificationCode.length !== 6)}\n                            style={{\n                                display: 'flex',\n                                alignItems: 'center',\n                                justifyContent: 'center',\n                                gap: '0.5rem'\n                            }}\n                        >\n                            {loading && <FaSpinner className=\"animate-spin\" />}\n                            {loading ? 'Verifying...' : 'Verify Code'}\n                        </button>\n                    </form>\n\n                    {/* Help Text */}\n                    <div style={{\n                        padding: '0.75rem 1rem',\n                        backgroundColor: 'rgba(59, 130, 246, 0.1)',\n                        border: '1px solid rgba(59, 130, 246, 0.2)',\n                        borderRadius: '0.5rem',\n                        marginTop: '1.5rem'\n                    }}>\n                        <div style={{ display: 'flex', alignItems: 'flex-start', gap: '0.75rem' }}>\n                            <FaQuestionCircle style={{ color: '#3b82f6', marginTop: '0.125rem', flexShrink: 0 }} />\n                            <div style={{ fontSize: '0.875rem', color: '#1e40af' }}>\n                                <p style={{ fontWeight: '500', marginBottom: '0.25rem' }}>Need help?</p>\n                                {useBackupCode ? (\n                                    <p style={{ margin: 0 }}>\n                                        Backup codes are 8-character codes you saved when setting up 2FA.\n                                        Each code can only be used once.\n                                    </p>\n                                ) : (\n                                    <p style={{ margin: 0 }}>\n                                        Open your Google Authenticator app and find the 6-digit code for FanBet247 Admin.\n                                        The code changes every 30 seconds.\n                                    </p>\n                                )}\n                            </div>\n                        </div>\n                    </div>\n                </div>\n            </div>\n\n            <div className=\"login-right-panel\"></div>\n        </div>\n    );\n};\n\nexport default Admin2FAVerification;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,EAAEC,KAAK,EAAEC,SAAS,EAAEC,qBAAqB,EAAEC,WAAW,EAAEC,gBAAgB,QAAQ,gBAAgB;AACpH,OAAO,gCAAgC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,YAAY,GAAG,UAAU;AAE/B,MAAMC,oBAAoB,GAAGA,CAAC;EAAEC,OAAO;EAAEC,QAAQ;EAAEC,SAAS;EAAEC;AAAO,CAAC,KAAK;EAAAC,EAAA;EACvE,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACmB,UAAU,EAAEC,aAAa,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACqB,aAAa,EAAEC,gBAAgB,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACuB,OAAO,EAAEC,UAAU,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACyB,KAAK,EAAEC,QAAQ,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC2B,OAAO,EAAEC,UAAU,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EAE1C,MAAM6B,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC1B,MAAMC,IAAI,GAAGT,aAAa,GAAGF,UAAU,GAAGF,gBAAgB;IAE1D,IAAI,CAACa,IAAI,KAAKT,aAAa,GAAGS,IAAI,CAACC,MAAM,KAAK,CAAC,GAAGD,IAAI,CAACC,MAAM,KAAK,CAAC,CAAC,EAAE;MAClEL,QAAQ,CAACL,aAAa,GAAG,8CAA8C,GAAG,mCAAmC,CAAC;MAC9G;IACJ;IAEA,IAAI;MACAG,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,EAAE,CAAC;MAEZ,MAAMM,QAAQ,GAAG,MAAM/B,KAAK,CAACgC,IAAI,CAAC,GAAGvB,YAAY,gCAAgC,EAAE;QAC/EwB,QAAQ,EAAEtB,OAAO;QACjBkB,IAAI,EAAEA,IAAI;QACVK,cAAc,EAAEd;MACpB,CAAC,CAAC;MAEF,IAAIW,QAAQ,CAACI,IAAI,CAACT,OAAO,EAAE;QACvBC,UAAU,CAAC,8BAA8B,CAAC;;QAE1C;QACA,IAAII,QAAQ,CAACI,IAAI,CAACC,mBAAmB,EAAE;UACnCC,UAAU,CAAC,MAAM;YACbC,KAAK,CAACP,QAAQ,CAACI,IAAI,CAACC,mBAAmB,CAAC;UAC5C,CAAC,EAAE,IAAI,CAAC;QACZ;QAEAC,UAAU,CAAC,MAAM;UACbxB,SAAS,CAAC;YACNoB,QAAQ,EAAEF,QAAQ,CAACI,IAAI,CAACF,QAAQ;YAChCrB,QAAQ,EAAEmB,QAAQ,CAACI,IAAI,CAACvB,QAAQ;YAChC2B,IAAI,EAAER,QAAQ,CAACI,IAAI,CAACI,IAAI;YACxBC,aAAa,EAAET,QAAQ,CAACI,IAAI,CAACK,aAAa;YAC1CC,WAAW,EAAEV,QAAQ,CAACI,IAAI,CAACM,WAAW;YACtCC,gBAAgB,EAAEX,QAAQ,CAACI,IAAI,CAACO,gBAAgB;YAChDC,sBAAsB,EAAEZ,QAAQ,CAACI,IAAI,CAACQ;UAC1C,CAAC,CAAC;QACN,CAAC,EAAE,IAAI,CAAC;MACZ,CAAC,MAAM;QACHlB,QAAQ,CAACM,QAAQ,CAACI,IAAI,CAACS,OAAO,IAAI,2BAA2B,CAAC;QAC9D;QACA,IAAIxB,aAAa,EAAE;UACfD,aAAa,CAAC,EAAE,CAAC;QACrB,CAAC,MAAM;UACHF,mBAAmB,CAAC,EAAE,CAAC;QAC3B;MACJ;IACJ,CAAC,CAAC,OAAO4B,GAAG,EAAE;MACVpB,QAAQ,CAAC,8CAA8C,CAAC;MACxDqB,OAAO,CAACtB,KAAK,CAAC,yBAAyB,EAAEqB,GAAG,CAAC;MAC7C,IAAIzB,aAAa,EAAE;QACfD,aAAa,CAAC,EAAE,CAAC;MACrB,CAAC,MAAM;QACHF,mBAAmB,CAAC,EAAE,CAAC;MAC3B;IACJ,CAAC,SAAS;MACNM,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,MAAMwB,cAAc,GAAIC,CAAC,IAAK;IAC1B,IAAIA,CAAC,CAACC,GAAG,KAAK,OAAO,EAAE;MACnBrB,SAAS,CAAC,CAAC;IACf;EACJ,CAAC;EAED,MAAMsB,YAAY,GAAIF,CAAC,IAAK;IACxBA,CAAC,CAACG,cAAc,CAAC,CAAC;IAClBvB,SAAS,CAAC,CAAC;EACf,CAAC;EAED,MAAMwB,gBAAgB,GAAGA,CAAA,KAAM;IAC3B/B,gBAAgB,CAAC,CAACD,aAAa,CAAC;IAChCH,mBAAmB,CAAC,EAAE,CAAC;IACvBE,aAAa,CAAC,EAAE,CAAC;IACjBM,QAAQ,CAAC,EAAE,CAAC;EAChB,CAAC;EAED,oBACIjB,OAAA;IAAK6C,SAAS,EAAC,uBAAuB;IAAAC,QAAA,gBAClC9C,OAAA;MAAK6C,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC7B9C,OAAA;QAAK6C,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACvB9C,OAAA;UAAK6C,SAAS,EAAC,WAAW;UAAAC,QAAA,eACtB9C,OAAA;YAAK+C,KAAK,EAAC,4BAA4B;YAACC,OAAO,EAAC,WAAW;YAACC,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,WAAW,EAAC,GAAG;YAACC,aAAa,EAAC,OAAO;YAACC,cAAc,EAAC,OAAO;YAAAP,QAAA,gBACtJ9C,OAAA;cAAMsD,CAAC,EAAC,GAAG;cAACC,CAAC,EAAC,GAAG;cAACC,KAAK,EAAC,IAAI;cAACC,MAAM,EAAC,IAAI;cAACC,EAAE,EAAC,GAAG;cAACC,EAAE,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC9D/D,OAAA;cAAMgE,EAAE,EAAC,GAAG;cAACC,EAAE,EAAC,IAAI;cAACC,EAAE,EAAC,IAAI;cAACC,EAAE,EAAC;YAAI;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC5C/D,OAAA;cAAMgE,EAAE,EAAC,IAAI;cAACC,EAAE,EAAC,IAAI;cAACC,EAAE,EAAC,IAAI;cAACC,EAAE,EAAC;YAAI;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACN/D,OAAA;UAAA8C,QAAA,EAAI;QAAS;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC,eAEN/D,OAAA;QAAK6C,SAAS,EAAC,sBAAsB;QAAAC,QAAA,gBACjC9C,OAAA;UAAA8C,QAAA,EAAI;QAAyB;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClC/D,OAAA;UAAG6C,SAAS,EAAC,gBAAgB;UAAAC,QAAA,EACxBlC,aAAa,GACR,4CAA4C,GAC5C;QAA2D;UAAAgD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAElE,CAAC,eAGJ/D,OAAA;UAAK6C,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC5B9C,OAAA;YAAK6C,SAAS,EAAC,eAAe;YAAAC,QAAA,eAC1B9C,OAAA,CAACP,WAAW;cAAAmE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd,CAAC,eACN/D,OAAA;YAAA8C,QAAA,GAAM,gBAAc,eAAA9C,OAAA;cAAA8C,QAAA,EAAS1C;YAAQ;cAAAwD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD,CAAC,EAGL/C,KAAK,iBAAIhB,OAAA;UAAK6C,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAE9B;QAAK;UAAA4C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EAGrD7C,OAAO,iBACJlB,OAAA;UAAKoE,KAAK,EAAE;YACRC,OAAO,EAAE,SAAS;YAClBC,eAAe,EAAE,wBAAwB;YACzCC,KAAK,EAAE,SAAS;YAChBC,YAAY,EAAE,QAAQ;YACtBC,YAAY,EAAE,QAAQ;YACtBC,SAAS,EAAE,QAAQ;YACnBC,QAAQ,EAAE,UAAU;YACpBC,MAAM,EAAE;UACZ,CAAE;UAAA9B,QAAA,EACG5B;QAAO;UAAA0C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CACR,eAED/D,OAAA;UAAM6E,QAAQ,EAAEnC,YAAa;UAAAI,QAAA,gBAEzB9C,OAAA;YAAK6C,SAAS,EAAC,YAAY;YAAAC,QAAA,EACtBlC,aAAa,gBACVZ,OAAA;cAAA8C,QAAA,gBACI9C,OAAA;gBAAO8E,OAAO,EAAC,YAAY;gBAAAhC,QAAA,EAAC;cAAW;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC/C/D,OAAA;gBAAK6C,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC5B9C,OAAA;kBACI+E,IAAI,EAAC,MAAM;kBACXC,EAAE,EAAC,YAAY;kBACfC,SAAS,EAAC,GAAG;kBACbC,KAAK,EAAExE,UAAW;kBAClByE,QAAQ,EAAG3C,CAAC,IAAK7B,aAAa,CAAC6B,CAAC,CAAC4C,MAAM,CAACF,KAAK,CAACG,WAAW,CAAC,CAAC,CAACC,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,CAAE;kBACvFC,UAAU,EAAEhD,cAAe;kBAC3BiD,WAAW,EAAC,UAAU;kBACtBC,QAAQ,EAAE3E,OAAQ;kBAClBsD,KAAK,EAAE;oBACHM,SAAS,EAAE,QAAQ;oBACnBC,QAAQ,EAAE,UAAU;oBACpBe,UAAU,EAAE,WAAW;oBACvBC,aAAa,EAAE;kBACnB;gBAAE;kBAAA/B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACF/D,OAAA;kBAAK6C,SAAS,EAAC,YAAY;kBAAAC,QAAA,eACvB9C,OAAA,CAACN,KAAK;oBAAAkE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACN/D,OAAA;gBAAGoE,KAAK,EAAE;kBAAEO,QAAQ,EAAE,SAAS;kBAAEJ,KAAK,EAAE,SAAS;kBAAEqB,SAAS,EAAE;gBAAU,CAAE;gBAAA9C,QAAA,EAAC;cAE3E;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,gBAEN/D,OAAA;cAAA8C,QAAA,gBACI9C,OAAA;gBAAO8E,OAAO,EAAC,kBAAkB;gBAAAhC,QAAA,EAAC;cAAkB;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC5D/D,OAAA;gBAAK6C,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC5B9C,OAAA;kBACI+E,IAAI,EAAC,MAAM;kBACXC,EAAE,EAAC,kBAAkB;kBACrBC,SAAS,EAAC,GAAG;kBACbC,KAAK,EAAE1E,gBAAiB;kBACxB2E,QAAQ,EAAG3C,CAAC,IAAK/B,mBAAmB,CAAC+B,CAAC,CAAC4C,MAAM,CAACF,KAAK,CAACI,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAE;kBACxEC,UAAU,EAAEhD,cAAe;kBAC3BiD,WAAW,EAAC,QAAQ;kBACpBC,QAAQ,EAAE3E,OAAQ;kBAClBsD,KAAK,EAAE;oBACHM,SAAS,EAAE,QAAQ;oBACnBC,QAAQ,EAAE,SAAS;oBACnBe,UAAU,EAAE,WAAW;oBACvBC,aAAa,EAAE;kBACnB;gBAAE;kBAAA/B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACF/D,OAAA;kBAAK6C,SAAS,EAAC,YAAY;kBAAAC,QAAA,eACvB9C,OAAA,CAACP,WAAW;oBAAAmE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACN/D,OAAA;gBAAGoE,KAAK,EAAE;kBAAEO,QAAQ,EAAE,SAAS;kBAAEJ,KAAK,EAAE,SAAS;kBAAEqB,SAAS,EAAE;gBAAU,CAAE;gBAAA9C,QAAA,EAAC;cAE3E;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UACR;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eAGN/D,OAAA;YAAKoE,KAAK,EAAE;cACRM,SAAS,EAAE,QAAQ;cACnBD,YAAY,EAAE;YAClB,CAAE;YAAA3B,QAAA,eACE9C,OAAA;cACI+E,IAAI,EAAC,QAAQ;cACbc,OAAO,EAAEjD,gBAAiB;cAC1B6C,QAAQ,EAAE3E,OAAQ;cAClBsD,KAAK,EAAE;gBACH0B,OAAO,EAAE,aAAa;gBACtBC,UAAU,EAAE,QAAQ;gBACpBC,GAAG,EAAE,QAAQ;gBACbzB,KAAK,EAAE,SAAS;gBAChBI,QAAQ,EAAE,UAAU;gBACpBsB,cAAc,EAAE,MAAM;gBACtBC,UAAU,EAAE,iBAAiB;gBAC7BC,UAAU,EAAE,MAAM;gBAClBvB,MAAM,EAAE,MAAM;gBACdwB,MAAM,EAAE,SAAS;gBACjB/B,OAAO,EAAE,QAAQ;gBACjBG,YAAY,EAAE,UAAU;gBACxB6B,UAAU,EAAE;cAChB,CAAE;cACFC,WAAW,EAAG9D,CAAC,IAAKA,CAAC,CAAC4C,MAAM,CAAChB,KAAK,CAACG,KAAK,GAAG,SAAU;cACrDgC,UAAU,EAAG/D,CAAC,IAAKA,CAAC,CAAC4C,MAAM,CAAChB,KAAK,CAACG,KAAK,GAAG,SAAU;cAAAzB,QAAA,gBAEpD9C,OAAA,CAACF,gBAAgB;gBAAA8D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,EACnBnD,aAAa,GAAG,uBAAuB,GAAG,iBAAiB;YAAA;cAAAgD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eAGN/D,OAAA;YACI+E,IAAI,EAAC,QAAQ;YACblC,SAAS,EAAC,cAAc;YACxB4C,QAAQ,EAAE3E,OAAO,KAAKF,aAAa,GAAGF,UAAU,CAACY,MAAM,KAAK,CAAC,GAAGd,gBAAgB,CAACc,MAAM,KAAK,CAAC,CAAE;YAC/F8C,KAAK,EAAE;cACH0B,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBS,cAAc,EAAE,QAAQ;cACxBR,GAAG,EAAE;YACT,CAAE;YAAAlD,QAAA,GAEDhC,OAAO,iBAAId,OAAA,CAACL,SAAS;cAACkD,SAAS,EAAC;YAAc;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EACjDjD,OAAO,GAAG,cAAc,GAAG,aAAa;UAAA;YAAA8C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eAGP/D,OAAA;UAAKoE,KAAK,EAAE;YACRC,OAAO,EAAE,cAAc;YACvBC,eAAe,EAAE,yBAAyB;YAC1CM,MAAM,EAAE,mCAAmC;YAC3CJ,YAAY,EAAE,QAAQ;YACtBoB,SAAS,EAAE;UACf,CAAE;UAAA9C,QAAA,eACE9C,OAAA;YAAKoE,KAAK,EAAE;cAAE0B,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE,YAAY;cAAEC,GAAG,EAAE;YAAU,CAAE;YAAAlD,QAAA,gBACtE9C,OAAA,CAACF,gBAAgB;cAACsE,KAAK,EAAE;gBAAEG,KAAK,EAAE,SAAS;gBAAEqB,SAAS,EAAE,UAAU;gBAAEa,UAAU,EAAE;cAAE;YAAE;cAAA7C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACvF/D,OAAA;cAAKoE,KAAK,EAAE;gBAAEO,QAAQ,EAAE,UAAU;gBAAEJ,KAAK,EAAE;cAAU,CAAE;cAAAzB,QAAA,gBACnD9C,OAAA;gBAAGoE,KAAK,EAAE;kBAAEsC,UAAU,EAAE,KAAK;kBAAEjC,YAAY,EAAE;gBAAU,CAAE;gBAAA3B,QAAA,EAAC;cAAU;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,EACvEnD,aAAa,gBACVZ,OAAA;gBAAGoE,KAAK,EAAE;kBAAEuC,MAAM,EAAE;gBAAE,CAAE;gBAAA7D,QAAA,EAAC;cAGzB;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,gBAEJ/D,OAAA;gBAAGoE,KAAK,EAAE;kBAAEuC,MAAM,EAAE;gBAAE,CAAE;gBAAA7D,QAAA,EAAC;cAGzB;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAEN/D,OAAA;MAAK6C,SAAS,EAAC;IAAmB;MAAAe,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACxC,CAAC;AAEd,CAAC;AAACxD,EAAA,CAxRIL,oBAAoB;AAAA0G,EAAA,GAApB1G,oBAAoB;AA0R1B,eAAeA,oBAAoB;AAAC,IAAA0G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}