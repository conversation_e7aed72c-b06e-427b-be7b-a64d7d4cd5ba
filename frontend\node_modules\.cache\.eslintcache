[{"C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\index.js": "1", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\App.js": "2", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\reportWebVitals.js": "3", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\utils\\axiosConfig.js": "4", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\context\\UserContext.js": "5", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\contexts\\ErrorContext.js": "6", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AdminLayout.js": "7", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\UserLayout.js": "8", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\WelcomeSplash.js": "9", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AdminLoginPage.js": "10", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserManagement.js": "11", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AdminDashboard.js": "12", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserRegistration.js": "13", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ChallengeSystem.js": "14", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\TransactionManagement.js": "15", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\BetManagement.js": "16", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserLogin.js": "17", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeaderboardManagement.js": "18", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AddUser.js": "19", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ReportsAnalytics.js": "20", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\SystemSettings.js": "21", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\PaymentMethods.js": "22", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Messages\\Messages.js": "23", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreditUser.js": "24", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\DebitUser.js": "25", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\TeamManagement.js": "26", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ChallengeManagement.js": "27", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueManagement.js": "28", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreditChallenge.js": "29", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueSeasonManagement.js": "30", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreateLeague.js": "31", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueDetails.js": "32", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\JoinChallenge.js": "33", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueUserManagement.js": "34", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserDashboard.js": "35", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\JoinChallenge2.js": "36", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ViewBets.js": "37", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\IncomingBets.js": "38", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\PaymentHistory.js": "39", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Profile.js": "40", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AcceptedBets.js": "41", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Leaderboard.js": "42", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ChangePassword.js": "43", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Deposit.js": "44", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Withdraw.js": "45", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\FriendRequests.js": "46", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueHome.js": "47", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Friends.js": "48", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserAchievements.js": "49", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\SeasonHistory.js": "50", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueSelection.js": "51", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Transfer.js": "52", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\MyLeagues.js": "53", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreditWallet.js": "54", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreditHistory.js": "55", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Challenges.js": "56", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\RecentBets.js": "57", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AdminHeader.js": "58", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Sidebar.js": "59", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\ErrorAlert.js": "60", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AdminFooter.js": "61", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\utils\\errorHandler.js": "62", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Header.js": "63", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\config.js": "64", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AlertContainer.js": "65", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Layout\\MainLayout.js": "66", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AlertMessage.js": "67", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\ScrollToTop.js": "68", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Footer.js": "69", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\OldSidebar.js": "70", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\WelcomePage\\ChallengesList.js": "71", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\WelcomePage\\RecentBets.js": "72", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\WelcomePage\\HeroSlider.js": "73", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\DefaultLeagueBanner.js": "74", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\GeneralSettings.js": "75", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\SecuritySettings.js": "76", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\SMTPSettings.js": "77", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\NotificationSettings.js": "78", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\contexts\\SiteConfigContext.js": "79", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AdminReports.js": "80", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AdminLeaderboard.js": "81", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\index.js": "82", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\Admin2FASetup.js": "83", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\Admin2FAVerification.js": "84", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\AdminOTPVerification.js": "85", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\AdminAuthPreferences.js": "86", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Admin2FASettings.js": "87", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserDetails.js": "88"}, {"size": 1593, "mtime": 1739215325917, "results": "89", "hashOfConfig": "90"}, {"size": 11192, "mtime": 1749281542598, "results": "91", "hashOfConfig": "90"}, {"size": 362, "mtime": 1725527312699, "results": "92", "hashOfConfig": "90"}, {"size": 2707, "mtime": 1749107911322, "results": "93", "hashOfConfig": "90"}, {"size": 1583, "mtime": 1738380484748, "results": "94", "hashOfConfig": "90"}, {"size": 1958, "mtime": 1738907546846, "results": "95", "hashOfConfig": "90"}, {"size": 1431, "mtime": 1747472215947, "results": "96", "hashOfConfig": "90"}, {"size": 17038, "mtime": 1745585449129, "results": "97", "hashOfConfig": "90"}, {"size": 5850, "mtime": 1747430002225, "results": "98", "hashOfConfig": "90"}, {"size": 10378, "mtime": 1749231333045, "results": "99", "hashOfConfig": "90"}, {"size": 39621, "mtime": 1749281417203, "results": "100", "hashOfConfig": "90"}, {"size": 34669, "mtime": 1747471954224, "results": "101", "hashOfConfig": "90"}, {"size": 6588, "mtime": 1738406509501, "results": "102", "hashOfConfig": "90"}, {"size": 18493, "mtime": 1749277164795, "results": "103", "hashOfConfig": "90"}, {"size": 25409, "mtime": 1739175447004, "results": "104", "hashOfConfig": "90"}, {"size": 31756, "mtime": 1749281155463, "results": "105", "hashOfConfig": "90"}, {"size": 16128, "mtime": 1745598311818, "results": "106", "hashOfConfig": "90"}, {"size": 43308, "mtime": 1749118478833, "results": "107", "hashOfConfig": "90"}, {"size": 11677, "mtime": 1747481352521, "results": "108", "hashOfConfig": "90"}, {"size": 484, "mtime": 1747774257496, "results": "109", "hashOfConfig": "90"}, {"size": 3867, "mtime": 1749105548034, "results": "110", "hashOfConfig": "90"}, {"size": 13219, "mtime": 1747676927010, "results": "111", "hashOfConfig": "90"}, {"size": 14883, "mtime": 1738334800331, "results": "112", "hashOfConfig": "90"}, {"size": 6473, "mtime": 1747481379508, "results": "113", "hashOfConfig": "90"}, {"size": 398, "mtime": 1725625029363, "results": "114", "hashOfConfig": "90"}, {"size": 10173, "mtime": 1749281125859, "results": "115", "hashOfConfig": "90"}, {"size": 22573, "mtime": 1749277333189, "results": "116", "hashOfConfig": "90"}, {"size": 27005, "mtime": 1747687217258, "results": "117", "hashOfConfig": "90"}, {"size": 22327, "mtime": 1739035705052, "results": "118", "hashOfConfig": "90"}, {"size": 9112, "mtime": 1734251655762, "results": "119", "hashOfConfig": "90"}, {"size": 11975, "mtime": 1734248094632, "results": "120", "hashOfConfig": "90"}, {"size": 12979, "mtime": 1739086616323, "results": "121", "hashOfConfig": "90"}, {"size": 12071, "mtime": 1739001265026, "results": "122", "hashOfConfig": "90"}, {"size": 12037, "mtime": 1747670557722, "results": "123", "hashOfConfig": "90"}, {"size": 16681, "mtime": 1739089849375, "results": "124", "hashOfConfig": "90"}, {"size": 12891, "mtime": 1739078247803, "results": "125", "hashOfConfig": "90"}, {"size": 29344, "mtime": 1738182744405, "results": "126", "hashOfConfig": "90"}, {"size": 5324, "mtime": 1737964400994, "results": "127", "hashOfConfig": "90"}, {"size": 205, "mtime": 1732832805260, "results": "128", "hashOfConfig": "90"}, {"size": 28050, "mtime": 1738011980316, "results": "129", "hashOfConfig": "90"}, {"size": 30253, "mtime": 1737968307123, "results": "130", "hashOfConfig": "90"}, {"size": 8917, "mtime": 1738228976181, "results": "131", "hashOfConfig": "90"}, {"size": 1242, "mtime": 1732832820214, "results": "132", "hashOfConfig": "90"}, {"size": 1134, "mtime": 1732832829902, "results": "133", "hashOfConfig": "90"}, {"size": 1098, "mtime": 1732832839965, "results": "134", "hashOfConfig": "90"}, {"size": 11530, "mtime": 1732983571250, "results": "135", "hashOfConfig": "90"}, {"size": 23454, "mtime": 1738253936762, "results": "136", "hashOfConfig": "90"}, {"size": 24467, "mtime": 1732988420840, "results": "137", "hashOfConfig": "90"}, {"size": 4310, "mtime": 1734245942035, "results": "138", "hashOfConfig": "90"}, {"size": 5623, "mtime": 1734245958195, "results": "139", "hashOfConfig": "90"}, {"size": 3339, "mtime": 1734245925091, "results": "140", "hashOfConfig": "90"}, {"size": 6337, "mtime": 1736487062211, "results": "141", "hashOfConfig": "90"}, {"size": 5681, "mtime": 1734287339563, "results": "142", "hashOfConfig": "90"}, {"size": 10920, "mtime": 1739168463615, "results": "143", "hashOfConfig": "90"}, {"size": 14257, "mtime": 1739212427178, "results": "144", "hashOfConfig": "90"}, {"size": 16913, "mtime": 1738012431449, "results": "145", "hashOfConfig": "90"}, {"size": 21192, "mtime": 1738014939015, "results": "146", "hashOfConfig": "90"}, {"size": 3211, "mtime": 1747478622718, "results": "147", "hashOfConfig": "90"}, {"size": 4667, "mtime": 1749233297141, "results": "148", "hashOfConfig": "90"}, {"size": 1352, "mtime": 1738907631772, "results": "149", "hashOfConfig": "90"}, {"size": 591, "mtime": 1737714035353, "results": "150", "hashOfConfig": "90"}, {"size": 4889, "mtime": 1739089917990, "results": "151", "hashOfConfig": "90"}, {"size": 4026, "mtime": 1749114060143, "results": "152", "hashOfConfig": "90"}, {"size": 981, "mtime": 1747467742714, "results": "153", "hashOfConfig": "90"}, {"size": 597, "mtime": 1738005020143, "results": "154", "hashOfConfig": "90"}, {"size": 2649, "mtime": 1745558530865, "results": "155", "hashOfConfig": "90"}, {"size": 856, "mtime": 1738005002533, "results": "156", "hashOfConfig": "90"}, {"size": 778, "mtime": 1737703033090, "results": "157", "hashOfConfig": "90"}, {"size": 9268, "mtime": 1739089382382, "results": "158", "hashOfConfig": "90"}, {"size": 4473, "mtime": 1739114665777, "results": "159", "hashOfConfig": "90"}, {"size": 6511, "mtime": 1747772230646, "results": "160", "hashOfConfig": "90"}, {"size": 3561, "mtime": 1747465926259, "results": "161", "hashOfConfig": "90"}, {"size": 2058, "mtime": 1745560016985, "results": "162", "hashOfConfig": "90"}, {"size": 3270, "mtime": 1747683592095, "results": "163", "hashOfConfig": "90"}, {"size": 13040, "mtime": 1749112054896, "results": "164", "hashOfConfig": "90"}, {"size": 35666, "mtime": 1749231075399, "results": "165", "hashOfConfig": "90"}, {"size": 17158, "mtime": 1749113919875, "results": "166", "hashOfConfig": "90"}, {"size": 12332, "mtime": 1749106493490, "results": "167", "hashOfConfig": "90"}, {"size": 1631, "mtime": 1749111942679, "results": "168", "hashOfConfig": "90"}, {"size": 35110, "mtime": 1749118685592, "results": "169", "hashOfConfig": "90"}, {"size": 17861, "mtime": 1749116896225, "results": "170", "hashOfConfig": "90"}, {"size": 317, "mtime": 1749231241721, "results": "171", "hashOfConfig": "90"}, {"size": 22414, "mtime": 1749237247953, "results": "172", "hashOfConfig": "90"}, {"size": 15311, "mtime": 1749239054836, "results": "173", "hashOfConfig": "90"}, {"size": 19477, "mtime": 1749236876615, "results": "174", "hashOfConfig": "90"}, {"size": 12103, "mtime": 1749231233630, "results": "175", "hashOfConfig": "90"}, {"size": 26689, "mtime": 1749237355112, "results": "176", "hashOfConfig": "90"}, {"size": 16917, "mtime": 1749281261258, "results": "177", "hashOfConfig": "90"}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "eamnk2", {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "307", "messages": "308", "suppressedMessages": "309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "310", "messages": "311", "suppressedMessages": "312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "313", "messages": "314", "suppressedMessages": "315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "322", "messages": "323", "suppressedMessages": "324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "325", "messages": "326", "suppressedMessages": "327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "328", "messages": "329", "suppressedMessages": "330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "331", "messages": "332", "suppressedMessages": "333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "334", "messages": "335", "suppressedMessages": "336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "337", "messages": "338", "suppressedMessages": "339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "340", "messages": "341", "suppressedMessages": "342", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "343", "messages": "344", "suppressedMessages": "345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "346", "messages": "347", "suppressedMessages": "348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "349", "messages": "350", "suppressedMessages": "351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "352", "messages": "353", "suppressedMessages": "354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "355", "messages": "356", "suppressedMessages": "357", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "358", "messages": "359", "suppressedMessages": "360", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "361", "messages": "362", "suppressedMessages": "363", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "364", "messages": "365", "suppressedMessages": "366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "367", "messages": "368", "suppressedMessages": "369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "370", "messages": "371", "suppressedMessages": "372", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "373", "messages": "374", "suppressedMessages": "375", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "376", "messages": "377", "suppressedMessages": "378", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "379", "messages": "380", "suppressedMessages": "381", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "382", "messages": "383", "suppressedMessages": "384", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "385", "messages": "386", "suppressedMessages": "387", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "388", "messages": "389", "suppressedMessages": "390", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "391", "messages": "392", "suppressedMessages": "393", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "394", "messages": "395", "suppressedMessages": "396", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "397", "messages": "398", "suppressedMessages": "399", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "400", "messages": "401", "suppressedMessages": "402", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "403", "messages": "404", "suppressedMessages": "405", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "406", "messages": "407", "suppressedMessages": "408", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "409", "messages": "410", "suppressedMessages": "411", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "412", "messages": "413", "suppressedMessages": "414", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "415", "messages": "416", "suppressedMessages": "417", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "418", "messages": "419", "suppressedMessages": "420", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "421", "messages": "422", "suppressedMessages": "423", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "424", "messages": "425", "suppressedMessages": "426", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "427", "messages": "428", "suppressedMessages": "429", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "430", "messages": "431", "suppressedMessages": "432", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "433", "messages": "434", "suppressedMessages": "435", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "436", "messages": "437", "suppressedMessages": "438", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "439", "messages": "440", "suppressedMessages": "441", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\index.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\App.js", ["442"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\reportWebVitals.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\utils\\axiosConfig.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\context\\UserContext.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\contexts\\ErrorContext.js", ["443"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AdminLayout.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\UserLayout.js", ["444", "445", "446"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\WelcomeSplash.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AdminLoginPage.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserManagement.js", ["447"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AdminDashboard.js", ["448", "449", "450", "451", "452", "453", "454", "455", "456", "457"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserRegistration.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ChallengeSystem.js", ["458", "459"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\TransactionManagement.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\BetManagement.js", ["460", "461", "462", "463", "464", "465"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserLogin.js", ["466"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeaderboardManagement.js", ["467", "468"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AddUser.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ReportsAnalytics.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\SystemSettings.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\PaymentMethods.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Messages\\Messages.js", ["469", "470", "471"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreditUser.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\DebitUser.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\TeamManagement.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ChallengeManagement.js", ["472"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueManagement.js", ["473", "474"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreditChallenge.js", ["475", "476", "477", "478"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueSeasonManagement.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreateLeague.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueDetails.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\JoinChallenge.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueUserManagement.js", ["479", "480", "481"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserDashboard.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\JoinChallenge2.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ViewBets.js", ["482", "483"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\IncomingBets.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\PaymentHistory.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Profile.js", ["484"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AcceptedBets.js", ["485", "486", "487", "488", "489"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Leaderboard.js", ["490"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ChangePassword.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Deposit.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Withdraw.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\FriendRequests.js", ["491"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueHome.js", ["492", "493", "494", "495", "496"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Friends.js", ["497", "498", "499", "500", "501"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserAchievements.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\SeasonHistory.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueSelection.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Transfer.js", ["502"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\MyLeagues.js", ["503"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreditWallet.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreditHistory.js", ["504"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Challenges.js", ["505", "506"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\RecentBets.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AdminHeader.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Sidebar.js", ["507", "508", "509", "510"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\ErrorAlert.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AdminFooter.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\utils\\errorHandler.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Header.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\config.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AlertContainer.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Layout\\MainLayout.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AlertMessage.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\ScrollToTop.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Footer.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\OldSidebar.js", ["511"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\WelcomePage\\ChallengesList.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\WelcomePage\\RecentBets.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\WelcomePage\\HeroSlider.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\DefaultLeagueBanner.js", ["512", "513"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\GeneralSettings.js", ["514"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\SecuritySettings.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\SMTPSettings.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\NotificationSettings.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\contexts\\SiteConfigContext.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AdminReports.js", ["515", "516", "517"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AdminLeaderboard.js", ["518"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\index.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\Admin2FASetup.js", ["519", "520", "521"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\Admin2FAVerification.js", ["522"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\AdminOTPVerification.js", ["523", "524"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\AdminAuthPreferences.js", ["525", "526", "527", "528"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Admin2FASettings.js", ["529", "530"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserDetails.js", ["531", "532"], [], {"ruleId": "533", "severity": 1, "message": "534", "line": 68, "column": 8, "nodeType": "535", "messageId": "536", "endLine": 68, "endColumn": 23}, {"ruleId": "537", "severity": 1, "message": "538", "line": 31, "column": 6, "nodeType": "539", "endLine": 31, "endColumn": 8, "suggestions": "540"}, {"ruleId": "537", "severity": 1, "message": "541", "line": 25, "column": 9, "nodeType": "542", "endLine": 25, "endColumn": 62}, {"ruleId": "537", "severity": 1, "message": "543", "line": 73, "column": 6, "nodeType": "539", "endLine": 73, "endColumn": 50, "suggestions": "544"}, {"ruleId": "537", "severity": 1, "message": "545", "line": 119, "column": 6, "nodeType": "539", "endLine": 119, "endColumn": 85, "suggestions": "546"}, {"ruleId": null, "fatal": true, "severity": 2, "message": "547", "line": 705, "column": 14, "nodeType": null}, {"ruleId": "533", "severity": 1, "message": "548", "line": 8, "column": 3, "nodeType": "535", "messageId": "536", "endLine": 8, "endColumn": 16}, {"ruleId": "533", "severity": 1, "message": "549", "line": 10, "column": 3, "nodeType": "535", "messageId": "536", "endLine": 10, "endColumn": 17}, {"ruleId": "533", "severity": 1, "message": "550", "line": 11, "column": 3, "nodeType": "535", "messageId": "536", "endLine": 11, "endColumn": 13}, {"ruleId": "533", "severity": 1, "message": "551", "line": 12, "column": 3, "nodeType": "535", "messageId": "536", "endLine": 12, "endColumn": 16}, {"ruleId": "533", "severity": 1, "message": "552", "line": 13, "column": 3, "nodeType": "535", "messageId": "536", "endLine": 13, "endColumn": 8}, {"ruleId": "533", "severity": 1, "message": "553", "line": 14, "column": 3, "nodeType": "535", "messageId": "536", "endLine": 14, "endColumn": 16}, {"ruleId": "533", "severity": 1, "message": "554", "line": 15, "column": 3, "nodeType": "535", "messageId": "536", "endLine": 15, "endColumn": 10}, {"ruleId": "533", "severity": 1, "message": "555", "line": 16, "column": 3, "nodeType": "535", "messageId": "536", "endLine": 16, "endColumn": 9}, {"ruleId": "533", "severity": 1, "message": "556", "line": 20, "column": 16, "nodeType": "535", "messageId": "536", "endLine": 20, "endColumn": 19}, {"ruleId": "533", "severity": 1, "message": "557", "line": 47, "column": 12, "nodeType": "535", "messageId": "536", "endLine": 47, "endColumn": 30}, {"ruleId": "533", "severity": 1, "message": "558", "line": 20, "column": 10, "nodeType": "535", "messageId": "536", "endLine": 20, "endColumn": 20}, {"ruleId": "533", "severity": 1, "message": "559", "line": 20, "column": 22, "nodeType": "535", "messageId": "536", "endLine": 20, "endColumn": 35}, {"ruleId": "533", "severity": 1, "message": "560", "line": 3, "column": 20, "nodeType": "535", "messageId": "536", "endLine": 3, "endColumn": 28}, {"ruleId": "533", "severity": 1, "message": "561", "line": 3, "column": 37, "nodeType": "535", "messageId": "536", "endLine": 3, "endColumn": 43}, {"ruleId": "533", "severity": 1, "message": "562", "line": 3, "column": 45, "nodeType": "535", "messageId": "536", "endLine": 3, "endColumn": 52}, {"ruleId": "533", "severity": 1, "message": "563", "line": 13, "column": 21, "nodeType": "535", "messageId": "536", "endLine": 13, "endColumn": 31}, {"ruleId": "537", "severity": 1, "message": "564", "line": 40, "column": 8, "nodeType": "539", "endLine": 40, "endColumn": 41, "suggestions": "565"}, {"ruleId": "533", "severity": 1, "message": "566", "line": 111, "column": 11, "nodeType": "535", "messageId": "536", "endLine": 111, "endColumn": 27}, {"ruleId": "533", "severity": 1, "message": "567", "line": 17, "column": 12, "nodeType": "535", "messageId": "536", "endLine": 17, "endColumn": 21}, {"ruleId": "533", "severity": 1, "message": "560", "line": 3, "column": 20, "nodeType": "535", "messageId": "536", "endLine": 3, "endColumn": 28}, {"ruleId": "537", "severity": 1, "message": "568", "line": 39, "column": 8, "nodeType": "539", "endLine": 39, "endColumn": 42, "suggestions": "569"}, {"ruleId": "533", "severity": 1, "message": "570", "line": 3, "column": 10, "nodeType": "535", "messageId": "536", "endLine": 3, "endColumn": 22}, {"ruleId": "537", "severity": 1, "message": "571", "line": 24, "column": 8, "nodeType": "539", "endLine": 24, "endColumn": 19, "suggestions": "572"}, {"ruleId": "537", "severity": 1, "message": "573", "line": 33, "column": 8, "nodeType": "539", "endLine": 33, "endColumn": 30, "suggestions": "574"}, {"ruleId": "537", "severity": 1, "message": "575", "line": 43, "column": 8, "nodeType": "539", "endLine": 43, "endColumn": 10, "suggestions": "576"}, {"ruleId": "533", "severity": 1, "message": "577", "line": 17, "column": 5, "nodeType": "535", "messageId": "536", "endLine": 17, "endColumn": 17}, {"ruleId": "533", "severity": 1, "message": "578", "line": 18, "column": 5, "nodeType": "535", "messageId": "536", "endLine": 18, "endColumn": 12}, {"ruleId": "533", "severity": 1, "message": "579", "line": 1, "column": 60, "nodeType": "535", "messageId": "536", "endLine": 1, "endColumn": 66}, {"ruleId": "533", "severity": 1, "message": "580", "line": 28, "column": 12, "nodeType": "535", "messageId": "536", "endLine": 28, "endColumn": 26}, {"ruleId": "533", "severity": 1, "message": "581", "line": 28, "column": 28, "nodeType": "535", "messageId": "536", "endLine": 28, "endColumn": 45}, {"ruleId": "537", "severity": 1, "message": "582", "line": 83, "column": 8, "nodeType": "539", "endLine": 83, "endColumn": 10, "suggestions": "583"}, {"ruleId": "533", "severity": 1, "message": "584", "line": 3, "column": 19, "nodeType": "535", "messageId": "536", "endLine": 3, "endColumn": 30}, {"ruleId": "533", "severity": 1, "message": "585", "line": 14, "column": 12, "nodeType": "535", "messageId": "536", "endLine": 14, "endColumn": 24}, {"ruleId": "533", "severity": 1, "message": "586", "line": 93, "column": 11, "nodeType": "535", "messageId": "536", "endLine": 93, "endColumn": 23}, {"ruleId": "533", "severity": 1, "message": "587", "line": 114, "column": 9, "nodeType": "535", "messageId": "536", "endLine": 114, "endColumn": 25}, {"ruleId": "533", "severity": 1, "message": "588", "line": 148, "column": 9, "nodeType": "535", "messageId": "536", "endLine": 148, "endColumn": 22}, {"ruleId": "533", "severity": 1, "message": "589", "line": 4, "column": 10, "nodeType": "535", "messageId": "536", "endLine": 4, "endColumn": 17}, {"ruleId": "533", "severity": 1, "message": "590", "line": 13, "column": 10, "nodeType": "535", "messageId": "536", "endLine": 13, "endColumn": 17}, {"ruleId": "533", "severity": 1, "message": "591", "line": 14, "column": 10, "nodeType": "535", "messageId": "536", "endLine": 14, "endColumn": 15}, {"ruleId": "533", "severity": 1, "message": "588", "line": 115, "column": 9, "nodeType": "535", "messageId": "536", "endLine": 115, "endColumn": 22}, {"ruleId": "533", "severity": 1, "message": "592", "line": 132, "column": 9, "nodeType": "535", "messageId": "536", "endLine": 132, "endColumn": 19}, {"ruleId": "533", "severity": 1, "message": "593", "line": 145, "column": 9, "nodeType": "535", "messageId": "536", "endLine": 145, "endColumn": 22}, {"ruleId": "537", "severity": 1, "message": "568", "line": 44, "column": 8, "nodeType": "539", "endLine": 44, "endColumn": 21, "suggestions": "594"}, {"ruleId": "537", "severity": 1, "message": "595", "line": 19, "column": 8, "nodeType": "539", "endLine": 19, "endColumn": 10, "suggestions": "596"}, {"ruleId": "533", "severity": 1, "message": "597", "line": 6, "column": 14, "nodeType": "535", "messageId": "536", "endLine": 6, "endColumn": 20}, {"ruleId": "533", "severity": 1, "message": "577", "line": 6, "column": 41, "nodeType": "535", "messageId": "536", "endLine": 6, "endColumn": 53}, {"ruleId": "533", "severity": 1, "message": "598", "line": 7, "column": 46, "nodeType": "535", "messageId": "536", "endLine": 7, "endColumn": 52}, {"ruleId": "533", "severity": 1, "message": "599", "line": 11, "column": 7, "nodeType": "535", "messageId": "536", "endLine": 11, "endColumn": 19}, {"ruleId": "533", "severity": 1, "message": "600", "line": 307, "column": 11, "nodeType": "535", "messageId": "536", "endLine": 307, "endColumn": 27}, {"ruleId": "533", "severity": 1, "message": "601", "line": 4, "column": 45, "nodeType": "535", "messageId": "536", "endLine": 4, "endColumn": 64}, {"ruleId": "533", "severity": 1, "message": "602", "line": 4, "column": 66, "nodeType": "535", "messageId": "536", "endLine": 4, "endColumn": 79}, {"ruleId": "533", "severity": 1, "message": "603", "line": 4, "column": 111, "nodeType": "535", "messageId": "536", "endLine": 4, "endColumn": 123}, {"ruleId": "537", "severity": 1, "message": "604", "line": 29, "column": 8, "nodeType": "539", "endLine": 29, "endColumn": 10, "suggestions": "605"}, {"ruleId": "533", "severity": 1, "message": "606", "line": 256, "column": 11, "nodeType": "535", "messageId": "536", "endLine": 256, "endColumn": 26}, {"ruleId": "537", "severity": 1, "message": "607", "line": 24, "column": 8, "nodeType": "539", "endLine": 24, "endColumn": 33, "suggestions": "608"}, {"ruleId": "533", "severity": 1, "message": "597", "line": 5, "column": 57, "nodeType": "535", "messageId": "536", "endLine": 5, "endColumn": 63}, {"ruleId": "537", "severity": 1, "message": "609", "line": 24, "column": 8, "nodeType": "539", "endLine": 24, "endColumn": 33, "suggestions": "610"}, {"ruleId": "533", "severity": 1, "message": "611", "line": 122, "column": 19, "nodeType": "535", "messageId": "536", "endLine": 122, "endColumn": 28}, {"ruleId": "533", "severity": 1, "message": "612", "line": 137, "column": 19, "nodeType": "535", "messageId": "536", "endLine": 137, "endColumn": 22}, {"ruleId": "533", "severity": 1, "message": "613", "line": 4, "column": 5, "nodeType": "535", "messageId": "536", "endLine": 4, "endColumn": 14}, {"ruleId": "533", "severity": 1, "message": "614", "line": 6, "column": 5, "nodeType": "535", "messageId": "536", "endLine": 6, "endColumn": 10}, {"ruleId": "533", "severity": 1, "message": "550", "line": 7, "column": 5, "nodeType": "535", "messageId": "536", "endLine": 7, "endColumn": 15}, {"ruleId": "533", "severity": 1, "message": "615", "line": 8, "column": 5, "nodeType": "535", "messageId": "536", "endLine": 8, "endColumn": 16}, {"ruleId": "533", "severity": 1, "message": "616", "line": 57, "column": 9, "nodeType": "535", "messageId": "536", "endLine": 57, "endColumn": 26}, {"ruleId": "533", "severity": 1, "message": "549", "line": 2, "column": 10, "nodeType": "535", "messageId": "536", "endLine": 2, "endColumn": 24}, {"ruleId": "533", "severity": 1, "message": "617", "line": 2, "column": 26, "nodeType": "535", "messageId": "536", "endLine": 2, "endColumn": 34}, {"ruleId": "533", "severity": 1, "message": "618", "line": 3, "column": 35, "nodeType": "535", "messageId": "536", "endLine": 3, "endColumn": 41}, {"ruleId": "533", "severity": 1, "message": "619", "line": 5, "column": 5, "nodeType": "535", "messageId": "536", "endLine": 5, "endColumn": 15}, {"ruleId": "533", "severity": 1, "message": "548", "line": 6, "column": 5, "nodeType": "535", "messageId": "536", "endLine": 6, "endColumn": 18}, {"ruleId": "537", "severity": 1, "message": "620", "line": 29, "column": 8, "nodeType": "539", "endLine": 29, "endColumn": 31, "suggestions": "621"}, {"ruleId": "537", "severity": 1, "message": "568", "line": 31, "column": 8, "nodeType": "539", "endLine": 31, "endColumn": 17, "suggestions": "622"}, {"ruleId": "533", "severity": 1, "message": "623", "line": 3, "column": 33, "nodeType": "535", "messageId": "536", "endLine": 3, "endColumn": 38}, {"ruleId": "533", "severity": 1, "message": "624", "line": 14, "column": 12, "nodeType": "535", "messageId": "536", "endLine": 14, "endColumn": 21}, {"ruleId": "537", "severity": 1, "message": "625", "line": 29, "column": 8, "nodeType": "539", "endLine": 29, "endColumn": 10, "suggestions": "626"}, {"ruleId": "533", "severity": 1, "message": "627", "line": 3, "column": 41, "nodeType": "535", "messageId": "536", "endLine": 3, "endColumn": 62}, {"ruleId": "533", "severity": 1, "message": "627", "line": 3, "column": 40, "nodeType": "535", "messageId": "536", "endLine": 3, "endColumn": 61}, {"ruleId": "537", "severity": 1, "message": "628", "line": 28, "column": 8, "nodeType": "539", "endLine": 28, "endColumn": 24, "suggestions": "629"}, {"ruleId": "533", "severity": 1, "message": "578", "line": 3, "column": 51, "nodeType": "535", "messageId": "536", "endLine": 3, "endColumn": 58}, {"ruleId": "533", "severity": 1, "message": "614", "line": 3, "column": 102, "nodeType": "535", "messageId": "536", "endLine": 3, "endColumn": 107}, {"ruleId": "533", "severity": 1, "message": "630", "line": 18, "column": 12, "nodeType": "535", "messageId": "536", "endLine": 18, "endColumn": 21}, {"ruleId": "537", "severity": 1, "message": "631", "line": 23, "column": 8, "nodeType": "539", "endLine": 23, "endColumn": 17, "suggestions": "632"}, {"ruleId": "533", "severity": 1, "message": "633", "line": 3, "column": 40, "nodeType": "535", "messageId": "536", "endLine": 3, "endColumn": 46}, {"ruleId": "537", "severity": 1, "message": "634", "line": 30, "column": 8, "nodeType": "539", "endLine": 30, "endColumn": 10, "suggestions": "635"}, {"ruleId": "533", "severity": 1, "message": "636", "line": 5, "column": 5, "nodeType": "535", "messageId": "536", "endLine": 5, "endColumn": 11}, {"ruleId": "537", "severity": 1, "message": "637", "line": 39, "column": 8, "nodeType": "539", "endLine": 39, "endColumn": 16, "suggestions": "638"}, "no-unused-vars", "'LeagueSelection' is defined but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useCallback has a missing dependency: 'removeError'. Either include it or remove the dependency array.", "ArrayExpression", ["639"], "The 'publicRoutes' array makes the dependencies of useCallback Hook (at line 97) change on every render. To fix this, wrap the initialization of 'publicRoutes' in its own useMemo() Hook.", "VariableDeclarator", "React Hook useCallback has a missing dependency: 'setUserData'. Either include it or remove the dependency array.", ["640"], "React Hook useEffect has a missing dependency: 'publicRoutes'. Either include it or remove the dependency array.", ["641"], "Parsing error: Unterminated JSX contents. (705:14)", "'FaCalendarAlt' is defined but never used.", "'FaFootballBall' is defined but never used.", "'FaChartBar' is defined but never used.", "'FaExchangeAlt' is defined but never used.", "'FaEye' is defined but never used.", "'FaCheckCircle' is defined but never used.", "'FaClock' is defined but never used.", "'FaBell' is defined but never used.", "'Bar' is defined but never used.", "'teamPopularityData' is assigned a value but never used.", "'challenges' is assigned a value but never used.", "'setChallenges' is assigned a value but never used.", "'FaFilter' is defined but never used.", "'FaEdit' is defined but never used.", "'FaTrash' is defined but never used.", "'setSuccess' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchAllBets'. Either include it or remove the dependency array.", ["642"], "'handlePageChange' is assigned a value but never used.", "'otpExpiry' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchLeaderboard'. Either include it or remove the dependency array.", ["643"], "'API_BASE_URL' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchConversations'. Either include it or remove the dependency array.", ["644"], "React Hook useEffect has missing dependencies: 'fetchMessages' and 'markMessagesAsRead'. Either include them or remove the dependency array.", ["645"], "React Hook useEffect has a missing dependency: 'checkChallengeStatus'. Either include it or remove the dependency array.", ["646"], "'FaInfoCircle' is defined but never used.", "'FaTimes' is defined but never used.", "'useRef' is defined but never used.", "'payoutPreviews' is assigned a value but never used.", "'setPayoutPreviews' is assigned a value but never used.", "React Hook useCallback has a missing dependency: 'fetchLeagueDetails'. Either include it or remove the dependency array.", ["647"], "'FaChartLine' is defined but never used.", "'loadingUsers' is assigned a value but never used.", "'currentUsers' is assigned a value but never used.", "'renderPagination' is assigned a value but never used.", "'calculateOdds' is assigned a value but never used.", "'FaCoins' is defined but never used.", "'loading' is assigned a value but never used.", "'error' is assigned a value but never used.", "'formatDate' is assigned a value but never used.", "'getUserStatus' is assigned a value but never used.", ["648"], "React Hook useEffect has a missing dependency: 'fetchPendingRequests'. Either include it or remove the dependency array.", ["649"], "'FaStar' is defined but never used.", "'FaFire' is defined but never used.", "'API_BASE_URL' is assigned a value but never used.", "'renderNavigation' is assigned a value but never used.", "'FaExclamationCircle' is defined but never used.", "'FaUserFriends' is defined but never used.", "'FaUserCircle' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchFriends'. Either include it or remove the dependency array.", ["650"], "'handleChallenge' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchInitialData'. Either include it or remove the dependency array.", ["651"], "React Hook useEffect has a missing dependency: 'fetchCreditRequests'. Either include it or remove the dependency array.", ["652"], "'matchDate' is assigned a value but never used.", "'now' is assigned a value but never used.", "'FaGamepad' is defined but never used.", "'FaCog' is defined but never used.", "'FaMoneyBill' is defined but never used.", "'handleLogoutClick' is assigned a value but never used.", "'FaTrophy' is defined but never used.", "'FaSave' is defined but never used.", "'FaDownload' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchReport'. Either include it or remove the dependency array.", ["653"], ["654"], "'FaKey' is defined but never used.", "'secretKey' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'initiate2FASetup'. Either include it or remove the dependency array.", ["655"], "'FaExclamationTriangle' is defined but never used.", "React Hook useEffect has a missing dependency: 'sendInitialOTP'. Either include it or remove the dependency array.", ["656"], "'adminInfo' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchPreferences'. Either include it or remove the dependency array.", ["657"], "'FaCopy' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchAdminData'. Either include it or remove the dependency array.", ["658"], "'FaUser' is defined but never used.", "React Hook useEffect has missing dependencies: 'fetchUserBets', 'fetchUserDetails', and 'fetchUserTransactions'. Either include them or remove the dependency array.", ["659"], {"desc": "660", "fix": "661"}, {"desc": "662", "fix": "663"}, {"desc": "664", "fix": "665"}, {"desc": "666", "fix": "667"}, {"desc": "668", "fix": "669"}, {"desc": "670", "fix": "671"}, {"desc": "672", "fix": "673"}, {"desc": "674", "fix": "675"}, {"desc": "676", "fix": "677"}, {"desc": "678", "fix": "679"}, {"desc": "680", "fix": "681"}, {"desc": "682", "fix": "683"}, {"desc": "684", "fix": "685"}, {"desc": "686", "fix": "687"}, {"desc": "688", "fix": "689"}, {"desc": "690", "fix": "691"}, {"desc": "692", "fix": "693"}, {"desc": "694", "fix": "695"}, {"desc": "696", "fix": "697"}, {"desc": "698", "fix": "699"}, {"desc": "700", "fix": "701"}, "Update the dependencies array to be: [removeError]", {"range": "702", "text": "703"}, "Update the dependencies array to be: [token, userId, setUserData, navigate, location.pathname]", {"range": "704", "text": "705"}, "Update the dependencies array to be: [token, userId, navigate, location.pathname, fetchUserData, fetchNotifications, publicRoutes]", {"range": "706", "text": "707"}, "Update the dependencies array to be: [pagination.currentPage, filters, fetchAllBets]", {"range": "708", "text": "709"}, "Update the dependencies array to be: [pagination.current_page, filters, fetchLeaderboard]", {"range": "710", "text": "711"}, "Update the dependencies array to be: [activeTab, fetchConversations]", {"range": "712", "text": "713"}, "Update the dependencies array to be: [fetchMessages, markMessagesAsRead, selectedConversation]", {"range": "714", "text": "715"}, "Update the dependencies array to be: [checkChallengeStatus]", {"range": "716", "text": "717"}, "Update the dependencies array to be: [fetchLeagueDetails]", {"range": "718", "text": "719"}, "Update the dependencies array to be: [currentPage, fetchLeaderboard]", {"range": "720", "text": "721"}, "Update the dependencies array to be: [fetchPendingRequests]", {"range": "722", "text": "723"}, "Update the dependencies array to be: [fetchFriends]", {"range": "724", "text": "725"}, "Update the dependencies array to be: [navigate, currentUserId, fetchInitialData]", {"range": "726", "text": "727"}, "Update the dependencies array to be: [navigate, currentUserId, fetchCreditRequests]", {"range": "728", "text": "729"}, "Update the dependencies array to be: [reportType, dateRange, fetchReport]", {"range": "730", "text": "731"}, "Update the dependencies array to be: [fetchLeaderboard, filters]", {"range": "732", "text": "733"}, "Update the dependencies array to be: [initiate2FASetup]", {"range": "734", "text": "735"}, "Update the dependencies array to be: [initialOtpSent, sendInitialOTP]", {"range": "736", "text": "737"}, "Update the dependencies array to be: [adminId, fetchPreferences]", {"range": "738", "text": "739"}, "Update the dependencies array to be: [fetchAdminData]", {"range": "740", "text": "741"}, "Update the dependencies array to be: [fetchUserBets, fetchUserDetails, fetchUserTransactions, userId]", {"range": "742", "text": "743"}, [985, 987], "[removeError]", [2571, 2615], "[token, userId, setUserData, navigate, location.pathname]", [4000, 4079], "[token, userId, navigate, location.pathname, fetchUserData, fetchNotifications, publicRoutes]", [1105, 1138], "[pagination.currentPage, filters, fetchAllBets]", [1249, 1283], "[pagination.current_page, filters, fetchLeaderboard]", [1116, 1127], "[activeTab, fetchConversations]", [1474, 1496], "[fetch<PERSON>essages, markMessagesAsRead, selectedConversation]", [1532, 1534], "[checkChallengeStatus]", [3030, 3032], "[fetchLeagueDetails]", [1160, 1173], "[currentPage, fetchLeaderboard]", [696, 698], "[fetchPendingRequests]", [1331, 1333], "[fetchFriends]", [856, 881], "[navigate, currentUserId, fetchInitialData]", [891, 916], "[navigate, currentUserId, fetchCreditRequests]", [864, 887], "[reportType, dateRange, fetchReport]", [887, 896], "[fetchLeaderboard, filters]", [1137, 1139], "[initiate2FASetup]", [1264, 1280], "[initialOtpSent, sendInitialOTP]", [856, 865], "[adminId, fetchPreferences]", [1269, 1271], "[fetchAdminData]", [1020, 1028], "[fetchUserBets, fetchUserDetails, fetchUserTransactions, userId]"]