# FanBet247 Enhanced Admin Authentication Setup Guide

## 🚀 Overview

This guide will help you set up the enhanced admin authentication system for FanBet247, which includes:

- **OTP (One-Time Password)** via email
- **2FA (Two-Factor Authentication)** via Google Authenticator
- **Comprehensive security features** including rate limiting, audit logging, and backup recovery
- **Admin configuration panel** for managing authentication settings

## 📋 Prerequisites

Before starting, ensure you have:

- PHP 7.4 or higher
- MySQL 5.7 or higher
- Composer installed
- SMTP server configured (for OTP functionality)
- Web server (Apache/Nginx) with proper permissions

## 🔧 Installation Steps

### Step 1: Install Dependencies

```bash
cd backend
composer install
```

Required packages:
- `phpmailer/phpmailer` - For email functionality
- `pragmarx/google2fa` - For 2FA functionality

### Step 2: Database Setup

1. **Run the database setup script:**
   ```bash
   php backend/sql/setup_admin_auth_tables.php
   ```

2. **Or manually execute the SQL:**
   ```sql
   SOURCE backend/sql/admin_authentication_schema.sql;
   ```

3. **Verify the setup:**
   ```bash
   curl http://localhost/FanBet247/backend/setup/verify_installation.php
   ```

### Step 3: Configure SMTP Settings

1. Navigate to Admin Panel → Email Settings
2. Configure your SMTP server details:
   - **Host**: Your SMTP server
   - **Port**: Usually 587 (TLS) or 465 (SSL)
   - **Username**: Your email username
   - **Password**: Your email password
   - **Encryption**: TLS or SSL

### Step 4: Enable Admin Authentication

1. Go to Admin Panel → Security Settings
2. Scroll to "Admin Authentication Security" section
3. Enable desired authentication methods:
   - ✅ **OTP via Email** (requires SMTP)
   - ✅ **2FA via Google Authenticator**
4. Configure settings as needed
5. Save settings

## ⚙️ Configuration Options

### OTP Settings
- **Expiry Time**: How long OTP codes remain valid (default: 5 minutes)
- **Max Attempts**: Maximum verification attempts before lockout (default: 3)
- **Lockout Time**: How long accounts are locked after max attempts (default: 30 minutes)

### 2FA Settings
- **Backup Codes**: Number of backup codes to generate (default: 10)
- **Required Actions**: Which actions require 2FA verification

### General Security
- **Max Login Attempts**: Failed attempts before account lockout (default: 5)
- **Session Timeout**: Admin session duration (default: 1 hour)

## 🔐 Admin Setup Process

### For OTP Authentication:
1. Admin selects OTP as authentication method in preferences
2. During login, after password verification, OTP is sent to registered email
3. Admin enters OTP code to complete login

### For 2FA Authentication:
1. Admin selects 2FA as authentication method in preferences
2. Admin sets up Google Authenticator:
   - Scan QR code or enter manual key
   - Verify setup with test code
   - Save backup codes securely
3. During login, admin enters 6-digit code from authenticator app

## 🧪 Testing

### Run Backend Tests
```bash
php backend/tests/admin_auth_test.php
```

### Run Frontend Tests
```bash
cd frontend
npm test -- AdminAuthIntegration.test.js
```

### Manual Testing Checklist

#### OTP Testing:
- [ ] OTP email is received
- [ ] OTP code works correctly
- [ ] Invalid OTP is rejected
- [ ] Expired OTP is rejected
- [ ] Rate limiting works after multiple failures

#### 2FA Testing:
- [ ] QR code displays correctly
- [ ] Google Authenticator setup works
- [ ] 6-digit codes are accepted
- [ ] Backup codes work
- [ ] Invalid codes are rejected

#### Security Testing:
- [ ] Rate limiting prevents brute force
- [ ] Audit logs are created
- [ ] Account lockout works
- [ ] Recovery options function

## 🛡️ Security Features

### Rate Limiting
- **IP-based limiting**: Prevents attacks from specific IPs
- **Account-based limiting**: Prevents targeted account attacks
- **Configurable thresholds**: Customizable attempt limits and timeouts

### Audit Logging
- **Comprehensive logging**: All authentication events are logged
- **Security incidents**: Suspicious activities are flagged
- **Admin dashboard**: Real-time security monitoring

### Backup Recovery
- **Email recovery codes**: For 2FA account recovery
- **Backup codes**: Alternative to authenticator app
- **Admin override**: Super admin can reset 2FA for other admins

## 📊 Monitoring and Maintenance

### Security Dashboard
Access the security dashboard at: `Admin Panel → Security → Authentication Logs`

Features:
- Real-time authentication statistics
- Failed login attempt monitoring
- Security incident alerts
- Admin activity tracking

### Log Management
- Logs are automatically cleaned after 90 days
- Critical events are logged to system log
- Security alerts can be configured for email notifications

### Regular Maintenance
1. **Review security logs** weekly
2. **Update backup codes** every 6 months
3. **Test recovery procedures** quarterly
4. **Review admin access** monthly

## 🚨 Troubleshooting

### Common Issues

#### OTP Not Received
1. Check SMTP configuration
2. Verify email address is correct
3. Check spam/junk folder
4. Test SMTP connection

#### 2FA Setup Fails
1. Ensure Google Authenticator is installed
2. Check system time synchronization
3. Verify QR code is readable
4. Try manual key entry

#### Account Locked
1. Check lockout time remaining
2. Use recovery options if available
3. Contact super admin for override
4. Review security logs for cause

#### Database Errors
1. Verify all tables exist
2. Check database permissions
3. Run verification script
4. Review error logs

### Error Codes
- **AUTH001**: Invalid credentials
- **AUTH002**: Account locked
- **AUTH003**: OTP expired
- **AUTH004**: 2FA setup required
- **AUTH005**: Rate limit exceeded

## 📞 Support

For technical support:
1. Check the troubleshooting section above
2. Review security logs for specific errors
3. Run the verification script for system status
4. Contact system administrator with error details

## 🔄 Updates and Upgrades

### Version Compatibility
- Current version: 1.0.0
- Minimum PHP: 7.4
- Minimum MySQL: 5.7

### Backup Before Updates
1. Backup database
2. Backup configuration files
3. Test in staging environment
4. Document current settings

## 📝 Additional Notes

- **Security**: Always use HTTPS in production
- **Backups**: Regular database backups are essential
- **Monitoring**: Set up log monitoring and alerts
- **Documentation**: Keep admin credentials secure and documented

---

**🎉 Congratulations!** Your enhanced admin authentication system is now ready to provide enterprise-level security for your FanBet247 platform.
