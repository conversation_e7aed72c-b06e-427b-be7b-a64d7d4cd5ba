<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: POST");
header("Access-Control-Max-Age: 3600");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

include_once '../includes/db_connect.php';
$conn = getDBConnection();

$data = json_decode(file_get_contents("php://input"));

if (
    !empty($data->username) &&
    !empty($data->full_name) &&
    !empty($data->email) &&
    !empty($data->password) &&
    !empty($data->favorite_team)
) {
    $username = htmlspecialchars(strip_tags($data->username));
    $full_name = htmlspecialchars(strip_tags($data->full_name));
    $email = htmlspecialchars(strip_tags($data->email));
    $password = password_hash($data->password, PASSWORD_DEFAULT);
    $favorite_team = htmlspecialchars(strip_tags($data->favorite_team));

    // Check if username or email already exists
    $check_query = "SELECT * FROM users WHERE username = :username OR email = :email";
    $check_stmt = $conn->prepare($check_query);
    $check_stmt->bindParam(":username", $username);
    $check_stmt->bindParam(":email", $email);
    $check_stmt->execute();

    if ($check_stmt->rowCount() > 0) {
        http_response_code(400);
        echo json_encode(array("success" => false, "message" => "Username or email already exists"));
    } else {
        $query = "INSERT INTO users (username, full_name, email, password_hash, favorite_team) VALUES (:username, :full_name, :email, :password, :favorite_team)";

        $stmt = $conn->prepare($query);
        $stmt->bindParam(":username", $username);
        $stmt->bindParam(":full_name", $full_name);
        $stmt->bindParam(":email", $email);
        $stmt->bindParam(":password", $password);
        $stmt->bindParam(":favorite_team", $favorite_team);

        if ($stmt->execute()) {
            http_response_code(201);
            echo json_encode(array("success" => true, "message" => "User registered successfully"));
        } else {
            http_response_code(503);
            echo json_encode(array("success" => false, "message" => "Unable to register user"));
        }
    }
} else {
    http_response_code(400);
    echo json_encode(array("success" => false, "message" => "Incomplete data"));
}
