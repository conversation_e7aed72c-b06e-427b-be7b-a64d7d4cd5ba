{"ast": null, "code": "var _jsxFileName = \"C:\\\\MAMP\\\\htdocs\\\\FanBet247\\\\frontend\\\\src\\\\pages\\\\SecuritySettings.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { FaShieldAlt, FaCheck, FaTimes, FaSave, FaKey, FaUserShield, FaExclamationTriangle, FaInfoCircle } from 'react-icons/fa';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst API_BASE_URL = '/backend';\nfunction SecuritySettings() {\n  _s();\n  const [settings, setSettings] = useState({\n    enable_2fa: 'false',\n    allowed_auth_methods: 'email_otp,google_auth',\n    otp_expiry_time: '300',\n    max_otp_attempts: '3',\n    lockout_time: '1800',\n    password_min_length: '8',\n    require_special_chars: 'true',\n    session_timeout: '3600',\n    max_login_attempts: '5'\n  });\n\n  // Admin authentication settings\n  const [adminAuthSettings, setAdminAuthSettings] = useState({\n    admin_auth_method: 'password_only',\n    admin_otp_enabled: 'false',\n    admin_2fa_enabled: 'false',\n    admin_otp_expiry_time: '300',\n    admin_max_otp_attempts: '3',\n    admin_max_login_attempts: '5',\n    admin_lockout_time: '1800',\n    admin_require_2fa_for: 'login,password_change',\n    admin_backup_codes_count: '10',\n    admin_session_timeout: '3600'\n  });\n  const [loading, setLoading] = useState(true);\n  const [saving, setSaving] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  const [adminAuthAvailable, setAdminAuthAvailable] = useState(false);\n  const [smtpConfigured, setSmtpConfigured] = useState(false);\n  const [adminStats, setAdminStats] = useState({});\n  useEffect(() => {\n    fetchSettings();\n  }, []);\n  const fetchSettings = async () => {\n    try {\n      setLoading(true);\n\n      // Fetch regular security settings\n      const response = await axios.get(`${API_BASE_URL}/handlers/get_security_settings.php`);\n      if (response.data.success && response.data.settings) {\n        const settingsData = {};\n        Object.keys(response.data.settings).forEach(key => {\n          settingsData[key] = response.data.settings[key].value;\n        });\n        setSettings(settingsData);\n\n        // Check if admin auth settings are available\n        if (response.data.admin_auth_available && response.data.admin_auth_settings) {\n          const adminSettingsData = {};\n          Object.keys(response.data.admin_auth_settings).forEach(key => {\n            adminSettingsData[key] = response.data.admin_auth_settings[key].value;\n          });\n          setAdminAuthSettings(adminSettingsData);\n          setAdminAuthAvailable(true);\n        } else {\n          // Try to fetch admin auth settings separately\n          try {\n            const adminResponse = await axios.get(`${API_BASE_URL}/handlers/get_admin_auth_settings.php`);\n            if (adminResponse.data.success) {\n              setAdminAuthSettings(adminResponse.data.settings);\n              setAdminAuthAvailable(adminResponse.data.table_exists);\n              setSmtpConfigured(adminResponse.data.smtp_configured);\n              setAdminStats(adminResponse.data.admin_stats || {});\n            }\n          } catch (adminErr) {\n            console.log('Admin auth settings not available yet');\n            setAdminAuthAvailable(false);\n          }\n        }\n      }\n    } catch (err) {\n      setError('Failed to load security settings');\n      console.error('Error fetching settings:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleInputChange = e => {\n    const {\n      name,\n      value,\n      type,\n      checked\n    } = e.target;\n    setSettings(prev => ({\n      ...prev,\n      [name]: type === 'checkbox' ? checked ? 'true' : 'false' : value\n    }));\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setError('');\n    setSuccess('');\n    try {\n      setSaving(true);\n      const response = await axios.post(`${API_BASE_URL}/handlers/update_security_settings.php`, {\n        settings: settings\n      });\n      if (response.data.success) {\n        setSuccess('Security settings saved successfully!');\n        setTimeout(() => setSuccess(''), 3000);\n      } else {\n        setError(response.data.message || 'Failed to save security settings');\n      }\n    } catch (err) {\n      setError('Failed to save security settings');\n      console.error('Error saving settings:', err);\n    } finally {\n      setSaving(false);\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-center h-64\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-lg text-gray-600\",\n          children: \"Loading security settings...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 127,\n      columnNumber: 13\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"p-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-2xl font-bold text-gray-800 flex items-center gap-3\",\n        children: [/*#__PURE__*/_jsxDEV(FaShieldAlt, {\n          className: \"text-blue-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 21\n        }, this), \"Security Settings\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-600 mt-2\",\n        children: \"Configure two-factor authentication (2FA) options and other security features.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 138,\n      columnNumber: 13\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-6 bg-red-50 border border-red-200 rounded-lg p-4 flex items-center gap-3\",\n      children: [/*#__PURE__*/_jsxDEV(FaTimes, {\n        className: \"text-red-500\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 151,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"text-red-700\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 152,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 150,\n      columnNumber: 17\n    }, this), success && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-6 bg-green-50 border border-green-200 rounded-lg p-4 flex items-center gap-3\",\n      children: [/*#__PURE__*/_jsxDEV(FaCheck, {\n        className: \"text-green-500\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"text-green-700\",\n        children: success\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 159,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 157,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg shadow-sm border\",\n      children: /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        className: \"p-6 space-y-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-lg font-semibold text-gray-800 mb-4\",\n            children: \"Two-Factor Authentication\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                id: \"enable_2fa\",\n                name: \"enable_2fa\",\n                checked: settings.enable_2fa === 'true',\n                onChange: handleInputChange,\n                className: \"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 171,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"enable_2fa\",\n                className: \"ml-2 block text-sm text-gray-900\",\n                children: \"Enable Two-Factor Authentication for users\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 179,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Allowed Authentication Methods\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                name: \"allowed_auth_methods\",\n                value: settings.allowed_auth_methods,\n                onChange: handleInputChange,\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                placeholder: \"email_otp,google_auth\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 188,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-gray-500 mt-1\",\n                children: \"Comma-separated list of allowed methods\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 196,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-lg font-semibold text-gray-800 mb-4\",\n            children: \"OTP Settings\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"OTP Expiry Time (seconds)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 206,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                name: \"otp_expiry_time\",\n                value: settings.otp_expiry_time,\n                onChange: handleInputChange,\n                min: \"60\",\n                max: \"3600\",\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 209,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Max OTP Attempts\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 221,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                name: \"max_otp_attempts\",\n                value: settings.max_otp_attempts,\n                onChange: handleInputChange,\n                min: \"1\",\n                max: \"10\",\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 224,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-lg font-semibold text-gray-800 mb-4\",\n            children: \"Password Security\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Minimum Password Length\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 242,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                name: \"password_min_length\",\n                value: settings.password_min_length,\n                onChange: handleInputChange,\n                min: \"6\",\n                max: \"50\",\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 245,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                id: \"require_special_chars\",\n                name: \"require_special_chars\",\n                checked: settings.require_special_chars === 'true',\n                onChange: handleInputChange,\n                className: \"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 257,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"require_special_chars\",\n                className: \"ml-2 block text-sm text-gray-900\",\n                children: \"Require special characters in passwords\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 265,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 256,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-lg font-semibold text-gray-800 mb-4\",\n            children: \"Login Security\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 274,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Max Login Attempts\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 277,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                name: \"max_login_attempts\",\n                value: settings.max_login_attempts,\n                onChange: handleInputChange,\n                min: \"1\",\n                max: \"20\",\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 280,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Lockout Time (seconds)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 292,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                name: \"lockout_time\",\n                value: settings.lockout_time,\n                onChange: handleInputChange,\n                min: \"300\",\n                max: \"86400\",\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 295,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 291,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Session Timeout (seconds)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 307,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                name: \"session_timeout\",\n                value: settings.session_timeout,\n                onChange: handleInputChange,\n                min: \"300\",\n                max: \"86400\",\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 310,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 275,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 273,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-end pt-6 border-t\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            disabled: saving,\n            className: \"flex items-center gap-2 px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n            children: [/*#__PURE__*/_jsxDEV(FaSave, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 330,\n              columnNumber: 29\n            }, this), saving ? 'Saving...' : 'Save Security Settings']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 325,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 324,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 165,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 164,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 136,\n    columnNumber: 9\n  }, this);\n}\n_s(SecuritySettings, \"5X8WKQH2TEbUhD71Ev9yed7+0Lo=\");\n_c = SecuritySettings;\nexport default SecuritySettings;\nvar _c;\n$RefreshReg$(_c, \"SecuritySettings\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "FaShieldAlt", "FaCheck", "FaTimes", "FaSave", "FaKey", "FaUserShield", "FaExclamationTriangle", "FaInfoCircle", "jsxDEV", "_jsxDEV", "API_BASE_URL", "SecuritySettings", "_s", "settings", "setSettings", "enable_2fa", "allowed_auth_methods", "otp_expiry_time", "max_otp_attempts", "lockout_time", "password_min_length", "require_special_chars", "session_timeout", "max_login_attempts", "adminAuthSettings", "setAdminAuthSettings", "admin_auth_method", "admin_otp_enabled", "admin_2fa_enabled", "admin_otp_expiry_time", "admin_max_otp_attempts", "admin_max_login_attempts", "admin_lockout_time", "admin_require_2fa_for", "admin_backup_codes_count", "admin_session_timeout", "loading", "setLoading", "saving", "setSaving", "error", "setError", "success", "setSuccess", "adminAuthAvailable", "setAdminAuthAvailable", "smtpConfigured", "setSmtpConfigured", "adminStats", "setAdminStats", "fetchSettings", "response", "get", "data", "settingsData", "Object", "keys", "for<PERSON>ach", "key", "value", "admin_auth_available", "admin_auth_settings", "adminSettingsData", "adminResponse", "table_exists", "smtp_configured", "admin_stats", "adminErr", "console", "log", "err", "handleInputChange", "e", "name", "type", "checked", "target", "prev", "handleSubmit", "preventDefault", "post", "setTimeout", "message", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "id", "onChange", "htmlFor", "placeholder", "min", "max", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/pages/SecuritySettings.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { FaShieldAlt, FaCheck, FaTimes, FaSave, FaKey, FaUserShield, FaExclamationTriangle, FaInfoCircle } from 'react-icons/fa';\n\nconst API_BASE_URL = '/backend';\n\nfunction SecuritySettings() {\n    const [settings, setSettings] = useState({\n        enable_2fa: 'false',\n        allowed_auth_methods: 'email_otp,google_auth',\n        otp_expiry_time: '300',\n        max_otp_attempts: '3',\n        lockout_time: '1800',\n        password_min_length: '8',\n        require_special_chars: 'true',\n        session_timeout: '3600',\n        max_login_attempts: '5'\n    });\n\n    // Admin authentication settings\n    const [adminAuthSettings, setAdminAuthSettings] = useState({\n        admin_auth_method: 'password_only',\n        admin_otp_enabled: 'false',\n        admin_2fa_enabled: 'false',\n        admin_otp_expiry_time: '300',\n        admin_max_otp_attempts: '3',\n        admin_max_login_attempts: '5',\n        admin_lockout_time: '1800',\n        admin_require_2fa_for: 'login,password_change',\n        admin_backup_codes_count: '10',\n        admin_session_timeout: '3600'\n    });\n\n    const [loading, setLoading] = useState(true);\n    const [saving, setSaving] = useState(false);\n    const [error, setError] = useState('');\n    const [success, setSuccess] = useState('');\n    const [adminAuthAvailable, setAdminAuthAvailable] = useState(false);\n    const [smtpConfigured, setSmtpConfigured] = useState(false);\n    const [adminStats, setAdminStats] = useState({});\n\n    useEffect(() => {\n        fetchSettings();\n    }, []);\n\n    const fetchSettings = async () => {\n        try {\n            setLoading(true);\n\n            // Fetch regular security settings\n            const response = await axios.get(`${API_BASE_URL}/handlers/get_security_settings.php`);\n\n            if (response.data.success && response.data.settings) {\n                const settingsData = {};\n                Object.keys(response.data.settings).forEach(key => {\n                    settingsData[key] = response.data.settings[key].value;\n                });\n                setSettings(settingsData);\n\n                // Check if admin auth settings are available\n                if (response.data.admin_auth_available && response.data.admin_auth_settings) {\n                    const adminSettingsData = {};\n                    Object.keys(response.data.admin_auth_settings).forEach(key => {\n                        adminSettingsData[key] = response.data.admin_auth_settings[key].value;\n                    });\n                    setAdminAuthSettings(adminSettingsData);\n                    setAdminAuthAvailable(true);\n                } else {\n                    // Try to fetch admin auth settings separately\n                    try {\n                        const adminResponse = await axios.get(`${API_BASE_URL}/handlers/get_admin_auth_settings.php`);\n                        if (adminResponse.data.success) {\n                            setAdminAuthSettings(adminResponse.data.settings);\n                            setAdminAuthAvailable(adminResponse.data.table_exists);\n                            setSmtpConfigured(adminResponse.data.smtp_configured);\n                            setAdminStats(adminResponse.data.admin_stats || {});\n                        }\n                    } catch (adminErr) {\n                        console.log('Admin auth settings not available yet');\n                        setAdminAuthAvailable(false);\n                    }\n                }\n            }\n        } catch (err) {\n            setError('Failed to load security settings');\n            console.error('Error fetching settings:', err);\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const handleInputChange = (e) => {\n        const { name, value, type, checked } = e.target;\n        setSettings(prev => ({\n            ...prev,\n            [name]: type === 'checkbox' ? (checked ? 'true' : 'false') : value\n        }));\n    };\n\n    const handleSubmit = async (e) => {\n        e.preventDefault();\n        setError('');\n        setSuccess('');\n\n        try {\n            setSaving(true);\n            const response = await axios.post(`${API_BASE_URL}/handlers/update_security_settings.php`, {\n                settings: settings\n            });\n\n            if (response.data.success) {\n                setSuccess('Security settings saved successfully!');\n                setTimeout(() => setSuccess(''), 3000);\n            } else {\n                setError(response.data.message || 'Failed to save security settings');\n            }\n        } catch (err) {\n            setError('Failed to save security settings');\n            console.error('Error saving settings:', err);\n        } finally {\n            setSaving(false);\n        }\n    };\n\n    if (loading) {\n        return (\n            <div className=\"p-6\">\n                <div className=\"flex items-center justify-center h-64\">\n                    <div className=\"text-lg text-gray-600\">Loading security settings...</div>\n                </div>\n            </div>\n        );\n    }\n\n    return (\n        <div className=\"p-6\">\n            {/* Header */}\n            <div className=\"mb-8\">\n                <h1 className=\"text-2xl font-bold text-gray-800 flex items-center gap-3\">\n                    <FaShieldAlt className=\"text-blue-500\" />\n                    Security Settings\n                </h1>\n                <p className=\"text-gray-600 mt-2\">\n                    Configure two-factor authentication (2FA) options and other security features.\n                </p>\n            </div>\n\n            {/* Alerts */}\n            {error && (\n                <div className=\"mb-6 bg-red-50 border border-red-200 rounded-lg p-4 flex items-center gap-3\">\n                    <FaTimes className=\"text-red-500\" />\n                    <span className=\"text-red-700\">{error}</span>\n                </div>\n            )}\n\n            {success && (\n                <div className=\"mb-6 bg-green-50 border border-green-200 rounded-lg p-4 flex items-center gap-3\">\n                    <FaCheck className=\"text-green-500\" />\n                    <span className=\"text-green-700\">{success}</span>\n                </div>\n            )}\n\n            {/* Settings Form */}\n            <div className=\"bg-white rounded-lg shadow-sm border\">\n                <form onSubmit={handleSubmit} className=\"p-6 space-y-6\">\n                    {/* Two-Factor Authentication */}\n                    <div>\n                        <h2 className=\"text-lg font-semibold text-gray-800 mb-4\">Two-Factor Authentication</h2>\n                        <div className=\"space-y-4\">\n                            <div className=\"flex items-center\">\n                                <input\n                                    type=\"checkbox\"\n                                    id=\"enable_2fa\"\n                                    name=\"enable_2fa\"\n                                    checked={settings.enable_2fa === 'true'}\n                                    onChange={handleInputChange}\n                                    className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                                />\n                                <label htmlFor=\"enable_2fa\" className=\"ml-2 block text-sm text-gray-900\">\n                                    Enable Two-Factor Authentication for users\n                                </label>\n                            </div>\n                            \n                            <div>\n                                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                                    Allowed Authentication Methods\n                                </label>\n                                <input\n                                    type=\"text\"\n                                    name=\"allowed_auth_methods\"\n                                    value={settings.allowed_auth_methods}\n                                    onChange={handleInputChange}\n                                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                                    placeholder=\"email_otp,google_auth\"\n                                />\n                                <p className=\"text-xs text-gray-500 mt-1\">Comma-separated list of allowed methods</p>\n                            </div>\n                        </div>\n                    </div>\n\n                    {/* OTP Settings */}\n                    <div>\n                        <h2 className=\"text-lg font-semibold text-gray-800 mb-4\">OTP Settings</h2>\n                        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                            <div>\n                                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                                    OTP Expiry Time (seconds)\n                                </label>\n                                <input\n                                    type=\"number\"\n                                    name=\"otp_expiry_time\"\n                                    value={settings.otp_expiry_time}\n                                    onChange={handleInputChange}\n                                    min=\"60\"\n                                    max=\"3600\"\n                                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                                />\n                            </div>\n                            \n                            <div>\n                                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                                    Max OTP Attempts\n                                </label>\n                                <input\n                                    type=\"number\"\n                                    name=\"max_otp_attempts\"\n                                    value={settings.max_otp_attempts}\n                                    onChange={handleInputChange}\n                                    min=\"1\"\n                                    max=\"10\"\n                                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                                />\n                            </div>\n                        </div>\n                    </div>\n\n                    {/* Password Security */}\n                    <div>\n                        <h2 className=\"text-lg font-semibold text-gray-800 mb-4\">Password Security</h2>\n                        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                            <div>\n                                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                                    Minimum Password Length\n                                </label>\n                                <input\n                                    type=\"number\"\n                                    name=\"password_min_length\"\n                                    value={settings.password_min_length}\n                                    onChange={handleInputChange}\n                                    min=\"6\"\n                                    max=\"50\"\n                                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                                />\n                            </div>\n                            \n                            <div className=\"flex items-center\">\n                                <input\n                                    type=\"checkbox\"\n                                    id=\"require_special_chars\"\n                                    name=\"require_special_chars\"\n                                    checked={settings.require_special_chars === 'true'}\n                                    onChange={handleInputChange}\n                                    className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                                />\n                                <label htmlFor=\"require_special_chars\" className=\"ml-2 block text-sm text-gray-900\">\n                                    Require special characters in passwords\n                                </label>\n                            </div>\n                        </div>\n                    </div>\n\n                    {/* Login Security */}\n                    <div>\n                        <h2 className=\"text-lg font-semibold text-gray-800 mb-4\">Login Security</h2>\n                        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n                            <div>\n                                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                                    Max Login Attempts\n                                </label>\n                                <input\n                                    type=\"number\"\n                                    name=\"max_login_attempts\"\n                                    value={settings.max_login_attempts}\n                                    onChange={handleInputChange}\n                                    min=\"1\"\n                                    max=\"20\"\n                                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                                />\n                            </div>\n                            \n                            <div>\n                                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                                    Lockout Time (seconds)\n                                </label>\n                                <input\n                                    type=\"number\"\n                                    name=\"lockout_time\"\n                                    value={settings.lockout_time}\n                                    onChange={handleInputChange}\n                                    min=\"300\"\n                                    max=\"86400\"\n                                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                                />\n                            </div>\n                            \n                            <div>\n                                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                                    Session Timeout (seconds)\n                                </label>\n                                <input\n                                    type=\"number\"\n                                    name=\"session_timeout\"\n                                    value={settings.session_timeout}\n                                    onChange={handleInputChange}\n                                    min=\"300\"\n                                    max=\"86400\"\n                                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                                />\n                            </div>\n                        </div>\n                    </div>\n\n                    {/* Submit Button */}\n                    <div className=\"flex justify-end pt-6 border-t\">\n                        <button\n                            type=\"submit\"\n                            disabled={saving}\n                            className=\"flex items-center gap-2 px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n                        >\n                            <FaSave />\n                            {saving ? 'Saving...' : 'Save Security Settings'}\n                        </button>\n                    </div>\n                </form>\n            </div>\n        </div>\n    );\n}\n\nexport default SecuritySettings;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,EAAEC,OAAO,EAAEC,OAAO,EAAEC,MAAM,EAAEC,KAAK,EAAEC,YAAY,EAAEC,qBAAqB,EAAEC,YAAY,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjI,MAAMC,YAAY,GAAG,UAAU;AAE/B,SAASC,gBAAgBA,CAAA,EAAG;EAAAC,EAAA;EACxB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGjB,QAAQ,CAAC;IACrCkB,UAAU,EAAE,OAAO;IACnBC,oBAAoB,EAAE,uBAAuB;IAC7CC,eAAe,EAAE,KAAK;IACtBC,gBAAgB,EAAE,GAAG;IACrBC,YAAY,EAAE,MAAM;IACpBC,mBAAmB,EAAE,GAAG;IACxBC,qBAAqB,EAAE,MAAM;IAC7BC,eAAe,EAAE,MAAM;IACvBC,kBAAkB,EAAE;EACxB,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG5B,QAAQ,CAAC;IACvD6B,iBAAiB,EAAE,eAAe;IAClCC,iBAAiB,EAAE,OAAO;IAC1BC,iBAAiB,EAAE,OAAO;IAC1BC,qBAAqB,EAAE,KAAK;IAC5BC,sBAAsB,EAAE,GAAG;IAC3BC,wBAAwB,EAAE,GAAG;IAC7BC,kBAAkB,EAAE,MAAM;IAC1BC,qBAAqB,EAAE,uBAAuB;IAC9CC,wBAAwB,EAAE,IAAI;IAC9BC,qBAAqB,EAAE;EAC3B,CAAC,CAAC;EAEF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGxC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACyC,MAAM,EAAEC,SAAS,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAAC2C,KAAK,EAAEC,QAAQ,CAAC,GAAG5C,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC6C,OAAO,EAAEC,UAAU,CAAC,GAAG9C,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC+C,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGhD,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACiD,cAAc,EAAEC,iBAAiB,CAAC,GAAGlD,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACmD,UAAU,EAAEC,aAAa,CAAC,GAAGpD,QAAQ,CAAC,CAAC,CAAC,CAAC;EAEhDC,SAAS,CAAC,MAAM;IACZoD,aAAa,CAAC,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,aAAa,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACAb,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAMc,QAAQ,GAAG,MAAMpD,KAAK,CAACqD,GAAG,CAAC,GAAG1C,YAAY,qCAAqC,CAAC;MAEtF,IAAIyC,QAAQ,CAACE,IAAI,CAACX,OAAO,IAAIS,QAAQ,CAACE,IAAI,CAACxC,QAAQ,EAAE;QACjD,MAAMyC,YAAY,GAAG,CAAC,CAAC;QACvBC,MAAM,CAACC,IAAI,CAACL,QAAQ,CAACE,IAAI,CAACxC,QAAQ,CAAC,CAAC4C,OAAO,CAACC,GAAG,IAAI;UAC/CJ,YAAY,CAACI,GAAG,CAAC,GAAGP,QAAQ,CAACE,IAAI,CAACxC,QAAQ,CAAC6C,GAAG,CAAC,CAACC,KAAK;QACzD,CAAC,CAAC;QACF7C,WAAW,CAACwC,YAAY,CAAC;;QAEzB;QACA,IAAIH,QAAQ,CAACE,IAAI,CAACO,oBAAoB,IAAIT,QAAQ,CAACE,IAAI,CAACQ,mBAAmB,EAAE;UACzE,MAAMC,iBAAiB,GAAG,CAAC,CAAC;UAC5BP,MAAM,CAACC,IAAI,CAACL,QAAQ,CAACE,IAAI,CAACQ,mBAAmB,CAAC,CAACJ,OAAO,CAACC,GAAG,IAAI;YAC1DI,iBAAiB,CAACJ,GAAG,CAAC,GAAGP,QAAQ,CAACE,IAAI,CAACQ,mBAAmB,CAACH,GAAG,CAAC,CAACC,KAAK;UACzE,CAAC,CAAC;UACFlC,oBAAoB,CAACqC,iBAAiB,CAAC;UACvCjB,qBAAqB,CAAC,IAAI,CAAC;QAC/B,CAAC,MAAM;UACH;UACA,IAAI;YACA,MAAMkB,aAAa,GAAG,MAAMhE,KAAK,CAACqD,GAAG,CAAC,GAAG1C,YAAY,uCAAuC,CAAC;YAC7F,IAAIqD,aAAa,CAACV,IAAI,CAACX,OAAO,EAAE;cAC5BjB,oBAAoB,CAACsC,aAAa,CAACV,IAAI,CAACxC,QAAQ,CAAC;cACjDgC,qBAAqB,CAACkB,aAAa,CAACV,IAAI,CAACW,YAAY,CAAC;cACtDjB,iBAAiB,CAACgB,aAAa,CAACV,IAAI,CAACY,eAAe,CAAC;cACrDhB,aAAa,CAACc,aAAa,CAACV,IAAI,CAACa,WAAW,IAAI,CAAC,CAAC,CAAC;YACvD;UACJ,CAAC,CAAC,OAAOC,QAAQ,EAAE;YACfC,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;YACpDxB,qBAAqB,CAAC,KAAK,CAAC;UAChC;QACJ;MACJ;IACJ,CAAC,CAAC,OAAOyB,GAAG,EAAE;MACV7B,QAAQ,CAAC,kCAAkC,CAAC;MAC5C2B,OAAO,CAAC5B,KAAK,CAAC,0BAA0B,EAAE8B,GAAG,CAAC;IAClD,CAAC,SAAS;MACNjC,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,MAAMkC,iBAAiB,GAAIC,CAAC,IAAK;IAC7B,MAAM;MAAEC,IAAI;MAAEd,KAAK;MAAEe,IAAI;MAAEC;IAAQ,CAAC,GAAGH,CAAC,CAACI,MAAM;IAC/C9D,WAAW,CAAC+D,IAAI,KAAK;MACjB,GAAGA,IAAI;MACP,CAACJ,IAAI,GAAGC,IAAI,KAAK,UAAU,GAAIC,OAAO,GAAG,MAAM,GAAG,OAAO,GAAIhB;IACjE,CAAC,CAAC,CAAC;EACP,CAAC;EAED,MAAMmB,YAAY,GAAG,MAAON,CAAC,IAAK;IAC9BA,CAAC,CAACO,cAAc,CAAC,CAAC;IAClBtC,QAAQ,CAAC,EAAE,CAAC;IACZE,UAAU,CAAC,EAAE,CAAC;IAEd,IAAI;MACAJ,SAAS,CAAC,IAAI,CAAC;MACf,MAAMY,QAAQ,GAAG,MAAMpD,KAAK,CAACiF,IAAI,CAAC,GAAGtE,YAAY,wCAAwC,EAAE;QACvFG,QAAQ,EAAEA;MACd,CAAC,CAAC;MAEF,IAAIsC,QAAQ,CAACE,IAAI,CAACX,OAAO,EAAE;QACvBC,UAAU,CAAC,uCAAuC,CAAC;QACnDsC,UAAU,CAAC,MAAMtC,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;MAC1C,CAAC,MAAM;QACHF,QAAQ,CAACU,QAAQ,CAACE,IAAI,CAAC6B,OAAO,IAAI,kCAAkC,CAAC;MACzE;IACJ,CAAC,CAAC,OAAOZ,GAAG,EAAE;MACV7B,QAAQ,CAAC,kCAAkC,CAAC;MAC5C2B,OAAO,CAAC5B,KAAK,CAAC,wBAAwB,EAAE8B,GAAG,CAAC;IAChD,CAAC,SAAS;MACN/B,SAAS,CAAC,KAAK,CAAC;IACpB;EACJ,CAAC;EAED,IAAIH,OAAO,EAAE;IACT,oBACI3B,OAAA;MAAK0E,SAAS,EAAC,KAAK;MAAAC,QAAA,eAChB3E,OAAA;QAAK0E,SAAS,EAAC,uCAAuC;QAAAC,QAAA,eAClD3E,OAAA;UAAK0E,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAA4B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd;EAEA,oBACI/E,OAAA;IAAK0E,SAAS,EAAC,KAAK;IAAAC,QAAA,gBAEhB3E,OAAA;MAAK0E,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACjB3E,OAAA;QAAI0E,SAAS,EAAC,0DAA0D;QAAAC,QAAA,gBACpE3E,OAAA,CAACT,WAAW;UAACmF,SAAS,EAAC;QAAe;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,qBAE7C;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACL/E,OAAA;QAAG0E,SAAS,EAAC,oBAAoB;QAAAC,QAAA,EAAC;MAElC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLhD,KAAK,iBACF/B,OAAA;MAAK0E,SAAS,EAAC,6EAA6E;MAAAC,QAAA,gBACxF3E,OAAA,CAACP,OAAO;QAACiF,SAAS,EAAC;MAAc;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACpC/E,OAAA;QAAM0E,SAAS,EAAC,cAAc;QAAAC,QAAA,EAAE5C;MAAK;QAAA6C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5C,CACR,EAEA9C,OAAO,iBACJjC,OAAA;MAAK0E,SAAS,EAAC,iFAAiF;MAAAC,QAAA,gBAC5F3E,OAAA,CAACR,OAAO;QAACkF,SAAS,EAAC;MAAgB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACtC/E,OAAA;QAAM0E,SAAS,EAAC,gBAAgB;QAAAC,QAAA,EAAE1C;MAAO;QAAA2C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChD,CACR,eAGD/E,OAAA;MAAK0E,SAAS,EAAC,sCAAsC;MAAAC,QAAA,eACjD3E,OAAA;QAAMgF,QAAQ,EAAEX,YAAa;QAACK,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAEnD3E,OAAA;UAAA2E,QAAA,gBACI3E,OAAA;YAAI0E,SAAS,EAAC,0CAA0C;YAAAC,QAAA,EAAC;UAAyB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvF/E,OAAA;YAAK0E,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACtB3E,OAAA;cAAK0E,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAC9B3E,OAAA;gBACIiE,IAAI,EAAC,UAAU;gBACfgB,EAAE,EAAC,YAAY;gBACfjB,IAAI,EAAC,YAAY;gBACjBE,OAAO,EAAE9D,QAAQ,CAACE,UAAU,KAAK,MAAO;gBACxC4E,QAAQ,EAAEpB,iBAAkB;gBAC5BY,SAAS,EAAC;cAAmE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChF,CAAC,eACF/E,OAAA;gBAAOmF,OAAO,EAAC,YAAY;gBAACT,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAAC;cAEzE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC,eAEN/E,OAAA;cAAA2E,QAAA,gBACI3E,OAAA;gBAAO0E,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR/E,OAAA;gBACIiE,IAAI,EAAC,MAAM;gBACXD,IAAI,EAAC,sBAAsB;gBAC3Bd,KAAK,EAAE9C,QAAQ,CAACG,oBAAqB;gBACrC2E,QAAQ,EAAEpB,iBAAkB;gBAC5BY,SAAS,EAAC,2GAA2G;gBACrHU,WAAW,EAAC;cAAuB;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC,eACF/E,OAAA;gBAAG0E,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAAuC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAGN/E,OAAA;UAAA2E,QAAA,gBACI3E,OAAA;YAAI0E,SAAS,EAAC,0CAA0C;YAAAC,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1E/E,OAAA;YAAK0E,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBAClD3E,OAAA;cAAA2E,QAAA,gBACI3E,OAAA;gBAAO0E,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR/E,OAAA;gBACIiE,IAAI,EAAC,QAAQ;gBACbD,IAAI,EAAC,iBAAiB;gBACtBd,KAAK,EAAE9C,QAAQ,CAACI,eAAgB;gBAChC0E,QAAQ,EAAEpB,iBAAkB;gBAC5BuB,GAAG,EAAC,IAAI;gBACRC,GAAG,EAAC,MAAM;gBACVZ,SAAS,EAAC;cAA2G;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAEN/E,OAAA;cAAA2E,QAAA,gBACI3E,OAAA;gBAAO0E,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR/E,OAAA;gBACIiE,IAAI,EAAC,QAAQ;gBACbD,IAAI,EAAC,kBAAkB;gBACvBd,KAAK,EAAE9C,QAAQ,CAACK,gBAAiB;gBACjCyE,QAAQ,EAAEpB,iBAAkB;gBAC5BuB,GAAG,EAAC,GAAG;gBACPC,GAAG,EAAC,IAAI;gBACRZ,SAAS,EAAC;cAA2G;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAGN/E,OAAA;UAAA2E,QAAA,gBACI3E,OAAA;YAAI0E,SAAS,EAAC,0CAA0C;YAAAC,QAAA,EAAC;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC/E/E,OAAA;YAAK0E,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBAClD3E,OAAA;cAAA2E,QAAA,gBACI3E,OAAA;gBAAO0E,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR/E,OAAA;gBACIiE,IAAI,EAAC,QAAQ;gBACbD,IAAI,EAAC,qBAAqB;gBAC1Bd,KAAK,EAAE9C,QAAQ,CAACO,mBAAoB;gBACpCuE,QAAQ,EAAEpB,iBAAkB;gBAC5BuB,GAAG,EAAC,GAAG;gBACPC,GAAG,EAAC,IAAI;gBACRZ,SAAS,EAAC;cAA2G;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAEN/E,OAAA;cAAK0E,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAC9B3E,OAAA;gBACIiE,IAAI,EAAC,UAAU;gBACfgB,EAAE,EAAC,uBAAuB;gBAC1BjB,IAAI,EAAC,uBAAuB;gBAC5BE,OAAO,EAAE9D,QAAQ,CAACQ,qBAAqB,KAAK,MAAO;gBACnDsE,QAAQ,EAAEpB,iBAAkB;gBAC5BY,SAAS,EAAC;cAAmE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChF,CAAC,eACF/E,OAAA;gBAAOmF,OAAO,EAAC,uBAAuB;gBAACT,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAAC;cAEpF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAGN/E,OAAA;UAAA2E,QAAA,gBACI3E,OAAA;YAAI0E,SAAS,EAAC,0CAA0C;YAAAC,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5E/E,OAAA;YAAK0E,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBAClD3E,OAAA;cAAA2E,QAAA,gBACI3E,OAAA;gBAAO0E,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR/E,OAAA;gBACIiE,IAAI,EAAC,QAAQ;gBACbD,IAAI,EAAC,oBAAoB;gBACzBd,KAAK,EAAE9C,QAAQ,CAACU,kBAAmB;gBACnCoE,QAAQ,EAAEpB,iBAAkB;gBAC5BuB,GAAG,EAAC,GAAG;gBACPC,GAAG,EAAC,IAAI;gBACRZ,SAAS,EAAC;cAA2G;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAEN/E,OAAA;cAAA2E,QAAA,gBACI3E,OAAA;gBAAO0E,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR/E,OAAA;gBACIiE,IAAI,EAAC,QAAQ;gBACbD,IAAI,EAAC,cAAc;gBACnBd,KAAK,EAAE9C,QAAQ,CAACM,YAAa;gBAC7BwE,QAAQ,EAAEpB,iBAAkB;gBAC5BuB,GAAG,EAAC,KAAK;gBACTC,GAAG,EAAC,OAAO;gBACXZ,SAAS,EAAC;cAA2G;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAEN/E,OAAA;cAAA2E,QAAA,gBACI3E,OAAA;gBAAO0E,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR/E,OAAA;gBACIiE,IAAI,EAAC,QAAQ;gBACbD,IAAI,EAAC,iBAAiB;gBACtBd,KAAK,EAAE9C,QAAQ,CAACS,eAAgB;gBAChCqE,QAAQ,EAAEpB,iBAAkB;gBAC5BuB,GAAG,EAAC,KAAK;gBACTC,GAAG,EAAC,OAAO;gBACXZ,SAAS,EAAC;cAA2G;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAGN/E,OAAA;UAAK0E,SAAS,EAAC,gCAAgC;UAAAC,QAAA,eAC3C3E,OAAA;YACIiE,IAAI,EAAC,QAAQ;YACbsB,QAAQ,EAAE1D,MAAO;YACjB6C,SAAS,EAAC,yJAAyJ;YAAAC,QAAA,gBAEnK3E,OAAA,CAACN,MAAM;cAAAkF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EACTlD,MAAM,GAAG,WAAW,GAAG,wBAAwB;UAAA;YAAA+C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd;AAAC5E,EAAA,CA3UQD,gBAAgB;AAAAsF,EAAA,GAAhBtF,gBAAgB;AA6UzB,eAAeA,gBAAgB;AAAC,IAAAsF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}