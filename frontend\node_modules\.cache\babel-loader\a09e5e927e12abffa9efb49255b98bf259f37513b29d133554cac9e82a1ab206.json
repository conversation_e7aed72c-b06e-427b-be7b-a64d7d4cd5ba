{"ast": null, "code": "var _jsxFileName = \"C:\\\\MAMP\\\\htdocs\\\\FanBet247\\\\frontend\\\\src\\\\components\\\\Admin\\\\AdminOTPVerification.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport axios from 'axios';\nimport { FaKey, FaEnvelope, FaSpinner, FaExclamationTriangle, FaArrowLeft, FaRedo } from 'react-icons/fa';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst API_BASE_URL = '/backend';\nconst AdminOTPVerification = ({\n  adminId,\n  username,\n  onSuccess,\n  onBack\n}) => {\n  _s();\n  const [otp, setOtp] = useState(['', '', '', '', '', '']);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  const [timeLeft, setTimeLeft] = useState(0);\n  const [canResend, setCanResend] = useState(false);\n  const [resendLoading, setResendLoading] = useState(false);\n  const [otpSent, setOtpSent] = useState(false);\n  const inputRefs = useRef([]);\n  useEffect(() => {\n    // Send initial OTP when component mounts\n    sendOTP();\n  }, []);\n  useEffect(() => {\n    // Timer for OTP expiry\n    if (timeLeft > 0) {\n      const timer = setTimeout(() => setTimeLeft(timeLeft - 1), 1000);\n      return () => clearTimeout(timer);\n    } else if (otpSent) {\n      setCanResend(true);\n    }\n  }, [timeLeft, otpSent]);\n  const sendOTP = async () => {\n    try {\n      setResendLoading(true);\n      setError('');\n      const response = await axios.post(`${API_BASE_URL}/handlers/admin_send_otp.php`, {\n        admin_id: adminId\n      });\n      if (response.data.success) {\n        setSuccess(`OTP sent to ${response.data.email_masked}`);\n        setTimeLeft(response.data.expires_in || 300);\n        setCanResend(false);\n        setOtpSent(true);\n        setTimeout(() => setSuccess(''), 3000);\n      } else {\n        setError(response.data.message || 'Failed to send OTP');\n      }\n    } catch (err) {\n      setError('Failed to send OTP. Please try again.');\n      console.error('OTP send error:', err);\n    } finally {\n      setResendLoading(false);\n    }\n  };\n  const handleOtpChange = (index, value) => {\n    if (value.length > 1) return; // Prevent multiple characters\n\n    const newOtp = [...otp];\n    newOtp[index] = value;\n    setOtp(newOtp);\n\n    // Auto-focus next input\n    if (value && index < 5) {\n      var _inputRefs$current;\n      (_inputRefs$current = inputRefs.current[index + 1]) === null || _inputRefs$current === void 0 ? void 0 : _inputRefs$current.focus();\n    }\n\n    // Auto-submit when all fields are filled\n    if (newOtp.every(digit => digit !== '') && newOtp.join('').length === 6) {\n      verifyOTP(newOtp.join(''));\n    }\n  };\n  const handleKeyDown = (index, e) => {\n    if (e.key === 'Backspace' && !otp[index] && index > 0) {\n      var _inputRefs$current2;\n      (_inputRefs$current2 = inputRefs.current[index - 1]) === null || _inputRefs$current2 === void 0 ? void 0 : _inputRefs$current2.focus();\n    }\n  };\n  const verifyOTP = async (otpCode = null) => {\n    const codeToVerify = otpCode || otp.join('');\n    if (codeToVerify.length !== 6) {\n      setError('Please enter the complete 6-digit OTP code');\n      return;\n    }\n    try {\n      setLoading(true);\n      setError('');\n      const response = await axios.post(`${API_BASE_URL}/handlers/admin_verify_otp.php`, {\n        admin_id: adminId,\n        otp: codeToVerify\n      });\n      if (response.data.success) {\n        setSuccess('OTP verified successfully!');\n        setTimeout(() => {\n          onSuccess({\n            admin_id: response.data.admin_id,\n            username: response.data.username,\n            role: response.data.role,\n            session_token: response.data.session_token,\n            auth_method: response.data.auth_method\n          });\n        }, 1000);\n      } else {\n        var _inputRefs$current$;\n        setError(response.data.message || 'Invalid OTP code');\n        // Clear OTP inputs on error\n        setOtp(['', '', '', '', '', '']);\n        (_inputRefs$current$ = inputRefs.current[0]) === null || _inputRefs$current$ === void 0 ? void 0 : _inputRefs$current$.focus();\n      }\n    } catch (err) {\n      var _inputRefs$current$2;\n      setError('Failed to verify OTP. Please try again.');\n      console.error('OTP verification error:', err);\n      setOtp(['', '', '', '', '', '']);\n      (_inputRefs$current$2 = inputRefs.current[0]) === null || _inputRefs$current$2 === void 0 ? void 0 : _inputRefs$current$2.focus();\n    } finally {\n      setLoading(false);\n    }\n  };\n  const formatTime = seconds => {\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return `${mins}:${secs.toString().padStart(2, '0')}`;\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-md w-full space-y-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-blue-100\",\n          children: /*#__PURE__*/_jsxDEV(FaKey, {\n            className: \"h-6 w-6 text-blue-600\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"mt-6 text-center text-3xl font-extrabold text-gray-900\",\n          children: \"Enter OTP Code\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-2 text-center text-sm text-gray-600\",\n          children: \"We've sent a 6-digit code to your registered email address\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-center text-sm font-medium text-gray-900\",\n          children: username\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 137,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-8 space-y-6\",\n        children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-red-50 border border-red-200 rounded-md p-4 flex items-center gap-3\",\n          children: [/*#__PURE__*/_jsxDEV(FaExclamationTriangle, {\n            className: \"text-red-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-red-700 text-sm\",\n            children: error\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 25\n        }, this), success && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-green-50 border border-green-200 rounded-md p-4 flex items-center gap-3\",\n          children: [/*#__PURE__*/_jsxDEV(FaEnvelope, {\n            className: \"text-green-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-green-700 text-sm\",\n            children: success\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 163,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-700 mb-4 text-center\",\n            children: \"Enter the 6-digit code\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-center space-x-3\",\n            children: otp.map((digit, index) => /*#__PURE__*/_jsxDEV(\"input\", {\n              ref: el => inputRefs.current[index] = el,\n              type: \"text\",\n              maxLength: \"1\",\n              value: digit,\n              onChange: e => handleOtpChange(index, e.target.value),\n              onKeyDown: e => handleKeyDown(index, e),\n              className: \"w-12 h-12 text-center text-xl font-bold border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n              disabled: loading\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 33\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 21\n        }, this), timeLeft > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-600\",\n            children: [\"Code expires in: \", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-medium text-blue-600\",\n              children: formatTime(timeLeft)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 50\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => verifyOTP(),\n            disabled: loading || otp.join('').length !== 6,\n            className: \"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\",\n            children: [loading && /*#__PURE__*/_jsxDEV(FaSpinner, {\n              className: \"animate-spin mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 41\n            }, this), loading ? 'Verifying...' : 'Verify OTP']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: sendOTP,\n            disabled: !canResend || resendLoading,\n            className: \"text-sm text-blue-600 hover:text-blue-500 disabled:text-gray-400 disabled:cursor-not-allowed flex items-center justify-center gap-2 mx-auto\",\n            children: [resendLoading ? /*#__PURE__*/_jsxDEV(FaSpinner, {\n              className: \"animate-spin\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 33\n            }, this) : /*#__PURE__*/_jsxDEV(FaRedo, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 33\n            }, this), resendLoading ? 'Sending...' : 'Resend OTP']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: onBack,\n            className: \"text-sm text-gray-600 hover:text-gray-500 flex items-center justify-center gap-2 mx-auto\",\n            children: [/*#__PURE__*/_jsxDEV(FaArrowLeft, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 29\n            }, this), \"Back to Login\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 229,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 152,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 136,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 135,\n    columnNumber: 9\n  }, this);\n};\n_s(AdminOTPVerification, \"x5K7pOLCN6FxXI/RGF6v5eUkOcA=\");\n_c = AdminOTPVerification;\nexport default AdminOTPVerification;\nvar _c;\n$RefreshReg$(_c, \"AdminOTPVerification\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "axios", "FaKey", "FaEnvelope", "FaSpinner", "FaExclamationTriangle", "FaArrowLeft", "FaRedo", "jsxDEV", "_jsxDEV", "API_BASE_URL", "AdminOTPVerification", "adminId", "username", "onSuccess", "onBack", "_s", "otp", "setOtp", "loading", "setLoading", "error", "setError", "success", "setSuccess", "timeLeft", "setTimeLeft", "canResend", "setCanResend", "resendLoading", "setResendLoading", "otpSent", "setOtpSent", "inputRefs", "sendOTP", "timer", "setTimeout", "clearTimeout", "response", "post", "admin_id", "data", "email_masked", "expires_in", "message", "err", "console", "handleOtpChange", "index", "value", "length", "newOtp", "_inputRefs$current", "current", "focus", "every", "digit", "join", "verifyOTP", "handleKeyDown", "e", "key", "_inputRefs$current2", "otpCode", "codeToVerify", "role", "session_token", "auth_method", "_inputRefs$current$", "_inputRefs$current$2", "formatTime", "seconds", "mins", "Math", "floor", "secs", "toString", "padStart", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "ref", "el", "type", "max<PERSON><PERSON><PERSON>", "onChange", "target", "onKeyDown", "disabled", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/components/Admin/AdminOTPVerification.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport axios from 'axios';\nimport { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>a<PERSON><PERSON><PERSON>, FaExclamationTriangle, FaArrowLeft, FaRedo } from 'react-icons/fa';\n\nconst API_BASE_URL = '/backend';\n\nconst AdminOTPVerification = ({ adminId, username, onSuccess, onBack }) => {\n    const [otp, setOtp] = useState(['', '', '', '', '', '']);\n    const [loading, setLoading] = useState(false);\n    const [error, setError] = useState('');\n    const [success, setSuccess] = useState('');\n    const [timeLeft, setTimeLeft] = useState(0);\n    const [canResend, setCanResend] = useState(false);\n    const [resendLoading, setResendLoading] = useState(false);\n    const [otpSent, setOtpSent] = useState(false);\n    \n    const inputRefs = useRef([]);\n\n    useEffect(() => {\n        // Send initial OTP when component mounts\n        sendOTP();\n    }, []);\n\n    useEffect(() => {\n        // Timer for OTP expiry\n        if (timeLeft > 0) {\n            const timer = setTimeout(() => setTimeLeft(timeLeft - 1), 1000);\n            return () => clearTimeout(timer);\n        } else if (otpSent) {\n            setCanResend(true);\n        }\n    }, [timeLeft, otpSent]);\n\n    const sendOTP = async () => {\n        try {\n            setResendLoading(true);\n            setError('');\n            \n            const response = await axios.post(`${API_BASE_URL}/handlers/admin_send_otp.php`, {\n                admin_id: adminId\n            });\n\n            if (response.data.success) {\n                setSuccess(`OTP sent to ${response.data.email_masked}`);\n                setTimeLeft(response.data.expires_in || 300);\n                setCanResend(false);\n                setOtpSent(true);\n                setTimeout(() => setSuccess(''), 3000);\n            } else {\n                setError(response.data.message || 'Failed to send OTP');\n            }\n        } catch (err) {\n            setError('Failed to send OTP. Please try again.');\n            console.error('OTP send error:', err);\n        } finally {\n            setResendLoading(false);\n        }\n    };\n\n    const handleOtpChange = (index, value) => {\n        if (value.length > 1) return; // Prevent multiple characters\n        \n        const newOtp = [...otp];\n        newOtp[index] = value;\n        setOtp(newOtp);\n\n        // Auto-focus next input\n        if (value && index < 5) {\n            inputRefs.current[index + 1]?.focus();\n        }\n\n        // Auto-submit when all fields are filled\n        if (newOtp.every(digit => digit !== '') && newOtp.join('').length === 6) {\n            verifyOTP(newOtp.join(''));\n        }\n    };\n\n    const handleKeyDown = (index, e) => {\n        if (e.key === 'Backspace' && !otp[index] && index > 0) {\n            inputRefs.current[index - 1]?.focus();\n        }\n    };\n\n    const verifyOTP = async (otpCode = null) => {\n        const codeToVerify = otpCode || otp.join('');\n        \n        if (codeToVerify.length !== 6) {\n            setError('Please enter the complete 6-digit OTP code');\n            return;\n        }\n\n        try {\n            setLoading(true);\n            setError('');\n\n            const response = await axios.post(`${API_BASE_URL}/handlers/admin_verify_otp.php`, {\n                admin_id: adminId,\n                otp: codeToVerify\n            });\n\n            if (response.data.success) {\n                setSuccess('OTP verified successfully!');\n                setTimeout(() => {\n                    onSuccess({\n                        admin_id: response.data.admin_id,\n                        username: response.data.username,\n                        role: response.data.role,\n                        session_token: response.data.session_token,\n                        auth_method: response.data.auth_method\n                    });\n                }, 1000);\n            } else {\n                setError(response.data.message || 'Invalid OTP code');\n                // Clear OTP inputs on error\n                setOtp(['', '', '', '', '', '']);\n                inputRefs.current[0]?.focus();\n            }\n        } catch (err) {\n            setError('Failed to verify OTP. Please try again.');\n            console.error('OTP verification error:', err);\n            setOtp(['', '', '', '', '', '']);\n            inputRefs.current[0]?.focus();\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const formatTime = (seconds) => {\n        const mins = Math.floor(seconds / 60);\n        const secs = seconds % 60;\n        return `${mins}:${secs.toString().padStart(2, '0')}`;\n    };\n\n    return (\n        <div className=\"min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8\">\n            <div className=\"max-w-md w-full space-y-8\">\n                <div>\n                    <div className=\"mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-blue-100\">\n                        <FaKey className=\"h-6 w-6 text-blue-600\" />\n                    </div>\n                    <h2 className=\"mt-6 text-center text-3xl font-extrabold text-gray-900\">\n                        Enter OTP Code\n                    </h2>\n                    <p className=\"mt-2 text-center text-sm text-gray-600\">\n                        We've sent a 6-digit code to your registered email address\n                    </p>\n                    <p className=\"text-center text-sm font-medium text-gray-900\">\n                        {username}\n                    </p>\n                </div>\n\n                <div className=\"mt-8 space-y-6\">\n                    {/* Error Message */}\n                    {error && (\n                        <div className=\"bg-red-50 border border-red-200 rounded-md p-4 flex items-center gap-3\">\n                            <FaExclamationTriangle className=\"text-red-500\" />\n                            <span className=\"text-red-700 text-sm\">{error}</span>\n                        </div>\n                    )}\n\n                    {/* Success Message */}\n                    {success && (\n                        <div className=\"bg-green-50 border border-green-200 rounded-md p-4 flex items-center gap-3\">\n                            <FaEnvelope className=\"text-green-500\" />\n                            <span className=\"text-green-700 text-sm\">{success}</span>\n                        </div>\n                    )}\n\n                    {/* OTP Input */}\n                    <div>\n                        <label className=\"block text-sm font-medium text-gray-700 mb-4 text-center\">\n                            Enter the 6-digit code\n                        </label>\n                        <div className=\"flex justify-center space-x-3\">\n                            {otp.map((digit, index) => (\n                                <input\n                                    key={index}\n                                    ref={el => inputRefs.current[index] = el}\n                                    type=\"text\"\n                                    maxLength=\"1\"\n                                    value={digit}\n                                    onChange={(e) => handleOtpChange(index, e.target.value)}\n                                    onKeyDown={(e) => handleKeyDown(index, e)}\n                                    className=\"w-12 h-12 text-center text-xl font-bold border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                                    disabled={loading}\n                                />\n                            ))}\n                        </div>\n                    </div>\n\n                    {/* Timer */}\n                    {timeLeft > 0 && (\n                        <div className=\"text-center\">\n                            <p className=\"text-sm text-gray-600\">\n                                Code expires in: <span className=\"font-medium text-blue-600\">{formatTime(timeLeft)}</span>\n                            </p>\n                        </div>\n                    )}\n\n                    {/* Verify Button */}\n                    <div>\n                        <button\n                            onClick={() => verifyOTP()}\n                            disabled={loading || otp.join('').length !== 6}\n                            className=\"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\"\n                        >\n                            {loading && <FaSpinner className=\"animate-spin mr-2\" />}\n                            {loading ? 'Verifying...' : 'Verify OTP'}\n                        </button>\n                    </div>\n\n                    {/* Resend OTP */}\n                    <div className=\"text-center\">\n                        <button\n                            onClick={sendOTP}\n                            disabled={!canResend || resendLoading}\n                            className=\"text-sm text-blue-600 hover:text-blue-500 disabled:text-gray-400 disabled:cursor-not-allowed flex items-center justify-center gap-2 mx-auto\"\n                        >\n                            {resendLoading ? (\n                                <FaSpinner className=\"animate-spin\" />\n                            ) : (\n                                <FaRedo />\n                            )}\n                            {resendLoading ? 'Sending...' : 'Resend OTP'}\n                        </button>\n                    </div>\n\n                    {/* Back Button */}\n                    <div className=\"text-center\">\n                        <button\n                            onClick={onBack}\n                            className=\"text-sm text-gray-600 hover:text-gray-500 flex items-center justify-center gap-2 mx-auto\"\n                        >\n                            <FaArrowLeft />\n                            Back to Login\n                        </button>\n                    </div>\n                </div>\n            </div>\n        </div>\n    );\n};\n\nexport default AdminOTPVerification;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,KAAK,EAAEC,UAAU,EAAEC,SAAS,EAAEC,qBAAqB,EAAEC,WAAW,EAAEC,MAAM,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1G,MAAMC,YAAY,GAAG,UAAU;AAE/B,MAAMC,oBAAoB,GAAGA,CAAC;EAAEC,OAAO;EAAEC,QAAQ;EAAEC,SAAS;EAAEC;AAAO,CAAC,KAAK;EAAAC,EAAA;EACvE,MAAM,CAACC,GAAG,EAAEC,MAAM,CAAC,GAAGpB,QAAQ,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;EACxD,MAAM,CAACqB,OAAO,EAAEC,UAAU,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACuB,KAAK,EAAEC,QAAQ,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACyB,OAAO,EAAEC,UAAU,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC2B,QAAQ,EAAEC,WAAW,CAAC,GAAG5B,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM,CAAC6B,SAAS,EAAEC,YAAY,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC+B,aAAa,EAAEC,gBAAgB,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACiC,OAAO,EAAEC,UAAU,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EAE7C,MAAMmC,SAAS,GAAGjC,MAAM,CAAC,EAAE,CAAC;EAE5BD,SAAS,CAAC,MAAM;IACZ;IACAmC,OAAO,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;EAENnC,SAAS,CAAC,MAAM;IACZ;IACA,IAAI0B,QAAQ,GAAG,CAAC,EAAE;MACd,MAAMU,KAAK,GAAGC,UAAU,CAAC,MAAMV,WAAW,CAACD,QAAQ,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC;MAC/D,OAAO,MAAMY,YAAY,CAACF,KAAK,CAAC;IACpC,CAAC,MAAM,IAAIJ,OAAO,EAAE;MAChBH,YAAY,CAAC,IAAI,CAAC;IACtB;EACJ,CAAC,EAAE,CAACH,QAAQ,EAAEM,OAAO,CAAC,CAAC;EAEvB,MAAMG,OAAO,GAAG,MAAAA,CAAA,KAAY;IACxB,IAAI;MACAJ,gBAAgB,CAAC,IAAI,CAAC;MACtBR,QAAQ,CAAC,EAAE,CAAC;MAEZ,MAAMgB,QAAQ,GAAG,MAAMrC,KAAK,CAACsC,IAAI,CAAC,GAAG7B,YAAY,8BAA8B,EAAE;QAC7E8B,QAAQ,EAAE5B;MACd,CAAC,CAAC;MAEF,IAAI0B,QAAQ,CAACG,IAAI,CAAClB,OAAO,EAAE;QACvBC,UAAU,CAAC,eAAec,QAAQ,CAACG,IAAI,CAACC,YAAY,EAAE,CAAC;QACvDhB,WAAW,CAACY,QAAQ,CAACG,IAAI,CAACE,UAAU,IAAI,GAAG,CAAC;QAC5Cf,YAAY,CAAC,KAAK,CAAC;QACnBI,UAAU,CAAC,IAAI,CAAC;QAChBI,UAAU,CAAC,MAAMZ,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;MAC1C,CAAC,MAAM;QACHF,QAAQ,CAACgB,QAAQ,CAACG,IAAI,CAACG,OAAO,IAAI,oBAAoB,CAAC;MAC3D;IACJ,CAAC,CAAC,OAAOC,GAAG,EAAE;MACVvB,QAAQ,CAAC,uCAAuC,CAAC;MACjDwB,OAAO,CAACzB,KAAK,CAAC,iBAAiB,EAAEwB,GAAG,CAAC;IACzC,CAAC,SAAS;MACNf,gBAAgB,CAAC,KAAK,CAAC;IAC3B;EACJ,CAAC;EAED,MAAMiB,eAAe,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;IACtC,IAAIA,KAAK,CAACC,MAAM,GAAG,CAAC,EAAE,OAAO,CAAC;;IAE9B,MAAMC,MAAM,GAAG,CAAC,GAAGlC,GAAG,CAAC;IACvBkC,MAAM,CAACH,KAAK,CAAC,GAAGC,KAAK;IACrB/B,MAAM,CAACiC,MAAM,CAAC;;IAEd;IACA,IAAIF,KAAK,IAAID,KAAK,GAAG,CAAC,EAAE;MAAA,IAAAI,kBAAA;MACpB,CAAAA,kBAAA,GAAAnB,SAAS,CAACoB,OAAO,CAACL,KAAK,GAAG,CAAC,CAAC,cAAAI,kBAAA,uBAA5BA,kBAAA,CAA8BE,KAAK,CAAC,CAAC;IACzC;;IAEA;IACA,IAAIH,MAAM,CAACI,KAAK,CAACC,KAAK,IAAIA,KAAK,KAAK,EAAE,CAAC,IAAIL,MAAM,CAACM,IAAI,CAAC,EAAE,CAAC,CAACP,MAAM,KAAK,CAAC,EAAE;MACrEQ,SAAS,CAACP,MAAM,CAACM,IAAI,CAAC,EAAE,CAAC,CAAC;IAC9B;EACJ,CAAC;EAED,MAAME,aAAa,GAAGA,CAACX,KAAK,EAAEY,CAAC,KAAK;IAChC,IAAIA,CAAC,CAACC,GAAG,KAAK,WAAW,IAAI,CAAC5C,GAAG,CAAC+B,KAAK,CAAC,IAAIA,KAAK,GAAG,CAAC,EAAE;MAAA,IAAAc,mBAAA;MACnD,CAAAA,mBAAA,GAAA7B,SAAS,CAACoB,OAAO,CAACL,KAAK,GAAG,CAAC,CAAC,cAAAc,mBAAA,uBAA5BA,mBAAA,CAA8BR,KAAK,CAAC,CAAC;IACzC;EACJ,CAAC;EAED,MAAMI,SAAS,GAAG,MAAAA,CAAOK,OAAO,GAAG,IAAI,KAAK;IACxC,MAAMC,YAAY,GAAGD,OAAO,IAAI9C,GAAG,CAACwC,IAAI,CAAC,EAAE,CAAC;IAE5C,IAAIO,YAAY,CAACd,MAAM,KAAK,CAAC,EAAE;MAC3B5B,QAAQ,CAAC,4CAA4C,CAAC;MACtD;IACJ;IAEA,IAAI;MACAF,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,EAAE,CAAC;MAEZ,MAAMgB,QAAQ,GAAG,MAAMrC,KAAK,CAACsC,IAAI,CAAC,GAAG7B,YAAY,gCAAgC,EAAE;QAC/E8B,QAAQ,EAAE5B,OAAO;QACjBK,GAAG,EAAE+C;MACT,CAAC,CAAC;MAEF,IAAI1B,QAAQ,CAACG,IAAI,CAAClB,OAAO,EAAE;QACvBC,UAAU,CAAC,4BAA4B,CAAC;QACxCY,UAAU,CAAC,MAAM;UACbtB,SAAS,CAAC;YACN0B,QAAQ,EAAEF,QAAQ,CAACG,IAAI,CAACD,QAAQ;YAChC3B,QAAQ,EAAEyB,QAAQ,CAACG,IAAI,CAAC5B,QAAQ;YAChCoD,IAAI,EAAE3B,QAAQ,CAACG,IAAI,CAACwB,IAAI;YACxBC,aAAa,EAAE5B,QAAQ,CAACG,IAAI,CAACyB,aAAa;YAC1CC,WAAW,EAAE7B,QAAQ,CAACG,IAAI,CAAC0B;UAC/B,CAAC,CAAC;QACN,CAAC,EAAE,IAAI,CAAC;MACZ,CAAC,MAAM;QAAA,IAAAC,mBAAA;QACH9C,QAAQ,CAACgB,QAAQ,CAACG,IAAI,CAACG,OAAO,IAAI,kBAAkB,CAAC;QACrD;QACA1B,MAAM,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;QAChC,CAAAkD,mBAAA,GAAAnC,SAAS,CAACoB,OAAO,CAAC,CAAC,CAAC,cAAAe,mBAAA,uBAApBA,mBAAA,CAAsBd,KAAK,CAAC,CAAC;MACjC;IACJ,CAAC,CAAC,OAAOT,GAAG,EAAE;MAAA,IAAAwB,oBAAA;MACV/C,QAAQ,CAAC,yCAAyC,CAAC;MACnDwB,OAAO,CAACzB,KAAK,CAAC,yBAAyB,EAAEwB,GAAG,CAAC;MAC7C3B,MAAM,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;MAChC,CAAAmD,oBAAA,GAAApC,SAAS,CAACoB,OAAO,CAAC,CAAC,CAAC,cAAAgB,oBAAA,uBAApBA,oBAAA,CAAsBf,KAAK,CAAC,CAAC;IACjC,CAAC,SAAS;MACNlC,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,MAAMkD,UAAU,GAAIC,OAAO,IAAK;IAC5B,MAAMC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,GAAG,EAAE,CAAC;IACrC,MAAMI,IAAI,GAAGJ,OAAO,GAAG,EAAE;IACzB,OAAO,GAAGC,IAAI,IAAIG,IAAI,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EACxD,CAAC;EAED,oBACIpE,OAAA;IAAKqE,SAAS,EAAC,qFAAqF;IAAAC,QAAA,eAChGtE,OAAA;MAAKqE,SAAS,EAAC,2BAA2B;MAAAC,QAAA,gBACtCtE,OAAA;QAAAsE,QAAA,gBACItE,OAAA;UAAKqE,SAAS,EAAC,6EAA6E;UAAAC,QAAA,eACxFtE,OAAA,CAACP,KAAK;YAAC4E,SAAS,EAAC;UAAuB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC,eACN1E,OAAA;UAAIqE,SAAS,EAAC,wDAAwD;UAAAC,QAAA,EAAC;QAEvE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL1E,OAAA;UAAGqE,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EAAC;QAEtD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJ1E,OAAA;UAAGqE,SAAS,EAAC,+CAA+C;UAAAC,QAAA,EACvDlE;QAAQ;UAAAmE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN1E,OAAA;QAAKqE,SAAS,EAAC,gBAAgB;QAAAC,QAAA,GAE1B1D,KAAK,iBACFZ,OAAA;UAAKqE,SAAS,EAAC,wEAAwE;UAAAC,QAAA,gBACnFtE,OAAA,CAACJ,qBAAqB;YAACyE,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAClD1E,OAAA;YAAMqE,SAAS,EAAC,sBAAsB;YAAAC,QAAA,EAAE1D;UAAK;YAAA2D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpD,CACR,EAGA5D,OAAO,iBACJd,OAAA;UAAKqE,SAAS,EAAC,4EAA4E;UAAAC,QAAA,gBACvFtE,OAAA,CAACN,UAAU;YAAC2E,SAAS,EAAC;UAAgB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACzC1E,OAAA;YAAMqE,SAAS,EAAC,wBAAwB;YAAAC,QAAA,EAAExD;UAAO;YAAAyD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxD,CACR,eAGD1E,OAAA;UAAAsE,QAAA,gBACItE,OAAA;YAAOqE,SAAS,EAAC,0DAA0D;YAAAC,QAAA,EAAC;UAE5E;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR1E,OAAA;YAAKqE,SAAS,EAAC,+BAA+B;YAAAC,QAAA,EACzC9D,GAAG,CAACmE,GAAG,CAAC,CAAC5B,KAAK,EAAER,KAAK,kBAClBvC,OAAA;cAEI4E,GAAG,EAAEC,EAAE,IAAIrD,SAAS,CAACoB,OAAO,CAACL,KAAK,CAAC,GAAGsC,EAAG;cACzCC,IAAI,EAAC,MAAM;cACXC,SAAS,EAAC,GAAG;cACbvC,KAAK,EAAEO,KAAM;cACbiC,QAAQ,EAAG7B,CAAC,IAAKb,eAAe,CAACC,KAAK,EAAEY,CAAC,CAAC8B,MAAM,CAACzC,KAAK,CAAE;cACxD0C,SAAS,EAAG/B,CAAC,IAAKD,aAAa,CAACX,KAAK,EAAEY,CAAC,CAAE;cAC1CkB,SAAS,EAAC,kIAAkI;cAC5Ic,QAAQ,EAAEzE;YAAQ,GARb6B,KAAK;cAAAgC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OASb,CACJ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,EAGL1D,QAAQ,GAAG,CAAC,iBACThB,OAAA;UAAKqE,SAAS,EAAC,aAAa;UAAAC,QAAA,eACxBtE,OAAA;YAAGqE,SAAS,EAAC,uBAAuB;YAAAC,QAAA,GAAC,mBAChB,eAAAtE,OAAA;cAAMqE,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAET,UAAU,CAAC7C,QAAQ;YAAC;cAAAuD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3F;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACR,eAGD1E,OAAA;UAAAsE,QAAA,eACItE,OAAA;YACIoF,OAAO,EAAEA,CAAA,KAAMnC,SAAS,CAAC,CAAE;YAC3BkC,QAAQ,EAAEzE,OAAO,IAAIF,GAAG,CAACwC,IAAI,CAAC,EAAE,CAAC,CAACP,MAAM,KAAK,CAAE;YAC/C4B,SAAS,EAAC,+QAA+Q;YAAAC,QAAA,GAExR5D,OAAO,iBAAIV,OAAA,CAACL,SAAS;cAAC0E,SAAS,EAAC;YAAmB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EACtDhE,OAAO,GAAG,cAAc,GAAG,YAAY;UAAA;YAAA6D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eAGN1E,OAAA;UAAKqE,SAAS,EAAC,aAAa;UAAAC,QAAA,eACxBtE,OAAA;YACIoF,OAAO,EAAE3D,OAAQ;YACjB0D,QAAQ,EAAE,CAACjE,SAAS,IAAIE,aAAc;YACtCiD,SAAS,EAAC,6IAA6I;YAAAC,QAAA,GAEtJlD,aAAa,gBACVpB,OAAA,CAACL,SAAS;cAAC0E,SAAS,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAEtC1E,OAAA,CAACF,MAAM;cAAAyE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CACZ,EACAtD,aAAa,GAAG,YAAY,GAAG,YAAY;UAAA;YAAAmD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eAGN1E,OAAA;UAAKqE,SAAS,EAAC,aAAa;UAAAC,QAAA,eACxBtE,OAAA;YACIoF,OAAO,EAAE9E,MAAO;YAChB+D,SAAS,EAAC,0FAA0F;YAAAC,QAAA,gBAEpGtE,OAAA,CAACH,WAAW;cAAA0E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,iBAEnB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAACnE,EAAA,CA3OIL,oBAAoB;AAAAmF,EAAA,GAApBnF,oBAAoB;AA6O1B,eAAeA,oBAAoB;AAAC,IAAAmF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}